package tokenizer

import (
	"cosy/log"
	"github.com/blevesearch/bleve/v2/analysis"
	"github.com/blevesearch/bleve/v2/registry"
	"runtime/debug"
)

const (
	JiebaTokenizerName = "jieba"
)

type JiebaBleveTokenizer struct {
	seg *JiebaTokenizer
}

func NewJiebaBleveTokenizer() *JiebaBleveTokenizer {
	return &JiebaBleveTokenizer{
		seg: NewJiebaTokenizer(true),
	}
}

// 代码切块时，分词的关键tokenizer
func (rt *JiebaBleveTokenizer) Tokenize(input []byte) analysis.TokenStream {
	defer func() {
		if r := recover(); r != nil {
			log.Debugf("recover from tokenize. input: %s, err: %+v stack: %s", input, r, debug.Stack())
		}
	}()
	rv := make(analysis.TokenStream, 0, 10)
	tokens, err := rt.seg.Tokenize(string(input))
	if err != nil {
		return rv
	}
	var mergeAnalysisToken *analysis.Token
	for _, token := range tokens {
		if mergeAnalysisToken != nil && mergeAnalysisToken.End == token.Start && token.Term != ">" {
			// 适配前端标签的情况，将尖括号与标签拼接在一起，减少误召回
			mergeAnalysisToken.End = token.End
			mergeAnalysisToken.Term = append(mergeAnalysisToken.Term, []byte(token.Term)...)
			continue
		} else if mergeAnalysisToken != nil {
			rv = append(rv, mergeAnalysisToken)
			mergeAnalysisToken = nil
		}
		bytes := []byte(token.Term)
		analysisToken := &analysis.Token{
			Term:     bytes,
			Start:    token.Start,
			End:      token.End,
			Position: token.Position,
			Type:     analysis.AlphaNumeric,
		}
		if token.Term == "<" || token.Term == "</" {
			// 适配前端标签
			mergeAnalysisToken = analysisToken
		}
		if !rt.seg.IsStop(token.Term) {
			rv = append(rv, analysisToken)
		}
	}
	if mergeAnalysisToken != nil {
		rv = append(rv, mergeAnalysisToken)
		mergeAnalysisToken = nil
	}
	return rv
}

func JiebaTokenizerConstructor(config map[string]interface{}, cache *registry.Cache) (analysis.Tokenizer, error) {
	return NewJiebaBleveTokenizer(), nil
}

func init() {
	registry.RegisterTokenizer(JiebaTokenizerName, JiebaTokenizerConstructor)
}
