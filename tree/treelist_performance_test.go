package tree

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"testing"
	"time"

	merkletree "code.alibaba-inc.com/cosy/mtree"
)

// TestTreeToList_MemoryUsage 测试内存使用情况
func TestTreeToList_MemoryUsage(t *testing.T) {
	// 创建临时测试目录
	tempDir, err := os.MkdirTemp("", "memory_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 创建5000个文件来测试内存使用
	const fileCount = 5000
	for i := 0; i < fileCount; i++ {
		dirName := fmt.Sprintf("dir%03d", i/100)
		dirPath := filepath.Join(tempDir, dirName)
		if err := os.MkdirAll(dirPath, 0755); err != nil {
			t.Fatalf("Failed to create dir: %v", err)
		}

		fileName := filepath.Join(dirPath, fmt.Sprintf("file%04d.go", i))
		if err := os.WriteFile(fileName, []byte("package main"), 0644); err != nil {
			t.Fatalf("Failed to create file: %v", err)
		}
	}

	merkleTree := &MerkleTree{
		Tree: merkletree.NewMerkleTree(tempDir),
	}
	merkleTree.Tree.Build(nil)
	merkleTree.Initialize(tempDir)

	// 记录初始内存使用
	var m1, m2 runtime.MemStats
	runtime.GC()
	runtime.ReadMemStats(&m1)

	// 执行 TreeToList
	start := time.Now()
	fileList := merkleTree.TreeToList()
	duration := time.Since(start)

	// 记录执行后内存使用
	runtime.ReadMemStats(&m2)

	// 验证结果
	if len(fileList) != fileCount {
		t.Errorf("Expected %d files, got %d", fileCount, len(fileList))
	}

	// 输出性能指标
	allocatedMemory := m2.TotalAlloc - m1.TotalAlloc
	t.Logf("TreeToList Performance Metrics:")
	t.Logf("  Files processed: %d", len(fileList))
	t.Logf("  Execution time: %v", duration)
	t.Logf("  Memory allocated: %d bytes (%.2f KB)", allocatedMemory, float64(allocatedMemory)/1024)
	t.Logf("  Memory per file: %.2f bytes", float64(allocatedMemory)/float64(len(fileList)))
	t.Logf("  Files per second: %.0f", float64(len(fileList))/duration.Seconds())

	// 验证内存使用是否合理（每个文件平均不超过100字节的额外内存）
	memoryPerFile := float64(allocatedMemory) / float64(len(fileList))
	if memoryPerFile > 100 {
		t.Errorf("Memory usage per file too high: %.2f bytes (expected < 100)", memoryPerFile)
	}
}

// TestTreeToList_MaxFileLimit_Exact 测试精确的最大文件限制
func TestTreeToList_MaxFileLimit_Exact(t *testing.T) {
	// 创建临时测试目录
	tempDir, err := os.MkdirTemp("", "maxlimit_exact_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 创建超过10000个文件来测试限制
	const fileCount = 12000
	for i := 0; i < fileCount; i++ {
		level1 := fmt.Sprintf("level1_%03d", i/1000)
		level2 := fmt.Sprintf("level2_%03d", (i%1000)/100)
		dirPath := filepath.Join(tempDir, level1, level2)
		if err := os.MkdirAll(dirPath, 0755); err != nil {
			t.Fatalf("Failed to create dir: %v", err)
		}

		fileName := filepath.Join(dirPath, fmt.Sprintf("file%05d.go", i))
		if err := os.WriteFile(fileName, []byte("package main"), 0644); err != nil {
			t.Fatalf("Failed to create file: %v", err)
		}
	}

	merkleTree := &MerkleTree{
		Tree: merkletree.NewMerkleTree(tempDir),
	}
	merkleTree.Tree.Build(nil)
	merkleTree.Initialize(tempDir)

	// 测试 TreeToList 方法
	fileList := merkleTree.TreeToList()

	// 验证返回的文件数量不超过10000
	const maxFiles = 10000
	if len(fileList) > maxFiles {
		t.Errorf("TreeToList should limit to %d files, but returned %d", maxFiles, len(fileList))
	}

	t.Logf("Created %d files, TreeToList returned %d files (limit: %d)", fileCount, len(fileList), maxFiles)
}

// TestTreeToList_ConcurrentAccess 测试并发访问安全性
func TestTreeToList_ConcurrentAccess(t *testing.T) {
	// 创建临时测试目录
	tempDir, err := os.MkdirTemp("", "concurrent_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 创建1000个文件
	const fileCount = 1000
	for i := 0; i < fileCount; i++ {
		fileName := filepath.Join(tempDir, fmt.Sprintf("file%04d.go", i))
		if err := os.WriteFile(fileName, []byte("package main"), 0644); err != nil {
			t.Fatalf("Failed to create file: %v", err)
		}
	}

	merkleTree := &MerkleTree{
		Tree: merkletree.NewMerkleTree(tempDir),
	}
	merkleTree.Tree.Build(nil)
	merkleTree.Initialize(tempDir)

	// 并发执行 TreeToList
	const goroutineCount = 10
	results := make(chan []string, goroutineCount)
	errors := make(chan error, goroutineCount)

	for i := 0; i < goroutineCount; i++ {
		go func() {
			fileList := merkleTree.TreeToList()
			if len(fileList) != fileCount {
				errors <- fmt.Errorf("expected %d files, got %d", fileCount, len(fileList))
				return
			}
			results <- fileList
		}()
	}

	// 收集结果
	for i := 0; i < goroutineCount; i++ {
		select {
		case err := <-errors:
			t.Errorf("Concurrent access error: %v", err)
		case result := <-results:
			if len(result) != fileCount {
				t.Errorf("Concurrent access returned wrong file count: %d", len(result))
			}
		case <-time.After(5 * time.Second):
			t.Errorf("Concurrent access timeout")
		}
	}

	t.Logf("Concurrent access test completed successfully with %d goroutines", goroutineCount)
}

// BenchmarkTreeToList_MemoryEfficiency 内存效率基准测试
func BenchmarkTreeToList_MemoryEfficiency(b *testing.B) {
	// 创建测试目录
	tempDir, err := os.MkdirTemp("", "bench_memory")
	if err != nil {
		b.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 创建1000个文件
	for i := 0; i < 1000; i++ {
		dirName := fmt.Sprintf("dir%02d", i/100)
		dirPath := filepath.Join(tempDir, dirName)
		if err := os.MkdirAll(dirPath, 0755); err != nil {
			b.Fatalf("Failed to create dir: %v", err)
		}

		fileName := filepath.Join(dirPath, fmt.Sprintf("file%03d.go", i))
		if err := os.WriteFile(fileName, []byte("package main"), 0644); err != nil {
			b.Fatalf("Failed to create file: %v", err)
		}
	}

	merkleTree := &MerkleTree{
		Tree: merkletree.NewMerkleTree(tempDir),
	}
	merkleTree.Tree.Build(nil)
	merkleTree.Initialize(tempDir)

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		fileList := merkleTree.TreeToList()
		if len(fileList) != 1000 {
			b.Errorf("Expected 1000 files, got %d", len(fileList))
		}
		// 强制释放内存，测试是否有内存泄漏
		fileList = nil
		if i%100 == 0 {
			runtime.GC()
		}
	}
}

// BenchmarkTreeToList_ScalabilityTest 可扩展性测试
func BenchmarkTreeToList_ScalabilityTest(b *testing.B) {
	fileCounts := []int{100, 500, 1000, 2000, 5000}

	for _, fileCount := range fileCounts {
		b.Run(fmt.Sprintf("Files_%d", fileCount), func(b *testing.B) {
			// 创建测试目录
			tempDir, err := os.MkdirTemp("", fmt.Sprintf("bench_scale_%d", fileCount))
			if err != nil {
				b.Fatalf("Failed to create temp dir: %v", err)
			}
			defer os.RemoveAll(tempDir)

			// 创建指定数量的文件
			for i := 0; i < fileCount; i++ {
				level1 := fmt.Sprintf("level1_%02d", i/100)
				level2 := fmt.Sprintf("level2_%02d", (i%100)/10)
				dirPath := filepath.Join(tempDir, level1, level2)
				if err := os.MkdirAll(dirPath, 0755); err != nil {
					b.Fatalf("Failed to create dir: %v", err)
				}

				fileName := filepath.Join(dirPath, fmt.Sprintf("file%04d.go", i))
				if err := os.WriteFile(fileName, []byte("package main"), 0644); err != nil {
					b.Fatalf("Failed to create file: %v", err)
				}
			}

			merkleTree := &MerkleTree{
				Tree: merkletree.NewMerkleTree(tempDir),
			}
			merkleTree.Tree.Build(nil)
			merkleTree.Initialize(tempDir)

			b.ResetTimer()
			b.ReportAllocs()

			for i := 0; i < b.N; i++ {
				fileList := merkleTree.TreeToList()
				if len(fileList) != fileCount {
					b.Errorf("Expected %d files, got %d", fileCount, len(fileList))
				}
			}
		})
	}
}
