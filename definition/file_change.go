package definition

type UpdateFileChangeParam struct {
	FilePath        string `json:"filePath"`
	OriginalContent string `json:"originalContent"`
	ModifiedContent string `json:"modifiedContent"`
	EditMode        string `json:"editMode"`
}

type FileChangeInfo struct {
	FilePath        string `json:"filePath"`
	OriginalContent string `json:"originalContent"`
	ModifiedContent string `json:"modifiedContent"`
	EditMode        string `json:"editMode"`
}
