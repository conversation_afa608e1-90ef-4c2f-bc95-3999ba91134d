package definition

import (
	"crypto/md5"
	"crypto/sha256"
	"fmt"
	"strings"
	"time"
)

const (
	// DatabaseVersion 数据库版本
	DatabaseVersion = "v6"

	EmbeddingSourceShareChunk = "source_share_chunk"
	EmbeddingSourceEmbedding  = "source_embedding"

	// SkipScanStoragePercentage 存储的文件数量达到这个比例时，跳过全量扫描
	SkipScanStoragePercentage = 0.9

	// RebuildIndexStatusTreeInterval 索引状态树重建的间隔时间
	// 并非直接重建，而是检查索引状态，如状态出现不一致，则执行重建
	RebuildIndexStatusTreeInterval = 30 * time.Minute
)

// StorageChunk
// 数据库中存储的实体
// 向量索引建立过程中的数据流动实体
// 数据中不存储切块内容，在召回时按照偏移直接读取本地文件
// 存储分成两个表
// ChunkId + Embedding 存入vec0向量表
// ChunkId + 剩余字段 存入chunk表
type StorageChunk struct {
	ChunkId     string    `json:"chunk_id"`               // 唯一Id，可计算，可用于验证content是否一致
	FilePath    string    `json:"file_path"`              // 绝对路径
	FileName    string    `json:"file_name"`              // 文件名
	StartLine   uint32    `json:"start_line,omitempty"`   // 代码块在文件中的起始行号
	EndLine     uint32    `json:"end_line,omitempty"`     // 代码块在文件中的结束行号
	StartOffset uint32    `json:"start_offset,omitempty"` // 代码块在文件中的起始偏移量
	EndOffset   uint32    `json:"end_offset,omitempty"`   // 代码块在文件中的结束偏移量
	Embedding   []float32 `json:"embedding"`              // 向量数据
	//Language      string    `json:"language"`                 // 代码块的语言类型
	//FileExtension string    `json:"file_extension,omitempty"` // 文件后缀，会在保存时自动识别，不用设置

	Content         string `json:"content,omitempty"`          // 不存储，代码块内容，仅在切块与存储时使用
	EmbeddingSource string `json:"embedding_source,omitempty"` // 不存储，标记embedding是来自于shareChunk还是embedding服务
}

// SqliteVecRetrieveResult
// 向量检索结果封装
type SqliteVecRetrieveResult struct {
	Chunks []SqliteVecRetrieveChunk
}

// SqliteVecRetrieveChunk
// 向量检索出来的chunk以及得分
type SqliteVecRetrieveChunk struct {
	StorageChunk
	Score float64
}

// PrettyString 用来调试的函数
func (chunk *SqliteVecRetrieveChunk) PrettyString() string {
	tmpl := `
		StartLine: %d,
		EndLine: %d,
		filePath: %s,
		FileName: %s,
	`
	//Content: %s,
	tmpl = strings.ReplaceAll(tmpl, "\t", "")
	tmpl = strings.ReplaceAll(tmpl, "\n", " ")
	return fmt.Sprintf(
		tmpl,
		chunk.StartLine,
		chunk.EndLine,
		chunk.FilePath,
		chunk.FileName,
		//chunk.Content,
	)
}

// QueryCondition 数据库检索的条件对象
// 未来可扩展的查询条件
type QueryCondition struct {
	QueryEmbedding []float32 // 查询向量，服务端检索时不使用这个，客户端检索时此值不能为空
	TopK           int       // 最多召回数量限制，相似度倒排
	ScoreThreshold float64   // 召回的相似度阈值
	Query          string    // 服务端检索的文本
	Strategy       string    // 服务端检索策略

	// 其他where条件，可以不传入
	// 以文件精确匹配为优先
	ExactFilePath   string // 文件路径的精确匹配
	FilePathPattern string // 文件路径的模糊匹配
}

// VectorIndexRecord
// 索引记录，最小粒度为文件
type VectorIndexRecord struct {
	FilePath   string `json:"file_path"`   // 文件绝对路径，用于唯一标识，索引记录表的主键
	Identifier string `json:"identifier"`  // 文件内容的md5，用来判断文件是否变更
	RecordTime int64  `json:"record_time"` // 索引建立时间
}

// VectorRunnerReportData
// 向量索引运行时向manager上报的数据
type VectorRunnerReportData struct {
	TotalTaskCnt        int // runner实际接收到的总任务数量
	FinishedTaskCnt     int // 实际执行embedding而完成的任务数量
	SkipTaskCnt         int // 跳过的任务数量，因为已建索引而跳过
	DiscardTaskCnt      int // 丢弃的任务数量，失败次数过多
	SkipTodoTaskCnt     int // 跳过的剩余任务数量，因为存储已满而跳过的剩余任务数量
	ShareChunkHitCnt    int // 命中的共享chunk数量
	DoEmbeddingChunkCnt int // 实际执行Embedding的chunk数量
}

func (reportData *VectorRunnerReportData) GetActualDoneTaskCnt() int {
	return reportData.FinishedTaskCnt + reportData.SkipTaskCnt + reportData.DiscardTaskCnt + reportData.SkipTodoTaskCnt
}

type ShareChunkReportData struct {
	TotalStorageChunkCnt int    // 总共存储的共享chunk数量
	StorageRepoNameTop10 string // 存储top10的仓库名字，以逗号分隔
	HitCntFrom1To5       int    // 命中的共享chunk数量，范围1-5
	HitCntFrom6To10      int    // 6-10
	HitCntFrom11To20     int    // 11-20
	HitCntUpper21        int    // 21以上
}

type ShareChunkSlsQuery struct {
	QueryMethod string
	MinVal      int
	MaxVal      int
}

type ShareChunkSlsResult struct {
	Cnt       int
	RepoNames string
}

func GetWorkspaceId(workspacePath string) string {
	return fmt.Sprintf("%x", md5.Sum([]byte(workspacePath)))
	//hashId := md5.Sum([]byte(workspacePath))
	//return hex.EncodeToString(hashId[:])
}

func GetFileId(content []byte) string {
	return fmt.Sprintf("%x", sha256.Sum256(content))
}

func GetIdentifier(content string) string {
	return fmt.Sprintf("%x", md5.Sum([]byte(content)))
}

func NewShareChunkFromStorageChunk(chunk StorageChunk) *ShareChunkEntity {
	if chunk.Content == "" {
		return nil
	}

	return &ShareChunkEntity{
		ContentChunkId: GetFileId([]byte(chunk.Content)),
		SqliteChunkId:  chunk.ChunkId,
		RepoPath:       chunk.FilePath,
		HitCnt:         0,
	}
}

type ShareChunkEntity struct {
	ContentChunkId string
	SqliteChunkId  string
	RepoPath       string
	HitCnt         int
}
