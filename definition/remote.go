package definition

type CompletionResult struct {
	InvokeTimestamp string `json:"invokeTimestamp"`
	FetchTimestamp  string `json:"fetchTimestamp"`
	Body            string `json:"body"`
}

type ApiResponse struct {
	HttpStatusCode int              `json:"httpStatusCode"`
	Success        bool             `json:"success"`
	Result         CompletionResult `json:"result"`
}

type GraphResult struct {
	Outputs     map[string]interface{} `json:"outputs,omitempty"`
	NodeOutputs map[string]interface{} `json:"nodesOutputs,omitempty"`
	NodePath    []string               `json:"nodesPath,omitempty"`
	NodeResults map[string]interface{} `json:"nodesResult,omitempty"`
	Success     bool                   `json:"success,omitempty"`
	Message     string                 `json:"message,omitempty"`
}

// Response represents the top-level structure of the JSON response.
type LlmResponse struct {
	Output    LlmOutput `json:"output"`
	Usage     LlmUsage  `json:"usage"`
	RequestID string    `json:"request_id"`
}

// Output represents the output field in the JSON response.
type LlmOutput struct {
	FinishReason string `json:"finish_reason"`
	Text         string `json:"text"`
}

type LlmUsage struct {
	TotalTokens    int     `json:"total_tokens"`
	OutputTokens   int     `json:"output_tokens"`
	InputTokens    int     `json:"input_tokens"`
	FirstTokenTime float64 `json:"first_token_time"`
	TotalTime      float64 `json:"total_time"`
}

type RequestOptions struct {
	RouteType string //可选，指定Endpoint 路由偏好  //infer推理，center中心化
}

const (
	RouteTypeAuto        = "auto"
	RouteTypeCenter      = "center"
	RouteTypeInfer       = "infer"
	RouteTypeCodebase    = "codebase"
	RouteTypeRemoteAgent = "remote_agent"
	RouteTypeUnknown     = "unknown"
)
