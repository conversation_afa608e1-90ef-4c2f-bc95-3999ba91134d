package definition

// AddOrUpdateResult represents the result of a task record update operation
type AddOrUpdateResult struct {
	ID      string // Task ID
	Success bool   // Operation success status
	Message string // Result description
	Action  string // "added", "updated", "skipped"
}

// DetailPlan describes the detailed plan structure
type DetailPlan struct {
	TaskTreeJson    string `json:"taskTreeJson"`    // JSON representation of
	MarkdownContent string `json:"markdownContent"` // Markdown representation of the task tree
}
