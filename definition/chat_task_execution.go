package definition

type ExecuteTaskParams struct {
	Id                 string `json:"id"`
	AgentClass         string `json:"agentClass"` // Agent 类型，支持值：[RemoteAgent]
	ExecutionSessionId string `json:"executionSessionId"`
	ExecutionRequestId string `json:"executionRequestId"`
	SourceBranch       string `json:"sourceBranch"`
	RawConfig          string `json:"rawConfig"`
}

type ExecuteTaskResponse struct {
	BaseResponse
	Data Execution `json:"data"` // 返回 Agent 的相关信息，暂时只包含 AgentId
}

type UpdateExecutionParams struct {
	SourceBranch string `json:"sourceBranch"`
	HeadCommitId string `json:"headCommitId"`
}

type UpdateExecutionResponse struct {
	BaseResponse
}
