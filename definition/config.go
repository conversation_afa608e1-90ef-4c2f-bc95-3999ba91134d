package definition

type BuildOption struct {
	//资源版本号
	AssertVersion string

	BuildFormType string
}

type DevOption struct {
	// 本地ide开发模式
	LocalDev bool

	//开启后处理debug
	EnablePostProcessDebug bool

	// debug模式version
	CosyVersion string

	//是否开启go pprof
	EnableProfiling bool
}

type CosyConfig struct {
	// 是否允许搜集统计数据
	AllowReportUsage *bool       `json:"allowReportUsage,omitempty"`
	Local            LocalConfig `json:"local,omitempty"`
	Cloud            CloudConfig `json:"cloud,omitempty"`
}

type LocalConfig struct {
	Enable *bool `json:"enable,omitempty"`
	// auto / speed / length
	InferenceMode *string `json:"inferenceMode,omitempty"`
	// 最大保留的候选项个数
	MaxCandidateNum *int `json:"maxCandidateNum,omitempty"`
}

type CloudConfig struct {
	Enable        *bool               `json:"enable,omitempty"`
	AutoTrigger   AutoTriggerConfig   `json:"autoTrigger,omitempty"`
	ManualTrigger ManualTriggerConfig `json:"manualTrigger,omitempty"`
}

type AutoTriggerConfig struct {
	Enable *bool `json:"enable,omitempty"`
	// small / base / large
	ModelLevel *string `json:"modelLevel,omitempty"`
	// line / level_1 / level_2 / level_3
	GenerateLength *string `json:"generateLength,omitempty"`
}

type ManualTriggerConfig struct {
	// small / base / large
	ModelLevel *string `json:"modelLevel,omitempty"`
	// line / level_1 / level_2 / level_3
	GenerateLength *string `json:"generateLength,omitempty"`
}

// GlobalConfigParam 全局配置
type GlobalConfigParam struct {

	// 代理模式，system/manual
	ProxyMode string `json:"proxyMode"`

	// HttpProxy http代理
	HttpProxy string `json:"httpProxy"`

	// 运行执行的命令白名单，一个由","分割的命令列表
	CommandAllowList string `json:"commandAllowList"`

	// 运行执行的命令黑名单，一个由","分割的命令列表
	CommandDenyList string `json:"commandDenyList"`

	// MCP自动执行, enable-启用
	McpAutoRun string `json:"mcpAutoRun"`

	// 命令行工具执行模式 autoRun / askEveryTime
	TerminalRunMode string `json:"terminalRunMode"`

	// WebToolsExecutionMode defines how web tools should be executed:
	// - disabled: web tools are not enabled
	// - enabledAutoExecute: web tools are enabled and execute automatically
	// - enabledAskEveryTime: web tools are enabled but require confirmation before execution
	WebToolsExecutionMode string `json:"webToolsExecutionMode"`

	//工具开关
	AskModeUseTools string `json:"askModeUseTools"`
}

type ConfigWebToolsExecutionModeParam struct {
	// Web 工具配置
	WebToolsExecutionMode string `json:"webToolsExecutionMode"`
}

type ConfigCommandListParam struct {

	// 完整的命令字符串，例如："pwd && ls"
	CommandString string `json:"commandString"`
}

type ConfigEndpointParam struct {

	// 专属版连接host，影响模型连接配置
	// 格式，不带algo后缀
	Endpoint string `json:"endpoint"`
}

type ConfigUpdateResult struct {
	Success bool `json:"success"`
}

const (
	// ProxyModeSystem 系统级proxy类型
	ProxyModeSystem = "system"

	// ProxyModeManual 手动proxy类型
	ProxyModeManual = "manual"
)

const (
	//国内版
	RegionCn = "cn"

	//国际版
	RegionIntl = "intl"
)

const (
	McpAutoRunEnable        = "enable"
	McpAutoRunDisable       = "disable"
	TerminalRunAskEveryTime = "askEveryTime"
	TerminalRunAutoRun      = "autoRun"
)

const (
	WebToolsExecutionModeDisabled = "disabled"

	WebToolsExecutionModeAutoExecute = "enabledAutoExecute"

	WebToolsExecutionModeAskEveryTime = "enabledAskEveryTime"
)
