package definition

import (
	"testing"
)

func TestAPIPathStructure(t *testing.T) {
	// 测试APIPath结构体是否包含SinceVersion字段
	path := GlobalAPIPathRegistry.Get("UrlPathLogin")
	if path == nil {
		t.Fatal("Expected to find UrlPathLogin path")
	}

	if path.SinceVersion == "" {
		t.<PERSON>rror("Expected SinceVersion field to be set")
	}
}

func TestAPIPathRegistry_IsRegistered(t *testing.T) {
	registry := NewAPIPathRegistry()

	tests := []struct {
		name     string
		url      string
		expected bool
	}{
		// 测试精确匹配
		{
			name:     "精确匹配 - 系统ping接口",
			url:      "/api/v1/ping",
			expected: true,
		},
		{
			name:     "精确匹配 - 心跳接口",
			url:      "/api/v1/heartbeat",
			expected: true,
		},
		{
			name:     "精确匹配 - 不存在的路径",
			url:      "/api/v1/nonexistent",
			expected: false,
		},

		// 测试RESTful API模式匹配
		{
			name:     "RESTful匹配 - 更新任务",
			url:      "/api/v2/remoteAgent/qoder/tasks/task123",
			expected: true,
		},
		{
			name:     "RESTful匹配 - 获取任务",
			url:      "/api/v2/remoteAgent/qoder/tasks/abc-def-123",
			expected: true,
		},
		{
			name:     "RESTful匹配 - 任务事件流",
			url:      "/api/v2/remoteAgent/sse/qoder/tasks/task456/events/stream",
			expected: true,
		},
		{
			name:     "RESTful匹配 - 任务启动日志",
			url:      "/api/v2/remoteAgent/qoder/tasks/task789/executions/boot-log",
			expected: true,
		},
		{
			name:     "精确匹配 - 任务列表接口",
			url:      "/api/v2/remoteAgent/qoder/tasks",
			expected: true, // 这是注册的精确路径
		},
		{
			name:     "RESTful匹配 - 错误的路径",
			url:      "/api/v2/remoteAgent/qoder/wrongpath/task123",
			expected: false,
		},

		// 测试边界情况
		{
			name:     "空字符串",
			url:      "",
			expected: false,
		},
		{
			name:     "只有斜杠",
			url:      "/",
			expected: false,
		},
		{
			name:     "带查询参数的URL",
			url:      "/api/v1/ping?param=value",
			expected: true, // 现在支持自动预处理
		},
		{
			name:     "完整URL - 带域名",
			url:      "https://example.com/api/v1/ping",
			expected: true,
		},
		{
			name:     "完整URL - 带域名和查询参数",
			url:      "https://example.com/api/v1/ping?param=value&other=test",
			expected: true,
		},
		{
			name:     "URL带fragment",
			url:      "/api/v1/ping#section",
			expected: true,
		},
		{
			name:     "RESTful URL - 带查询参数",
			url:      "/api/v2/remoteAgent/qoder/tasks/task123?withExecution=true",
			expected: true,
		},
		{
			name:     "完整RESTful URL",
			url:      "https://api.example.com/api/v2/remoteAgent/qoder/tasks/task456?param=value",
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := registry.IsRegistered(tt.url)
			if result != tt.expected {
				t.Errorf("IsRegistered(%q) = %v, expected %v", tt.url, result, tt.expected)
			}
		})
	}
}

func TestAPIPathRegistry_matchPattern(t *testing.T) {
	registry := NewAPIPathRegistry()

	tests := []struct {
		name     string
		pattern  string
		url      string
		expected bool
	}{
		{
			name:     "单个参数匹配",
			pattern:  "/api/v2/tasks/%s",
			url:      "/api/v2/tasks/task123",
			expected: true,
		},
		{
			name:     "多个参数匹配",
			pattern:  "/api/v2/users/%s/tasks/%s",
			url:      "/api/v2/users/user123/tasks/task456",
			expected: true,
		},
		{
			name:     "无参数模式",
			pattern:  "/api/v2/tasks",
			url:      "/api/v2/tasks/task123",
			expected: false,
		},
		{
			name:     "段数不匹配",
			pattern:  "/api/v2/tasks/%s",
			url:      "/api/v2/tasks",
			expected: false,
		},
		{
			name:     "路径不匹配",
			pattern:  "/api/v2/tasks/%s",
			url:      "/api/v2/users/user123",
			expected: false,
		},
		{
			name:     "复杂路径匹配",
			pattern:  "/api/v2/remoteAgent/sse/qoder/tasks/%s/events/stream",
			url:      "/api/v2/remoteAgent/sse/qoder/tasks/task789/events/stream",
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := registry.matchPattern(tt.pattern, tt.url)
			if result != tt.expected {
				t.Errorf("matchPattern(%q, %q) = %v, expected %v", tt.pattern, tt.url, result, tt.expected)
			}
		})
	}
}

func TestAPIPathRegistry_cleanURL(t *testing.T) {
	registry := NewAPIPathRegistry()

	tests := []struct {
		name     string
		inputURL string
		expected string
	}{
		{
			name:     "纯路径",
			inputURL: "/api/v1/ping",
			expected: "/api/v1/ping",
		},
		{
			name:     "带查询参数",
			inputURL: "/api/v1/ping?param=value",
			expected: "/api/v1/ping",
		},
		{
			name:     "带多个查询参数",
			inputURL: "/api/v1/ping?param1=value1&param2=value2",
			expected: "/api/v1/ping",
		},
		{
			name:     "带fragment",
			inputURL: "/api/v1/ping#section",
			expected: "/api/v1/ping",
		},
		{
			name:     "带查询参数和fragment",
			inputURL: "/api/v1/ping?param=value#section",
			expected: "/api/v1/ping",
		},
		{
			name:     "完整URL - HTTPS",
			inputURL: "https://example.com/api/v1/ping",
			expected: "/api/v1/ping",
		},
		{
			name:     "完整URL - HTTP",
			inputURL: "http://api.example.com/api/v1/ping",
			expected: "/api/v1/ping",
		},
		{
			name:     "完整URL - 带端口",
			inputURL: "https://example.com:8080/api/v1/ping",
			expected: "/api/v1/ping",
		},
		{
			name:     "完整URL - 带查询参数",
			inputURL: "https://example.com/api/v1/ping?param=value",
			expected: "/api/v1/ping",
		},
		{
			name:     "完整URL - 带所有组件",
			inputURL: "https://user:<EMAIL>:8080/api/v1/ping?param=value#section",
			expected: "/api/v1/ping",
		},
		{
			name:     "空字符串",
			inputURL: "",
			expected: "",
		},
		{
			name:     "根路径",
			inputURL: "/",
			expected: "/",
		},
		{
			name:     "复杂路径",
			inputURL: "/api/v2/remoteAgent/qoder/tasks/task123/executions/boot-log",
			expected: "/api/v2/remoteAgent/qoder/tasks/task123/executions/boot-log",
		},
		{
			name:     "复杂路径带参数",
			inputURL: "https://api.example.com/api/v2/remoteAgent/qoder/tasks/task123/executions/boot-log?withDetails=true&format=json",
			expected: "/api/v2/remoteAgent/qoder/tasks/task123/executions/boot-log",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := registry.cleanURL(tt.inputURL)
			if result != tt.expected {
				t.Errorf("cleanURL(%q) = %q, expected %q", tt.inputURL, result, tt.expected)
			}
		})
	}
}

// 基准测试
func BenchmarkAPIPathRegistry_IsRegistered(b *testing.B) {
	registry := NewAPIPathRegistry()
	testURL := "/api/v2/remoteAgent/qoder/tasks/task123"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		registry.IsRegistered(testURL)
	}
}
