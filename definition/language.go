package definition

// Languages for processors
const (
	Ada string = "ada"

	Agda string = "agda"

	Alloy string = "alloy"

	ANTLR string = "antlr"

	AppleScript string = "applescript"

	Assembly string = "assembly"

	Augeas string = "augeas"

	Awk string = "awk"

	Batch string = "batchfile"

	BlueSpec string = "bluespec"

	C string = "c"

	CSharp string = "c-sharp"

	Clojure string = "clojure"

	CMake string = "cmake"

	CoffeeScript string = "coffeescript"

	CommonLisp string = "common-lisp"

	Cpp string = "cpp"

	C_Cpp string = "c/c++"

	CSS string = "css"

	CUDA string = "cuda"

	Dart string = "dart"

	Dockerfile string = "dockerfile"

	Elixir string = "elixir"

	Elm string = "elm"

	EmacsLisp string = "emacs-lisp"

	Erlang string = "erlang"

	FSharp string = "f-sharp"

	Fortran string = "fortran"

	GLSL string = "glsl"

	Golang string = "go"

	Groovy string = "groovy"

	Haskell string = "haskell"

	HTML string = "html"

	Idris string = "idris"

	Isabelle string = "isabelle"

	Java string = "java"

	JSP string = "java-server-pages"

	JavaScript string = "javascript"

	JSON string = "json"

	Julia string = "julia"

	Kotlin string = "kotlin"

	Lean string = "lean"

	Lua string = "lua"

	Makefile string = "makefile"

	Maple string = "maple"

	Markdown string = "markdown"

	Mathematica string = "mathematica"

	MATLAB string = "matlab"

	OCaml string = "ocaml"

	Pascal string = "pascal"

	Perl string = "perl"

	PHP string = "php"

	PowerShell string = "powershell"

	Prolog string = "prolog"

	ProtocolBuffer string = "protocol-buffer"

	Python string = "python"

	R string = "r"

	Racket string = "racket"

	RestructuredText string = "restructuredtext"

	RMarkdown string = "rmarkdown"

	Ruby string = "ruby"

	Rust string = "rust"

	SAS string = "sas"

	Scala string = "scala"

	Scheme string = "scheme"

	Shell string = "shell"

	Smalltalk string = "smalltalk"

	Solidity string = "solidity"

	SPARQL string = "sparql"

	SQL string = "sql"

	Stan string = "stan"

	StandardML string = "standard-ml"

	Stata string = "stata"

	SystemVerilog string = "systemverilog"

	Tcl string = "tcl"

	Tcsh string = "tcsh"

	TeX string = "tex"

	Thrift string = "thrift"

	Toml string = "toml"

	TypeScript string = "typescript"

	Vue string = "vue"

	Verilog string = "verilog"

	VHDL string = "vhdl"

	VisualBasic string = "visual-basic"

	XSLT string = "xslt"

	Yacc string = "yacc"

	YAML string = "yaml"

	Properties string = "properties"

	Zig string = "zig"

	XML string = "xml"

	FreeMarker = "freeMarker"

	Velocity = "velocity"

	Arkts = "arkts"

	PlantUML string = "plantUML"

	PlainText string = "plainText"

	Txt string = "txt"

	Untitled string = "untitled"

	Coco string = "coco"

	Boo string = "coco"

	ASP_XHTML = "asp/xhtml"

	Patch string = "patch"

	ActionScript = "actionscript"

	HLSL string = "hlsl"

	Ini string = "ini"

	Squirrel string = "squirrel"

	VTL string = "vtl"

	IPYNB string = "ipynb"

	Others string = "others"
)
