package definition

import (
	"bytes"
	"encoding/binary"
	"fmt"
	"time"

	vec "github.com/asg017/sqlite-vec-go-bindings/cgo"
)

const (
	TransformPathSeparator   = "/"
	MerkleTreeExportFileName = "merkle_data.json"
)

// 导出数据的业务类型
const (
	Codebase = "codebase"

	Memory = "memory"

	Wiki = "wiki"
)

var NeedExportDataBizTypes = []string{Codebase, Memory, Wiki}

var NeedImportDataBizTypes = []string{Codebase, Memory, Wiki}

type MemoryTransformRecord struct {
	ID             string  `json:"id" db:"id"`
	GmtCreate      int     `json:"gmt_create,omitempty" db:"gmt_create"`
	GmtModified    int     `json:"gmt_modified,omitempty" db:"gmt_modified"`
	Scope          string  `json:"scope" db:"scope"`
	ScopeId        string  `json:"scope_id,omitempty" db:"scope_id"`
	Keywords       string  `json:"keywords,omitempty" db:"keywords"`
	Title          string  `json:"title,omitempty" db:"title"`
	Content        string  `json:"content" db:"content"`
	SessionID      string  `json:"session_id,omitempty" db:"session_id"`
	IsMerged       int     `json:"is_merged" db:"is_merged"`
	Freq           int     `json:"freq" db:"freq"`
	Source         string  `json:"source,omitempty" db:"source"`
	TokenCount     int     `json:"token_count,omitempty" db:"token_count"`
	Type           string  `json:"type,omitempty" db:"type"`
	UserId         string  `json:"user_id" db:"user_id"`
	Category       string  `json:"category,omitempty" db:"category"`
	RetentionScore float64 `json:"retention_score,omitempty" db:"retention_score"`
	NextReviewTime int     `json:"next_review_time,omitempty" db:"next_review_time"`
	LastReviewTime int     `json:"last_review_time,omitempty" db:"last_review_time"`
	ForgetCount    int     `json:"forget_count,omitempty" db:"forget_count"`
	Status         string  `json:"status,omitempty" db:"status"`
	ReviewHistory  string  `json:"review_history,omitempty" db:"review_history"`
	QualityScore   float64 `json:"quality_score,omitempty" db:"quality_score"`
}

type MemoryEmbeddingTransformRecord struct {
	MemoryId        string    `json:"memory_id" db:"memory_id"`
	GmtCreate       int       `json:"gmt_create,omitempty" db:"gmt_create"`
	GmtModified     int       `json:"gmt_modified,omitempty" db:"gmt_modified"`
	MemoryEmbedding []float32 `json:"memory_embedding" db:"memory_embedding"`
}

type MemoryEmbeddingDO struct {
	MemoryId        string `json:"memory_id" db:"memory_id"`
	GmtCreate       int    `json:"gmt_create,omitempty" db:"gmt_create"`
	GmtModified     int    `json:"gmt_modified,omitempty" db:"gmt_modified"`
	MemoryEmbedding []byte `json:"memory_embedding" db:"memory_embedding"`
}

type WikiItemTransformRecord struct {
	CatalogID      string    `json:"catalog_id" db:"catalog_id"`
	Content        string    `json:"content" db:"content"`
	Title          string    `json:"title" db:"title"`
	Description    string    `json:"description" db:"description"`
	Extend         string    `json:"extend" db:"extend"`
	ProgressStatus string    `json:"progress_status" db:"progress_status"`
	RepoID         string    `json:"repo_id" db:"repo_id"`
	WorkspacePath  string    `json:"workspace_path" db:"workspace_path"`
	ID             string    `json:"id" db:"id"`
	GmtCreate      time.Time `json:"gmt_create" db:"gmt_create"`
	GmtModified    time.Time `json:"gmt_modified" db:"gmt_modified"`
}

func ConvertToTransformRecord(record *MemoryEmbeddingDO) *MemoryEmbeddingTransformRecord {
	if record == nil {
		return nil
	}
	vecFloat, err := deserializeFloat32(record.MemoryEmbedding)
	if err != nil {
		return nil
	}
	return &MemoryEmbeddingTransformRecord{
		MemoryId:        record.MemoryId,
		GmtCreate:       record.GmtCreate,
		GmtModified:     record.GmtModified,
		MemoryEmbedding: vecFloat,
	}
}

func ConvertToMemoryEmbeddingDO(record *MemoryEmbeddingTransformRecord) *MemoryEmbeddingDO {
	if record == nil {
		return nil
	}
	vecByte, err := vec.SerializeFloat32(record.MemoryEmbedding)
	if err != nil {
		return nil
	}
	return &MemoryEmbeddingDO{
		MemoryId:        record.MemoryId,
		GmtCreate:       record.GmtCreate,
		GmtModified:     record.GmtModified,
		MemoryEmbedding: vecByte,
	}
}

// deserializeFloat32 将二进制数据反序列化为float32切片
func deserializeFloat32(data []byte) ([]float32, error) {
	if len(data) == 0 {
		return []float32{}, nil
	}

	// 确保数据长度是4的倍数（float32的字节数）
	if len(data)%4 != 0 {
		return nil, fmt.Errorf("invalid data length: %d", len(data))
	}

	count := len(data) / 4
	result := make([]float32, count)

	buf := bytes.NewReader(data)
	for i := 0; i < count; i++ {
		err := binary.Read(buf, binary.LittleEndian, &result[i])
		if err != nil {
			return nil, err
		}
	}

	return result, nil
}
