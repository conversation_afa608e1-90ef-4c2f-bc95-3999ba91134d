package definition

import (
	"encoding/json"
	"github.com/gorilla/websocket"
	"github.com/sourcegraph/jsonrpc2"
)

const (
	WebViewEditionEnPersonalStandard       = "Individual"
	WebViewEditionEnPersonalProfessional   = "Individual"
	WebViewEditionEnEnterpriseStandard     = "Business"
	WebViewEditionEnEnterpriseProfessional = "Enterprise Dedicated"
	WebViewEditionEnEnterprisePrivate      = "Enterprise Private"

	WebViewEditionZhPersonalStandard       = "个人版"
	WebViewEditionZhPersonalProfessional   = "个人版"
	WebViewEditionZhEnterpriseStandard     = "企业标准版"
	WebViewEditionZhEnterpriseProfessional = "企业专属版"
	WebViewEditionZhEnterprisePrivate      = "企业私有版"
)

const (
	CustomCommandShowPositionTop    = "top"
	CustomCommandShowPositionBottom = "bottom"
)

// WebViewWireRequest 接口定义，webview和cosy通信传输数据都依赖这个接口
type WebViewWireRequest struct {
	Id     jsonrpc2.ID     `json:"id"`
	Method string          `json:"method"`
	Params json.RawMessage `json:"params"`
}

// InnerWebViewRequest 对通信接口的一次封装
type InnerWebViewRequest struct {
	WebViewWireRequest
	IdeType       string          `json:"ideType"`
	Conn          *websocket.Conn `json:"conn"`
	WorkspacePath string          `json:"workspacePath"`

	// 非广播请求时，Conn必须不为空
	// 广播请求时，Conn可为空
	// 默认为非广播请求
	// 只有传给forward时，有用
	IsBroadCast bool `json:"IsBroadCast"`
}

type WebViewParams struct {
	FontColor        string `json:"fontColor"`     // 目标目标主题，不应为空
	FontColorGray    string `json:"fontColorGray"` // 目标辅助文案主题，不应为空
	Bg               string `json:"bg"`            // 主背景色，不做更改嵌入html中
	CardBg           string `json:"cardBg"`        // 卡片背景色，不做更改嵌入html中
	Locale           string `json:"locale"`        // 目标语言，不应为空
	IdeType          string `json:"ideType"`       // 修改的IDE类型，不应为空,VSCode/JetBrains
	WorkspacePath    string `json:"workspacePath"` // 当前IDE打开工程
	ActiveBg         string `json:"activeBg"`
	SelectBg         string `json:"selectBg"`
	MemoryId         string `json:"memoryId"` // 新增的memoryId
	FocusBorder      string `json:"focusBorder"`
	InputBg          string `json:"inputBg"`
	ScrollbarBg      string `json:"scrollbarBg"`
	ScrollbarThumbBg string `json:"scrollbarThumbBg"`
	McpListView      string `json:"mcpListView"`
	TextLinkColor    string `json:"textLinkColor"`
	WidgetBorder     string `json:"widgetBorder"`
	WidgetShadow     string `json:"widgetShadow"`
	DividerColor     string `json:"dividerColor"`
	McpMarketView    string `json:"mcpMarketView"`
	TabColor         string `json:"tabColor"`
	ThemeColor       string `json:"themeColor"`
	ButtonBg         string `json:"buttonBg"`
	ButtonTextColor  string `json:"buttonTextColor"`
	ButtonHoverBg    string `json:"buttonHoverBg"`
	EditorBackground string `json:"editorBackground"`
}

type WebViewUrlParams struct {
	Url string `json:"url"`
}

type WebViewUpdateDataPolicyParams struct {
	Agree bool `json:"agree"`
}

type WebViewOpenUrlParams struct {
	Url string `json:"url"`
}

type WebViewUpdateOfficialCommandEnd struct {
	Agree bool `json:"agree"`
}

type WebViewRenderPageParams struct {
	FontColor        string `json:"fontColor"`
	FontColorGray    string `json:"fontColorGray"`
	CardBg           string `json:"cardBg"`
	Bg               string `json:"bg"`
	Locale           string `json:"locale"`
	Edition          string `json:"edition"`
	SelectBg         string `json:"selectBg"`
	ActiveBg         string `json:"activeBg"`
	FocusBorder      string `json:"focusBorder"`
	InputBg          string `json:"inputBg"`
	ScrollbarBg      string `json:"scrollbarBg"`
	ScrollbarThumbBg string `json:"scrollbarThumbBg"`
	TextLinkColor    string `json:"textLinkColor"`
	WidgetBorder     string `json:"widgetBorder"`
	WidgetShadow     string `json:"widgetShadow"`
	DividerColor     string `json:"dividerColor"`
	TabColor         string `json:"tabColor"`
	ThemeColor       string `json:"themeColor"`
	ButtonBg         string `json:"buttonBg"`
	ButtonTextColor  string `json:"buttonTextColor"`
	ButtonHoverBg    string `json:"buttonHoverBg"`
	EditorBackground string `json:"editorBackground"`
}

// WebViewFilterJsObject Js中用于展示的对象
type WebViewFilterJsObject struct {
	IsConfigured   bool   `json:"is_configured"`
	Enabled        bool   `json:"enabled"`
	IsScript       bool   `json:"is_script"`
	HandleStrategy string `json:"handle_strategy"`
}

// WebViewCommandJsObject Js中展示用的对象
type WebViewCommandJsObject struct {
	Identifier string `json:"identifier"`
	NameLeft   string `json:"name_left"`
	NameRight  string `json:"name_right"`
}

// ProfileData 用于Json文件中存储的数据
// ProfileData需要是WebViewShowData的一个子集
type ProfileData struct {
	CommandShowOrder   []string `json:"commandShowOrder"`     // 存储排序指令的Identifier列表
	OfficialCommandEnd bool     `json:"official_command_end"` // 官方指令是否放在自定义指令之后
}

// WebViewShowData webview界面和extension之间通信接口
// WebView页面中，所有可能修改的数据都要放在这里
type WebViewShowData struct {
	CommandOrder WebViewCommandOrder `json:"command_order"` // Index即为展示的顺序
}

type WebViewCommandOrder struct {
	Commands           []WebViewCommandJsObject `json:"commands"`
	OfficialCommandEnd bool                     `json:"official_command_end"` //官方指令是否在末尾展示
}

const (
	NotificationIdMcpUserJsonError    = "mcp_parse_user_json_error"
	NotificationIdMcpUserJsonNoError  = "mcp_parse_user_json_no_error"
	NotificationIdMcpUserJsonUpdating = "mcp_updating_from_user_json"
	NotificationIdMcpUserJsonUpdated  = "mcp_updated_from_user_json"
)

type WebViewNotification struct {
	NotificationId string `json:"notification_id"` // 这个是webview用来判定分发的id
	ErrorMsg       string `json:"error_msg"`
}

type WebViewConfigParams struct {
	State                   string `json:"state"`
	ProfileWebsocketPort    int    `json:"profile_websocket_port"`
	IsSelectAccount         bool   `json:"is_select_account"`
	ErrorCode               int    `json:"error_code"`
	ErrorMessage            string `json:"error_message"`
	ErrorMessageCode        int    `json:"error_message_code"`
	UserInfo                string `json:"user_info"`
	OnPremise               bool   `json:"on_premise"`
	Edition                 string `json:"edition"`
	UserName                string `json:"user_name"`
	UserId                  string `json:"user_id"`
	OrgName                 string `json:"org_name"`
	PolicyAgreed            bool   `json:"policy_agreed"`
	UserAvatar              string `json:"user_avatar"`
	ResourceVersion         string `json:"resource_version"`
	TeamixUiResourceVersion string `json:"teamix_ui_resource_version"`
	ChatFilter              string `json:"chat_filter"`
	CompletionFilter        string `json:"completion_filter"`
	PostChatFilter          string `json:"post_chat_filter"`
	PostCompletionFilter    string `json:"post_completion_filter"`
	OfficialCommandsEnd     bool   `json:"official_commands_end"`
	Commands                string `json:"commands"`
	RegionEnv               string `json:"region_env"`
	MemoryCount             int    `json:"memory_count"`
	WorkspacePath           string `json:"workspace_path"`
	McpDocsUrl              string `json:"mcp_docs_url"`
	McpConfigOverview       string `json:"mcp_config_overview"`
	ProjectRuleCount        int    `json:"project_rule_count"`
}
