package definition

// 参考格式
//
//		{
//	  "repo": "repo3",
//	  "ra": "ra2"
//	}
type UserDataRegion struct {
	UserId string `json:"userId"`
	Repo   string `json:"repo"` //codebase region
	Ra     string `json:"ra"`   //remote agent region
}

func (u *UserDataRegion) IsEmpty() bool {
	return u.Repo == "" && u.Ra == ""
}

type UserRegionResponse struct {
	BaseResult
	Result bool `json:"result"`
}

type UpdateUserRegionResponse struct {
	BaseResult
	Result bool `json:"result"`
}
