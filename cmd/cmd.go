package cmd

import (
	"cosy/global"
	"cosy/log"
	"cosy/product"
	"cosy/util"
	"fmt"
	"os"

	"github.com/spf13/cobra"
)

var (
	branding = product.GetBranding()
	rootCmd  = &cobra.Command{
		Use:   branding.GetMessage(product.KeyCmdName),
		Short: branding.GetMessage(product.KeyCmdShort),
		Long:  branding.GetMessage(product.KeyCmdLong),
		Run: func(cmd *cobra.Command, args []string) {
			showAbout()
		},
	}
)

func Execute() {
	if err := rootCmd.Execute(); err != nil {
		log.Error(err)
		os.Exit(1)
	}
}

func showAbout() {
	fmt.Println(branding.GetMessage(product.KeyVersionPrefix) + " current version " + global.CosyVersion)
	fmt.Println(branding.GetMessage(product.KeyAboutMessage))
}

func init() {
	defaultSocketPort, _ := util.GetWebsocketPortRange()
	defaultHttpPort, _ := util.GetHttpPortRange()
	startCmd.Flags().StringVarP(&global.DefaultLocalLanguage, "defaultLocal", "l", "java", "Default local language")
	startCmd.Flags().StringVarP(&global.HttpsProxy, "httpProxy", "", "", "Use HTTP proxy")
	startCmd.Flags().IntVarP(&global.SocketPort, "socketPort", "p", defaultSocketPort, "Socket API port")
	startCmd.Flags().IntVarP(&global.HttpPort, "httpPort", "", defaultHttpPort, "HTTP API port")
	startCmd.Flags().StringVarP(&global.CustomizedEndpoint, "endpoint", "", "", "Use Custom Endpoint")
	startCmd.Flags().StringVarP(&global.TransportType, "transportType", "", "", "Use Websocket or Stdio channel")
	startCmd.Flags().StringVarP(&global.WorkDir, "workDir", "", "", "Specific work dir")

	versionCmd.Flags().BoolVarP(&global.MoreInfo, "more", "", false, "Mode information")

	startRemoteAgentCmd.Flags().IntVarP(&global.SocketPort, "socketPort", "p", defaultSocketPort, "Socket API port")
	startRemoteAgentCmd.Flags().IntVarP(&global.HttpPort, "httpPort", "", defaultHttpPort, "HTTP API port")
	startRemoteAgentCmd.Flags().StringVarP(&global.WorkDir, "workDir", "", "", "Specific work dir")

	rootCmd.AddCommand(startRemoteAgentCmd)
	rootCmd.AddCommand(startCmd)
	rootCmd.AddCommand(uninstallCmd)
	rootCmd.AddCommand(versionCmd)
	rootCmd.AddCommand(importCmd)
}
