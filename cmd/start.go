package cmd

import (
	"context"
	"cosy/auth"
	"cosy/chat/agents"
	"cosy/chat/agents/support"
	"cosy/chat/service"
	"cosy/client"
	"cosy/components"
	"cosy/config"
	"cosy/deepwiki"
	"cosy/definition"
	"cosy/extension"
	"cosy/extension/mcp"
	"cosy/extension/mcpconfig"
	"cosy/global"
	"cosy/graph"
	"cosy/indexing/index_setting"
	"cosy/locale"
	"cosy/localservice"
	"cosy/log"
	"cosy/memory"
	"cosy/pipe"
	"cosy/prompt"
	"cosy/regionha"
	"cosy/remote"
	"cosy/server"
	"cosy/sls"
	"cosy/stable"
	"cosy/storage/database"
	"cosy/storage/factory"
	"cosy/update"
	"cosy/user"
	"cosy/util"
	"cosy/util/ops"
	"cosy/websocket"
	"net/http"
	"os"
	"os/signal"
	"path/filepath"
	"runtime"
	"strconv"
	"syscall"
	"time"

	graph_log "code.alibaba-inc.com/cosy/lingma-codebase-graph/log"
	"github.com/gofrs/flock"
	"github.com/spf13/cobra"
)

var startCmd = &cobra.Command{
	Use:   "start",
	Short: "Start Lingma and serve",
	Long:  "Start Lingma and serve",
	Run: func(cmd *cobra.Command, args []string) {
		StartServer(cmd, args)
	},
}

// StartServer Start lingma server
func StartServer(cmd *cobra.Command, args []string) {
	defer func() {
		if r := recover(); r != nil {
			// 获取当前堆栈信息
			stack := make([]byte, 4096)
			stack = stack[:runtime.Stack(stack, false)]
			log.Warnf("recover from crash. err: %+v, stack: %s", r, stack)
		}
	}()

	// Create lingma folders
	wd := initLingmaFolder()
	remoteAgentMode := config.IsRemoteAgentMode()

	// Add .info file lock
	processLockFile := filepath.Join(wd, ".lock")
	processLock := flock.New(processLockFile)
	processLocked, err := processLock.TryLock()
	if err == nil && processLocked {
		// Obtained the file lock
		defer func() {
			err := processLock.Unlock()
			if err != nil {
				log.Warnf("Failed to release lock: %v", err)
			}
			rmErr := os.Remove(processLockFile)
			if rmErr != nil {
				log.Warnf("Failed to remove lock file: %v", rmErr)
			} else {
				log.Infof("lock file removed.")
			}
		}()
	} else {
		log.Warnf("Failed to obtain info lock: %v, exit", err)
		return
	}

	//TODO 先注销掉，待服务端时区配置好后再对齐
	//if global.IsQoderProduct() {
	//	locale.InitQoderTimeZone()
	//}

	// Read configurations
	config.InitLocalConfig()

	// Initialize logger
	log.UseFileLogger(wd)
	log.Info("===== Starting lingma service =====")
	log.Debugf("client build options: proruct type: %s, build form type: %s", global.ProductType, global.BuildFormType)

	log.Debugf("Lingma home: %s", util.GetCosyHomePath())
	log.Debugf("Lingma cache path: %s", util.GetCosyCachePath())

	// Initialize http clients
	client.InitClients()

	//初始化region选择服务
	if global.IsQoderProduct() {
		//TODO 暂时增加开关启用，避免影响测试
		if remote.IsRegionFailoverEnable() {
			go remote.InitRegionFailoverService()
		}
	}

	// Initialize configures
	config.InitConfig()

	// Initialize reporters
	sls.InitReporter()
	server.InitHeartbeat()
	sls.InitCompletion()

	//本地数据库&连接
	database.InitDatabase()
	service.InitSessionService()
	service.InitWorkingSpaceService()
	if remoteAgentMode {
		initDatabaseHookForRemoteAgentMode()
		initWorkingSpaceListenerForRemoteAgentMode()
		// RemoteAgent模式下，从特定路径读取用户数据并写入缓存
		if err := user.LoadAndSaveUserInfoForRemoteAgent(config.GetRemoteAgentUserInfoPath()); err != nil {
			// RemoteAgent模式下，读取用户登录态失败，需要主动退出
			log.Errorf("Failed to load user info in Remote Agent Mode: %v", err)
			return
		}
	}

	//prompt模板引擎
	prompt.InitializeRepo()

	//初始化本地化语言资源
	locale.Initialize()

	// 初始化codebase本地配置
	global.InitCodebaseConfig()

	// 初始化codebase settings设置
	index_setting.InitCodebaseSettings()

	log.Infof("Lingma running on %s(%s, %s) at %s, version: %s", runtime.GOOS, runtime.GOARCH, util.GetOsVersion(), wd, global.CosyVersion)

	// 设置默认系统代理
	client.SetupHttpProxy()

	// 检查网络连通性
	if err := remote.PingBigModelServer(); err != nil {
		log.Warnf("Access lingma server failed: %v", err)
	} else {
		log.Infof("Access lingma server success")
	}
	//初始化本地已登录账号授权信息
	cachedUser := user.GetCachedUserInfo()
	if cachedUser == nil || cachedUser.Uid == "" {
		log.Infof("No cached user info, please login.")
	} else {
		log.Debugf("Cached user info: userName:%s, userId: %s, orgnizationId: %s", cachedUser.Name, cachedUser.Uid, cachedUser.OrgId)

		//加载数据节点配置
		go regionha.CheckUpdateUserDataRegion()
	}

	//特定平台自动登录
	go auth.AutoLoginWhenInit()

	// 初始化自定义扩展配置
	extension.Initialize()

	// 下载扩展引擎依赖的node环境
	extension.InitExtensionNodeVm()

	// 初始化扩展引擎
	extension.InitApiExecutor()

	// 启动扩展引擎配置拉取监听器
	extension.InitExtensionConfigListener()

	// 启动mcpSever
	mcp.InitMCP()

	components.InitModelConfigure()
	memory.InitMemoryForgetSystem()
	// 初始化agent server
	support.Initialize()

	agents.Initialize()

	deepwiki.Initialize()

	// Check update
	if global.IsLingmaProduct() {
		initLocalModelEnvAndService(wd)
	}

	ops.InitProfile()

	quit := make(chan error)

	// Try to get free port, retry at most 10 times:
	firstPort, lastPort := util.GetHttpPortRange()
	httpPort := util.GetFreePort(global.HttpPort, firstPort, lastPort, 10)
	global.HttpPort = httpPort

	auth.AuthServer = auth.HttpServer{
		HttpPort: httpPort,
		Cm:       auth.InitWebViewClientManager(),
	}
	if httpPort == 0 {
		log.Error("Failed to found a free http port")
		return
	}
	stable.GoSafe(context.Background(), func() {
		url := "127.0.0.1:" + strconv.Itoa(httpPort)
		err := http.ListenAndServe(url, server.CreateHttpServer(auth.AuthServer))
		if err != nil {
			log.Error("Local http server down: ", err)
		}
		quit <- err
	}, stable.SceneSystem)

	profileWebsocketPort := util.GetFreePort(definition.CosyProfileWebSocketDefaultPort, firstPort, lastPort, 10)
	global.ProfileWebsocketPort = profileWebsocketPort
	if profileWebsocketPort == 0 {
		log.Error("Failed to found a free http port for profile websocket")
	}
	log.Infof("Using profile websocket channel: %d", profileWebsocketPort)
	stable.GoSafe(context.Background(), func() {
		url := "127.0.0.1:" + strconv.Itoa(profileWebsocketPort)
		err := http.ListenAndServe(url, server.CreateProfileServer(auth.AuthServer))
		if err != nil {
			log.Error("Local profile server down: ", err)
		}
		quit <- err
	}, stable.SceneSystem)

	wsPort := 0

	serverIdlTimeout := websocket.IdleTimeout
	if global.TransportType == definition.TransportStdio {
		serverIdlTimeout = websocket.IdleUnlimitedTimeout
	}
	if remoteAgentMode {
		serverIdlTimeout = websocket.IdleUnlimitedTimeout
	}

	serverTimer := time.NewTimer(serverIdlTimeout)

	db, err := factory.NewKvStore(wd, factory.BBlotStore, false)
	if err != nil || db == nil {
		log.Error("Failed to initialize local storage, error:", err)
	}
	var cosyServer *server.CosyServer
	if remoteAgentMode {
		cosyServer = server.NewRemoteAgentCosyServer(server.InitializeProcessors(wd), db)
	} else {
		cosyServer = server.NewCosyServer(server.InitializeProcessors(wd), db)
	}

	if global.TransportType == "" || global.TransportType == definition.TransportWebSocket {
		// Try to get free port, retry at most 10 times:
		firstPort, lastPort := util.GetWebsocketPortRange()
		wsPort = util.GetFreePort(global.SocketPort, firstPort, lastPort, 10)
		if wsPort == 0 {
			log.Error("Failed to found a free websocket port")
			return
		}
		websocket.InitWebsocketServer(cosyServer, serverTimer)

		stable.GoSafe(context.Background(), func() {
			url := "127.0.0.1:" + strconv.Itoa(wsPort)
			err := http.ListenAndServe(url, new(server.WebsocketServe))
			if err != nil {
				log.Error("Local ws server down: ", err)
			}
			quit <- err
		}, stable.SceneSystem)
	} else {
		log.Info("Using stdio channel")
		wsPort = 0
		// Pipe
		websocket.WsInst = pipe.NewPipeServer(cosyServer)
	}

	startGraphService(cosyServer)
	pid := os.Getpid()
	info := strconv.Itoa(wsPort) + "\n" + strconv.Itoa(pid) + "\n"
	infoFile := filepath.Join(wd, ".info")
	err = os.WriteFile(infoFile, []byte(info), 0644)
	if err != nil {
		log.Error("Unable to write process info: ", err)
		return
	} else {
		log.Info("info file created.")
	}
	defer func() {
		rmErr := os.Remove(infoFile)
		if rmErr != nil {
			log.Warnf("Failed to remove info file: %v", rmErr)
		} else {
			log.Info("info file removed when exit.")
		}
	}()
	if remoteAgentMode {
		remoteCosyPostStartHooks()
	}

	// Everything is ready, now send first heartbeat
	server.ReportHeartbeat()

	log.Infof("Server is ready at: %d / %d", wsPort, httpPort)
	// Exit gracefully
	ch := make(chan os.Signal, 1)
	signal.Notify(ch, os.Interrupt, syscall.SIGTERM, syscall.SIGINT, os.Kill)

	stable.CheckCrashLogWhenInit()

	// Quit
	select {
	case <-quit:
		log.Info("Server crashed, exit cosy")
		closeGracefully()
		return
	case exitSignal := <-ch:
		log.Info("Received exit signal: ", exitSignal)
		closeGracefully()
		return
	case <-serverTimer.C:
		if global.TransportType == "" || global.TransportType == definition.TransportWebSocket {
			//websocket模式下监听客户端连接活跃度
			log.Info("No connected client, quit cosy automatically")
			closeGracefully()
			return
		}
	}
	log.Info("Cosy quit.")
}

func initLingmaFolder() string {
	cosyHome := util.GetCosyHomePath()
	cosyCache := util.GetCosyCachePath()
	pathCache := []string{
		filepath.Join(cosyHome, "bin"),
		filepath.Join(cosyHome, "cache"),
		filepath.Join(cosyHome, "logs"),
		filepath.Join(cosyHome, "model"),
		filepath.Join(cosyHome, "tmp"),
		filepath.Join(cosyCache, "env"),
	}
	for _, path := range pathCache {
		if !util.PathExists(path) {
			e := os.MkdirAll(path, 0755)
			if e != nil {
				log.Errorf("Failed to create path: %s, error: %v", path, e)
			}
		}
	}
	return cosyHome
}

func closeGracefully() {
	if localservice.ServiceManager != nil {
		localservice.ServiceManager.Shutdown()
	}
	mcpconfig.ShutdownMcpServer()
	database.Close()
}

func startGraphService(cosyServer *server.CosyServer) {
	graph_log.SetLogger(log.GetLogger())
	lock := graph.NewGraphLock()

	manager := graph.NewGraphManager(cosyServer, lock)
	worker := graph.NewGraphWorker(cosyServer, lock)

	go manager.Run()
	go worker.Run()

}

func initLocalModelEnvAndService(wd string) {
	go update.DoUpdate(wd)

	// Check local inference environment
	ok := localservice.PrepareInferenceEnvironment()
	if !ok && global.CosyConfig.Local.Enable != nil && *global.CosyConfig.Local.Enable {
		log.Warn("Local model is not ready, local inference is currently unavailable")
	}
	// Local completion service
	localservice.InitializeLocalService(util.GetCosyProcessPath())
}
