package cmd

import (
	"context"
	"cosy/chat/service"
	"cosy/config"
	"cosy/definition"
	"cosy/log"
	"cosy/longruntask"
	"cosy/storage/database"
	"cosy/util"
	"fmt"
	"github.com/spf13/cobra"
)

var startRemoteAgentCmd = &cobra.Command{
	Use:   "start-remote-agent",
	Short: "Start remote agent ",
	Long:  "Start remote agent ",
	Run: func(cmd *cobra.Command, args []string) {
		StartRemoteCosyServer(cmd, args)
	},
}

// StartRemoteCosyServer Remote Cosy模式
func StartRemoteCosyServer(cmd *cobra.Command, args []string) {
	// 设置为RemoteAgent模式
	config.SetAgentMode(config.AgentModeRemoteAgent)
	StartServer(cmd, args)
}

func initDatabaseHookForRemoteAgentMode() {
	database.AddHookBeforeCreateChatSession([]func(session *definition.ChatSession) error{longruntask.SyncToServerForChatSessionCreateOrUpdate})
	database.AddHookBeforeUpdateChatSession([]func(session *definition.ChatSession) error{longruntask.SyncToServerForChatSessionCreateOrUpdate})

	database.AddHookBeforeCreateChatRecord([]func(session *definition.ChatRecord) error{longruntask.SyncToServerForChatRecordCreatedOrUpdated})
	database.AddHookBeforeUpdateChatRecord([]func(session *definition.ChatRecord) error{longruntask.SyncToServerForChatRecordCreatedOrUpdated})

	database.AddHookBeforeCreateChatMessage([]func(session *definition.ChatMessage) error{longruntask.SyncToServerForChatMessageCreated})
	database.AddHookBeforeUpdateChatMessage([]func(id, sessionId, toolResult string) error{longruntask.SyncToServerForChatMessageUpdated})
}

func initWorkingSpaceListenerForRemoteAgentMode() {
	taskId, err := config.GetRemoteModeTaskId()
	if err != nil {
		log.Errorf("Failed to get remote mode task id: %v", err)
		return
	}
	l := service.NewRemoteAgentFileChangeListener(nil, func(ctx context.Context, fileVO *definition.WorkingSpaceFileVO) {
		// 同步给IDE
		if err := longruntask.NotificationForFileChanged(fileVO); err != nil {
			log.Errorf("Failed to notify file change: %v", err)
		}
		log.Debugf("File change notification sent for file %s, status %s", fileVO.FileId, fileVO.Status)
	})
	service.WorkingSpaceServiceManager.RegisterListener(l)
	log.Infof("init remote agent file change listener for task %s", taskId)
}

// remoteCosyPostStartHooks CosyRemote启动后需要执行的hooks
func remoteCosyPostStartHooks() {
	taskId, _ := config.GetRemoteModeTaskId()
	ctx := context.Background()
	longruntask.PostStartUpdateRemoteAgentTaskExecution(ctx, taskId)

	if err := util.CommitWorkspace(config.GetRemoteAgentWorkspacePath(), fmt.Sprintf("Checkpoint for task %s", taskId)); err != nil {
		log.Errorf("Failed to commit workspace: %v", err)
		// 不阻塞其他
	}
}
