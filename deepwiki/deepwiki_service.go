package deepwiki

import (
	"context"
	chainsCommon "cosy/chat/chains/common"
	wikiChains "cosy/deepwiki/chains"
	"cosy/deepwiki/queue"
	wikiService "cosy/deepwiki/service"
	"cosy/deepwiki/storage"
	"cosy/deepwiki/support"
	"cosy/definition"
	"cosy/indexing"
	"cosy/indexing/api"
	"cosy/lang/indexer"
	"cosy/log"
	"cosy/storage/factory"
	"cosy/user"
	"cosy/util"
	"cosy/util/session"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/go-git/go-git/v5/plumbing/object"
	"github.com/google/uuid"

	"github.com/tmc/langchaingo/chains"
)

const (
	//非git工程，检查wiki的更新过期时间
	noneGitRepoCheckExpireTime = 14 * 24 * time.Hour
)

var GlobalWikiService *DeepwikiService
var GlobalKnowledgeService *wikiService.KnowledgeIntegrationService

func NewWikiService(storageService *storage.LingmaWikiStorageService) *DeepwikiService {
	commitMonitorService := wikiService.NewCommitMonitorService(storageService)

	// 初始化knowledge集成服务
	if GlobalKnowledgeService == nil {
		GlobalKnowledgeService = wikiService.NewKnowledgeIntegrationService()
	}

	service := &DeepwikiService{
		storageService:       storageService,
		commitMonitorService: commitMonitorService,
		locks:                sync.Map{},
	}

	// 创建任务队列管理器
	config := queue.DefaultTaskQueueConfig()
	config.WorkerCount = 1 // 默认1个worker
	config.QueueSize = 100 // 队列大小100

	// 创建任务执行器
	executor := queue.NewDeepWikiTaskExecutor(
		service.executeFullGeneration,
		service.executeIncrementalUpdate,
	)

	service.taskQueueManager = queue.NewTaskQueueManager(config, executor)

	return service
}

type DeepwikiService struct {
	storageService       *storage.LingmaWikiStorageService
	commitMonitorService *wikiService.CommitMonitorService
	taskQueueManager     *queue.TaskQueueManager
	locks                sync.Map
	// lastIndexCheckTimes 记录每个workspace的最后索引检查时间
	lastIndexCheckTimes sync.Map // map[string]time.Time
}

// TaskQueueAdapter 实现TaskSubmitter接口的适配器
type TaskQueueAdapter struct {
	manager *queue.TaskQueueManager
}

func (t *TaskQueueAdapter) SubmitTask(task *queue.Task) error {
	return t.manager.SubmitTask(task)
}

func (t *TaskQueueAdapter) GetQueueStatus() queue.QueueStatus {
	return t.manager.GetQueueStatus()
}

// GetTaskSubmitter 获取TaskSubmitter接口的实现
func (d *DeepwikiService) GetTaskSubmitter() support.TaskSubmitter {
	return &TaskQueueAdapter{manager: d.taskQueueManager}
}

// StartQueue 启动任务队列
func (d *DeepwikiService) StartQueue() error {
	if d.taskQueueManager == nil {
		return fmt.Errorf("task queue manager not initialized")
	}
	err := d.taskQueueManager.Start()
	if err != nil {
		return err
	}

	return nil
}

// StopQueue 停止任务队列
func (d *DeepwikiService) StopQueue() error {
	if d.taskQueueManager == nil {
		return nil
	}
	return d.taskQueueManager.Stop()
}

// GetQueueStatus 获取队列状态
func (d *DeepwikiService) GetQueueStatus() queue.QueueStatus {
	if d.taskQueueManager == nil {
		return queue.QueueStatus{}
	}
	return d.taskQueueManager.GetQueueStatus()
}

// executeFullGeneration 执行全量生成（队列调用）
func (d *DeepwikiService) executeFullGeneration(ctx context.Context, request definition.CreateDeepwikiRequest) error {
	repoName := util.GetProjectName(request.WorkspacePath)
	log.Infof("Deepwiki: Project start - Repo: %s, Workspace: %s", repoName, request.WorkspacePath)

	// 调用原有的全量生成逻辑
	d.generateNew(ctx, request)
	return nil
}

// executeIncrementalUpdate 执行增量更新（队列调用）
func (d *DeepwikiService) executeIncrementalUpdate(ctx context.Context, request definition.CreateDeepwikiRequest, diffInfo interface{}) error {
	repoName := util.GetProjectName(request.WorkspacePath)
	log.Infof("Deepwiki: IncrementalUpdate start - Repo: %s, Workspace: %s", repoName, request.WorkspacePath)

	// 类型断言
	commitDiffInfo, ok := diffInfo.(*definition.CommitDiffInfo)
	if !ok {
		log.Infof("Deepwiki: IncrementalUpdate failed - Repo: %s, Workspace: %s, Error: invalid diff info type", repoName, request.WorkspacePath)
		return fmt.Errorf("invalid diff info type")
	}

	// 调用原有的增量更新逻辑
	err := d.performIncrementalUpdate(ctx, request, commitDiffInfo)
	if err != nil {
		log.Infof("Deepwiki: IncrementalUpdate failed - Repo: %s, Workspace: %s, Error: %v", repoName, request.WorkspacePath, err)
		return err
	}

	log.Infof("Deepwiki: IncrementalUpdate end - Repo: %s, Workspace: %s", repoName, request.WorkspacePath)
	return nil
}

func (d *DeepwikiService) GenerateUpdate(ctx context.Context, request definition.CreateDeepwikiRequest) {
	workspacePath := request.WorkspacePath

	// 禁止remote agent模式下进行wiki生成
	if !support.IsEnableWiki() {
		log.Infof("Skip wiki generate for workspace: %s", workspacePath)
		return
	}

	//设置ctx信息
	workspaceFolder := definition.WorkspaceInfo{
		WorkspaceFolders: []definition.WorkspaceFolder{
			{
				Name: util.GetProjectName(workspacePath),
				URI:  workspacePath,
			},
		},
	}

	if user.GetCachedUserInfo() == nil {
		log.Infof("user not login, skip wiki generate.")
		return
	}

	mutexVal, ok := d.locks.Load(workspacePath)
	if !ok {
		mutexVal = &sync.Mutex{}
		d.locks.Store(request.WorkspacePath, mutexVal)
	}
	mutex, ok := mutexVal.(*sync.Mutex)
	if !mutex.TryLock() {
		log.Debugf("not get lock. workspace: %s", workspacePath)
		return
	}
	log.Debugf("get wiki generqte lock. workspace: %s", workspacePath)
	defer mutex.Unlock()

	log.Debugf("generate wiki update. workspacePath: %s", workspacePath)

	ctx = context.WithValue(ctx, definition.ContextKeyWorkspace, workspaceFolder)

	// 优先检测和处理孤儿进程 - 只针对当前工程
	if d.checkAndHandleOrphanedReposForWorkspace(ctx, request) {
		log.Debugf("Found and handled orphaned repo for workspace: %s", workspacePath)
		return
	}

	wikiRepo, err := d.storageService.GetWikiRepoByWorkspacePath(workspacePath)
	if err != nil {
		log.Errorf("get wiki repo error. workpacePath: %s, err: %v", workspacePath, err)
		return
	}

	if wikiRepo == nil {
		log.Debugf("Wiki repo not found, submitting full generation task for: %s", workspacePath)
		d.submitFullGenerationTask(ctx, request)
		return
	}

	// 针对当前workspace检查和修复failed catalog状态，并判断是否需要恢复
	needsRecovery := d.checkAndFixFailedCatalogsForWorkspace(ctx, workspacePath)
	if needsRecovery {
		// 如果检测到failed catalog并已修复repo状态，直接进行恢复
		log.Debugf("Detected failed catalogs for workspace %s, triggering recovery", workspacePath)
		d.checkAndRecoverFailedRepoForWorkspace(ctx, request)
		d.submitFullGenerationTask(ctx, request)
		return
	}

	// 检查当前workspace的failed状态repo并进行恢复
	d.checkAndRecoverFailedRepoForWorkspace(ctx, request)

	// 重新获取repo状态（可能在恢复过程中被修改）
	wikiRepo, err = d.storageService.GetWikiRepoByWorkspacePath(workspacePath)
	if err != nil {
		log.Errorf("get wiki repo error after recovery check. workpacePath: %s, err: %v", workspacePath, err)
		return
	}

	if wikiRepo == nil {
		log.Debugf("Wiki repo not found after recovery check, submitting full generation task for: %s", workspacePath)
		d.submitFullGenerationTask(ctx, request)
		return
	}

	// 检查repo状态
	if wikiRepo.ProgressStatus == definition.DeepWikiProgressStatusProcessing {
		log.Debugf("wiki repo is currently processing, skip. workpacePath: %s", workspacePath)
		return
	}

	// Pending状态的repo应该被处理（恢复场景）
	if wikiRepo.ProgressStatus == definition.DeepWikiProgressStatusPending {
		log.Debugf("wiki repo is pending (recovery scenario), submitting task. workpacePath: %s", workspacePath)
		d.submitFullGenerationTask(ctx, request)
		return
	}

	// 如果repo状态为completed，进行增量更新检查
	if wikiRepo.ProgressStatus == definition.DeepWikiProgressStatusCompleted {
		// 0. 检查和修复wiki索引完整性（基于时间间隔和repo修改时间）
		if d.shouldPerformIndexCheck(workspacePath) {
			go d.checkAndFixWikiIndex(ctx, workspacePath)
		}

		// 1. 立即检测一次增量更新（通过队列）
		err = d.commitMonitorService.CheckAndTriggerProjectUpdate(workspacePath, func(diffInfo *definition.CommitDiffInfo) error {
			log.Debugf("[deepwiki-incremental-update] Immediate incremental update triggered for %s", workspacePath)
			return d.submitIncrementalUpdateTask(request, diffInfo)
		})
		if err != nil {
			log.Errorf("Failed to check and trigger project update: %v", err)
		}

		// 2. 智能注册到监控服务（处理重复注册情况）
		err = d.setupIncrementalMonitoring(workspacePath, request.PreferredLanguage)
		if err != nil {
			log.Errorf("Failed to setup incremental monitoring for %s: %v", workspacePath, err)
		} else {
			log.Infof("Wiki repo exists and completed, monitoring enabled with callback for: %s", workspacePath)
		}
	} else {
		log.Infof("Wiki repo status is %s, submitting full generation task for: %s", wikiRepo.ProgressStatus, workspacePath)
		d.submitFullGenerationTask(ctx, request)
	}
}

// submitFullGenerationTask 提交全量生成任务到队列
func (d *DeepwikiService) submitFullGenerationTask(ctx context.Context, request definition.CreateDeepwikiRequest) {
	// 禁止remote agent模式下进行wiki生成任务提交
	if !support.IsEnableWiki() {
		log.Infof("Skip wiki generate for workspace: %s", request.WorkspacePath)
		return
	}

	if d.taskQueueManager == nil {
		log.Errorf("Task queue manager not initialized")
		return
	}

	// 改进的去重检查逻辑，支持恢复场景
	if d.shouldSkipTaskSubmission(request.WorkspacePath, "full_generation") {
		return
	}

	task := queue.NewFullGenerationTask(ctx, request)
	err := d.taskQueueManager.SubmitTask(task)
	if err != nil {
		log.Errorf("Failed to submit full generation task for %s: %v", request.WorkspacePath, err)
		return
	}

	log.Infof("Full generation task submitted successfully for workspace: %s, task ID: %s",
		request.WorkspacePath, task.ID)
}

// submitIncrementalUpdateTask 提交增量更新任务到队列
func (d *DeepwikiService) submitIncrementalUpdateTask(request definition.CreateDeepwikiRequest, diffInfo *definition.CommitDiffInfo) error {
	// 禁止remote agent模式下进行wiki增量更新任务提交
	if !support.IsEnableWiki() {
		log.Infof("Skip wiki generate for workspace: %s", request.WorkspacePath)
		return nil
	}

	if d.taskQueueManager == nil {
		log.Errorf("Task queue manager not initialized, falling back to direct execution")
		return d.performIncrementalUpdate(context.Background(), request, diffInfo)
	}

	// 改进的去重检查逻辑，支持恢复场景
	if d.shouldSkipTaskSubmission(request.WorkspacePath, "incremental_update") {
		return nil
	}

	// 检查修改行数阈值（250行）
	const changedLinesThreshold = 250
	if diffInfo != nil && diffInfo.TotalChangedLines < changedLinesThreshold {
		log.Debugf("[deepwiki-incremental-update] Commit changes (%d lines) below threshold (%d lines), skipping incremental update for workspace: %s",
			diffInfo.TotalChangedLines, changedLinesThreshold, request.WorkspacePath)
		return nil
	}

	log.Debugf("[deepwiki-incremental-update] Commit changes exceed threshold (%d lines), proceeding with incremental update for workspace: %s",
		changedLinesThreshold, request.WorkspacePath)

	task := queue.NewIncrementalUpdateTask(request, diffInfo)
	err := d.taskQueueManager.SubmitTask(task)
	if err != nil {
		log.Errorf("Failed to submit incremental update task for %s: %v", request.WorkspacePath, err)
		// 如果队列提交失败，回退到直接执行
		return d.performIncrementalUpdate(context.Background(), request, diffInfo)
	}

	log.Infof("Incremental update task submitted successfully for workspace: %s, task ID: %s",
		request.WorkspacePath, task.ID)
	return nil
}

// shouldSkipTaskSubmission 判断是否应该跳过任务提交的改进逻辑
func (d *DeepwikiService) shouldSkipTaskSubmission(workspacePath, taskType string) bool {
	// 1. 首先检查队列中是否已有该workspace的未完成任务
	tasks := d.taskQueueManager.GetTasksByWorkspace(workspacePath)
	for _, task := range tasks {
		if !task.IsFinished() {
			log.Infof("Workspace %s already has active task %s in queue, skipping %s task submission", workspacePath, task.ID, taskType)
			return true
		}
	}

	// 2. 检查数据库中repo的状态
	repo, err := d.storageService.GetWikiRepoByWorkspacePath(workspacePath)
	if err != nil {
		log.Warnf("Failed to check repo status for workspace %s: %v", workspacePath, err)
		return false // 无法确定状态时，允许提交
	}

	if repo == nil {
		log.Debugf("No existing repo found for workspace %s, allowing %s task submission", workspacePath, taskType)
		return false // 新repo，允许提交
	}

	// 3. 根据repo状态决定是否跳过
	switch repo.ProgressStatus {
	case definition.DeepWikiProgressStatusProcessing:
		// 检查是否为孤儿进程
		if d.isRepoOrphaned(repo) {
			log.Infof("Workspace %s has orphaned processing repo, allowing %s task submission for recovery", workspacePath, taskType)
			return false // 孤儿进程，允许恢复任务
		}
		log.Infof("Workspace %s has active processing repo, skipping %s task submission", workspacePath, taskType)
		return true // 真正在处理中，跳过

	case definition.DeepWikiProgressStatusPending:
		// Pending状态允许提交任务（恢复场景）
		log.Infof("Workspace %s has pending repo (recovery scenario), allowing %s task submission", workspacePath, taskType)
		return false

	case definition.DeepWikiProgressStatusCompleted:
		// 已完成的repo，根据任务类型决定
		if taskType == "incremental_update" {
			return false // 增量更新允许
		}
		log.Infof("Workspace %s already completed, skipping %s task submission", workspacePath, taskType)
		return true // 全量生成跳过

	case definition.DeepWikiProgressStatusFailed:
		// 失败状态允许重试
		log.Infof("Workspace %s has failed repo, allowing %s task submission for retry", workspacePath, taskType)
		return false

	default:
		log.Debugf("Workspace %s has repo with status %s, allowing %s task submission", workspacePath, repo.ProgressStatus, taskType)
		return false
	}
}

// checkAndHandleOrphanedReposForWorkspace 检测并处理指定workspace的孤儿进程，优先于新repo创建
func (d *DeepwikiService) checkAndHandleOrphanedReposForWorkspace(ctx context.Context, request definition.CreateDeepwikiRequest) bool {
	workspacePath := request.WorkspacePath

	// 检查当前workspace是否有processing状态的repo
	repo, err := d.storageService.GetWikiRepoByWorkspacePath(workspacePath)
	if err != nil {
		log.Errorf("Failed to check existing repo for orphan detection: %v", err)
		return false
	}

	if repo == nil {
		// 没有现有repo，不是孤儿进程情况
		return false
	}

	// 只处理processing状态的repo
	if repo.ProgressStatus != definition.DeepWikiProgressStatusProcessing {
		return false
	}

	// 检查是否真的是孤儿进程（processing状态但没有活跃任务）
	isOrphaned := d.isRepoOrphaned(repo)
	if !isOrphaned {
		log.Debugf("Repo is processing with active worker, not orphaned: %s", workspacePath)
		return false
	}

	log.Warnf("Detected orphaned repo for workspace: %s (processing status but no active worker)", workspacePath)

	// 将孤儿repo转为pending状态并提交恢复任务
	err = d.requeueOrphanedRepo(ctx, repo, request)
	if err != nil {
		log.Errorf("Failed to requeue orphaned repo: %v", err)
		// 即使恢复失败，也认为已经处理了孤儿进程（避免无限循环）
		// 但将repo状态设置为failed，以便后续流程能够处理
		repo.ProgressStatus = definition.DeepWikiProgressStatusFailed
		repo.GmtModified = time.Now()
		updateErr := d.storageService.UpdateWikiRepo(repo)
		if updateErr != nil {
			log.Errorf("Failed to update orphaned repo status to failed: %v", updateErr)
		}
		return true // 返回true避免无限重试，让后续流程处理failed状态
	}

	log.Infof("[Deepwiki] Successfully requeued orphaned repo for workspace: %s", workspacePath)
	return true
}

// isRepoOrphaned 检查repo是否为孤儿进程（processing状态但没有活跃任务）
func (d *DeepwikiService) isRepoOrphaned(repo *definition.AgentWikiRepo) bool {
	if d.taskQueueManager == nil {
		// 如果没有队列管理器，假设不是孤儿进程
		return false
	}

	// 检查队列中是否有该workspace的活跃任务
	tasks := d.taskQueueManager.GetTasksByWorkspace(repo.WorkspacePath)
	for _, task := range tasks {
		if !task.IsFinished() {
			// 有未完成的任务，不是孤儿进程
			return false
		}
	}

	// processing状态但没有活跃任务，确认为孤儿进程
	return true
}

// requeueOrphanedRepo 重新将孤儿repo放入队列进行恢复处理
func (d *DeepwikiService) requeueOrphanedRepo(ctx context.Context, repo *definition.AgentWikiRepo, request definition.CreateDeepwikiRequest) error {
	// 禁止remote agent模式下进行孤儿repo恢复任务提交
	if !support.IsEnableWiki() {
		log.Infof("Skip wiki generate for workspace: %s", repo.WorkspacePath)
		return nil
	}

	log.Infof("Re-queuing orphaned repo: %s (workspace: %s)", repo.ID, repo.WorkspacePath)

	// 1. 将repo状态重置为pending，以便恢复处理
	originalStatus := repo.ProgressStatus
	repo.ProgressStatus = definition.DeepWikiProgressStatusPending
	repo.GmtModified = time.Now()
	err := d.storageService.UpdateWikiRepo(repo)
	if err != nil {
		return fmt.Errorf("failed to update orphaned repo status to pending: %w", err)
	}
	log.Debugf("Updated orphaned repo %s status: %s -> PENDING", repo.ID, originalStatus)

	// 2. 创建恢复任务（使用原始request但生成新的requestId）
	recoveryRequest := definition.CreateDeepwikiRequest{
		WorkspacePath:     request.WorkspacePath,
		RequestId:         uuid.New().String(),
		PreferredLanguage: request.PreferredLanguage,
	}

	// 3. 提交恢复任务
	if d.taskQueueManager == nil {
		log.Errorf("Task queue manager not initialized, setting repo to failed")
		// 队列未初始化，将repo设置为failed状态
		repo.ProgressStatus = definition.DeepWikiProgressStatusFailed
		repo.GmtModified = time.Now()
		err := d.storageService.UpdateWikiRepo(repo)
		if err != nil {
			return err
		}
		return fmt.Errorf("task queue manager not initialized")
	}

	task := queue.NewFullGenerationTask(ctx, recoveryRequest)
	err = d.taskQueueManager.SubmitTask(task)
	if err != nil {
		// 如枟提交失败，将状态设置为failed（而不是恢复到processing）
		log.Errorf("Failed to submit recovery task, setting repo to failed status")
		repo.ProgressStatus = definition.DeepWikiProgressStatusFailed
		repo.GmtModified = time.Now()
		updateErr := d.storageService.UpdateWikiRepo(repo)
		if updateErr != nil {
			log.Errorf("Failed to update repo status to failed: %v", updateErr)
		}
		return fmt.Errorf("failed to submit recovery task for orphaned repo: %w", err)
	}

	log.Infof("Successfully submitted recovery task for orphaned repo %s as task %s", repo.ID, task.ID)
	return nil
}

func (d *DeepwikiService) UpdateWithCodeChunks(ctx context.Context, request definition.CreateDeepwikiRequest) {

	//TODO 基于codebase相关工具调用结果进行更新
}

// 更新wiki检查
func (d *DeepwikiService) checkAndUpdate(ctx context.Context, request definition.CreateDeepwikiRequest) {

	// 禁止remote agent模式下进行wiki检查和更新
	if !support.IsEnableWiki() {
		log.Infof("Skip wiki generate for workspace: %s", request.WorkspacePath)
		return
	}

	isNoWiki, err := support.IsNoWikiExist(request.WorkspacePath)
	if err != nil {
		log.Errorf("check is no wiki exist error. workspace: %s, err: %v", request.WorkspacePath, err)
		return
	}
	if isNoWiki {
		d.submitFullGenerationTask(ctx, request)
		return
	} else {
		wikiRepo, err := storage.GlobalStorageService.GetWikiRepoByWorkspacePath(request.WorkspacePath)
		if err != nil {
			log.Errorf("get wiki repo error. workspace: %s, err: %v", request.WorkspacePath, err)
			return
		}
		isGitRepo, err := support.IsGitRepo(request.WorkspacePath)
		if err != nil {
			log.Errorf("check is git repo error. workspace: %s, err: %v", request.WorkspacePath, err)
			return
		}
		if !isGitRepo {
			//非git工程检查wiki的上次更新时间
			if time.Since(wikiRepo.GmtModified) < noneGitRepoCheckExpireTime {
				return
			} else {
				//全量更新一次
				d.submitFullGenerationTask(ctx, request)
			}
		} else {
			commitDiffInfo, err := support.GetCommitDiff(request.WorkspacePath, wikiRepo.LastCommitID)
			if err != nil {
				log.Errorf("Get commit diff error. workspace: %s, err: %v", request.WorkspacePath, err)
				return
			}
			err = d.submitIncrementalUpdateTask(request, commitDiffInfo)
			if err != nil {
				return
			}
		}
	}
}

// performIncrementalUpdate: 实际执行增量更新链
func (d *DeepwikiService) performIncrementalUpdate(ctx context.Context, request definition.CreateDeepwikiRequest, diffInfo *definition.CommitDiffInfo) error {
	log.Debugf("[deepwiki-incremental-update] Performing incremental update for: %s", request.WorkspacePath)
	log.Debugf("[deepwiki-incremental-update] Commit diff info: %d total commits", diffInfo.TotalCommits)

	// 禁止remote agent模式下进行增量wiki更新
	if !support.IsEnableWiki() {
		log.Infof("Skip wiki generate for workspace: %s", request.WorkspacePath)
		return nil
	}

	// 1. 设置ctx信息
	workspaceFolder := definition.WorkspaceInfo{
		WorkspaceFolders: []definition.WorkspaceFolder{
			{
				Name: util.GetProjectName(request.WorkspacePath),
				URI:  request.WorkspacePath,
			},
		},
	}
	ctx = context.WithValue(ctx, definition.ContextKeyWorkspace, workspaceFolder)

	// 初始化 fileIndexer 以避免索引操作超时
	log.Debugf("[deepwiki-incremental-update] Initializing file indexer for workspace: %s", request.WorkspacePath)
	db, err := factory.NewKvStoreWithPath(request.WorkspacePath, factory.BBlotStore)
	if err != nil {
		log.Errorf("[deepwiki-incremental-update] Failed to create database for file indexer: %v", err)
		return fmt.Errorf("failed to create database for file indexer: %w", err)
	}

	globalIndex := indexing.NewGlobalFileIndex(db)
	fileIndexer := globalIndex.GetOrAddIndexer(workspaceFolder)
	ctx = context.WithValue(ctx, definition.ContextKeyFileIndexer, fileIndexer)
	log.Debugf("[deepwiki-incremental-update] Successfully initialized file indexer")

	// 2. 更新repo状态为processing
	repo, err := d.storageService.GetWikiRepoByWorkspacePath(request.WorkspacePath)
	if err != nil {
		log.Errorf("[deepwiki-incremental-update] Failed to get repo: %v", err)
		return fmt.Errorf("failed to get repo: %w", err)
	}
	if repo == nil {
		log.Errorf("[deepwiki-incremental-update] Repo not found for workspace: %s", request.WorkspacePath)
		return fmt.Errorf("repo not found for workspace: %s", request.WorkspacePath)
	}
	log.Debugf("[deepwiki-incremental-update] Found repo: %s (ID: %s, Status: %s)", repo.Name, repo.ID, repo.ProgressStatus)

	originalStatus := repo.ProgressStatus
	repo.ProgressStatus = definition.DeepWikiProgressStatusProcessing
	err = d.storageService.UpdateWikiRepo(repo)
	if err != nil {
		log.Errorf("[deepwiki-incremental-update] Failed to update repo status to processing: %v", err)
	} else {
		log.Debugf("[deepwiki-incremental-update] Updated repo status from %s to %s", originalStatus, definition.DeepWikiProgressStatusProcessing)
	}

	// 3. 执行增量更新链
	log.Debugf("[deepwiki-incremental-update] Creating incremental update chains...")
	c, err := newCommitDiffBasedUpdateChains()
	if err != nil {
		log.Errorf("[deepwiki-incremental-update] create chains error: %v", err)
		d.rollbackRepoStatus(repo, originalStatus)
		return err
	}
	log.Debugf("[deepwiki-incremental-update] Successfully created incremental update chains")

	// 创建agent统计信息，与全量生成保持一致
	agentStats := definition.NewDeepWikiAgentStats(request.RequestId, request.WorkspacePath)

	inputs := map[string]any{
		chainsCommon.KeyCreateDeepwikiRequest: request,
		chainsCommon.KeyRequestId:             request.RequestId,
		chainsCommon.KeySessionId:             request.SessionId,
		chainsCommon.KeyWikiCommitDiff:        diffInfo,
		chainsCommon.KeyPreferredLanguage:     request.PreferredLanguage,
		chainsCommon.KeyWikiGenerateStat:      agentStats,
	}

	log.Debugf("[deepwiki-incremental-update] Starting incremental update chain execution with %d inputs...", len(inputs))
	_, err = chains.Call(ctx, c, inputs)
	if err != nil {
		log.Errorf("[deepwiki-incremental-update] call incremental update chains error: %v", err)
		d.rollbackRepoStatus(repo, originalStatus)
		return err
	}
	log.Infof("[deepwiki-incremental-update] Successfully completed incremental update chain execution")

	// 3. 更新repo状态为completed，同时更新commit信息
	repo.ProgressStatus = definition.DeepWikiProgressStatusCompleted

	// 在同一个操作中更新commit信息，避免数据库事务冲突
	if diffInfo != nil && len(diffInfo.Commits) > 0 {
		latestCommit := diffInfo.Commits[0]
		log.Debugf("[deepwiki-incremental-update] Updating repo status to completed and commit info: %s -> %s (date: %v)",
			repo.LastCommitID, latestCommit.Hash, latestCommit.Date)

		// 在更新repo状态的同时更新commit信息
		repo.LastCommitID = latestCommit.Hash
		repo.LastCommitUpdate = latestCommit.Date
	} else {
		log.Warnf("[deepwiki-incremental-update] No commit info available for updating")
	}

	err = d.storageService.UpdateWikiRepo(repo)
	if err != nil {
		log.Errorf("[deepwiki-incremental-update] Failed to update repo status and commit info: %v", err)
		return err
	}
	log.Debugf("[deepwiki-incremental-update] Updated repo status to %s", definition.DeepWikiProgressStatusCompleted)

	// 4. 同步更新内存中的监控信息
	log.Debugf("[deepwiki-incremental-update] Starting step 4: update memory commit info")
	if diffInfo != nil && len(diffInfo.Commits) > 0 {
		latestCommit := diffInfo.Commits[0]
		log.Debugf("[deepwiki-incremental-update] About to update memory commit info for: %s", latestCommit.Hash[:8])

		// 使用带超时的context来防止卡死
		updateCtx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		// 在goroutine中执行更新操作
		updateDone := make(chan error, 1)
		go func() {
			log.Debugf("[deepwiki-incremental-update] Starting UpdateMemoryCommitInfo goroutine")
			updateErr := d.commitMonitorService.UpdateMemoryCommitInfo(
				request.WorkspacePath,
				latestCommit.Hash,
				latestCommit.Date,
			)
			log.Debugf("[deepwiki-incremental-update] UpdateMemoryCommitInfo goroutine completed")
			updateDone <- updateErr
		}()

		// 等待更新完成或超时
		select {
		case err = <-updateDone:
			if err != nil {
				log.Warnf("[deepwiki-incremental-update] Failed to update memory commit info: %v", err)
				// 不阻断流程，只记录警告
			} else {
				log.Debugf("[deepwiki-incremental-update] Successfully updated commit info: %s", latestCommit.Hash[:8])
			}
		case <-updateCtx.Done():
			log.Warnf("[deepwiki-incremental-update] UpdateMemoryCommitInfo operation timed out after 30 seconds, continuing...")
			// 超时也不阻断流程，继续执行
		}
	} else {
		log.Debugf("[deepwiki-incremental-update] No commit info to update (diffInfo: %v, commits: %d)", diffInfo != nil, func() int {
			if diffInfo != nil {
				return len(diffInfo.Commits)
			}
			return 0
		}())
	}
	log.Debugf("[deepwiki-incremental-update] Completed step 4: update memory commit info")

	// 完善agent统计信息，与全量生成保持一致
	log.Debugf("[deepwiki-incremental-update] Starting step 5: calculate agent statistics")
	agentStats.EndCall = time.Now()
	log.Debugf("[deepwiki-incremental-update] About to call CalStatSummary, GraphStats: %v", agentStats.GraphStats != nil)
	agentStats.CalStatSummary()
	log.Debugf("[deepwiki-incremental-update] Completed CalStatSummary")

	// 保存增量更新的统计信息
	err = support.SaveWikiGenerateResponseToFile(util.ToJsonStr(agentStats), "incremental-update-stat")
	if err != nil {
		return err
	}

	log.Infof("[deepwiki-incremental-update] Successfully completed incremental update for workspace: %s", request.WorkspacePath)
	return nil
}

// rollbackRepoStatus 回退repo状态
func (d *DeepwikiService) rollbackRepoStatus(repo *definition.AgentWikiRepo, originalStatus string) {
	repo.ProgressStatus = originalStatus
	err := d.storageService.UpdateWikiRepo(repo)
	if err != nil {
		log.Errorf("Failed to rollback repo status: %v", err)
	} else {
		log.Infof("Rolled back repo status to: %s", originalStatus)
	}
}

// StopMonitoring: 停止监控服务
func (d *DeepwikiService) StopMonitoring() {
	// 停止监控服务（这会清理所有监控项目和回调函数）
	d.commitMonitorService.StopMonitoring()

	// 同时停止任务队列
	if d.taskQueueManager != nil {
		err := d.taskQueueManager.Stop()
		if err != nil {
			log.Errorf("Failed to stop task queue manager: %v", err)
		} else {
			log.Debugf("Task queue manager stopped successfully")
		}
	}
}

// RemoveWorkspaceFromMonitoring 从监控中移除指定的workspace
func (d *DeepwikiService) RemoveWorkspaceFromMonitoring(workspacePath string) {
	if d.commitMonitorService != nil {
		d.commitMonitorService.RemoveProject(workspacePath)
		log.Debugf("Removed workspace from monitoring: %s", workspacePath)
	}
}

// setupIncrementalMonitoring 智能设置增量监控，处理重复注册情况
func (d *DeepwikiService) setupIncrementalMonitoring(workspacePath string, preferredLanguage string) error {
	if d.commitMonitorService == nil {
		return fmt.Errorf("commit monitor service is nil")
	}

	// 检查项目是否已经在监控中
	monitoringStatus := d.commitMonitorService.GetMonitoringStatus()
	_, alreadyMonitored := monitoringStatus[workspacePath]

	// 尝试添加项目到监控（如果还未监控）
	if !alreadyMonitored {
		err := d.commitMonitorService.AddProject(workspacePath, 30*time.Minute)
		if err != nil {
			// 如果添加失败，检查是否是因为已经存在
			if strings.Contains(err.Error(), "already being monitored") {
				log.Debugf("Project %s is already being monitored, will ensure callback is registered", workspacePath)
				alreadyMonitored = true
			} else {
				return fmt.Errorf("failed to add project to monitor: %w", err)
			}
		} else {
			log.Debugf("Successfully added project %s to monitor", workspacePath)
		}
	} else {
		log.Debugf("Project %s is already being monitored, ensuring callback is up-to-date", workspacePath)
	}

	// 注册或更新增量更新回调函数（始终执行以确保回调是最新的）
	d.commitMonitorService.RegisterIncrementalUpdateCallback(workspacePath, func(workspace string, diffInfo *definition.CommitDiffInfo) error {
		updateRequest := definition.CreateDeepwikiRequest{
			WorkspacePath:     workspace,
			RequestId:         uuid.New().String(),
			PreferredLanguage: preferredLanguage,
		}
		log.Debugf("[deepwiki-incremental-update] Monitor triggered incremental update for %s", workspace)
		return d.submitIncrementalUpdateTask(updateRequest, diffInfo)
	})
	log.Debugf("Registered/updated incremental update callback for workspace: %s", workspacePath)

	// 启动监控服务（如果未启动）
	err := d.commitMonitorService.StartMonitoring(30 * time.Minute)
	if err != nil {
		// StartMonitoring 会返回错误如果已经在运行，这是正常的
		if !strings.Contains(err.Error(), "already running") {
			return fmt.Errorf("failed to start monitoring service: %w", err)
		}
		log.Debugf("Monitoring service is already running")
	}

	return nil
}

// StopWorkspaceTasks 停止指定workspace的所有wiki生成任务和监控
func (d *DeepwikiService) StopWorkspaceTasks(workspacePath string) int {
	var totalStopped int

	// 1. 取消任务队列中该workspace的所有未完成任务
	if d.taskQueueManager != nil {
		cancelled := d.taskQueueManager.CancelTasksForWorkspace(workspacePath)
		totalStopped += cancelled
		log.Debugf("Cancelled %d queue tasks for workspace: %s", cancelled, workspacePath)
	}

	// 2. 从监控服务中移除该workspace
	d.RemoveWorkspaceFromMonitoring(workspacePath)

	// 3. 清理workspace锁
	if _, exists := d.locks.Load(workspacePath); exists {
		d.locks.Delete(workspacePath)
		log.Debugf("Removed workspace lock for: %s", workspacePath)
	}

	if totalStopped > 0 {
		log.Infof("Successfully stopped %d wiki generation tasks for workspace: %s", totalStopped, workspacePath)
	} else {
		log.Debugf("No active wiki generation tasks found for workspace: %s", workspacePath)
	}

	return totalStopped
}

// 全量构建
func (d *DeepwikiService) generateNew(ctx context.Context, request definition.CreateDeepwikiRequest) {
	workspaceFolder := definition.WorkspaceInfo{
		WorkspaceFolders: []definition.WorkspaceFolder{
			{
				Name: util.GetProjectName(request.WorkspacePath),
				URI:  request.WorkspacePath,
			},
		},
	}

	ctx = context.WithValue(ctx, definition.ContextKeyWorkspace, workspaceFolder)
	// 禁止remote agent模式下进行全量wiki生成
	if !support.IsEnableWiki() {
		log.Infof("Skip wiki generate for workspace: %s", request.WorkspacePath)
		return
	}

	gitSupport, openGitErr := support.NewGitSupport(request.WorkspacePath)
	if openGitErr != nil {
		log.Errorf("new git support error. workspace: %s, err: %v", request.WorkspacePath, openGitErr)
	}

	var latestCommit *object.Commit
	var err error
	if gitSupport != nil && gitSupport.IsAvailable() {
		latestCommit, err = gitSupport.GetHeadCommit()
		if err != nil {
			log.Errorf("get head commit error. workspace: %s, err: %v", request.WorkspacePath, err)
		}
		if latestCommit == nil {
			latestCommit = support.NewFixedCommit()
		}
	} else {
		latestCommit = support.NewFixedCommit()
	}

	wikiRepo := &definition.AgentWikiRepo{
		ID:               uuid.NewString(),
		LastCommitID:     latestCommit.Hash.String(),
		LastCommitUpdate: latestCommit.Committer.When,
		Name:             util.GetProjectName(request.WorkspacePath),
		ProgressStatus:   definition.DeepWikiProgressStatusPending,
		WorkspacePath:    request.WorkspacePath,
		GmtCreate:        time.Now(),
		GmtModified:      time.Now(),
	}

	// 使用安全的创建方法，避免创建重复的repo
	actualRepo, isNewRepo, err := storage.GlobalStorageService.CreateWikiRepoIfNotExists(wikiRepo)
	if err != nil {
		log.Errorf("create wiki repo error. workspace: %s, err: %v", request.WorkspacePath, err)
		return
	}

	// 如果repo已存在，根据状态决定处理策略
	if !isNewRepo {
		if actualRepo.ProgressStatus == definition.DeepWikiProgressStatusCompleted {
			log.Debugf("Wiki repo already completed, skipping generation for workspace: %s", request.WorkspacePath)
			return
		}
		if actualRepo.ProgressStatus == definition.DeepWikiProgressStatusProcessing {
			log.Debugf("Wiki repo is currently processing, skipping generation for workspace: %s", request.WorkspacePath)
			return
		}

		// 如果状态是失败，先更新repo状态为pending
		if actualRepo.ProgressStatus == definition.DeepWikiProgressStatusFailed {
			actualRepo.ProgressStatus = definition.DeepWikiProgressStatusPending
			actualRepo.GmtModified = time.Now()
			if updateErr := storage.GlobalStorageService.UpdateWikiRepo(actualRepo); updateErr != nil {
				log.Errorf("Failed to update failed repo status to pending: %v", updateErr)
				return
			}
			log.Debugf("Updated failed repo status to pending for workspace: %s", request.WorkspacePath)
		}

		// Pending状态允许继续处理（恢复场景和失败重试场景）
		if actualRepo.ProgressStatus == definition.DeepWikiProgressStatusPending {
			log.Debugf("Wiki repo is pending (recovery/retry scenario), continuing with generation for workspace: %s", request.WorkspacePath)
			// 继续执行，不return
		}
	}

	c, err := newDeepWikiCreateChains()
	if err != nil {
		log.Errorf("create chains error: %v", err)
		// todo 创建失败状态链来处理错误
		failChain := wikiChains.NewDeepWikiStatusChain("fail")
		inputs := map[string]any{
			chainsCommon.KeyCreateDeepwikiRequest: request,
		}
		failChain.Call(ctx, inputs)
		return
	}

	agentStats := definition.NewDeepWikiAgentStats(request.RequestId, request.WorkspacePath)

	inputs := map[string]any{
		chainsCommon.KeyCreateDeepwikiRequest: request,
		chainsCommon.KeyRequestId:             request.RequestId,
		chainsCommon.KeySessionId:             request.SessionId,
		chainsCommon.KeyPreferredLanguage:     request.PreferredLanguage,
		chainsCommon.KeyWikiGenerateStat:      agentStats,
	}

	_, err = chains.Call(ctx, c, inputs)
	if err != nil {
		log.Errorf("call chains error: %v", err)
		// 创建失败状态链来处理错误
		failChain := wikiChains.NewDeepWikiStatusChain("fail")
		failInputs := map[string]any{
			chainsCommon.KeyCreateDeepwikiRequest: request,
		}
		failChain.Call(ctx, failInputs)
		return
	} else {
		//go api.MemoryIndexAll(ctx, util.GetProjectName(request.WorkspacePath), request.WorkspacePath)
	}

	agentStats.EndCall = time.Now()
	agentStats.CalStatSummary()

	err = support.SaveWikiGenerateResponseToFile(util.ToJsonStr(agentStats), "full-generate-stat")
	if err != nil {
		log.Errorf("Failed to save wiki generate response to file: %v", err)
	}

	repoName := util.GetProjectName(request.WorkspacePath)
	log.Infof("Deepwiki: full-generation of project wiki finished - Repo: %s, Workspace: %s", repoName, request.WorkspacePath)
	return
}

// 创建基于commit diff的wiki增量更新流程
func newCommitDiffBasedUpdateChains() (*chains.SequentialChain, error) {
	catalogDiffService := wikiService.NewCommitDiffService(storage.GlobalStorageService)

	chatChains := []chains.Chain{
		wikiChains.NewGenerateCommitsDiffChain(catalogDiffService),
		wikiChains.NewGenerateCatalogueDiffChainDefault(),
		wikiChains.NewUpdateWikiChain(),
	}
	delegate, err := chains.NewSequentialChain(chatChains, []string{}, []string{})
	if err != nil {
		return nil, err
	}
	return delegate, nil
}

// 创建项目wiki全量创建的流程
func newDeepWikiCreateChains() (*chains.SequentialChain, error) {
	// 创建服务实例
	catalogueFilterService := wikiService.NewCatalogueFilterService()
	catalogueService := wikiService.NewGenerateCatalogueService()

	chatChains := []chains.Chain{
		wikiChains.NewRecoveryCheckChain(),         // 恢复检查（新增）
		wikiChains.NewDeepWikiStatusChain("start"), // 开始状态管理
		&wikiChains.GenerateReadmeChain{},
		&wikiChains.GenerateOverviewChain{},
		wikiChains.NewCatalogueFilterChain(catalogueFilterService),
		wikiChains.NewGenerateCataloguePlanChain(catalogueService),
		wikiChains.NewGenerateCatalogueChain(catalogueService),
		wikiChains.NewGenerateWikiChain(),
		wikiChains.NewDeepWikiStatusChain("complete"), // 完成状态管理
	}
	delegate, err := chains.NewSequentialChain(chatChains, []string{}, []string{})
	if err != nil {
		return nil, err
	}
	return delegate, nil
}

// 创建基于问答过程中的code chunk的wiki增量更新流程
func UpdateChatCode(ctx context.Context, request definition.CreateDeepwikiRequest) {

	// 禁止remote agent模式下进行基于代码块的wiki增量更新
	if !support.IsEnableWiki() {
		log.Infof("Skip wiki generate for workspace: %s", request.WorkspacePath)
		return
	}

	sessionContexts, _ := session.GetSessionContexts(request.SessionId, session.SessionFlowToolCallResultContext)
	if sessionContexts == nil || len(sessionContexts) <= 0 {
		return
	}
	codeChunks := extractRagCodeChunks(sessionContexts)
	if codeChunks == nil || len(codeChunks) <= 0 {
		return
	}
	c, err := newCodeChunkBasedUpdateChains()
	if err != nil {
		log.Errorf("create chains error: %v", err)
		return
	}

	// 1. 设置ctx信息
	workspaceFolder := definition.WorkspaceInfo{
		WorkspaceFolders: []definition.WorkspaceFolder{
			{
				Name: util.GetProjectName(request.WorkspacePath),
				URI:  request.WorkspacePath,
			},
		},
	}

	ctx = context.WithValue(ctx, definition.ContextKeyWorkspace, workspaceFolder)

	// 创建agent统计信息，与全量生成保持一致
	agentStats := definition.NewDeepWikiAgentStats(request.RequestId, request.WorkspacePath)

	inputs := map[string]any{
		chainsCommon.KeyCreateDeepwikiRequest:    request,
		chainsCommon.KeyRequestId:                request.RequestId,
		chainsCommon.KeySessionId:                request.SessionId,
		chainsCommon.KeyWikiConversationRagCodes: codeChunks,
		chainsCommon.KeyPreferredLanguage:        request.PreferredLanguage,
		chainsCommon.KeyWikiGenerateStat:         agentStats,
	}
	newInputs, err := chains.Call(ctx, c, inputs)
	if err != nil {
		log.Errorf("call chains error: %v", err)
		return
	} else {
		//增量更新
		//go api.MemoryIndexAll(ctx, util.GetProjectName(request.WorkspacePath), request.WorkspacePath)
	}

	// 完善agent统计信息，与全量生成保持一致
	agentStats.EndCall = time.Now()
	agentStats.CalStatSummary()

	// 保存基于代码块的增量更新统计信息
	err = support.SaveWikiGenerateResponseToFile(util.ToJsonStr(agentStats), "chat-code-update-stat")
	if err != nil {
		log.Errorf("Failed to save wiki generate response to file: %v", err)
	}

	log.Infof("newInputs: %v", newInputs)

	log.Infof("generate project wiki finished. workspace: %s", request.WorkspacePath)
	return
}

func extractRagCodeChunks(sessionContexts []session.SessionFlowContext) []indexer.CodeChunk {
	if sessionContexts == nil || len(sessionContexts) <= 0 {
		return nil
	}
	var codeChunks []indexer.CodeChunk
	for _, s := range sessionContexts {
		if s.ContextKey == session.SessionContextKeySearchCodebase {
			if s.ContextValue != nil {
				chunks := s.ContextValue.([]indexer.CodeChunk)
				if chunks != nil {
					codeChunks = append(codeChunks, chunks...)
				}
			}

		}
	}
	return codeChunks
}

// 创建基于代码RAG的wiki增量更新流程
func newCodeChunkBasedUpdateChains() (*chains.SequentialChain, error) {
	chatChains := []chains.Chain{
		&wikiChains.CatalogueUpdateWithCodeChain{},
		wikiChains.NewGenerateCatalogueDiffChainDefault(),
		wikiChains.NewUpdateWikiChain(),
	}
	delegate, err := chains.NewSequentialChain(chatChains, []string{}, []string{})
	if err != nil {
		return nil, err
	}
	return delegate, nil
}

// DeleteWikiIndex 删除指定工程的wiki向量化索引
func (d *DeepwikiService) DeleteWikiIndex(ctx context.Context, workspacePath string) error {
	log.Infof("[DeepwikiService] Deleting wiki index for workspace: %s", workspacePath)

	// 获取workspace信息
	workspaceFolder := definition.WorkspaceInfo{
		WorkspaceFolders: []definition.WorkspaceFolder{
			{
				Name: util.GetProjectName(workspacePath),
				URI:  workspacePath,
			},
		},
	}
	ctx = context.WithValue(ctx, definition.ContextKeyWorkspace, workspaceFolder)

	// 获取wikiId
	wikiId := util.GetProjectName(workspacePath)

	// 调用内存索引删除函数
	err := api.MemoryDeleteWiki(ctx, wikiId, workspacePath)
	if err != nil {
		log.Errorf("[DeepwikiService] Failed to delete wiki index for workspace %s: %v", workspacePath, err)
		return err
	}

	log.Infof("[DeepwikiService] Successfully deleted wiki index for workspace: %s", workspacePath)
	return nil
}

// RebuildWikiIndex 重建指定工程的wiki向量化索引
func (d *DeepwikiService) RebuildWikiIndex(ctx context.Context, workspacePath string) error {
	log.Infof("[DeepwikiService] Rebuilding wiki index for workspace: %s", workspacePath)

	// 获取workspace信息
	workspaceFolder := definition.WorkspaceInfo{
		WorkspaceFolders: []definition.WorkspaceFolder{
			{
				Name: util.GetProjectName(workspacePath),
				URI:  workspacePath,
			},
		},
	}
	ctx = context.WithValue(ctx, definition.ContextKeyWorkspace, workspaceFolder)

	// 检查wiki repo是否存在
	wikiRepo, err := d.storageService.GetWikiRepoByWorkspacePath(workspacePath)
	if err != nil {
		log.Errorf("[DeepwikiService] Failed to get wiki repo for workspace %s: %v", workspacePath, err)
		return fmt.Errorf("failed to get wiki repo: %w", err)
	}

	if wikiRepo == nil {
		log.Errorf("[DeepwikiService] Wiki repo not found for workspace: %s", workspacePath)
		return fmt.Errorf("wiki repo not found for workspace: %s", workspacePath)
	}

	// 确保wiki已经生成完成
	if wikiRepo.ProgressStatus != definition.DeepWikiProgressStatusCompleted {
		log.Errorf("[DeepwikiService] Wiki is not completed for workspace %s, current status: %s", workspacePath, wikiRepo.ProgressStatus)
		return fmt.Errorf("wiki is not completed, current status: %s", wikiRepo.ProgressStatus)
	}

	// 获取wikiId
	wikiId := util.GetProjectName(workspacePath)

	// 调用内存索引重建函数
	err = api.MemoryRebuildWiki(ctx, wikiId, workspacePath)
	if err != nil {
		log.Errorf("[DeepwikiService] Failed to rebuild wiki index for workspace %s: %v", workspacePath, err)
		return err
	}

	log.Infof("[DeepwikiService] Successfully rebuilt wiki index for workspace: %s", workspacePath)
	return nil
}

// GetKnowledgeService 获取knowledge集成服务
func (d *DeepwikiService) GetKnowledgeService() *wikiService.KnowledgeIntegrationService {
	return GlobalKnowledgeService
}

// SetReposWithFailedCatalogsToFailed 将含有failed状态catalog的repo都设置成failed状态
func (d *DeepwikiService) SetReposWithFailedCatalogsToFailed(ctx context.Context) error {
	log.Infof("[SetReposWithFailedCatalogsToFailed] Starting to check and update repos with failed catalogs...")

	// 获取所有的repo
	repos, err := d.storageService.GetAllWikiRepos()
	if err != nil {
		log.Errorf("[SetReposWithFailedCatalogsToFailed] Failed to get all repos: %v", err)
		return fmt.Errorf("failed to get all repos: %w", err)
	}

	if len(repos) == 0 {
		log.Infof("[SetReposWithFailedCatalogsToFailed] No repos found")
		return nil
	}

	log.Infof("[SetReposWithFailedCatalogsToFailed] Found %d repos to check", len(repos))

	var updatedCount int
	var errors []error

	// 检查每个repo是否含有failed的catalog
	for _, repo := range repos {
		// 获取repo的catalog状态统计
		statusCount, err := d.storageService.GetCatalogStatusByRepoID(repo.ID)
		if err != nil {
			log.Errorf("[SetReposWithFailedCatalogsToFailed] Failed to get catalog status for repo %s: %v", repo.ID, err)
			errors = append(errors, fmt.Errorf("failed to get catalog status for repo %s: %w", repo.ID, err))
			continue
		}

		// 检查是否有failed的catalog
		failedCount := statusCount[definition.DeepWikiProgressStatusFailed]
		if failedCount > 0 {
			// 如果repo状态不是failed，则更新为failed
			if repo.ProgressStatus != definition.DeepWikiProgressStatusFailed {
				oldStatus := repo.ProgressStatus
				repo.ProgressStatus = definition.DeepWikiProgressStatusFailed
				repo.GmtModified = time.Now()

				err = d.storageService.UpdateWikiRepo(repo)
				if err != nil {
					log.Errorf("[SetReposWithFailedCatalogsToFailed] Failed to update repo %s status to failed: %v", repo.ID, err)
					errors = append(errors, fmt.Errorf("failed to update repo %s to failed: %w", repo.ID, err))
					continue
				}

				updatedCount++
				log.Infof("[SetReposWithFailedCatalogsToFailed] Updated repo %s (%s) status: %s -> FAILED (contains %d failed catalogs)",
					repo.ID, repo.Name, oldStatus, failedCount)
			} else {
				log.Debugf("[SetReposWithFailedCatalogsToFailed] Repo %s (%s) already has FAILED status (contains %d failed catalogs)",
					repo.ID, repo.Name, failedCount)
			}
		} else {
			log.Debugf("[SetReposWithFailedCatalogsToFailed] Repo %s (%s) has no failed catalogs, current status: %s",
				repo.ID, repo.Name, repo.ProgressStatus)
		}
	}

	if len(errors) > 0 {
		log.Errorf("[SetReposWithFailedCatalogsToFailed] Completed with %d errors, updated %d repos", len(errors), updatedCount)
		for _, err := range errors {
			log.Errorf("[SetReposWithFailedCatalogsToFailed] Error: %v", err)
		}
		return fmt.Errorf("some repos failed to update: %d errors", len(errors))
	}

	log.Infof("[SetReposWithFailedCatalogsToFailed] Successfully completed: updated %d repos to FAILED status", updatedCount)
	return nil
}

// checkAndFixFailedCatalogsForWorkspace 检查并修复指定workspace的failed catalog状态
// 返回 true 表示检测到failed catalogs并修改了repo状态
func (d *DeepwikiService) checkAndFixFailedCatalogsForWorkspace(ctx context.Context, workspacePath string) bool {
	log.Debugf("[checkAndFixFailedCatalogsForWorkspace] Checking failed catalogs for workspace: %s", workspacePath)

	// 获取当前workspace的repo
	repo, err := d.storageService.GetWikiRepoByWorkspacePath(workspacePath)
	if err != nil {
		log.Errorf("[checkAndFixFailedCatalogsForWorkspace] Failed to get repo for workspace %s: %v", workspacePath, err)
		return false
	}

	if repo == nil {
		log.Debugf("[checkAndFixFailedCatalogsForWorkspace] No repo found for workspace: %s", workspacePath)
		return false
	}

	// 获取repo的catalog状态统计
	statusCount, err := d.storageService.GetCatalogStatusByRepoID(repo.ID)
	if err != nil {
		log.Errorf("[checkAndFixFailedCatalogsForWorkspace] Failed to get catalog status for repo %s: %v", repo.ID, err)
		return false
	}

	// 检查是否有failed的catalog
	failedCount := statusCount[definition.DeepWikiProgressStatusFailed]
	if failedCount > 0 {
		// 如果repo状态不是failed，则更新为failed
		if repo.ProgressStatus != definition.DeepWikiProgressStatusFailed {
			oldStatus := repo.ProgressStatus
			repo.ProgressStatus = definition.DeepWikiProgressStatusFailed
			repo.GmtModified = time.Now()

			err = d.storageService.UpdateWikiRepo(repo)
			if err != nil {
				log.Errorf("[checkAndFixFailedCatalogsForWorkspace] Failed to update repo %s status to failed: %v", repo.ID, err)
				return false
			}

			log.Infof("[checkAndFixFailedCatalogsForWorkspace] Updated repo %s (%s) status: %s -> FAILED (contains %d failed catalogs)",
				repo.ID, repo.Name, oldStatus, failedCount)
			return true // 检测到failed catalogs并修改了状态
		} else {
			log.Debugf("[checkAndFixFailedCatalogsForWorkspace] Repo %s (%s) already has FAILED status (contains %d failed catalogs)",
				repo.ID, repo.Name, failedCount)
			return true // 检测到failed catalogs（已经是failed状态）
		}
	} else {
		log.Debugf("[checkAndFixFailedCatalogsForWorkspace] Repo %s (%s) has no failed catalogs, current status: %s",
			repo.ID, repo.Name, repo.ProgressStatus)
		return false // 没有failed catalogs
	}
}

// checkAndRecoverFailedRepoForWorkspace 检查并恢复指定workspace的failed状态repo
func (d *DeepwikiService) checkAndRecoverFailedRepoForWorkspace(ctx context.Context, request definition.CreateDeepwikiRequest) {
	workspacePath := request.WorkspacePath
	log.Debugf("[checkAndRecoverFailedRepoForWorkspace] Checking failed repo for workspace: %s", workspacePath)

	// 获取当前workspace的repo
	repo, err := d.storageService.GetWikiRepoByWorkspacePath(workspacePath)
	if err != nil {
		log.Errorf("[checkAndRecoverFailedRepoForWorkspace] Failed to get repo for workspace %s: %v", workspacePath, err)
		return
	}

	if repo == nil {
		log.Debugf("[checkAndRecoverFailedRepoForWorkspace] No repo found for workspace: %s", workspacePath)
		return
	}

	// 只处理failed状态的repo
	if repo.ProgressStatus != definition.DeepWikiProgressStatusFailed {
		log.Debugf("[checkAndRecoverFailedRepoForWorkspace] Repo status is %s, not failed, skipping recovery for workspace: %s",
			repo.ProgressStatus, workspacePath)
		return
	}

	log.Infof("[checkAndRecoverFailedRepoForWorkspace] Found failed repo for workspace: %s, preparing for recovery", workspacePath)

	// 创建恢复服务管理器
	recoveryManager := support.NewRecoveryServiceManager(d.storageService)

	// 执行恢复准备
	err = recoveryManager.GetRecoveryService().RecoverRepo(ctx, repo)
	if err != nil {
		log.Errorf("[checkAndRecoverFailedRepoForWorkspace] Failed to prepare repo for recovery %s: %v", repo.ID, err)
		return
	}

	log.Infof("[checkAndRecoverFailedRepoForWorkspace] Successfully prepared failed repo for recovery: %s", workspacePath)
}

// shouldPerformIndexCheck 检查是否需要执行索引检查（基于时间间隔和repo修改时间）
func (d *DeepwikiService) shouldPerformIndexCheck(workspacePath string) bool {
	// 获取repo信息以检查修改时间
	repo, err := d.storageService.GetWikiRepoByWorkspacePath(workspacePath)
	if err != nil {
		log.Errorf("[shouldPerformIndexCheck] Failed to get repo for %s: %v", workspacePath, err)
		return false
	}
	if repo == nil {
		log.Debugf("[shouldPerformIndexCheck] No repo found for %s", workspacePath)
		return false
	}

	// 检查repo是否在1.5小时内被修改过
	repoModifiedRecently := time.Since(repo.GmtModified) < 90*time.Minute

	// 获取上次检查时间
	if lastCheck, ok := d.lastIndexCheckTimes.Load(workspacePath); ok {
		lastCheckTime := lastCheck.(time.Time)
		timeSinceLastCheck := time.Since(lastCheckTime)

		// 如果repo在1.5小时内被修改过，即使在3小时冷却期内也要检查
		if repoModifiedRecently {
			log.Debugf("[shouldPerformIndexCheck] Repo modified recently (%v ago), performing index check for %s",
				time.Since(repo.GmtModified), workspacePath)
			d.lastIndexCheckTimes.Store(workspacePath, time.Now())
			return true
		}

		// 如果距离上次检查少于3小时且repo未被近期修改，跳过检查
		if timeSinceLastCheck < 3*time.Hour {
			log.Debugf("[shouldPerformIndexCheck] Skipping index check for %s, last check was %v ago, repo modified %v ago",
				workspacePath, timeSinceLastCheck, time.Since(repo.GmtModified))
			return false
		}
	}

	// 更新检查时间
	d.lastIndexCheckTimes.Store(workspacePath, time.Now())
	log.Debugf("[shouldPerformIndexCheck] Performing index check for %s (repo modified %v ago)",
		workspacePath, time.Since(repo.GmtModified))
	return true
}

// checkAndFixWikiIndex 检查并修复wiki索引完整性
func (d *DeepwikiService) checkAndFixWikiIndex(ctx context.Context, workspacePath string) {
	log.Debugf("[checkAndFixWikiIndex] Checking wiki index completeness for workspace: %s", workspacePath)

	// 获取所有wiki item IDs
	wikiIds, err := d.storageService.GetWikiItemIdsByRepoAndWorkspace(workspacePath)
	if err != nil {
		log.Errorf("[checkAndFixWikiIndex] Failed to get wiki item ids for workspace %s: %v", workspacePath, err)
		return
	}

	if len(wikiIds) == 0 {
		log.Debugf("[checkAndFixWikiIndex] No wiki items found for workspace: %s", workspacePath)
		return
	}

	log.Debugf("[checkAndFixWikiIndex] Found %d wiki items for workspace: %s", len(wikiIds), workspacePath)

	// 检查哪些没有被index完成
	failedIds := api.GetBuiltRecords(ctx, workspacePath, wikiIds)

	if len(failedIds) == 0 {
		log.Debugf("[checkAndFixWikiIndex] All wiki items are already indexed for workspace: %s", workspacePath)
		return
	}

	log.Infof("[checkAndFixWikiIndex] Found %d unindexed wiki items for workspace: %s, starting to index them",
		len(failedIds), workspacePath)

	wikiRepo, err := d.storageService.GetWikiRepoByWorkspacePath(workspacePath)
	if err != nil || wikiRepo == nil {
		log.Errorf("Failed to get wiki repo for workspace: %s", workspacePath)
		return
	}
	repoId := wikiRepo.ID // 使用数据库中的唯一ID
	api.MemoryIndexUpdate(ctx, repoId, workspacePath, failedIds, []string{})

	log.Infof("[checkAndFixWikiIndex] Successfully indexed %d wiki items for workspace: %s",
		len(failedIds), workspacePath)
	return
}
