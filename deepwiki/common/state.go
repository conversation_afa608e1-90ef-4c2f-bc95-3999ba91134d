package common

import (
	"context"
	"sync"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/memory"
)

var _ graph.State = &DeepWikiGenerateState{}

type DeepWikiGenerateState struct {
	CtxForClient    context.Context
	Inputs          *sync.Map // 使用 sync.Map 替代普通 map，解决并发访问问题
	ShortTermMemory memory.ShortTermMemory
	ToolCallCount   int

	WikiRepo WikiRepoInfo

	// 新增：记录每个 agent 步骤的最后一条 message
	AgentLastMessages map[string]interface{} // 用 interface{} 以兼容 agentDefinition.Message
}

type WikiRepoInfo struct {
	RepoId        string
	WorkspacePath string
}

// GetInput 安全地获取input值
func (s *DeepWikiGenerateState) GetInput(key string) (any, bool) {
	if s.Inputs == nil {
		return nil, false
	}
	return s.Inputs.Load(key)
}

// SetInput 安全地设置input值
func (s *DeepWikiGenerateState) SetInput(key string, value any) {
	if s.Inputs == nil {
		s.Inputs = &sync.Map{}
	}
	s.Inputs.Store(key, value)
}

// CopyInputs 安全地复制所有inputs
func (s *DeepWikiGenerateState) CopyInputs() map[string]any {
	copy := make(map[string]any)
	if s.Inputs != nil {
		s.Inputs.Range(func(key, value interface{}) bool {
			if keyStr, ok := key.(string); ok {
				copy[keyStr] = value
			}
			return true
		})
	}
	return copy
}

// SetInputs 安全地批量设置inputs
func (s *DeepWikiGenerateState) SetInputs(inputs map[string]any) {
	if s.Inputs == nil {
		s.Inputs = &sync.Map{}
	}
	for k, v := range inputs {
		s.Inputs.Store(k, v)
	}
}

func (s *DeepWikiGenerateState) GetShortTermMemory() memory.ShortTermMemory {
	return s.ShortTermMemory
}

func (s *DeepWikiGenerateState) GetCtxForClient() context.Context {
	return s.CtxForClient
}

func (s *DeepWikiGenerateState) Clone() graph.State {
	// 深拷贝Inputs sync.Map
	inputsCopy := &sync.Map{}
	if s.Inputs != nil {
		s.Inputs.Range(func(key, value interface{}) bool {
			inputsCopy.Store(key, value)
			return true
		})
	}

	return &DeepWikiGenerateState{
		CtxForClient:    s.CtxForClient,
		Inputs:          inputsCopy,
		ShortTermMemory: s.ShortTermMemory,
		ToolCallCount:   s.ToolCallCount,
	}
}

func (s *DeepWikiGenerateState) ToChainInput() map[string]interface{} {
	return s.CopyInputs()
}

func (s *DeepWikiGenerateState) FromChainOutput(source map[string]interface{}) graph.State {
	// 将source复制到Inputs
	if s.Inputs == nil {
		s.Inputs = &sync.Map{}
	}
	for k, v := range source {
		s.Inputs.Store(k, v)
	}

	return &DeepWikiGenerateState{
		CtxForClient:    s.CtxForClient,
		Inputs:          s.Inputs,
		ShortTermMemory: s.ShortTermMemory,
		ToolCallCount:   s.ToolCallCount,
	}
}

// AgentContextDeepCopy 深拷贝AgentContext（仅State深拷贝，其他字段浅拷贝或按需处理）
// 需要在 deepwiki/common/definition.go 中定义 AgentContext 时调用
func AgentContextDeepCopy(ctx *AgentContext) *AgentContext {
	if ctx == nil {
		return nil
	}
	var stateCopy graph.State
	if ctx.State != nil {
		// 目前只支持 DeepWikiGenerateState 的深拷贝
		if s, ok := ctx.State.(*DeepWikiGenerateState); ok {
			stateCopy = s.DeepCopy()
		} else if cloner, ok := ctx.State.(interface{ Clone() graph.State }); ok {
			stateCopy = cloner.Clone()
		} else {
			stateCopy = ctx.State // fallback: 浅拷贝
		}
	}
	// LLMConfig、LLMClient、Tools 视为只读或线程安全，直接引用
	return &AgentContext{
		LLMConfig: ctx.LLMConfig, // 若需深拷贝可补充
		LLMClient: ctx.LLMClient,
		Tools:     ctx.Tools,
		State:     stateCopy,
	}
}

// DeepCopy 深拷贝 DeepWikiGenerateState
func (s *DeepWikiGenerateState) DeepCopy() *DeepWikiGenerateState {
	if s == nil {
		return nil
	}
	inputsCopy := &sync.Map{}
	if s.Inputs != nil {
		s.Inputs.Range(func(key, value interface{}) bool {
			inputsCopy.Store(key, value)
			return true
		})
	}
	// AgentLastMessages 深拷贝
	var agentLastMessagesCopy map[string]interface{}
	if s.AgentLastMessages != nil {
		agentLastMessagesCopy = make(map[string]interface{}, len(s.AgentLastMessages))
		for k, v := range s.AgentLastMessages {
			agentLastMessagesCopy[k] = v
		}
	}
	return &DeepWikiGenerateState{
		CtxForClient:      s.CtxForClient,
		Inputs:            inputsCopy,
		ShortTermMemory:   s.ShortTermMemory,
		ToolCallCount:     s.ToolCallCount,
		WikiRepo:          s.WikiRepo,
		AgentLastMessages: agentLastMessagesCopy,
	}
}
