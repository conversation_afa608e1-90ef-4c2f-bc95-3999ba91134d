package deepwiki

import (
	"context"
	"cosy/deepwiki/storage"
	"cosy/knowledge"
	"cosy/log"
	"cosy/storage/database"
)

func Initialize() {
	//wiki agent 模块初始化
	s, err := storage.NewStorageWikiService(database.GetDB())
	if err != nil {
		log.Errorf("failed to initialize deepwiki generate module: %v", err)
		return
	}
	storage.GlobalStorageService = s
	GlobalWikiService = NewWikiService(s)

	// 初始化knowledge扩展
	ctx := context.Background()
	if err := knowledge.InitializeKnowledgeDatabase(ctx, database.GetDB()); err != nil {
		log.Errorf("failed to initialize knowledge extension: %v", err)
	} else {
		log.Infof("Knowledge extension initialized successfully")
	}

	// 启动任务队列
	err = GlobalWikiService.StartQueue()
	if err != nil {
		log.Errorf("failed to start deepwiki task queue: %v", err)
		return
	}
	log.Infof("DeepWiki task queue started successfully")
}
