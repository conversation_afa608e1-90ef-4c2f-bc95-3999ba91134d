package chains

import (
	"context"
	common2 "cosy/chat/chains/common"
	service2 "cosy/deepwiki/service"
	"cosy/deepwiki/storage"
	"cosy/definition"
	"cosy/log"
	"fmt"

	"github.com/tmc/langchaingo/chains"
	"github.com/tmc/langchaingo/memory"
	"github.com/tmc/langchaingo/schema"
)

type GenerateCatalogueDiffChain struct {
	storageService     *storage.LingmaWikiStorageService
	catalogDiffService *service2.CatalogDiffService
}

func NewGenerateCatalogueDiffChain(storageService *storage.LingmaWikiStorageService) *GenerateCatalogueDiffChain {
	return &GenerateCatalogueDiffChain{
		storageService:     storageService,
		catalogDiffService: service2.NewCommitDiffService(storageService),
	}
}

func NewGenerateCatalogueDiffChainDefault() *GenerateCatalogueDiffChain {
	return &GenerateCatalogueDiffChain{
		storageService:     storage.GlobalStorageService,
		catalogDiffService: service2.NewCommitDiffService(storage.GlobalStorageService),
	}
}

func (g *GenerateCatalogueDiffChain) Call(ctx context.Context, inputs map[string]any, options ...chains.ChainCallOption) (map[string]any, error) {
	log.Debugf("[deepwiki-incremental-update] Starting catalogue diff processing")

	// 获取输入参数
	request, ok := inputs[common2.KeyCreateDeepwikiRequest].(definition.CreateDeepwikiRequest)
	if request.WorkspacePath == "" {
		log.Errorf("[deepwiki-incremental-update] Missing or invalid CreateDeepwikiRequest")
		return nil, fmt.Errorf("missing or invalid CreateDeepwikiRequest")
	}

	analysisResponse, ok := inputs[common2.KeyWikiCommitDiffAnalysis].(*service2.CatalogDiffAnalysisResponse)
	if !ok {
		log.Errorf("[deepwiki-incremental-update] Missing or invalid commit diff analysis response")
		return nil, fmt.Errorf("missing or invalid commit diff analysis response")
	}

	// 设置 RepoInfo，供后续的 UpdateWikiChain 使用
	repoName := service2.GetProjectNameFromPath(request.WorkspacePath)
	repoInfo := definition.RepositoryInfo{
		WorkspacePath: request.WorkspacePath,
		Name:          repoName,
	}
	inputs[common2.KeyRepoInfo] = repoInfo
	log.Debugf("[deepwiki-incremental-update] Set RepoInfo for workspace: %s, repo: %s", request.WorkspacePath, repoName)

	log.Debugf("[deepwiki-incremental-update] Processing commit diff for repository: %s", request.WorkspacePath)
	log.Debugf("[deepwiki-incremental-update] Analysis result: %d items to process, %d items to delete",
		len(analysisResponse.Items), len(analysisResponse.DeleteIDs))

	// 打印分析结果的详细信息
	for i, item := range analysisResponse.Items {
		log.Infof("[deepwiki-incremental-update] Item %d: type=%s, title=%s, id=%s, dependent_files=%v",
			i, item.Type, item.Title, item.ID, item.DependentFile)
	}
	for i, deleteID := range analysisResponse.DeleteIDs {
		log.Infof("[deepwiki-incremental-update] DeleteID %d: %s", i, deleteID)
	}

	// 获取commit diff信息
	commitDiffInfo, _ := inputs[common2.KeyWikiCommitDiff].(*definition.CommitDiffInfo)
	if commitDiffInfo != nil {
		log.Infof("[deepwiki-incremental-update] Found commit diff info with %d commits", commitDiffInfo.TotalCommits)
	} else {
		log.Warnf("[deepwiki-incremental-update] No commit diff info found in inputs")
	}

	// 调用service处理业务逻辑
	log.Infof("[deepwiki-incremental-update] Starting ProcessCatalogueDiff...")
	result, err := g.catalogDiffService.ProcessCatalogueDiff(ctx, request, analysisResponse, commitDiffInfo)
	if err != nil {
		log.Errorf("[deepwiki-incremental-update] Failed to process catalogue diff: %v", err)
		return nil, fmt.Errorf("failed to process catalogue diff: %w", err)
	}
	log.Infof("[deepwiki-incremental-update] ProcessCatalogueDiff completed successfully")

	// 将处理结果分别存储到不同的keys中，供后续不同的chains使用
	inputs[common2.KeyWikiUpdateCatalogues] = result.UpdateCatalogs                                      // 给 update wiki chain 使用
	inputs[common2.KeyWikiAddCatalogues] = result.AddCatalogs                                            // 给 generate wiki chain 使用
	inputs[common2.KeyWikiAllProcessedCatalogues] = append(result.UpdateCatalogs, result.AddCatalogs...) // 兜底，供需要所有处理过的catalogues的chain使用

	log.Infof("[deepwiki-incremental-update] Stored processing results in inputs:")
	log.Infof("[deepwiki-incremental-update] - UpdateCatalogues: %d items", len(result.UpdateCatalogs))
	log.Infof("[deepwiki-incremental-update] - AddCatalogues: %d items", len(result.AddCatalogs))
	log.Infof("[deepwiki-incremental-update] - AllProcessedCatalogues: %d items", len(result.UpdateCatalogs)+len(result.AddCatalogs))

	// 【新增】详细列出传递给后续chains的catalogues
	if len(result.UpdateCatalogs) > 0 {
		log.Infof("[deepwiki-incremental-update] UpdateCatalogues passed to chains:")
		for i, catalog := range result.UpdateCatalogs {
			log.Infof("[deepwiki-incremental-update]   %d. ID: %s, Name: %s, Description: %s", i+1, catalog.Id, catalog.Name, catalog.Description)
		}
	} else {
		log.Warnf("[deepwiki-incremental-update] !! WARNING: No UpdateCatalogues to pass to UpdateWiki chain!")
	}

	if len(result.AddCatalogs) > 0 {
		log.Infof("[deepwiki-incremental-update] AddCatalogues passed to chains:")
		for i, catalog := range result.AddCatalogs {
			log.Infof("[deepwiki-incremental-update]   %d. ID: %s, Name: %s, Description: %s", i+1, catalog.Id, catalog.Name, catalog.Description)
		}
	} else {
		log.Warnf("[deepwiki-incremental-update] !! WARNING: No AddCatalogues to pass to GenerateWiki chain!")
	}

	// 【新增】验证传递给chains的数据
	if len(result.UpdateCatalogs) == 0 && len(result.AddCatalogs) == 0 {
		log.Errorf("[deepwiki-incremental-update] !! CRITICAL: No catalogues to pass to subsequent chains!")
	}

	// 传递相关提交信息的映射，供后续的update wiki chain使用
	if result.RelatedCommitsMapping != nil && len(result.RelatedCommitsMapping) > 0 {
		inputs[common2.KeyRelatedCommitsMapping] = result.RelatedCommitsMapping
		log.Infof("[deepwiki-incremental-update] Passed related commits mapping for %d catalogs", len(result.RelatedCommitsMapping))

		// 打印每个catalog的相关提交详情
		for catalogID, commits := range result.RelatedCommitsMapping {
			log.Infof("[deepwiki-incremental-update] Catalog %s has %d related commits", catalogID, len(commits))
		}
	} else {
		log.Infof("[deepwiki-incremental-update] No related commits mapping to pass")
	}

	log.Infof("[deepwiki-incremental-update] Catalogue diff processing completed for repository: %s", request.WorkspacePath)
	log.Infof("[deepwiki-incremental-update] Final Chain Results: %d catalogs for update, %d catalogs for add, %d total processed",
		len(result.UpdateCatalogs), len(result.AddCatalogs), result.TotalProcessed)

	log.Infof("[deepwiki-incremental-update] Chain execution completed successfully, passing control to next chain")

	return inputs, nil
}

func (g *GenerateCatalogueDiffChain) GetMemory() schema.Memory {
	return memory.NewSimple()
}

func (g *GenerateCatalogueDiffChain) GetInputKeys() []string {
	return []string{}
}

func (g *GenerateCatalogueDiffChain) GetOutputKeys() []string {
	return []string{}
}
