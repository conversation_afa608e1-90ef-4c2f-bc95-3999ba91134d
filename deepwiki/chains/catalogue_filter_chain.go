package chains

import (
	"context"
	"cosy/chat/agents/deepwiki/agent"
	common2 "cosy/chat/chains/common"
	"cosy/deepwiki/service"
	"cosy/deepwiki/storage"
	wikiSupport "cosy/deepwiki/support"
	"cosy/definition"
	"cosy/indexing/chat_indexing/workspace_tree_indexing"
	"cosy/log"
	"encoding/json"
	"errors"
	"fmt"
	"strings"

	"github.com/tmc/langchaingo/memory"

	"github.com/tmc/langchaingo/chains"
	"github.com/tmc/langchaingo/schema"
)

type CatalogueFilterChain struct {
	catalogueService *service.CatalogueFilterService
}

func NewCatalogueFilterChain(catalogueService *service.CatalogueFilterService) *CatalogueFilterChain {
	return &CatalogueFilterChain{
		catalogueService: catalogueService,
	}
}

func (c *CatalogueFilterChain) Call(ctx context.Context, inputs map[string]any, options ...chains.ChainCallOption) (map[string]any, error) {
	// 获取输入
	request, ok := inputs[common2.KeyCreateDeepwikiRequest].(definition.CreateDeepwikiRequest)
	if !ok {
		return nil, fmt.Errorf("missing or invalid CreateDeepwikiRequest")
	}

	workSpacePath := request.WorkspacePath

	// 优先检查数据库中repo的OptimizedCatalog字段
	repo, err := storage.GlobalStorageService.GetWikiRepoByWorkspacePath(workSpacePath)
	if err == nil && repo != nil && repo.OptimizedCatalog != "" {
		// 尝试解析存储的OptimizedCatalog
		var optimizedCatalogue string
		// 先尝试JSON解析，如果失败则直接使用原字符串
		if strings.HasPrefix(repo.OptimizedCatalog, "\"") && strings.HasSuffix(repo.OptimizedCatalog, "\"") {
			// JSON字符串格式，需要反序列化
			if err := json.Unmarshal([]byte(repo.OptimizedCatalog), &optimizedCatalogue); err != nil {
				optimizedCatalogue = repo.OptimizedCatalog
			}
		} else {
			optimizedCatalogue = repo.OptimizedCatalog
		}

		if optimizedCatalogue != "" {
			// 确保RepoInfo也存在
			repoName := service.GetProjectNameFromPath(workSpacePath)
			repoInfo := definition.RepositoryInfo{
				WorkspacePath: workSpacePath,
				Name:          repoName,
			}
			inputs[common2.KeyOptimizedCatalogue] = optimizedCatalogue
			inputs[common2.KeyRepoInfo] = repoInfo

			log.Infof("Deepwiki: Found existing OptimizedCatalog in database, skipping filtering - Workspace: %s, Content length: %d",
				workSpacePath, len(optimizedCatalogue))
			return inputs, nil
		}
	}

	// 检查inputs中是否已有OptimizedCatalogue（恢复场景的二级检查）
	if existingCatalogue, exists := inputs[common2.KeyOptimizedCatalogue]; exists && existingCatalogue != nil {
		catalogueContent := existingCatalogue.(string)
		if catalogueContent != "" {
			// 确保RepoInfo也存在
			repoName := service.GetProjectNameFromPath(workSpacePath)
			repoInfo := definition.RepositoryInfo{
				WorkspacePath: workSpacePath,
				Name:          repoName,
			}
			inputs[common2.KeyRepoInfo] = repoInfo

			log.Infof("Deepwiki: OptimizedCatalogue already exists in inputs (recovery mode), skipping filtering - Workspace: %s", workSpacePath)
			return inputs, nil //直接跳过生成
		}
	}

	referenceCatalogItemsString := ""
	workspaceTreeIndexer, ok := workspace_tree_indexing.WorkspaceTreeFileIndexers.GetFileIndexer(workSpacePath)
	if ok {
		referenceCatalogItemsString = workspaceTreeIndexer.WorkspaceTree.GetTree(agent.WorkspaceTreeTokenLimit)
	}

	if referenceCatalogItemsString == "" {
		return nil, errors.New("workspace index tree is nil")
	}
	if referenceCatalogItemsString == "." {
		referenceCatalogItemsString = ""
	}

	repoName := service.GetProjectNameFromPath(workSpacePath)

	repoInfo := definition.RepositoryInfo{
		WorkspacePath: workSpacePath,
		Name:          repoName,
	}

	// 直接使用索引后的目录树作为优化的目录结构
	optimizedCatalogue := referenceCatalogItemsString
	log.Infof("Using indexed workspace tree as optimized catalogue, content length: %d", len(optimizedCatalogue))

	// 保存优化的catalog到repo的OptimizedCatalog字段
	err = c.saveOptimizedCatalogueToRepo(workSpacePath, optimizedCatalogue)
	if err != nil {
		log.Warnf("Failed to save optimized catalogue to repo: %v", err)
		// 不阻断流程，只记录警告
	} else {
		// 目录过滤完成并保存成功后更新检查点
		wikiSupport.UpdateCheckpointOnSuccess(workSpacePath, wikiSupport.CheckpointCatalogueFilterCompleted)
	}

	// 输出结果
	inputs[common2.KeyOptimizedCatalogue] = optimizedCatalogue
	inputs[common2.KeyRepoInfo] = repoInfo

	// 简单统计文件数量（按行计算，过滤空行和目录）
	fileCount := c.getFileCountFromCatalogue(optimizedCatalogue)
	log.Infof("Catalogue filtering completed, optimized catalogue contains %d files", fileCount)
	return inputs, nil
}

// getFileCountFromCatalogue 从目录字符串中统计文件数量
func (c *CatalogueFilterChain) getFileCountFromCatalogue(catalogue string) int {
	if catalogue == "" {
		return 0
	}

	lines := strings.Split(catalogue, "\n")
	fileCount := 0
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line != "" && !strings.HasSuffix(line, "/") {
			fileCount++
		}
	}
	return fileCount
}

// saveOptimizedCatalogueToRepo 将优化的catalog保存到repo的OptimizedCatalog字段
func (c *CatalogueFilterChain) saveOptimizedCatalogueToRepo(workspacePath, optimizedCatalogue string) error {
	// 获取repo
	repo, err := storage.GlobalStorageService.GetWikiRepoByWorkspacePath(workspacePath)
	if err != nil {
		return fmt.Errorf("failed to get repo: %w", err)
	}

	if repo == nil {
		log.Infof("Repo not found for workspace: %s, skipping optimized catalogue save", workspacePath)
		return nil
	}

	// 优先尝试从catalogueResult中获取结构化的DocumentCatalogs
	var toStore string
	if optimizedCatalogue != "" {
		jsonBytes, err := json.Marshal(optimizedCatalogue)
		if err == nil {
			toStore = string(jsonBytes)
		} else {
			log.Warnf("Failed to marshal optimizedCatalogue to JSON: %v", err)
		}
	}
	if toStore == "" {
		// fallback: 存储优化后的目录字符串
		toStore = optimizedCatalogue
	}

	repo.OptimizedCatalog = toStore

	// 保存更新
	err = storage.GlobalStorageService.UpdateWikiRepo(repo)
	if err != nil {
		return fmt.Errorf("failed to update repo with optimized catalogue: %w", err)
	}

	log.Infof("Successfully saved optimized catalogue to repo: %s", repo.ID)
	return nil
}

func (c *CatalogueFilterChain) GetMemory() schema.Memory {
	return memory.NewSimple()
}

func (c *CatalogueFilterChain) GetInputKeys() []string {
	return []string{}
}

func (c *CatalogueFilterChain) GetOutputKeys() []string {
	return []string{}
}
