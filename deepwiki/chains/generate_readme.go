package chains

import (
	"context"
	"cosy/chat/agents/deepwiki/agent"
	"cosy/chat/agents/support"
	chainsCommon "cosy/chat/chains/common"
	"cosy/deepwiki/common"
	"cosy/deepwiki/storage"
	wikiSupport "cosy/deepwiki/support"
	"cosy/definition"
	"cosy/log"
	"cosy/util"
	"fmt"
	"os"
	"runtime/debug"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/tmc/langchaingo/memory"

	"github.com/tmc/langchaingo/chains"
	"github.com/tmc/langchaingo/schema"
)

// 定义开始和结束标记
const (
	startMarker = "<readme>"
	endMarker   = "</readme>"
)

type GenerateReadmeChain struct {
}

func (g GenerateReadmeChain) Call(ctx context.Context, inputs map[string]any, options ...chains.ChainCallOption) (map[string]any, error) {
	request, ok := inputs[chainsCommon.KeyCreateDeepwikiRequest].(definition.CreateDeepwikiRequest)
	if !ok {
		log.Infof("Deepwiki: Readme failed - Error: missing CreateDeepwikiRequest in inputs")
		return nil, fmt.Errorf("missing CreateDeepwikiRequest in inputs")
	}

	requestId, _ := inputs[chainsCommon.KeyRequestId].(string)
	repoName := util.GetProjectName(request.WorkspacePath)

	// 检查inputs中是否已有README内容（恢复场景）
	if existingReadme, exists := inputs[chainsCommon.KeyReadmeContent]; exists && existingReadme != nil {
		readmeContent := existingReadme.(string)
		if readmeContent != "" {
			log.Infof("Deepwiki: README already exists in inputs (recovery mode), skipping generation - Repo: %s, Workspace: %s",
				repoName, request.WorkspacePath)
			return inputs, nil // 直接跳过生成
		}
	}
	found := g.fetchExistReadmeFile(ctx, inputs, &request)

	if !found {
		err := g.generateReadme(ctx, inputs, &request, repoName)
		if err != nil {
			return inputs, nil
		}
	}

	// 保存README内容到数据库
	if readmeContent, exists := inputs[chainsCommon.KeyReadmeContent]; exists && readmeContent != nil {
		parsedContent, err := g.extractReadmeContent(readmeContent.(string))
		if err == nil {
			readmeContent = parsedContent
		}

		workspace, _ := ctx.Value(definition.ContextKeyWorkspace).(definition.WorkspaceInfo)
		log.Debugf("deepwiki-generate-readme: requestId: %s, workspacePath: %s", requestId, workspace.GetWorkspaceFolders()[0])

		err = g.saveReadmeToDatabase(ctx, inputs, readmeContent.(string))
		if err != nil {
			log.Errorf("Failed to save README to database: %v", err)
			// 不阻断流程继续，只记录错误
		} else {
			// README生成并保存成功后更新检查点
			wikiSupport.UpdateCheckpointOnSuccess(request.WorkspacePath, wikiSupport.CheckpointReadmeCompleted)
		}
	}

	log.Infof("Deepwiki: Readme end - Repo: %s, Workspace: %s", repoName, request.WorkspacePath)
	return inputs, nil
}

// generateReadme 生成README
func (g GenerateReadmeChain) generateReadme(ctx context.Context, inputs map[string]any, request *definition.CreateDeepwikiRequest, repoName string) error {
	log.Infof("Deepwiki: Readme start - Repo: %s, Workspace: %s", repoName, request.WorkspacePath)

	ctx, _, err := agent.InitAgentContext(ctx)
	if err != nil {
		log.Infof("Deepwiki: Readme failed - Repo: %s, Workspace: %s, Error: %v", repoName, request.WorkspacePath, err)
		return err
	}
	requestId, _ := inputs[chainsCommon.KeyRequestId].(string)
	readmeGenerateAgent, err := support.MakeAgent(requestId, common.ReadmeGenerateAgentBuilderIdentifier)
	if err != nil {
		log.Infof("Deepwiki: Readme failed - Repo: %s, Workspace: %s, Error: failed to create agent", repoName, request.WorkspacePath)
		panic(err)
	}

	wikiSupport.InitCurrentGraphStat(inputs, "ReadmeGenerateGraph")

	var runErr error
	func() {
		defer func() {
			if r := recover(); r != nil {
				stack := debug.Stack()
				errMsg := fmt.Sprintf("Fatal error in agent.RunSync: %v\n%s", r, stack)
				log.Errorf("%s", errMsg)
				runErr = fmt.Errorf("fatal error: %v", r)
			}
		}()
		runErr = readmeGenerateAgent.RunSync(ctx, inputs)
	}()
	if runErr != nil {
		log.Errorf("Deepwiki: Readme failed - Repo: %s, Workspace: %s, Error: %v", repoName, request.WorkspacePath, runErr)
		return runErr // 不阻断流程，只记录错误
	}

	wikiSupport.AppendGraphStatToWikiStat(inputs)

	// 从agent context中获取最终状态并同步结果 - 使用线程安全方法
	if agentContext, ok := ctx.Value(chainsCommon.KeyDeepwikiAgentContext).(*common.AgentContext); ok {
		if agentState, ok := agentContext.State.(*common.DeepWikiGenerateState); ok {
			// 使用线程安全的方法复制inputs
			tempInputs := agentState.CopyInputs()

			// 将副本合并到inputs中
			for key, value := range tempInputs {
				inputs[key] = value
			}

			log.Debugf("[deepwiki-generate-readme] Synced %d keys from agent state to inputs", len(tempInputs))
		}
	}
	return nil
}

// 读当前工程文件中已有的readme文件内容
func (g GenerateReadmeChain) fetchExistReadmeFile(ctx context.Context, inputs map[string]any, request *definition.CreateDeepwikiRequest) bool {
	workspacePath := request.WorkspacePath
	// 支持的README文件名列表
	readmeFilenames := []string{"README.md", "Readme.md", "readme.md", "README", "Readme", "readme"}

	var readmeContent string
	for _, filename := range readmeFilenames {
		filePath := fmt.Sprintf("%s/%s", workspacePath, filename)
		content, err := os.ReadFile(filePath)
		if err == nil && strings.TrimSpace(string(content)) != "" {
			readmeContent = string(content)
			log.Debugf("Found existing README file: %s", filePath)
			break
		}
	}

	if readmeContent != "" {
		inputs[chainsCommon.KeyReadmeContent] = readmeContent
		log.Debugf("Successfully loaded existing README content from: %s", workspacePath)
		return true
	} else {
		log.Debugf("No valid README file found in: %s", workspacePath)
		return false
	}
}

// saveReadmeToDatabase 保存README内容到数据库
func (g GenerateReadmeChain) saveReadmeToDatabase(ctx context.Context, inputs map[string]any, content string) error {
	// 获取必要的信息
	request, ok := inputs[chainsCommon.KeyCreateDeepwikiRequest].(definition.CreateDeepwikiRequest)
	if !ok {
		return fmt.Errorf("missing CreateDeepwikiRequest in inputs")
	}

	// 获取项目名称
	repoName := getProjectNameFromPath(request.WorkspacePath)

	// 查找或创建repo记录
	repo, err := storage.GlobalStorageService.GetWikiRepoByWorkspacePath(request.WorkspacePath)
	if err != nil {
		return fmt.Errorf("failed to get wiki repo: %w", err)
	}

	var repoID string
	if repo == nil {
		// 如果还没有repo记录，先创建
		repoID = uuid.NewString()
		newRepo := &definition.AgentWikiRepo{
			ID:               repoID,
			WorkspacePath:    request.WorkspacePath,
			Name:             repoName,
			ProgressStatus:   definition.DeepWikiProgressStatusProcessing,
			LastCommitID:     "",
			LastCommitUpdate: time.Now(),
			GmtCreate:        time.Now(),
			GmtModified:      time.Now(),
		}
		err = storage.GlobalStorageService.CreateWikiRepo(newRepo)
		if err != nil {
			return fmt.Errorf("failed to create wiki repo: %w", err)
		}
	} else {
		repoID = repo.ID
	}

	// 创建README记录
	readme := &definition.AgentWikiReadme{
		ID:            uuid.NewString(),
		RepoID:        repoID,
		Content:       content,
		WorkspacePath: request.WorkspacePath,
		GmtCreate:     time.Now(),
		GmtModified:   time.Now(),
	}

	err = storage.GlobalStorageService.CreateReadme(readme)
	if err != nil {
		return fmt.Errorf("failed to create README: %w", err)
	}

	log.Infof("Successfully saved README to database for repository: %s (ID: %s)", repoName, readme.ID)
	return nil
}

// getProjectNameFromPath 从路径中获取项目名称
func getProjectNameFromPath(projectPath string) string {
	// 获取路径中的最后一个元素作为项目名称
	parts := strings.Split(strings.TrimSuffix(projectPath, "/"), "/")
	if len(parts) > 0 {
		return parts[len(parts)-1]
	}
	return "unknown"
}

// 提取README内容
// 格式参考：
// <readme>
// # Script Engine
// </readme>
func (g GenerateReadmeChain) extractReadmeContent(content string) (string, error) {

	// 查找标记的位置
	startIndex := strings.Index(content, startMarker)
	endIndex := strings.LastIndex(content, endMarker)

	if startIndex == -1 || endIndex == -1 {
		return "", fmt.Errorf("missing readme markers in content")
	}

	// 调整开始索引到标记之后的位置
	startIndex += len(startMarker)

	// 提取标记之间的内容
	readmeContent := strings.TrimSpace(content[startIndex:endIndex])

	return readmeContent, nil
}

func (g GenerateReadmeChain) GetMemory() schema.Memory {
	return memory.NewSimple()
}

func (g GenerateReadmeChain) GetInputKeys() []string {
	return []string{}
}

func (g GenerateReadmeChain) GetOutputKeys() []string {
	return []string{}
}
