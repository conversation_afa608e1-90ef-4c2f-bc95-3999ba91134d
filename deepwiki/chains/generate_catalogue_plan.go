package chains

import (
	"context"
	"cosy/chat/agents/deepwiki/agent"
	"cosy/chat/agents/support"
	chainsCommon "cosy/chat/chains/common"
	wikiCommon "cosy/deepwiki/common"
	"cosy/deepwiki/service"
	"cosy/deepwiki/storage"
	wikiSupport "cosy/deepwiki/support"
	"cosy/definition"
	"cosy/log"
	"cosy/util"
	"fmt"
	"runtime/debug"
	"time"

	"github.com/tmc/langchaingo/memory"

	"github.com/google/uuid"

	"github.com/tmc/langchaingo/chains"
	"github.com/tmc/langchaingo/schema"
)

type GenerateCataloguePlanChain struct {
	catalogueService *service.GenerateCatalogueService
}

func NewGenerateCataloguePlanChain(catalogueService *service.GenerateCatalogueService) *GenerateCataloguePlanChain {
	return &GenerateCataloguePlanChain{
		catalogueService: catalogueService,
	}
}

func (g *GenerateCataloguePlanChain) Call(ctx context.Context, inputs map[string]any, options ...chains.ChainCallOption) (map[string]any, error) {
	request, ok := inputs[chainsCommon.KeyCreateDeepwikiRequest].(definition.CreateDeepwikiRequest)
	if !ok {
		log.Infof("Deepwiki: CataloguePlan failed - Error: missing CreateDeepwikiRequest in inputs")
		return nil, fmt.Errorf("missing CreateDeepwikiRequest in inputs")
	}

	repoName := util.GetProjectName(request.WorkspacePath)

	// 优先检查数据库中repo的CatalogueThinkContent字段
	repo, err := storage.GlobalStorageService.GetWikiRepoByWorkspacePath(request.WorkspacePath)
	if err == nil && repo != nil && repo.CatalogueThinkContent != "" {
		log.Infof("Deepwiki: Found existing CatalogueThinkContent in database, skipping generation - Repo: %s, Workspace: %s, Content length: %d",
			repoName, request.WorkspacePath, len(repo.CatalogueThinkContent))

		// 将数据库中的内容加入到inputs中
		inputs[chainsCommon.KeyCatalogueThink] = repo.CatalogueThinkContent

		log.Infof("Deepwiki: CataloguePlan skipped - Repo: %s, Workspace: %s", repoName, request.WorkspacePath)
		return inputs, nil
	}

	// 检查inputs中是否已有CatalogueThink内容（恢复场景的二级检查）
	if existingThink, exists := inputs[chainsCommon.KeyCatalogueThink]; exists && existingThink != nil {
		thinkContent := existingThink.(string)
		if thinkContent != "" {
			log.Debugf("Deepwiki: CatalogueThink already exists in inputs (recovery mode), skipping generation - Repo: %s, Workspace: %s",
				repoName, request.WorkspacePath)
			return inputs, nil // 直接跳过生成
		}
	}

	log.Infof("Deepwiki: CataloguePlan start - Repo: %s, Workspace: %s", repoName, request.WorkspacePath)

	// 带重试的agent执行
	var lastErr error
	for attempt := 1; attempt <= wikiCommon.MaxRetryAttempts; attempt++ {
		agentCtx, _, err := agent.InitAgentContext(ctx)
		if err != nil {
			log.Infof("Deepwiki: CataloguePlan failed - Repo: %s, Workspace: %s, Error: %v", repoName, request.WorkspacePath, err)
			return nil, err
		}
		requestId := uuid.NewString()
		cataloguePlanAgent, err := support.MakeAgent(requestId, wikiCommon.CatalogueThinkAgentBuilderIdentifier)
		if err != nil {
			log.Infof("Deepwiki: CataloguePlan failed - Repo: %s, Workspace: %s, Error: failed to create agent", repoName, request.WorkspacePath)
			panic(err)
		}

		wikiSupport.InitCurrentGraphStat(inputs, "GenerateCataloguePlanGraph")

		var runErr error
		func() {
			defer func() {
				if r := recover(); r != nil {
					stack := debug.Stack()
					errMsg := fmt.Sprintf("Fatal error in agent.RunSync (attempt %d/%d): %v\n%s", attempt, wikiCommon.MaxRetryAttempts, r, stack)
					log.Errorf("[deepwiki-catalogue-plan] %s", errMsg)
					runErr = fmt.Errorf("fatal error: %v", r)
				}
			}()
			runErr = cataloguePlanAgent.RunSync(agentCtx, inputs)
		}()

		if runErr == nil {
			if attempt > 1 {
				log.Debugf("[deepwiki-catalogue-plan] Agent succeeded on attempt %d/%d", attempt, wikiCommon.MaxRetryAttempts)
			}

			// 从agent context中获取最终状态并同步结果 - 使用线程安全方法
			if agentContext, ok := agentCtx.Value(chainsCommon.KeyDeepwikiAgentContext).(*wikiCommon.AgentContext); ok {
				if agentState, ok := agentContext.State.(*wikiCommon.DeepWikiGenerateState); ok {
					// 使用线程安全的方法复制inputs
					tempInputs := agentState.CopyInputs()

					// 将副本合并到inputs中
					for key, value := range tempInputs {
						inputs[key] = value
					}

					log.Debugf("[deepwiki-catalogue-plan] Synced %d keys from agent state to inputs", len(tempInputs))
				} else {
					log.Errorf("[deepwiki-catalogue-plan] Failed to get agentState from agentContext")
				}
			} else {
				log.Errorf("[deepwiki-catalogue-plan] Failed to get agentContext from agentCtx")
			}

			break
		}

		wikiSupport.AppendGraphStatToWikiStat(inputs)

		lastErr = runErr
		log.Errorf("[deepwiki-catalogue-plan] Agent failed on attempt %d/%d: %v", attempt, wikiCommon.MaxRetryAttempts, runErr)

		// 如果不是最后一次尝试，等待后重试
		if attempt < wikiCommon.MaxRetryAttempts {
			log.Infof("[deepwiki-catalogue-plan] Retrying in %v... (attempt %d/%d)", wikiCommon.RetryDelay, attempt+1, wikiCommon.MaxRetryAttempts)

			// 检查context是否已被取消
			select {
			//case <-agentCtx.Done():
			//	return nil, fmt.Errorf("[deepwiki-catalogue-plan] context cancelled during retry wait: %w", ctx.Err())
			case <-time.After(wikiCommon.RetryDelay):
				// 继续重试
			}
		}
	}

	if lastErr != nil {
		log.Errorf("[deepwiki-catalogue-plan] Agent failed after %d attempts, last error: %v", wikiCommon.MaxRetryAttempts, lastErr)
		log.Infof("Deepwiki: CataloguePlan failed - Repo: %s, Workspace: %s, Error: %v", repoName, request.WorkspacePath, lastErr)
		return nil, fmt.Errorf("catalogue plan agent run sync error: %w", lastErr)
	}

	if _, ok := inputs[chainsCommon.KeyCatalogueThink]; !ok {
		log.Errorf("catalogue plan think generate failed, raw output not found after %d attempts", wikiCommon.MaxRetryAttempts)
		log.Infof("Deepwiki: CataloguePlan failed - Repo: %s, Workspace: %s, Error: no think result found", repoName, request.WorkspacePath)
		return nil, fmt.Errorf("catalogue plan think generate failed: raw output not found after all retry attempts")
	}

	// 添加调试信息
	rawThinkOutput := inputs[chainsCommon.KeyCatalogueThink].(string)
	log.Infof("catalogue plan think generate completed, content length: %d", len(rawThinkOutput))

	// 保存完整的AI响应到文件
	if err := wikiSupport.SaveWikiGenerateResponseToFile(rawThinkOutput, "catalogue_plan"); err != nil {
		log.Warnf("Failed to save catalogue structure AI response to file: %v", err)
	}

	think, err := g.catalogueService.ExtractThinkContent(rawThinkOutput)
	if err != nil {
		log.Warnf("failed to extract think content, using full response: %v", err)
		think = rawThinkOutput
	}
	inputs[chainsCommon.KeyCatalogueThink] = think

	// 保存思考内容到数据库
	err = g.saveCatalogueThinkToDatabase(request.WorkspacePath, think)
	if err != nil {
		log.Errorf("Failed to save catalogue think content to database: %v", err)
		// 不阻断流程，只记录错误
	} else {
		// 目录规划完成并保存成功后更新检查点
		wikiSupport.UpdateCheckpointOnSuccess(request.WorkspacePath, wikiSupport.CheckpointCataloguePlanCompleted)
	}

	log.Infof("Deepwiki: CataloguePlan end - Repo: %s, Workspace: %s", repoName, request.WorkspacePath)
	return inputs, nil
}

func (g GenerateCataloguePlanChain) GetMemory() schema.Memory {
	return memory.NewSimple()
}

func (g GenerateCataloguePlanChain) GetInputKeys() []string {
	return []string{}
}

func (g GenerateCataloguePlanChain) GetOutputKeys() []string {
	return []string{}
}

// saveCatalogueThinkToDatabase 保存目录思考内容到数据库
func (g *GenerateCataloguePlanChain) saveCatalogueThinkToDatabase(workspacePath, thinkContent string) error {
	// 获取repo信息
	repo, err := storage.GlobalStorageService.GetWikiRepoByWorkspacePath(workspacePath)
	if err != nil {
		return fmt.Errorf("failed to get wiki repo: %w", err)
	}

	if repo == nil {
		return fmt.Errorf("wiki repo not found for workspace path: %s", workspacePath)
	}

	// 保存思考内容到数据库
	err = storage.GlobalStorageService.UpdateCatalogueThinkContent(repo.ID, thinkContent)
	if err != nil {
		return fmt.Errorf("failed to update catalogue think content: %w", err)
	}

	log.Debugf("Successfully saved catalogue think content to database for repo: %s (length: %d)", repo.ID, len(thinkContent))
	return nil
}
