package chains

import (
	"context"
	"cosy/chat/agents/deepwiki/agent"
	"cosy/chat/agents/support"
	common2 "cosy/chat/chains/common"
	common3 "cosy/deepwiki/common"
	service2 "cosy/deepwiki/service"
	"cosy/deepwiki/storage"
	wikiSupport "cosy/deepwiki/support"
	"cosy/definition"
	"cosy/log"
	"cosy/util"
	"encoding/json"
	"fmt"
	"runtime/debug"
	"strings"
	"time"

	"github.com/google/uuid"

	"github.com/tmc/langchaingo/memory"

	"github.com/tmc/langchaingo/chains"
	"github.com/tmc/langchaingo/schema"
)

// 新增常量
const (
	catalogueMaxRetryAttempts = 5
	catalogueRetryDelay       = 5 * time.Second
	maxCatalogueDepth         = 5 // 最大目录深度
)

type GenerateCatalogueChain struct {
	catalogueService *service2.GenerateCatalogueService
}

func NewGenerateCatalogueChain(catalogueService *service2.GenerateCatalogueService) *GenerateCatalogueChain {
	return &GenerateCatalogueChain{
		catalogueService: catalogueService,
	}
}

func (g *GenerateCatalogueChain) Call(ctx context.Context, inputs map[string]any, options ...chains.ChainCallOption) (map[string]any, error) {
	// 预检查阶段
	if shouldSkip, result, err := g.preCheck(inputs); shouldSkip {
		return result, err
	}

	request := inputs[common2.KeyCreateDeepwikiRequest].(definition.CreateDeepwikiRequest)
	repoName := util.GetProjectName(request.WorkspacePath)

	log.Infof("Deepwiki: CatalogueGenerate start - Repo: %s, Workspace: %s", repoName, request.WorkspacePath)

	// 生成目录结构
	documentCatalogs, err := g.generateCatalogueWithQueue(ctx, inputs)
	if err != nil {
		return nil, err
	}

	// 构建最终结果
	return g.buildFinalResult(ctx, inputs, documentCatalogs)
}

// preCheck 预检查阶段，检查是否需要跳过生成
func (g *GenerateCatalogueChain) preCheck(inputs map[string]any) (shouldSkip bool, result map[string]any, err error) {
	request, ok := inputs[common2.KeyCreateDeepwikiRequest].(definition.CreateDeepwikiRequest)
	if !ok {
		log.Infof("Deepwiki: CatalogueGenerate failed - Error: missing CreateDeepwikiRequest in inputs")
		return true, nil, fmt.Errorf("missing CreateDeepwikiRequest in inputs")
	}

	repoName := util.GetProjectName(request.WorkspacePath)

	// 优先检查数据库中是否已存在catalogs
	repo, err := storage.GlobalStorageService.GetWikiRepoByWorkspacePath(request.WorkspacePath)
	if err == nil && repo != nil {
		existingCatalogs, err := storage.GlobalStorageService.GetCatalogsByRepoID(repo.ID)
		if err == nil && len(existingCatalogs) > 0 {
			log.Infof("Deepwiki: Found %d existing catalogs in database, reconstructing CatalogueResult and skipping generation - Repo: %s, Workspace: %s",
				len(existingCatalogs), repoName, request.WorkspacePath)

			// 从数据库中的catalogs重构CatalogueResult
			catalogueResult := g.reconstructCatalogueResultFromDatabase(existingCatalogs, repo)
			inputs[common2.KeyCatalogueResult] = catalogueResult

			log.Infof("Deepwiki: CatalogueGenerate skipped - Repo: %s, Workspace: %s, Reconstructed: %d catalogs",
				repoName, request.WorkspacePath, len(existingCatalogs))
			return true, inputs, nil
		}
	}

	// 检查inputs中是否已有CatalogueResult（恢复场景的二级检查）
	if existingResult, exists := inputs[common2.KeyCatalogueResult]; exists && existingResult != nil {
		if catalogueResult, ok := existingResult.(definition.CatalogueResult); ok && catalogueResult.TotalSections > 0 {
			log.Infof("Deepwiki: CatalogueResult already exists in inputs (recovery mode), skipping generation - Repo: %s, Workspace: %s",
				repoName, request.WorkspacePath)
			return true, inputs, nil // 直接跳过生成
		}
	}

	return false, nil, nil
}

// generateCatalogueWithQueue 使用队列方式生成目录结构
func (g *GenerateCatalogueChain) generateCatalogueWithQueue(ctx context.Context, inputs map[string]any) ([]definition.DocumentCatalog, error) {
	var allDocumentSection []*definition.DocumentationSection
	nodeQueue := make([]*definition.DocumentationSection, 0)
	totalCount := 0

	// 首先生成根节点
	rootStructure, err := g.generateRootNodes(ctx, inputs)
	if err != nil {
		return nil, fmt.Errorf("failed to generate root nodes: %w", err)
	}
	repoInfo, ok := inputs[common2.KeyRepoInfo].(definition.RepositoryInfo)
	if !ok {
		return nil, fmt.Errorf("invalid repo info")
	}

	warehouseId := repoInfo.Name
	documentId := fmt.Sprintf("doc_%s", repoInfo.Name)

	// 将根节点加入队列
	for _, rootNode := range rootStructure.Items {
		if rootNode.IsHasChildren() {
			nodeQueue = append(nodeQueue, rootNode)
		}
		allDocumentSection = append(allDocumentSection, rootNode)
		totalCount++
	}
	log.Debugf("[deepwiki-catalogue-generate] generate root sections: %d", len(allDocumentSection))

	// 处理队列中的节点
	for len(nodeQueue) > 0 {
		// 出队一个节点
		currentNode := nodeQueue[0]
		nodeQueue = nodeQueue[1:]

		// 为当前节点生成子节点
		log.Debugf("[deepwiki-catalogue-generate] generate child section for %s, queue: %d", currentNode.Title, len(nodeQueue))
		childStructure, err := g.generateChildNodes(ctx, inputs, currentNode)
		if err != nil {
			log.Errorf("Failed to generate child nodes for %s: %v", currentNode.Name, err)
			continue // 继续处理其他节点
		}

		// 将子节点加入结果和队列
		for _, childNode := range childStructure.Items {
			if childNode.IsHasChildren() {
				// 将子节点加入队列以生成其子节点
				nodeQueue = append(nodeQueue, childNode)
			}
			currentNode.Children = append(currentNode.Children, childNode)
			totalCount++
		}
		log.Debugf("[deepwiki-catalogue-generate] Generated %d child nodes for %s", len(childStructure.Items), currentNode.Title)
	}
	sectionJson, err := json.MarshalIndent(allDocumentSection, "", "  ")
	if err == nil {
		if err := wikiSupport.SaveWikiGenerateResponseToFile(string(sectionJson), "catalogue_structure_merge"); err != nil {
			log.Debugf("Failed to save catalogue structure AI response to file: %v", err)
		}
	}
	documentCatalogs := g.catalogueService.GenerateDocumentCatalogs(allDocumentSection, warehouseId, documentId)

	log.Infof("Catalogue generation completed, total nodes: %d", totalCount)
	return documentCatalogs, nil
}

// generateRootNodes 生成根节点列表
func (g *GenerateCatalogueChain) generateRootNodes(ctx context.Context, inputs map[string]any) (*definition.DocumentationStructure, error) {
	// 设置特殊输入标记，告诉agent生成根节点
	tempInputs := make(map[string]any)
	for k, v := range inputs {
		tempInputs[k] = v
	}

	err := g.runAgentWithRetry(ctx, tempInputs)
	if err != nil {
		return nil, fmt.Errorf("failed to generate root nodes: %w", err)
	}

	return g.parseAgentOutput(tempInputs)
}

// generateChildNodes 为指定父节点生成子节点
func (g *GenerateCatalogueChain) generateChildNodes(ctx context.Context, inputs map[string]any, parentNode *definition.DocumentationSection) (*definition.DocumentationStructure, error) {
	// 设置特殊输入标记，告诉agent生成指定父节点的子节点
	tempInputs := make(map[string]any)
	for k, v := range inputs {
		tempInputs[k] = v
	}
	tempInputs[common2.KeyWikiParentCatalogNode] = parentNode

	err := g.runAgentWithRetry(ctx, tempInputs)
	if err != nil {
		return nil, fmt.Errorf("failed to generate child nodes for parent %s: %w", parentNode.Name, err)
	}

	return g.parseAgentOutput(tempInputs)
}

// runAgentWithRetry 带重试机制运行agent
func (g *GenerateCatalogueChain) runAgentWithRetry(ctx context.Context, inputs map[string]any) error {
	request := inputs[common2.KeyCreateDeepwikiRequest].(definition.CreateDeepwikiRequest)
	repoName := util.GetProjectName(request.WorkspacePath)

	var lastErr error
	for attempt := 1; attempt <= catalogueMaxRetryAttempts; attempt++ {
		log.Debugf("[deepwiki-catalogue-generate] Starting agent execution attempt %d/%d - Repo: %s", attempt, catalogueMaxRetryAttempts, repoName)

		agentCtx, _, err := agent.InitAgentContext(ctx)
		if err != nil {
			log.Debugf("[deepwiki-catalogue-generate] Failed to init agent context (attempt %d): %v", attempt, err)
			lastErr = err
			continue
		}

		requestId := uuid.NewString()
		catalogueGenerateAgent, err := support.MakeAgent(requestId, common3.CatalogueGenerateAgentBuilderIdentifier)
		if err != nil {
			log.Debugf("[deepwiki-catalogue-generate] Failed to create agent (attempt %d): %v", attempt, err)
			lastErr = err
			continue
		}

		wikiSupport.InitCurrentGraphStat(inputs, "GenerateCatalogue")

		var runErr error
		func() {
			defer func() {
				if r := recover(); r != nil {
					stack := debug.Stack()
					errMsg := fmt.Sprintf("Fatal error in agent.RunSync (attempt %d/%d): %v\n%s", attempt, catalogueMaxRetryAttempts, r, stack)
					log.Debugf("[deepwiki-catalogue-generate] %s", errMsg)
					runErr = fmt.Errorf("fatal error: %v", r)
				}
			}()
			runErr = catalogueGenerateAgent.RunSync(agentCtx, inputs)
		}()

		if runErr == nil {
			// Agent执行成功，进行状态同步
			if agentContext, ok := agentCtx.Value(common2.KeyDeepwikiAgentContext).(*common3.AgentContext); ok {
				if agentState, ok := agentContext.State.(*common3.DeepWikiGenerateState); ok {
					// 使用线程安全的方法复制inputs
					tempInputs := agentState.CopyInputs()

					// 将副本合并到inputs中
					for key, value := range tempInputs {
						inputs[key] = value
					}

					log.Debugf("[deepwiki-catalogue-generate] Synced %d keys from agent state to inputs", len(tempInputs))
				}
			}

			// 验证是否生成了有效的catalogue raw output
			var hasValidContent bool
			var contentLength int
			const minCatalogueContentLength = 300 // 目录结构最少需要100字符

			if rawOutput, exists := inputs[common2.KeyCatalogueRawOutput]; exists {
				if outputStr, ok := rawOutput.(string); ok {
					trimmedContent := strings.TrimSpace(outputStr)
					contentLength = len(trimmedContent)

					if trimmedContent != "" && contentLength >= minCatalogueContentLength {
						hasValidContent = true
						log.Debugf("[deepwiki-catalogue-generate] Agent generated valid catalogue content, length: %d", contentLength)
					} else {
						log.Debugf("[deepwiki-catalogue-generate] Agent generated insufficient catalogue content, length: %d (min required: %d)", contentLength, minCatalogueContentLength)
					}
				}
			}

			if hasValidContent {
				if attempt > 1 {
					log.Debugf("[deepwiki-catalogue-generate] Agent succeeded on attempt %d/%d, content length: %d", attempt, catalogueMaxRetryAttempts, contentLength)
				}
				// 成功时记录统计信息
				wikiSupport.AppendGraphStatToWikiStat(inputs)
				lastErr = nil
				break
			} else {
				// Agent执行成功但没有生成有效内容，视为失败需要重试
				if contentLength == 0 {
					runErr = fmt.Errorf("agent executed successfully but generated empty catalogue content")
					log.Debugf("[deepwiki-catalogue-generate] Agent generated empty catalogue content (attempt %d/%d)", attempt, catalogueMaxRetryAttempts)
				} else {
					runErr = fmt.Errorf("agent executed successfully but generated insufficient catalogue content (length: %d, min required: %d)", contentLength, minCatalogueContentLength)
					log.Debugf("[deepwiki-catalogue-generate] Agent generated insufficient catalogue content, length: %d (min required: %d) (attempt %d/%d)", contentLength, minCatalogueContentLength, attempt, catalogueMaxRetryAttempts)
				}
			}
		}

		// 失败时记录统计信息
		wikiSupport.AppendGraphStatToWikiStat(inputs)

		lastErr = runErr
		log.Debugf("[deepwiki-catalogue-generate] Agent failed on attempt %d/%d: %v", attempt, catalogueMaxRetryAttempts, runErr)

		// 如果不是最后一次尝试，等待后重试
		if attempt < catalogueMaxRetryAttempts {
			log.Infof("[deepwiki-catalogue-generate] Retrying in %v... (attempt %d/%d)", catalogueRetryDelay, attempt+1, catalogueMaxRetryAttempts)

			// 检查context是否已被取消
			select {
			case <-ctx.Done():
				return fmt.Errorf("[deepwiki-catalogue-generate] context cancelled during retry wait: %w", ctx.Err())
			case <-time.After(catalogueRetryDelay):
				// 继续重试
			}
		}
	}

	if lastErr != nil {
		log.Debugf("[deepwiki-catalogue-generate] Agent failed after %d attempts, last error: %v", catalogueMaxRetryAttempts, lastErr)
		return fmt.Errorf("catalogue agent run sync error: %w", lastErr)
	}

	return nil
}

// parseAgentOutput 解析agent输出并返回DocumentCatalog列表
func (g *GenerateCatalogueChain) parseAgentOutput(inputs map[string]any) (*definition.DocumentationStructure, error) {
	if _, ok := inputs[common2.KeyCatalogueRawOutput]; !ok {
		return nil, fmt.Errorf("catalogue structure generate failed: raw output not found")
	}

	rawOutput := inputs[common2.KeyCatalogueRawOutput].(string)
	log.Debugf("catalogue structure generate completed, content length: %d", len(rawOutput))

	// 保存完整的AI响应到文件
	if err := wikiSupport.SaveWikiGenerateResponseToFile(rawOutput, "catalogue_structure"); err != nil {
		log.Debugf("Failed to save catalogue structure AI response to file: %v", err)
	}

	structure, _, err := g.catalogueService.ParseDocumentationStructure(rawOutput)
	if err != nil {
		return nil, fmt.Errorf("failed to parse documentation structure: %w", err)
	}

	// 验证结构完整性
	if err := g.catalogueService.ValidateStructure(*structure); err != nil {
		return nil, fmt.Errorf("catalogue structure validation failed: %w", err)
	}

	return structure, nil
}

// buildFinalResult 构建最终结果
func (g *GenerateCatalogueChain) buildFinalResult(ctx context.Context, inputs map[string]any, documentCatalogs []definition.DocumentCatalog) (map[string]any, error) {
	request := inputs[common2.KeyCreateDeepwikiRequest].(definition.CreateDeepwikiRequest)
	repoName := util.GetProjectName(request.WorkspacePath)

	repoInfo, ok := inputs[common2.KeyRepoInfo].(definition.RepositoryInfo)
	if !ok {
		return nil, fmt.Errorf("invalid repo info")
	}

	// 构建目录结果结构体
	catalogueResult := definition.CatalogueResult{
		RepositoryName:        repoInfo.Name,
		RawJSON:               "", // 可以根据需要生成合并的JSON
		DocumentCatalogs:      documentCatalogs,
		TotalSections:         len(documentCatalogs),
		TotalDocumentCatalogs: len(documentCatalogs),
	}

	inputs[common2.KeyCatalogueResult] = catalogueResult

	// 保存目录结构到数据库
	err := g.saveCatalogueToDatabase(ctx, inputs, catalogueResult)
	if err != nil {
		log.Errorf("Failed to save catalogue to database: %v", err)
		// 不阻断流程继续，只记录错误
	}

	// 新增：保存结构化目录到repo的CurrentDocumentStructure字段
	var catalogueGenerationSuccess = false
	repo, err := storage.GlobalStorageService.GetWikiRepoByWorkspacePath(request.WorkspacePath)
	if err == nil && repo != nil {
		documentCatalogData, err := json.Marshal(catalogueResult.DocumentCatalogs)
		if err != nil {
			log.Errorf("Failed to marshal documentation structure: %v", err)
		} else {
			repo.CurrentDocumentStructure = string(documentCatalogData)
			err = storage.GlobalStorageService.UpdateWikiRepo(repo)
			if err != nil {
				log.Errorf("Failed to update repo with current document structure: %v", err)
			} else {
				catalogueGenerationSuccess = true
			}
		}
	}

	// 目录生成完成并保存成功后更新检查点
	if catalogueGenerationSuccess {
		wikiSupport.UpdateCheckpointOnSuccess(request.WorkspacePath, wikiSupport.CheckpointCatalogueGenCompleted)
	}

	log.Infof("Deepwiki: CatalogueGenerate end - Repo: %s, Workspace: %s, Generated: %d catalogs", repoName, request.WorkspacePath, len(documentCatalogs))
	return inputs, nil
}

// saveCatalogueToDatabase 保存目录结构到数据库
func (g *GenerateCatalogueChain) saveCatalogueToDatabase(ctx context.Context, inputs map[string]any, catalogueResult definition.CatalogueResult) error {
	// 获取必要的信息
	request, ok := inputs[common2.KeyCreateDeepwikiRequest].(definition.CreateDeepwikiRequest)
	if !ok {
		return fmt.Errorf("missing CreateDeepwikiRequest in inputs")
	}

	// 查找repo记录
	repo, err := storage.GlobalStorageService.GetWikiRepoByWorkspacePath(request.WorkspacePath)
	if err != nil {
		return fmt.Errorf("failed to get wiki repo: %w", err)
	}

	if repo == nil {
		return fmt.Errorf("wiki repo not found for workspace path: %s", request.WorkspacePath)
	}

	// 保存DocumentCatalogs到数据库
	documentCatalogs, ok := catalogueResult.DocumentCatalogs.([]definition.DocumentCatalog)
	if !ok {
		return fmt.Errorf("invalid document catalogs type in catalogue result")
	}

	for _, docCatalog := range documentCatalogs {
		// 转换为数据库实体
		catalog := &definition.AgentWikiCatalog{
			ID:             docCatalog.Id,
			RepoID:         repo.ID,
			Name:           docCatalog.Name,
			Description:    docCatalog.Description,
			Prompt:         docCatalog.Prompt,
			ParentID:       convertParentId(docCatalog.ParentId),
			Order:          docCatalog.Order,
			ProgressStatus: definition.DeepWikiProgressStatusPending,
			DependentFiles: strings.Join(docCatalog.DependentFile, ","),
			Keywords:       "", // todo 可以根据需要从docCatalog中提取,作为关键词检索
			WorkspacePath:  request.WorkspacePath,
			GmtCreate:      time.Now(),
			GmtModified:    time.Now(),
		}

		err = storage.GlobalStorageService.CreateCatalog(catalog)
		if err != nil {
			log.Errorf("Failed to save catalog %s to database: %v", docCatalog.Name, err)
			return fmt.Errorf("failed to save catalog %s: %w", docCatalog.Name, err)
		}

		log.Debugf("Successfully saved catalog to database: %s (ID: %s)", docCatalog.Name, catalog.ID)
	}

	log.Debugf("Successfully saved %d catalogs to database for repository: %s",
		len(documentCatalogs), catalogueResult.RepositoryName)
	return nil
}

// convertParentId 转换父级ID指针为字符串
func convertParentId(parentId *string) string {
	if parentId == nil {
		return ""
	}
	return *parentId
}

func (g GenerateCatalogueChain) GetMemory() schema.Memory {
	return memory.NewSimple()
}

func (g GenerateCatalogueChain) GetInputKeys() []string {
	return []string{}
}

func (g GenerateCatalogueChain) GetOutputKeys() []string {
	return []string{}
}

// reconstructCatalogueResultFromDatabase 从数据库中的catalogs重构CatalogueResult
func (g *GenerateCatalogueChain) reconstructCatalogueResultFromDatabase(catalogs []*definition.AgentWikiCatalog, repo *definition.AgentWikiRepo) definition.CatalogueResult {
	// 转换数据库catalogs为DocumentCatalog结构
	var documentCatalogs []definition.DocumentCatalog
	for _, catalog := range catalogs {
		var parentId *string
		if catalog.ParentID != "" {
			parentId = &catalog.ParentID
		}

		var dependentFiles []string
		if catalog.DependentFiles != "" {
			dependentFiles = strings.Split(catalog.DependentFiles, ",")
		}

		docCatalog := definition.DocumentCatalog{
			Id:            catalog.ID,
			Name:          catalog.Name,
			Description:   catalog.Description,
			Prompt:        catalog.Prompt,
			ParentId:      parentId,
			Order:         catalog.Order,
			DependentFile: dependentFiles,
			WarehouseId:   repo.Name,
			DocumentId:    fmt.Sprintf("doc_%s", repo.Name),
		}
		documentCatalogs = append(documentCatalogs, docCatalog)
	}

	// 构建CatalogueResult
	catalogueResult := definition.CatalogueResult{
		RepositoryName:        repo.Name,
		RawJSON:               repo.CurrentDocumentStructure, // 从repo中获取原始JSON
		DocumentCatalogs:      documentCatalogs,
		TotalSections:         len(documentCatalogs),
		TotalDocumentCatalogs: len(documentCatalogs),
	}

	log.Infof("Reconstructed CatalogueResult from database: %d catalogs for repo %s", len(documentCatalogs), repo.Name)
	return catalogueResult
}
