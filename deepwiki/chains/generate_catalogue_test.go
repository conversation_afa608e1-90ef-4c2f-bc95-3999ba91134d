package chains

import (
	"cosy/chat/chains/common"
	"cosy/deepwiki/storage"
	"cosy/definition"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// 定义存储接口用于mock
type StorageInterface interface {
	GetWikiRepoByWorkspacePath(workspacePath string) (*definition.LingmaWikiRepo, error)
	GetCatalogsByRepoID(repoID string) ([]*definition.LingmaWikiCatalog, error)
	CreateCatalog(catalog *definition.LingmaWikiCatalog) error
	UpdateWikiRepo(repo *definition.LingmaWikiRepo) error
}

// 定义目录服务接口用于mock
type CatalogueServiceInterface interface {
	ParseDocumentationStructure(rawOutput string) (*interface{}, string, error)
	ValidateStructure(structure interface{}) error
	GenerateDocumentCatalogs(structure interface{}, warehouseId, documentId string) []definition.DocumentCatalog
	FlattenSections(structure interface{}) interface{}
}

// MockStorageService 实现存储接口
type MockStorageService struct {
	mock.Mock
}

func (m *MockStorageService) GetWikiRepoByWorkspacePath(workspacePath string) (*definition.LingmaWikiRepo, error) {
	args := m.Called(workspacePath)
	return args.Get(0).(*definition.LingmaWikiRepo), args.Error(1)
}

func (m *MockStorageService) GetCatalogsByRepoID(repoID string) ([]*definition.LingmaWikiCatalog, error) {
	args := m.Called(repoID)
	return args.Get(0).([]*definition.LingmaWikiCatalog), args.Error(1)
}

func (m *MockStorageService) CreateCatalog(catalog *definition.LingmaWikiCatalog) error {
	args := m.Called(catalog)
	return args.Error(0)
}

func (m *MockStorageService) UpdateWikiRepo(repo *definition.LingmaWikiRepo) error {
	args := m.Called(repo)
	return args.Error(0)
}

// MockCatalogueService 实现目录服务接口
type MockCatalogueService struct {
	mock.Mock
}

func (m *MockCatalogueService) ParseDocumentationStructure(rawOutput string) (*interface{}, string, error) {
	args := m.Called(rawOutput)
	return args.Get(0).(*interface{}), args.String(1), args.Error(2)
}

func (m *MockCatalogueService) ValidateStructure(structure interface{}) error {
	args := m.Called(structure)
	return args.Error(0)
}

func (m *MockCatalogueService) GenerateDocumentCatalogs(structure interface{}, warehouseId, documentId string) []definition.DocumentCatalog {
	args := m.Called(structure, warehouseId, documentId)
	return args.Get(0).([]definition.DocumentCatalog)
}

func (m *MockCatalogueService) FlattenSections(structure interface{}) interface{} {
	args := m.Called(structure)
	return args.Get(0)
}

// TestGenerateCatalogueChain 测试主结构
type TestGenerateCatalogueChain struct {
	*GenerateCatalogueChain
	mockStorage   *MockStorageService
	mockCatalogue *MockCatalogueService
}

func setupTestChain() *TestGenerateCatalogueChain {
	mockStorage := &MockStorageService{}
	mockCatalogue := &MockCatalogueService{}

	// 这里我们无法直接注入mock，所以测试时需要模拟GlobalStorageService
	return &TestGenerateCatalogueChain{
		GenerateCatalogueChain: &GenerateCatalogueChain{},
		mockStorage:            mockStorage,
		mockCatalogue:          mockCatalogue,
	}
}

func TestGenerateCatalogueChain_preCheck(t *testing.T) {
	tests := []struct {
		name         string
		inputs       map[string]any
		setupMocks   func(*MockStorageService)
		wantSkip     bool
		wantErr      bool
		expectedLogs []string
	}{
		{
			name: "missing CreateDeepwikiRequest should return error",
			inputs: map[string]any{
				"invalid_key": "invalid_value",
			},
			setupMocks: func(ms *MockStorageService) {},
			wantSkip:   true,
			wantErr:    true,
		},
		{
			name: "existing catalogs in database should skip generation",
			inputs: map[string]any{
				common.KeyCreateDeepwikiRequest: definition.CreateDeepwikiRequest{
					WorkspacePath: "/test/workspace",
				},
			},
			setupMocks: func(ms *MockStorageService) {
				repo := &definition.LingmaWikiRepo{
					ID:   "repo1",
					Name: "test-repo",
				}
				catalogs := []*definition.LingmaWikiCatalog{
					{ID: "cat1", Name: "Category 1"},
					{ID: "cat2", Name: "Category 2"},
				}
				ms.On("GetWikiRepoByWorkspacePath", "/test/workspace").Return(repo, nil)
				ms.On("GetCatalogsByRepoID", "repo1").Return(catalogs, nil)
			},
			wantSkip: true,
			wantErr:  false,
		},
		{
			name: "existing CatalogueResult in inputs should skip generation",
			inputs: map[string]any{
				common.KeyCreateDeepwikiRequest: definition.CreateDeepwikiRequest{
					WorkspacePath: "/test/workspace",
				},
				common.KeyCatalogueResult: definition.CatalogueResult{
					TotalSections: 5,
				},
			},
			setupMocks: func(ms *MockStorageService) {
				ms.On("GetWikiRepoByWorkspacePath", "/test/workspace").Return((*definition.LingmaWikiRepo)(nil), assert.AnError)
			},
			wantSkip: true,
			wantErr:  false,
		},
		{
			name: "no existing catalogs should proceed with generation",
			inputs: map[string]any{
				common.KeyCreateDeepwikiRequest: definition.CreateDeepwikiRequest{
					WorkspacePath: "/test/workspace",
				},
			},
			setupMocks: func(ms *MockStorageService) {
				ms.On("GetWikiRepoByWorkspacePath", "/test/workspace").Return((*definition.LingmaWikiRepo)(nil), assert.AnError)
			},
			wantSkip: false,
			wantErr:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建测试链
			testChain := setupTestChain()
			tt.setupMocks(testChain.mockStorage)

			// 临时替换全局存储服务 - 这里我们需要创建一个wrapper
			originalStorage := storage.GlobalStorageService
			// 注意：在实际测试中，我们可能需要创建一个wrapper或者使用依赖注入
			defer func() {
				storage.GlobalStorageService = originalStorage
			}()

			// 执行测试 - 注意：由于无法直接替换GlobalStorageService，这个测试可能需要调整
			shouldSkip, result, err := testChain.preCheck(tt.inputs)

			// 验证结果
			assert.Equal(t, tt.wantSkip, shouldSkip)
			if tt.wantErr {
				assert.Error(t, err)
			} else if shouldSkip {
				// 只有在应该跳过且没有错误时才检查result
				if !tt.wantErr {
					assert.NotNil(t, result)
				}
			}
		})
	}
}

func TestConvertParentId(t *testing.T) {
	tests := []struct {
		name     string
		input    *string
		expected string
	}{
		{
			name:     "nil pointer should return empty string",
			input:    nil,
			expected: "",
		},
		{
			name:     "valid string pointer should return string value",
			input:    stringPtr("parent123"),
			expected: "parent123",
		},
		{
			name:     "empty string pointer should return empty string",
			input:    stringPtr(""),
			expected: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := convertParentId(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestGenerateCatalogueChain_QueueProcessing(t *testing.T) {
	tests := []struct {
		name          string
		initialNodes  []definition.DocumentCatalog
		maxDepth      int
		expectedCount int
		description   string
	}{
		{
			name: "single root node without children",
			initialNodes: []definition.DocumentCatalog{
				{Id: "root1", Name: "Root", Level: 0},
			},
			maxDepth:      2,
			expectedCount: 1,
			description:   "Should process only the root node",
		},
		{
			name: "multiple root nodes",
			initialNodes: []definition.DocumentCatalog{
				{Id: "root1", Name: "Root1", Level: 0},
				{Id: "root2", Name: "Root2", Level: 0},
			},
			maxDepth:      2,
			expectedCount: 2,
			description:   "Should process all root nodes",
		},
		{
			name: "nested structure with depth limit",
			initialNodes: []definition.DocumentCatalog{
				{Id: "root1", Name: "Root", Level: 0},
			},
			maxDepth:      1,
			expectedCount: 1,
			description:   "Should respect max depth limit",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建队列并验证基本行为
			queue := make([]*definition.DocumentCatalog, 0)

			// 添加初始节点到队列
			for _, node := range tt.initialNodes {
				nodeCopy := node // 避免循环变量引用问题
				queue = append(queue, &nodeCopy)
			}

			processedCount := 0

			// 模拟队列处理
			for len(queue) > 0 {
				currentNode := queue[0]
				queue = queue[1:]
				processedCount++

				// 如果没有达到最大深度，可以继续添加子节点
				if currentNode.Level < tt.maxDepth {
					// 这里可以模拟添加子节点的逻辑
				}
			}

			assert.Equal(t, tt.expectedCount, processedCount, tt.description)
		})
	}
}

// 辅助函数
func stringPtr(s string) *string {
	return &s
}
