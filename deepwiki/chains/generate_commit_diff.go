package chains

import (
	"context"
	"cosy/chat/agents/deepwiki/agent"
	agentSupport "cosy/chat/agents/support"
	chainsCommon "cosy/chat/chains/common"
	"cosy/deepwiki/common"
	"cosy/deepwiki/service"
	wikiSupport "cosy/deepwiki/support"
	"cosy/definition"
	"cosy/global"
	"cosy/indexing/chat_indexing/workspace_tree_indexing"
	"cosy/log"
	"errors"
	"fmt"
	"os"
	"runtime/debug"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/tmc/langchaingo/chains"
	"github.com/tmc/langchaingo/memory"
	"github.com/tmc/langchaingo/schema"
)

const (
	commitDiffMaxRetryAttempts = 5
	commitDiffRetryDelay       = 5 * time.Second
)

type GenerateCommitsDiffChain struct {
	commitDiffService *service.CatalogDiffService
}

func NewGenerateCommitsDiffChain(commitDiffService *service.CatalogDiffService) *GenerateCommitsDiffChain {
	return &GenerateCommitsDiffChain{
		commitDiffService: commitDiffService,
	}
}

func (g *GenerateCommitsDiffChain) Call(ctx context.Context, inputs map[string]any, options ...chains.ChainCallOption) (map[string]any, error) {
	log.Debugf("[deepwiki-incremental-update] Starting commit diff analysis")

	// 获取输入参数
	request, ok := inputs[chainsCommon.KeyCreateDeepwikiRequest].(definition.CreateDeepwikiRequest)
	if !ok {
		log.Errorf("[deepwiki-incremental-update] Missing or invalid CreateDeepwikiRequest")
		return nil, fmt.Errorf("missing or invalid CreateDeepwikiRequest")
	}

	workSpacePath := request.WorkspacePath

	commitDiffInfo, ok := inputs[chainsCommon.KeyWikiCommitDiff].(*definition.CommitDiffInfo)
	if !ok {
		log.Errorf("[deepwiki-incremental-update] Missing or invalid commit diff info")
		return nil, fmt.Errorf("missing or invalid commit diff info")
	}

	log.Debugf("[deepwiki-incremental-update] Processing repository: %s with %d commits",
		request.WorkspacePath, commitDiffInfo.TotalCommits)

	referenceCatalogItemsString := ""
	workspaceTreeIndexer, ok := workspace_tree_indexing.WorkspaceTreeFileIndexers.GetFileIndexer(workSpacePath)
	if ok {
		referenceCatalogItemsString = workspaceTreeIndexer.WorkspaceTree.GetTree(agent.WorkspaceTreeTokenLimit)
	}

	if referenceCatalogItemsString == "" {
		return nil, errors.New("workspace index tree is nil")
	}
	if referenceCatalogItemsString == "." {
		referenceCatalogItemsString = ""
	}

	repoName := service.GetProjectNameFromPath(workSpacePath)

	// 直接使用索引后的目录树作为优化的目录结构
	optimizedCatalogue := referenceCatalogItemsString
	log.Infof("Using indexed workspace tree as optimized catalogue, content length: %d", len(optimizedCatalogue))

	// 获取现有的文档目录结构
	existingCatalogues, err := g.commitDiffService.GetExistingDocumentCatalogues(request.WorkspacePath)
	if err != nil {
		log.Warnf("[deepwiki-incremental-update] Failed to get existing catalogues: %v", err)
		// 设置空数组以防止崩溃
		emptyResult := make([]definition.DocumentCatalog, 0)
		existingCatalogues = &emptyResult
	}
	log.Infof("[deepwiki-incremental-update] Retrieved existing document catalogues: %d items", len(*existingCatalogues))

	// 获取仓库名称
	repoName = service.GetProjectNameFromPath(request.WorkspacePath)

	// 获取偏好语言
	preferredLanguage, ok := ctx.Value(chainsCommon.KeyPreferredLanguage).(string)
	if !ok || preferredLanguage == "" {
		preferredLanguage = wikiSupport.DefaultPreferredLanguage // 默认中文
	}
	log.Infof("[deepwiki-incremental-update] Using preferred language: %s", preferredLanguage)

	// 检查是否为截断版本的commit diff
	originalTotalCommits := commitDiffInfo.TotalCommits
	actualCommitCount := len(commitDiffInfo.Commits)
	if originalTotalCommits > actualCommitCount {
		log.Debugf("[deepwiki-token-optimization] Detected truncated commit diff: showing %d out of %d total commits to fit token limit",
			actualCommitCount, originalTotalCommits)
	}

	// 构建分析请求并放入inputs中供agent使用
	analysisRequest := common.CommitDiffAnalysisRequest{
		BaseDiffAnalysisRequest: common.BaseDiffAnalysisRequest{
			WorkspacePath:         request.WorkspacePath,
			RepositoryName:        repoName,
			CurrentCatalogue:      optimizedCatalogue,
			ExistingDocCatalogues: *existingCatalogues,
			Language:              preferredLanguage,
		},
		CommitInfo: commitDiffInfo,
	}

	// 将分析请求放入inputs供agent使用
	inputs[chainsCommon.KeyWikiCommitDiffAnalysisRequest] = analysisRequest

	// 带重试的agent执行
	var lastErr error
	for attempt := 1; attempt <= commitDiffMaxRetryAttempts; attempt++ {
		log.Debugf("[deepwiki-incremental-update] Starting agent execution attempt %d/%d", attempt, commitDiffMaxRetryAttempts)

		agentCtx, _, err := agent.InitCommitDiffAgentContext(ctx)
		if err != nil {
			log.Errorf("[deepwiki-incremental-update] Failed to init agent context (attempt %d): %v", attempt, err)
			return nil, err
		}

		requestId := uuid.NewString()
		commitDiffAgent, err := agentSupport.MakeAgent(requestId, common.CommitDiffAgentBuilderIdentifier)
		if err != nil {
			log.Errorf("[deepwiki-incremental-update] Failed to create commit diff agent (attempt %d): %v", attempt, err)
			return nil, fmt.Errorf("failed to create commit diff agent: %w", err)
		}

		// 初始化图统计信息，与其他chains保持一致
		wikiSupport.InitCurrentGraphStat(inputs, "GenerateCommitDiff")

		var runErr error
		func() {
			defer func() {
				if r := recover(); r != nil {
					stack := debug.Stack()
					errMsg := fmt.Sprintf("Fatal error in commit diff agent.RunSync (attempt %d/%d): %v\n%s", attempt, commitDiffMaxRetryAttempts, r, stack)
					log.Errorf("[deepwiki-incremental-update] %s", errMsg)
					runErr = fmt.Errorf("fatal error: %v", r)
				}
			}()
			log.Debugf("[deepwiki-incremental-update] Executing agent.RunSync (attempt %d)", attempt)
			runErr = commitDiffAgent.RunSync(agentCtx, inputs)
			log.Debugf("[deepwiki-incremental-update] Agent.RunSync completed (attempt %d)", attempt)
		}()

		if runErr == nil {
			// Agent执行成功，但需要验证是否生成了有效内容
			// 从agent context中获取最终状态并同步结果 - 使用线程安全方法
			if agentContext, ok := agentCtx.Value(chainsCommon.KeyDeepwikiAgentContext).(*common.AgentContext); ok {
				if agentState, ok := agentContext.State.(*common.DeepWikiGenerateState); ok {
					// 使用线程安全的方法复制inputs
					tempInputs := agentState.CopyInputs()

					// 将副本合并到inputs中
					for key, value := range tempInputs {
						inputs[key] = value
					}

					log.Debugf("[deepwiki-incremental-update] Synced %d keys from agent state to inputs", len(tempInputs))
				} else {
					log.Errorf("[deepwiki-incremental-update] Failed to get agentState from agentContext")
				}
			} else {
				log.Errorf("[deepwiki-incremental-update] Failed to get agentContext from agentCtx")
			}

			// 检查是否生成了有效的Commit Diff分析结果（不为空且长度大于等于150字符）
			var hasValidContent bool
			var contentLength int
			const minCommitDiffContentLength = 250

			if commitDiffResponse, exists := inputs[chainsCommon.KeyWikiCommitDiffResponse]; exists {
				if contentStr, ok := commitDiffResponse.(string); ok {
					trimmedContent := strings.TrimSpace(contentStr)
					contentLength = len(trimmedContent)

					if trimmedContent != "" && contentLength >= minCommitDiffContentLength {
						hasValidContent = true
						log.Debugf("[deepwiki-incremental-update] Agent generated valid commit diff analysis, content length: %d", contentLength)
					} else {
						log.Debugf("[deepwiki-incremental-update] Agent generated insufficient commit diff analysis, length: %d (min required: %d)", contentLength, minCommitDiffContentLength)
					}
				}
			}

			if hasValidContent {
				if attempt > 1 {
					log.Infof("[deepwiki-incremental-update] Agent succeeded on attempt %d/%d, commit diff analysis length: %d", attempt, commitDiffMaxRetryAttempts, contentLength)
				} else {
					log.Debugf("[deepwiki-incremental-update] Agent succeeded on first attempt, commit diff analysis length: %d", contentLength)
				}

				// 成功时记录统计信息
				wikiSupport.AppendGraphStatToWikiStat(inputs)
				// 成功时清除lastErr
				lastErr = nil
				break
			} else {
				// Agent执行成功但没有生成有效的Commit Diff分析结果（为空或长度不足），视为失败需要重试
				if contentLength == 0 {
					runErr = fmt.Errorf("agent executed successfully but generated empty commit diff analysis")
					log.Debugf("[deepwiki-incremental-update] Agent generated empty commit diff analysis (attempt %d/%d)", attempt, commitDiffMaxRetryAttempts)
				} else {
					runErr = fmt.Errorf("agent executed successfully but generated insufficient commit diff analysis (length: %d, min required: %d)", contentLength, minCommitDiffContentLength)
					log.Debugf("[deepwiki-incremental-update] Agent generated insufficient commit diff analysis, length: %d (min required: %d) (attempt %d/%d)", contentLength, minCommitDiffContentLength, attempt, commitDiffMaxRetryAttempts)
				}
			}
		}

		// 收集图统计信息
		wikiSupport.AppendGraphStatToWikiStat(inputs)

		lastErr = runErr
		log.Errorf("[deepwiki-incremental-update] Agent failed on attempt %d/%d: %v", attempt, commitDiffMaxRetryAttempts, runErr)

		// 如果不是最后一次尝试，等待后重试
		if attempt < commitDiffMaxRetryAttempts {
			log.Infof("[deepwiki-incremental-update] Retrying in %v... (attempt %d/%d)", commitDiffRetryDelay, attempt+1, commitDiffMaxRetryAttempts)

			// 检查context是否已被取消
			select {
			case <-time.After(commitDiffRetryDelay):
				// 继续重试
			}
		}
	}

	if lastErr != nil {
		log.Errorf("[deepwiki-incremental-update] Agent failed after %d attempts, last error: %v", commitDiffMaxRetryAttempts, lastErr)
		return nil, fmt.Errorf("commit diff agent run sync error: %w", lastErr)
	}

	log.Debugf("[deepwiki-incremental-update] Agent execution completed successfully")

	if _, ok := inputs[chainsCommon.KeyWikiCommitDiffResponse]; !ok {
		log.Errorf("[deepwiki-incremental-update] commit diff analysis failed, raw output not found after %d attempts", commitDiffMaxRetryAttempts)
		return nil, fmt.Errorf("commit diff analysis failed: raw output not found after all retry attempts")
	}

	// 获取AI响应并解析
	rawOutput := inputs[chainsCommon.KeyWikiCommitDiffResponse].(string)
	log.Infof("[deepwiki-incremental-update] commit diff analysis completed, content length: %d", len(rawOutput))

	if global.IsEvaluationMode() {
		// 保存完整的AI响应到文件
		if err := saveCommitDiffResponseToFile(rawOutput, "commit_diff_analysis"); err != nil {
			log.Warnf("[deepwiki-incremental-update] Failed to save commit diff AI response to file: %v", err)
		}
	}

	// 解析AI响应
	log.Infof("[deepwiki-incremental-update] Parsing AI response...")
	analysisResponse, _, err := g.commitDiffService.ParseCatalogDiffAnalysis(rawOutput)
	if err != nil {
		log.Errorf("[deepwiki-incremental-update] Failed to parse commit diff analysis: %v", err)
		return nil, fmt.Errorf("failed to parse commit diff analysis: %w", err)
	}
	log.Debugf("[deepwiki-incremental-update] Successfully parsed AI response")

	// 验证分析结果
	log.Infof("[deepwiki-incremental-update] Validating analysis result...")
	if err := g.commitDiffService.ValidateCommitDiffAnalysis(analysisResponse); err != nil {
		log.Warnf("[deepwiki-incremental-update] Commit diff analysis validation failed: %v", err)
		// 继续处理，但记录警告
	} else {
		log.Debugf("[deepwiki-incremental-update] Analysis result validation passed")
	}

	// 将结果存储到inputs中
	inputs[chainsCommon.KeyWikiCommitDiffAnalysis] = analysisResponse

	log.Debugf("[deepwiki-incremental-update] Commit diff analysis completed: %d items to process, %d items to delete",
		len(analysisResponse.Items), len(analysisResponse.DeleteIDs))

	log.Debugf("[deepwiki-incremental-update] Chain execution completed successfully, passing control to next chain")

	return inputs, nil
}

// saveCommitDiffResponseToFile 保存commit diff AI响应到文件
func saveCommitDiffResponseToFile(response, stage string) error {
	timestamp := time.Now().Format("20060102_150405")
	filename := fmt.Sprintf("chat/agents/deepwiki/deepwiki_output/ai_response_%s_%s.txt", stage, timestamp)
	// 确保目录存在
	if err := os.MkdirAll("chat/agents/deepwiki/deepwiki_output", 0755); err != nil {
		return fmt.Errorf("failed to create output directory: %w", err)
	}
	// 写入文件
	if err := os.WriteFile(filename, []byte(response), 0644); err != nil {
		return fmt.Errorf("failed to write response to file: %w", err)
	}
	log.Infof("Commit Diff AI response saved to: %s", filename)
	return nil
}
func (g *GenerateCommitsDiffChain) GetMemory() schema.Memory {
	return memory.NewSimple()
}

func (g *GenerateCommitsDiffChain) GetInputKeys() []string {
	return []string{}
}

func (g *GenerateCommitsDiffChain) GetOutputKeys() []string {
	return []string{}
}
