package queue

import (
	"context"
	"cosy/client"
	"cosy/definition"
	"cosy/global"
	"cosy/log"
	"cosy/remote"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/google/uuid"
)

// worker 工作协程
type worker struct {
	id                     int
	fullGenerationQueue    chan *Task
	incrementalUpdateQueue chan *Task
	executor               TaskExecutor
}

// 排队信息
type LineUpInfo struct {
	needLineUp  bool
	lineUpId    string
	lineUpType  string
	lineUpToken string
}

// newWorker 创建新的worker
func newWorker(id int, fullGenerationQueue chan *Task, incrementalUpdateQueue chan *Task, executor TaskExecutor) *worker {
	return &worker{
		id:                     id,
		fullGenerationQueue:    fullGenerationQueue,
		incrementalUpdateQueue: incrementalUpdateQueue,
		executor:               executor,
	}
}

// run 运行worker主循环
func (w *worker) run(ctx context.Context, onStatusChange func(*Task)) {
	log.Debugf("Worker %d started", w.id)
	defer log.Debugf("Worker %d stopped", w.id)

	for {
		select {
		case task, ok := <-w.fullGenerationQueue:
			if !ok {
				// 队列已关闭
				log.Debugf("Worker %d: task queue closed, shutting down", w.id)
				return
			}

			// 测评模式,单独队列
			if global.IsEvaluationMode() {
				w.lineUpProcessTask(ctx, task, onStatusChange, "evaluation_full", 120*time.Second)
			} else {
				w.lineUpProcessTask(ctx, task, onStatusChange, "full", 120*time.Second)
			}
		case task, ok := <-w.incrementalUpdateQueue:
			if !ok {
				// 队列已关闭
				log.Debugf("Worker %d: task queue closed, shutting down", w.id)
				return
			}
			if global.IsEvaluationMode() {
				w.lineUpProcessTask(ctx, task, onStatusChange, "evaluation_increment", 60*time.Second)
			} else {
				w.lineUpProcessTask(ctx, task, onStatusChange, "increment", 60*time.Second)
			}
		case <-ctx.Done():
			// 上下文取消，退出
			log.Debugf("Worker %d: context cancelled, shutting down", w.id)
			return
		}
		log.Debugf("Worker %d: fullGenerationQueue size:%d, incrementalUpdateQueue size:%d.", w.id, len(w.fullGenerationQueue), len(w.incrementalUpdateQueue))
	}
}

// processTask 处理单个任务
func (w *worker) processTask(ctx context.Context, task *Task, onStatusChange func(*Task)) {
	log.Debugf("Worker %d: processing task %s (%s) for workspace: %s",
		w.id, task.ID, task.Type.String(), task.WorkspacePath)

	// 标记任务开始
	task.MarkStarted()
	if onStatusChange != nil {
		onStatusChange(task)
	}

	// 直接执行任务
	err := w.executor.ExecuteTask(ctx, task)

	// 处理执行结果
	if err != nil {
		log.Errorf("Worker %d: task %s failed: %v", w.id, task.ID, err)
		task.MarkFailed(err)
	} else {
		log.Debugf("Worker %d: task %s completed successfully", w.id, task.ID)
		task.MarkCompleted()
	}

	// 通知状态变更
	if onStatusChange != nil {
		onStatusChange(task)
	}
}

func getLineUpInfo(lineUpType string, lineUpId string) *LineUpInfo {
	// 默认返回值
	defaultLineUpInfo := &LineUpInfo{
		needLineUp: true,
		lineUpType: lineUpType,
		lineUpId:   lineUpId,
	}

	url := fmt.Sprintf("%s?id=%s&type=%s", definition.UrlPathWikiLineUpUrl, lineUpId, lineUpType)
	request, err := remote.BuildBigModelAuthRequest(http.MethodGet, url, nil)
	if err != nil {
		log.Error("Failed to build LineUp request:%w", err)
		return defaultLineUpInfo
	}

	response, err := client.GetDefaultClient().Do(request)
	if err != nil {
		log.Error("Failed to LineUp :%w", err)
		return defaultLineUpInfo
	}

	defer response.Body.Close()
	if response.StatusCode != http.StatusOK {
		log.Errorf("Failed to LineUp, statusCode=%d, url=%s", response.StatusCode, url)
		return defaultLineUpInfo
	}
	responseBody, err := io.ReadAll(response.Body)
	var data map[string]interface{}
	if err != nil || json.Unmarshal(responseBody, &data) != nil {
		log.Errorf("Failed to read response:%v", err)
		return defaultLineUpInfo
	}

	// 获取排队结果
	isQueued, ok := data["isQueued"].(bool)
	if !ok {
		log.Errorf("Failed to read isQueued:%v", err)
		return defaultLineUpInfo
	}

	if isQueued {
		return defaultLineUpInfo
	}

	passkey, ok := data["passKey"].(string)
	if !ok {
		return defaultLineUpInfo
	}

	return &LineUpInfo{
		needLineUp:  false,
		lineUpId:    lineUpId,
		lineUpType:  lineUpType,
		lineUpToken: passkey,
	}
}

func releaseLineUpToken(lineUpInfo *LineUpInfo) {
	url := fmt.Sprintf("%s?id=%s&type=%s", definition.UrlPathWikiReleaseLineUpUrl, lineUpInfo.lineUpId, lineUpInfo.lineUpType)
	request, err := remote.BuildBigModelAuthRequest(http.MethodGet, url, nil)
	if err != nil {
		log.Error("Failed to build releaseLineUp request:%w", err)
		return
	}

	response, err := client.GetDefaultClient().Do(request)
	if err != nil {
		log.Error("Failed to releaseLineUp :%w", err)
		return
	}

	defer response.Body.Close()
	if response.StatusCode != http.StatusOK {
		log.Errorf("Failed to releaseLineUp, statusCode=%d, url=%s", response.StatusCode, url)
		return
	}
}

func (w *worker) lineUpProcessTask(ctx context.Context, task *Task, onStatusChange func(*Task), lineUpType string, lineUpSleepTime time.Duration) {
	// 生成任务排队ID
	var lineUpId string
	if task.Request.LineUpId == "" {
		lineUpId = uuid.NewString()
	} else {
		lineUpId = task.Request.LineUpId
	}

	// 获取远程排队信息
	info := getLineUpInfo(lineUpType, lineUpId)
	log.Debugf("Worker %d: get line up info:%+v .", w.id, info)

	// 需要排队，放回任务,休眠后返回
	if info.needLineUp {
		task.Request.LineUpId = lineUpId
		if lineUpType == "full" || lineUpType == "evaluation_full" {
			w.fullGenerationQueue <- task
		}
		if lineUpType == "increment" || lineUpType == "evaluation_increment" {
			w.incrementalUpdateQueue <- task
		}
		log.Debugf("Worker %d: get line up token fail, sleep.", w.id)
		time.Sleep(lineUpSleepTime)
		return
	}

	//不排队执行对应任务，并释放排队令牌
	task.Request.LineUpId = info.lineUpId
	task.Request.LineUpType = info.lineUpType
	task.Request.LineUpToken = info.lineUpToken
	w.processTask(ctx, task, onStatusChange)
	releaseLineUpToken(info)
	log.Debugf("Worker %d: release line up info:%+v.", w.id, info)
}
