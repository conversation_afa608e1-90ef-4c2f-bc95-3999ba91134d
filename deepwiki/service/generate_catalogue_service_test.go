package service

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestGenerateCatalogueService_ParseDocumentationStructure(t *testing.T) {
	service := NewGenerateCatalogueService()

	tests := []struct {
		name            string
		response        string
		expectError     bool
		expectStructure bool
		expectedItems   int
		expectedRawJSON string
		errorContains   string
	}{
		{
			name: "正常解析带有documentation_structure标签的JSON",
			response: `<documentation_structure>
{
  "items": [
    {
      "title": "admin-user-management",
      "name": "Admin User Management API",
      "dependent_file": [
        "mall-admin/src/main/java/com/macro/mall/controller/UmsAdminController.java",
        "mall-admin/src/main/java/com/macro/mall/service/UmsAdminService.java"
      ],
      "prompt": "Develop detailed content for the Admin User Management API section.",
      "children_plan": "No further splitting needed as this represents a cohesive component.",
      "has_children": "NO"
    }
  ]
}
</documentation_structure>`,
			expectError:     false,
			expectStructure: true,
			expectedItems:   1,
		},
		{
			name: "解析包含多个items的复杂结构",
			response: `<documentation_structure>
{
  "items": [
    {
      "title": "admin-order-management",
      "name": "Admin Order Management API",
      "dependent_file": [
        "mall-admin/src/main/java/com/macro/mall/controller/OmsOrderController.java",
        "mall-admin/src/main/java/com/macro/mall/service/OmsOrderService.java"
      ],
      "prompt": "Develop detailed content for the Admin Order Management API section.",
      "children_plan": "Subdivide into Order Query API, Order Status Update API.",
      "has_children": "YES"
    },
    {
      "title": "customer-portal-order",
      "name": "Customer Portal Order API",
      "dependent_file": [
        "mall-portal/src/main/java/com/macro/mall/portal/controller/OmsPortalOrderController.java"
      ],
      "prompt": "Develop detailed content for the Customer Portal Order API section.",
      "children_plan": "Subdivide into Order Creation API, Order Tracking API.",
      "has_children": "YES"
    }
  ]
}
</documentation_structure>`,
			expectError:     false,
			expectStructure: true,
			expectedItems:   2,
		},
		{
			name: "解析JSON代码块格式",
			response: "```json\n" +
				"{\n" +
				`  "items": [` + "\n" +
				"    {\n" +
				`      "title": "test-section",` + "\n" +
				`      "name": "Test Section",` + "\n" +
				`      "dependent_file": ["test.java"],` + "\n" +
				`      "prompt": "Test prompt",` + "\n" +
				`      "children_plan": "No children",` + "\n" +
				`      "has_children": "NO"` + "\n" +
				"    }\n" +
				"  ]\n" +
				"}\n" +
				"```",
			expectError:     false,
			expectStructure: true,
			expectedItems:   1,
		},
		{
			name: "解析直接数组格式JSON",
			response: `<documentation_structure>
[
  {
    "title": "admin-user-management",
    "name": "Admin User Management API",
    "dependent_file": [
      "mall-admin/src/main/java/com/macro/mall/controller/UmsAdminController.java",
      "mall-admin/src/main/java/com/macro/mall/service/UmsAdminService.java"
    ],
    "prompt": "Develop detailed content for the Admin User Management API section.",
    "children_plan": "No further splitting needed.",
    "has_children": "NO"
  },
  {
    "title": "customer-member-api",
    "name": "Customer Member API",
    "dependent_file": [
      "mall-portal/src/main/java/com/macro/mall/portal/controller/UmsMemberController.java"
    ],
    "prompt": "Develop detailed content for the Customer Member API section.",
    "children_plan": "No further splitting needed.",
    "has_children": "NO"
  }
]
</documentation_structure>`,
			expectError:     false,
			expectStructure: true,
			expectedItems:   2,
		},
		{
			name: "解析包含额外文本的响应",
			response: `这里是一些前置文本
<documentation_structure>
This is some extra text before JSON
{
  "items": [
    {
      "title": "test-api",
      "name": "Test API",
      "dependent_file": ["controller.java"],
      "prompt": "Test API documentation",
      "children_plan": "No children needed",
      "has_children": "NO"
    }
  ]
}
This is some text after JSON
</documentation_structure>
后续还有其他内容`,
			expectError:     false,
			expectStructure: true,
			expectedItems:   1,
		},
		{
			name: "空的items数组",
			response: `<documentation_structure>
{
  "items": []
}
</documentation_structure>`,
			expectError:     false,
			expectStructure: true,
			expectedItems:   0,
		},
		{
			name: "缺少documentation_structure标签和JSON代码块",
			response: `这是一个没有正确格式的响应
{
  "items": [{"title": "test"}]
}
没有标签包围`,
			expectError:     true,
			expectStructure: false,
			errorContains:   "could not find documentation structure in response",
		},
		{
			name: "documentation_structure标签内容为空",
			response: `<documentation_structure>
</documentation_structure>`,
			expectError:     true,
			expectStructure: false,
			errorContains:   "no valid JSON found in documentation_structure tag",
		},
		{
			name: "无效的JSON格式",
			response: `<documentation_structure>
{
  "items": [
    {
      "title": "test"
      "name": "missing comma"
    }
  ]
}
</documentation_structure>`,
			expectError:     true,
			expectStructure: false,
			errorContains:   "no valid JSON found in documentation_structure tag",
		},
		{
			name: "JSON格式正确但缺少必需字段",
			response: `<documentation_structure>
{
  "wrong_field": []
}
</documentation_structure>`,
			expectError:     false,
			expectStructure: true,
			expectedItems:   0, // items字段缺失时会解析为空数组
		},
		{
			name: "嵌套的JSON结构",
			response: `<documentation_structure>
{
  "items": [
    {
      "title": "parent-section",
      "name": "Parent Section",
      "dependent_file": ["parent.java"],
      "prompt": "Parent section prompt",
      "children_plan": "Has child sections",
      "has_children": "YES",
      "children": [
        {
          "title": "child-section",
          "name": "Child Section",
          "dependent_file": ["child.java"],
          "prompt": "Child section prompt",
          "children_plan": "No children",
          "has_children": "NO"
        }
      ]
    }
  ]
}
</documentation_structure>`,
			expectError:     false,
			expectStructure: true,
			expectedItems:   1, // 只计算顶层items
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			structure, rawJSON, err := service.ParseDocumentationStructure(tt.response)

			if tt.expectError {
				assert.Error(t, err)
				if tt.errorContains != "" {
					assert.Contains(t, err.Error(), tt.errorContains)
				}
				if !tt.expectStructure {
					assert.Nil(t, structure)
				}
			} else {
				assert.NoError(t, err)
				require.NotNil(t, structure)
				assert.Len(t, structure.Items, tt.expectedItems)
				assert.NotEmpty(t, rawJSON)

				// 验证返回的rawJSON是有效的JSON
				var testObj interface{}
				assert.NoError(t, json.Unmarshal([]byte(rawJSON), &testObj))

				// 验证解析出的结构体字段
				if tt.expectedItems > 0 {
					firstItem := structure.Items[0]
					assert.NotEmpty(t, firstItem.Title)
					assert.NotEmpty(t, firstItem.Name)
					assert.NotEmpty(t, firstItem.Prompt)
					assert.NotNil(t, firstItem.DependentFile)
				}
			}
		})
	}
}

func TestGenerateCatalogueService_ParseDocumentationStructure_RealData(t *testing.T) {
	service := NewGenerateCatalogueService()

	// 使用附件中的真实JSON数据进行测试
	realResponse1 := `<documentation_structure>
[
  {
    "title": "admin-user-management",
    "name": "Admin User Management API",
    "dependent_file": [
      "mall-admin/src/main/java/com/macro/mall/controller/UmsAdminController.java",
      "mall-admin/src/main/java/com/macro/mall/service/UmsAdminService.java",
      "mall-admin/src/main/java/com/macro/mall/config/MallSecurityConfig.java"
    ],
    "prompt": "Develop detailed content for the Admin User Management API section focused on UmsAdminController implementation.",
    "children_plan": "No further splitting needed as this represents a cohesive component with related functionality in the codebase.",
    "has_children": "NO"
  },
  {
    "title": "customer-member-api",
    "name": "Customer Member API",
    "dependent_file": [
      "mall-portal/src/main/java/com/macro/mall/portal/controller/UmsMemberController.java",
      "mall-portal/src/main/java/com/macro/mall/portal/service/UmsMemberService.java",
      "mall-security/src/main/java/com/macro/mall/security/component/JwtAuthenticationTokenFilter.java"
    ],
    "prompt": "Develop detailed content for the Customer Member API section focused on UmsMemberController implementation.",
    "children_plan": "No further splitting needed as this represents a cohesive component with related functionality in the codebase.",
    "has_children": "NO"
  }
]
</documentation_structure>`

	realResponse2 := `<documentation_structure>
{
  "items": [
    {
      "title": "admin-order-management",
      "name": "Admin Order Management API",
      "dependent_file": [
        "mall-admin/src/main/java/com/macro/mall/controller/OmsOrderController.java",
        "mall-admin/src/main/java/com/macro/mall/service/OmsOrderService.java",
        "mall-admin/src/main/java/com/macro/mall/dto/OmsOrderDetail.java"
      ],
      "prompt": "Develop detailed content for the Admin Order Management API section focused on OmsOrderController implementation.",
      "children_plan": "Subdivide into Order Query API, Order Status Update API, Order Delivery API, and Order Return API based on repository analysis.",
      "has_children": "YES"
    },
    {
      "title": "customer-portal-order",
      "name": "Customer Portal Order API", 
      "dependent_file": [
        "mall-portal/src/main/java/com/macro/mall/portal/controller/OmsPortalOrderController.java",
        "mall-portal/src/main/java/com/macro/mall/portal/service/OmsPortalOrderService.java",
        "mall-portal/src/main/java/com/macro/mall/portal/domain/OmsOrderDetail.java"
      ],
      "prompt": "Develop detailed content for the Customer Portal Order API section focused on OmsPortalOrderController implementation.",
      "children_plan": "Subdivide into Order Creation API, Order Tracking API, Order Cancellation API, and Order Return Request API based on repository analysis.",
      "has_children": "YES"
    }
  ]
}
</documentation_structure>`

	t.Run("解析真实数据1-数组格式", func(t *testing.T) {
		structure, rawJSON, err := service.ParseDocumentationStructure(realResponse1)

		assert.NoError(t, err)
		require.NotNil(t, structure)
		assert.Len(t, structure.Items, 2)
		assert.NotEmpty(t, rawJSON)

		// 验证第一个item
		firstItem := structure.Items[0]
		assert.Equal(t, "admin-user-management", firstItem.Title)
		assert.Equal(t, "Admin User Management API", firstItem.Name)
		assert.Len(t, firstItem.DependentFile, 3)
		assert.Contains(t, firstItem.DependentFile, "mall-admin/src/main/java/com/macro/mall/controller/UmsAdminController.java")
		assert.Equal(t, "NO", firstItem.HasChildren)

		// 验证第二个item
		secondItem := structure.Items[1]
		assert.Equal(t, "customer-member-api", secondItem.Title)
		assert.Equal(t, "Customer Member API", secondItem.Name)
		assert.Len(t, secondItem.DependentFile, 3)
		assert.Equal(t, "NO", secondItem.HasChildren)
	})

	t.Run("解析真实数据2-对象格式", func(t *testing.T) {
		structure, rawJSON, err := service.ParseDocumentationStructure(realResponse2)

		assert.NoError(t, err)
		require.NotNil(t, structure)
		assert.Len(t, structure.Items, 2)
		assert.NotEmpty(t, rawJSON)

		// 验证第一个item
		firstItem := structure.Items[0]
		assert.Equal(t, "admin-order-management", firstItem.Title)
		assert.Equal(t, "Admin Order Management API", firstItem.Name)
		assert.Len(t, firstItem.DependentFile, 3)
		assert.Equal(t, "YES", firstItem.HasChildren)
		assert.Contains(t, firstItem.ChildrenPlan, "Subdivide into Order Query API")

		// 验证第二个item
		secondItem := structure.Items[1]
		assert.Equal(t, "customer-portal-order", secondItem.Title)
		assert.Equal(t, "Customer Portal Order API", secondItem.Name)
		assert.Len(t, secondItem.DependentFile, 3)
		assert.Equal(t, "YES", secondItem.HasChildren)
		assert.Contains(t, secondItem.ChildrenPlan, "Subdivide into Order Creation API")
	})
}

func TestGenerateCatalogueService_ExtractPureJSON(t *testing.T) {
	service := NewGenerateCatalogueService()

	tests := []struct {
		name     string
		content  string
		expected string
	}{
		{
			name:     "简单JSON对象",
			content:  `{"items": []}`,
			expected: `{"items": []}`,
		},
		{
			name:     "带有前后文本的JSON",
			content:  `Some text before {"items": [{"title": "test"}]} some text after`,
			expected: `{"items": [{"title": "test"}]}`,
		},
		{
			name:     "嵌套JSON对象",
			content:  `{"items": [{"nested": {"key": "value"}}]}`,
			expected: `{"items": [{"nested": {"key": "value"}}]}`,
		},
		{
			name:     "简单JSON数组",
			content:  `[{"title": "test"}]`,
			expected: `[{"title": "test"}]`,
		},
		{
			name:     "带有前后文本的JSON数组",
			content:  `Some text before [{"title": "test"}, {"title": "test2"}] some text after`,
			expected: `[{"title": "test"}, {"title": "test2"}]`,
		},
		{
			name:     "嵌套JSON数组",
			content:  `[{"nested": [{"key": "value"}]}]`,
			expected: `[{"nested": [{"key": "value"}]}]`,
		},
		{
			name:     "混合情况-数组在前",
			content:  `[{"title": "test"}] {"items": []}`,
			expected: `[{"title": "test"}]`,
		},
		{
			name:     "混合情况-对象在前",
			content:  `{"items": []} [{"title": "test"}]`,
			expected: `{"items": []}`,
		},
		{
			name:     "没有JSON内容",
			content:  `This is just text without any JSON`,
			expected: ``,
		},
		{
			name:     "不匹配的大括号",
			content:  `{"incomplete": "json"`,
			expected: ``,
		},
		{
			name:     "不匹配的中括号",
			content:  `[{"incomplete": "json"`,
			expected: ``,
		},
		{
			name:     "空字符串",
			content:  ``,
			expected: ``,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := service.extractPureJSON(tt.content)
			assert.Equal(t, tt.expected, result)
		})
	}
}
