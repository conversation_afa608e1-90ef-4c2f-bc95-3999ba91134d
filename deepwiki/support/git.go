package support

import (
	"cosy/definition"
	"cosy/log"
	"errors"
	"fmt"
	"reflect"
	"strings"
	"time"

	"github.com/go-git/go-git/v5"
	"github.com/go-git/go-git/v5/plumbing"
	"github.com/go-git/go-git/v5/plumbing/object"
)

type GitSupport struct {
	workspacePath string
	repo          *git.Repository
}

func NewGitSupport(workspacePath string) (*GitSupport, error) {
	repo, err := git.PlainOpen(workspacePath)
	if err != nil {
		return nil, err
	}
	return &GitSupport{
		workspacePath: workspacePath,
		repo:          repo,
	}, nil
}

func (g *GitSupport) IsAvailable() bool {
	return g.repo != nil
}

// 生成固定的commit，用于标记
func NewFixedCommit() *object.Commit {
	return &object.Commit{
		Hash: plumbing.NewHash("000000000"),
		Author: object.Signature{
			Name:  "FixedCommit",
			Email: "<EMAIL>",
			When:  time.Now(),
		},
		Committer: object.Signature{
			Name:  "FixedCommit",
			Email: "<EMAIL>",
			When:  time.Now(),
		},
		Message: "FixedCommit",
	}
}

func (g *GitSupport) GetHeadCommit() (*object.Commit, error) {
	headRef, err := g.repo.Head()
	if err != nil {
		return nil, err
	}
	commit, err := g.repo.CommitObject(headRef.Hash())
	if err != nil {
		return nil, err
	}
	return commit, nil
}

// GetCommitDiff retrieves the commit diff information between HEAD and lastCommitID.
// 当commit diff内容过多时，会自动简化返回结果以避免token超限。
func GetCommitDiff(workspacePath, lastCommitID string) (*definition.CommitDiffInfo, error) {
	repo, err := git.PlainOpen(workspacePath)
	if err != nil {
		return nil, fmt.Errorf("failed to open git repository: %w", err)
	}

	headRef, err := repo.Head()
	if err != nil {
		return nil, fmt.Errorf("failed to get HEAD: %w", err)
	}

	currentCommitID := headRef.Hash().String()
	if currentCommitID == lastCommitID {
		return nil, nil
	}

	if lastCommitID != "" {
		currentCommit, err := repo.CommitObject(headRef.Hash())
		if err == nil {
			lastCommitHash := plumbing.NewHash(lastCommitID)
			lastCommit, err := repo.CommitObject(lastCommitHash)
			if err == nil {
				if currentCommit.Author.When.Before(lastCommit.Author.When) {
					return nil, nil
				}
			}
		}
	}

	commitIter, err := repo.Log(&git.LogOptions{From: headRef.Hash()})
	if err != nil {
		return nil, fmt.Errorf("failed to get commit log: %w", err)
	}

	var commits []definition.CommitInfo
	var foundLastCommit bool
	// 添加去重机制：使用map跟踪已处理的commit hash
	processedCommits := make(map[string]bool)

	err = commitIter.ForEach(func(commit *object.Commit) error {
		commitHash := commit.Hash.String()

		if commitHash == lastCommitID {
			foundLastCommit = true
			return fmt.Errorf("stop iteration")
		}

		// 检查是否已经处理过这个commit
		if processedCommits[commitHash] {
			log.Debugf("Skipping duplicate commit: %s (%s)", commitHash[:8], strings.TrimSpace(commit.Message))
			return nil
		}

		// 标记该commit已处理
		processedCommits[commitHash] = true

		fileChanges, err := getFileChanges(repo, commit)
		if err != nil {
			fileChanges = []definition.FileChange{}
		}
		commitInfo := definition.CommitInfo{
			Hash:        commitHash,
			Message:     strings.TrimSpace(commit.Message),
			Author:      commit.Author.Name,
			Date:        commit.Author.When,
			FileChanges: fileChanges,
		}
		commits = append(commits, commitInfo)
		return nil
	})
	if err != nil && foundLastCommit {
		err = nil
	}
	if err != nil {
		return nil, fmt.Errorf("failed to iterate commits: %w", err)
	}
	if !foundLastCommit && lastCommitID != "" {
		// treat as new repo or commit deleted
	}

	// 输出去重统计信息
	log.Debugf("Commit diff processing: found %d unique commits, processed %d total iterations", len(commits), len(processedCommits))

	// 计算总的修改行数
	totalChangedLines := 0
	for _, commit := range commits {
		for _, fileChange := range commit.FileChanges {
			totalChangedLines += fileChange.AddedLines + fileChange.DeletedLines
		}
	}

	diffInfo := &definition.CommitDiffInfo{
		Commits:           commits,
		TotalCommits:      len(commits),
		WorkspacePath:     workspacePath,
		FromCommitID:      lastCommitID,
		ToCommitID:        currentCommitID,
		TotalChangedLines: totalChangedLines,
	}

	// 检查token数量，如果超限则截断commits
	if ShouldTruncateCommitDiff(diffInfo) {
		originalCommitCount := len(commits)
		truncatedDiff := TruncateCommitDiffToTokenLimit(diffInfo)
		log.Infof("[deepwiki-token-optimization] Commit diff exceeds token limit (%d), truncated from %d to %d commits\n",
			TokenLimit, originalCommitCount, len(truncatedDiff.Commits))
		return truncatedDiff, nil
	}

	return diffInfo, nil
}

func getFileChanges(repo *git.Repository, commit *object.Commit) ([]definition.FileChange, error) {
	var changes []definition.FileChange
	if commit.NumParents() == 0 {
		tree, err := commit.Tree()
		if err != nil {
			return nil, err
		}
		err = tree.Files().ForEach(func(file *object.File) error {
			// 对于初始提交，只标记为新增，不计算具体行数
			changes = append(changes, definition.FileChange{
				Status:       "Added",
				Path:         file.Name,
				AddedLines:   0, // 初始提交不计算行数
				DeletedLines: 0,
			})
			return nil
		})
		return changes, err
	}
	parent, err := commit.Parent(0)
	if err != nil {
		return nil, err
	}
	parentTree, err := parent.Tree()
	if err != nil {
		return nil, err
	}
	currentTree, err := commit.Tree()
	if err != nil {
		return nil, err
	}
	diff, err := parentTree.Diff(currentTree)
	if err != nil {
		return nil, err
	}

	for _, change := range diff {
		fileChange := definition.FileChange{
			Path: change.To.Name,
		}

		// 计算行数变化
		addedLines, deletedLines := calculateLineChanges(change)
		fileChange.AddedLines = addedLines
		fileChange.DeletedLines = deletedLines

		switch {
		case change.From.Name == "" && change.To.Name != "":
			fileChange.Status = "Added"
		case change.From.Name != "" && change.To.Name == "":
			fileChange.Status = "Deleted"
			fileChange.Path = change.From.Name
		case change.From.Name != change.To.Name:
			fileChange.Status = "Renamed"
			fileChange.OldPath = change.From.Name
		default:
			fileChange.Status = "Modified"
		}
		changes = append(changes, fileChange)
	}
	return changes, nil
}

// calculateLineChanges 计算文件变更的行数
func calculateLineChanges(change *object.Change) (addedLines, deletedLines int) {
	patch, err := change.Patch()
	if err != nil {
		// 如果无法获取patch信息，返回0
		return 0, 0
	}

	stats := patch.Stats()
	if len(stats) > 0 {
		return stats[0].Addition, stats[0].Deletion
	}

	return 0, 0
}

// IsCommitNewer returns true if commitA is newer than commitB.
func IsCommitNewer(repo *git.Repository, commitA, commitB string) (bool, error) {
	hashA := plumbing.NewHash(commitA)
	hashB := plumbing.NewHash(commitB)
	objA, err := repo.CommitObject(hashA)
	if err != nil {
		return false, err
	}
	objB, err := repo.CommitObject(hashB)
	if err != nil {
		return false, err
	}
	return objA.Author.When.After(objB.Author.When), nil
}

// IsCommitOlder returns true if commitA is older than commitB.
func IsCommitOlder(repo *git.Repository, commitA, commitB string) (bool, error) {
	hashA := plumbing.NewHash(commitA)
	hashB := plumbing.NewHash(commitB)
	objA, err := repo.CommitObject(hashA)
	if err != nil {
		return false, err
	}
	objB, err := repo.CommitObject(hashB)
	if err != nil {
		return false, err
	}
	return objA.Author.When.Before(objB.Author.When), nil
}

// IsGitRepo 判断当前目录是否为git仓库
func IsGitRepo(workspacePath string) (bool, error) {
	_, err := git.PlainOpen(workspacePath)
	if err == nil {
		return true, nil
	} else if errors.Is(err, git.ErrRepositoryNotExists) {
		return false, nil
	} else {
		return false, err
	}
}

// CheckAndTriggerIncrementalUpdate 封装检测并触发增量更新的逻辑，可用于项目打开和定时检测
// triggerFunc: 发现有新commit时的回调处理（如触发增量文档更新）
func CheckAndTriggerIncrementalUpdate(workspacePath, lastCommitID string, triggerFunc func(diffInfo *definition.CommitDiffInfo) error) error {
	diffInfo, err := GetCommitDiff(workspacePath, lastCommitID)
	if err != nil {
		return err
	}
	if diffInfo == nil || diffInfo.TotalCommits == 0 {
		return nil // 无需更新
	}
	return triggerFunc(diffInfo)
}

// Add a public method to access the underlying *git.Repository
func (g *GitSupport) GetRepository() *git.Repository {
	return g.repo
}

// GetFieldString 通过反射获取字符串字段值
func GetFieldString(v reflect.Value, fieldName string) string {
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}
	if v.Kind() != reflect.Struct {
		return ""
	}
	field := v.FieldByName(fieldName)
	if !field.IsValid() || field.Kind() != reflect.String {
		return ""
	}
	return field.String()
}

// GetFieldStringSlice 通过反射获取字符串切片字段值
func GetFieldStringSlice(v reflect.Value, fieldName string) []string {
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}
	if v.Kind() != reflect.Struct {
		return nil
	}
	field := v.FieldByName(fieldName)
	if !field.IsValid() || field.Kind() != reflect.Slice {
		return nil
	}

	var result []string
	for i := 0; i < field.Len(); i++ {
		elem := field.Index(i)
		if elem.Kind() == reflect.String {
			result = append(result, elem.String())
		}
	}
	return result
}

// BuildRelatedCommitsContent 构建相关提交信息的内容
// 接受任何类型的 relatedCommits 参数，通过反射处理 RelatedCommit 切片
func BuildRelatedCommitsContent(relatedCommits interface{}) string {
	if relatedCommits == nil {
		return ""
	}

	// 使用反射来处理 RelatedCommit 类型，避免导入循环
	rv := reflect.ValueOf(relatedCommits)
	if rv.Kind() != reflect.Slice || rv.Len() == 0 {
		return ""
	}

	var commitBuilder strings.Builder
	commitBuilder.WriteString("## Related Code Changes Analysis\n\n")
	commitBuilder.WriteString("This document update is based on the following code changes. Please carefully analyze how these changes affect the current document:\n\n")

	for i := 0; i < rv.Len(); i++ {
		commit := rv.Index(i)
		commitBuilder.WriteString(fmt.Sprintf("### Change %d\n", i+1))

		// 通过反射获取字段值
		if commitHash := GetFieldString(commit, "CommitHash"); commitHash != "" {
			commitBuilder.WriteString(fmt.Sprintf("**Commit Hash:** `%s`\n", commitHash))
		}
		if commitMessage := GetFieldString(commit, "CommitMessage"); commitMessage != "" {
			commitBuilder.WriteString(fmt.Sprintf("**Change Description:** %s\n", commitMessage))
		}
		if changeSummary := GetFieldString(commit, "ChangeSummary"); changeSummary != "" {
			commitBuilder.WriteString(fmt.Sprintf("**Change Summary:** %s\n", changeSummary))
		}

		// 获取文件变更数组
		if fileChanges := GetFieldStringSlice(commit, "FileChanges"); len(fileChanges) > 0 {
			commitBuilder.WriteString("**Affected Files:**\n")
			for _, fileChange := range fileChanges {
				commitBuilder.WriteString(fmt.Sprintf("- %s\n", fileChange))
			}
		}

		commitBuilder.WriteString("\n")
	}

	commitBuilder.WriteString("## Documentation Update Guidelines\n\n")
	commitBuilder.WriteString("Based on the above code changes, please:\n")
	commitBuilder.WriteString("1. **Analyze Change Impact**: Identify which changes directly affect the current document content\n")
	commitBuilder.WriteString("2. **Update Relevant Sections**: Modify affected document sections to accurately reflect the latest code state\n")
	commitBuilder.WriteString("3. **Add New Content**: If there are new features or components, add corresponding documentation\n")
	commitBuilder.WriteString("4. **Fix Outdated Information**: Remove or correct information that has become outdated due to code changes\n")
	commitBuilder.WriteString("5. **Maintain Consistency**: Ensure the updated documentation remains consistent with code implementation\n\n")

	return commitBuilder.String()
}
