package support

import (
	"cosy/definition"
	"cosy/tokenizer"
	"encoding/json"
	"fmt"
	"strings"
)

const (
	// TokenLimit 定义commit diff的token限制阈值
	TokenLimit = 85000
)

// CalculateCommitDiffTokens 计算commit diff信息的精确token数量(使用qwen tokenizer)
func CalculateCommitDiffTokens(diffInfo *definition.CommitDiffInfo) int {
	if diffInfo == nil {
		return 0
	}

	// 将commit diff信息序列化为JSON来计算token
	jsonData, _ := json.Marshal(diffInfo)

	// 使用qwen tokenizer精确计算token数量
	tokens := tokenizer.GetQwenTokenSize(string(jsonData))
	return tokens
}

// TruncateCommitDiffToTokenLimit 截断commit diff信息以符合token限制
// 保留最新的commits，直到达到token限制
func TruncateCommitDiffToTokenLimit(originalDiff *definition.CommitDiffInfo) *definition.CommitDiffInfo {
	if originalDiff == nil {
		return nil
	}

	if len(originalDiff.Commits) == 0 {
		return originalDiff
	}

	// 创建一个新的CommitDiffInfo来存储截断后的结果
	truncated := &definition.CommitDiffInfo{
		Commits:           make([]definition.CommitInfo, 0),
		TotalCommits:      originalDiff.TotalCommits, // 保留原始的总commit数
		WorkspacePath:     originalDiff.WorkspacePath,
		FromCommitID:      originalDiff.FromCommitID,
		ToCommitID:        originalDiff.ToCommitID,
		IsSimplified:      false, // 不是简化版本，而是截断版本
		TotalChangedLines: originalDiff.TotalChangedLines,
	}

	// 逐个添加commits，直到达到token限制
	for _, commit := range originalDiff.Commits {
		// 尝试添加这个commit
		tempCommits := make([]definition.CommitInfo, len(truncated.Commits)+1)
		copy(tempCommits, truncated.Commits)
		tempCommits[len(tempCommits)-1] = commit

		// 创建临时的CommitDiffInfo来计算token
		tempDiff := &definition.CommitDiffInfo{
			Commits:           tempCommits,
			TotalCommits:      len(tempCommits),
			WorkspacePath:     truncated.WorkspacePath,
			FromCommitID:      truncated.FromCommitID,
			ToCommitID:        truncated.ToCommitID,
			IsSimplified:      false,
			TotalChangedLines: 0, // 这里不需要精确计算，只是为了token计算
		}

		// 计算添加这个commit后的token数量
		tokens := CalculateCommitDiffTokens(tempDiff)
		if tokens > TokenLimit {
			// 如果添加这个commit会超过限制，则停止添加
			break
		}

		// 如果没有超过限制，则添加这个commit
		truncated.Commits = tempCommits
	}

	// 更新实际的commit数量
	truncated.TotalCommits = len(truncated.Commits)

	// 如果commits被截断了，更新FromCommitID为最后一个被包含的commit的hash
	// 这样下次检查时就能正确地从那个点开始
	if len(truncated.Commits) > 0 && len(truncated.Commits) < len(originalDiff.Commits) {
		lastIncludedCommit := truncated.Commits[len(truncated.Commits)-1]
		truncated.FromCommitID = lastIncludedCommit.Hash
	}

	return truncated
}

// ShouldTruncateCommitDiff 判断是否应该截断commit diff信息
func ShouldTruncateCommitDiff(diffInfo *definition.CommitDiffInfo) bool {
	if diffInfo == nil {
		return false
	}

	estimatedTokens := CalculateCommitDiffTokens(diffInfo)
	return estimatedTokens > TokenLimit
}

// ShouldSimplifyCommitDiff 判断是否应该简化commit diff信息
// 保留此函数以防其他地方仍在使用，但标记为deprecated
func ShouldSimplifyCommitDiff(diffInfo *definition.CommitDiffInfo) bool {
	if diffInfo == nil {
		return false
	}

	estimatedTokens := CalculateCommitDiffTokens(diffInfo)
	return estimatedTokens > TokenLimit
}

// GetCommitDiffSummary 获取commit diff的摘要信息
func GetCommitDiffSummary(diffInfo *definition.CommitDiffInfo) string {
	if diffInfo == nil {
		return "No commit diff information available"
	}

	var summary strings.Builder
	summary.WriteString(fmt.Sprintf("Total commits: %d\n", diffInfo.TotalCommits))
	summary.WriteString(fmt.Sprintf("From: %s to %s\n", diffInfo.FromCommitID, diffInfo.ToCommitID))

	// 统计总的文件变更数量
	totalFiles := 0
	for _, commit := range diffInfo.Commits {
		totalFiles += len(commit.FileChanges)
	}
	summary.WriteString(fmt.Sprintf("Total file changes: %d\n", totalFiles))

	// 列出commit信息
	summary.WriteString("Commits:\n")
	for i, commit := range diffInfo.Commits {
		if i >= 10 { // 最多显示10个commit
			summary.WriteString(fmt.Sprintf("... and %d more commits\n", len(diffInfo.Commits)-10))
			break
		}
		hashDisplay := commit.Hash
		if len(commit.Hash) > 8 {
			hashDisplay = commit.Hash[:8]
		}
		summary.WriteString(fmt.Sprintf("- %s: %s (%s)\n",
			hashDisplay,
			truncateString(commit.Message, 60),
			commit.Author))
	}

	return summary.String()
}

// truncateString 截断字符串到指定长度
func truncateString(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}
	return s[:maxLen-3] + "..."
}

// CalculateTextTokens 计算任意文本的token数量
func CalculateTextTokens(text string) int {
	return tokenizer.GetQwenTokenSize(text)
}
