package support

import (
	chainsCommon "cosy/chat/chains/common"
	"cosy/definition"
	"cosy/global"
	"cosy/log"
	"fmt"
	"os"
	"time"
)

// InitCurrentGraphStat 初始化当前图的统计信息
// 参数:
//
//	inputs - 输入的映射表，用于存储统计信息
//	graphName - 图的名称
func InitCurrentGraphStat(inputs map[string]any, graphName string) {
	inputs[chainsCommon.KeyWikiGenerateStatCurrentGraph] = &definition.WikiGenerateGraphStat{
		Name:              graphName,
		TotalInputTokens:  0,
		TotalOutputTokens: 0,
		TotalCachedTokens: 0,
		LlmCalls:          []definition.LlmCallStat{},
		ToolCalls:         []definition.ToolCallStat{},
		StartCall:         time.Now(),
	}
}

// AppendGraphStatToWikiStat 将当前图的统计信息追加到wiki的统计信息中
// 参数:
//
//	inputs - 输入的映射表，包含当前图的统计信息和wiki的统计信息
func AppendGraphStatToWikiStat(inputs map[string]any) {
	currentGraphStat, ok := inputs[chainsCommon.KeyWikiGenerateStatCurrentGraph].(*definition.WikiGenerateGraphStat)
	if !ok {
		return
	}
	currentGraphStat.EndCall = time.Now()

	// 计算当前图的token汇总统计
	currentGraphStat.CalcTotalTokens()

	wikiStat, ok := inputs[chainsCommon.KeyWikiGenerateStat].(*definition.DeepWikiAgentStats)
	if !ok {
		return
	}
	wikiStat.UpdateLock.Lock()
	defer wikiStat.UpdateLock.Unlock()

	wikiStat.GraphStats[currentGraphStat.Name] = currentGraphStat
}

// SaveWikiGenerateResponseToFile 保存catalogue structure AI响应到文件
func SaveWikiGenerateResponseToFile(response, stage string) error {
	if !global.IsEvaluationMode() {
		return nil
	}
	timestamp := time.Now().Format("20060102_150405")
	filename := fmt.Sprintf("chat/agents/deepwiki/deepwiki_output/ai_response_%s_%s.txt", stage, timestamp)

	// 确保目录存在
	if err := os.MkdirAll("chat/agents/deepwiki/deepwiki_output", 0755); err != nil {
		return fmt.Errorf("failed to create output directory: %w", err)
	}

	// 写入文件
	if err := os.WriteFile(filename, []byte(response), 0644); err != nil {
		return fmt.Errorf("failed to write response to file: %w", err)
	}

	log.Debugf("AI response saved to: %s", filename)
	return nil
}
