package support

import (
	"cosy/config"
	"cosy/definition"
	"cosy/experiment"
	"cosy/global"
	"cosy/log"
)

const (
	wikiDefaultConcurrent    = 1 // 默认并发任务数
	DefaultPreferredLanguage = "English"
)

// IsEnableWiki returns true if wiki is enabled
func IsEnableWiki() bool {
	if config.IsRemoteAgentMode() {
		log.Infof("In remote agent mode, skip wiki generate")
		return false
	}
	return experiment.ConfigService.GetBoolConfigWithEnv(definition.ExperimentWikiGlobalEnabled, experiment.ConfigScopeClient, true)
}

func GetConcurrentTaskCount(configKey string, evalTaskCount int) int {
	maxConcurrentTasks := experiment.ConfigService.GetIntConfigWithEnv(configKey, experiment.ConfigScopeClient, wikiDefaultConcurrent)
	if global.IsEvaluationMode() {
		// 在评估模式下，增加并发数
		maxConcurrentTasks = evalTaskCount
	}
	return maxConcurrentTasks
}
