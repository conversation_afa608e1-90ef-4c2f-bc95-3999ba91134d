package support

import (
	"context"
	"cosy/chat/chains/common"
	"cosy/deepwiki/queue"
	"cosy/deepwiki/storage"
	"cosy/definition"
	"cosy/log"
	"encoding/json"
	"fmt"
	"sync"
	"time"
)

// RecoveryCheckpoint 恢复检查点常量
const (
	CheckpointNotStarted               = "not_started" // 未开始，需要全量重建
	CheckpointReadmeCompleted          = "readme_completed"
	CheckpointOverviewCompleted        = "overview_completed"
	CheckpointCatalogueFilterCompleted = "catalogue_filter_completed"
	CheckpointCataloguePlanCompleted   = "catalogue_plan_completed"
	CheckpointCatalogueGenCompleted    = "catalogue_generation_completed"
	CheckpointWikiGenCompleted         = "wiki_generation_completed"
)

// WikiRecoveryService 中断恢复服务
type WikiRecoveryService struct {
	storageService *storage.LingmaWikiStorageService
	mutex          sync.RWMutex
	// 在单worker模式下，这里可以简单地用一个map来追踪正在处理的repo
	activeRepos map[string]bool
}

// TaskSubmitter 接口，用于重新提交任务到队列
type TaskSubmitter interface {
	SubmitTask(task *queue.Task) error
	GetQueueStatus() queue.QueueStatus
}

// NewWikiRecoveryService 创建恢复服务实例
func NewWikiRecoveryService(storageService *storage.LingmaWikiStorageService) *WikiRecoveryService {
	return &WikiRecoveryService{
		storageService: storageService,
		activeRepos:    make(map[string]bool),
	}
}

// RecoveryPoint 恢复点信息
type RecoveryPoint struct {
	Checkpoint          string `json:"checkpoint"`
	RequiresFullRebuild bool   `json:"requires_full_rebuild"`
	CommitChanged       bool   `json:"commit_changed"`
}

// RecoveryInputs 恢复时的输入参数
type RecoveryInputs struct {
	Inputs     map[string]any `json:"inputs"`
	Checkpoint string         `json:"checkpoint"`
}

// DetectFailedRepos 检测失败的repo并返回需要恢复的列表
func (r *WikiRecoveryService) DetectFailedRepos() ([]*definition.AgentWikiRepo, error) {
	log.Debugf("Detecting failed repos for recovery...")

	failedRepos, err := r.storageService.GetFailedRepos()
	if err != nil {
		return nil, fmt.Errorf("failed to get failed repos: %w", err)
	}

	log.Debugf("Found %d failed repos", len(failedRepos))
	return failedRepos, nil
}

// DetectOrphanedProcessingRepos 检测processing状态但无worker处理的repo
func (r *WikiRecoveryService) DetectOrphanedProcessingRepos() ([]*definition.AgentWikiRepo, error) {
	log.Debugf("Detecting orphaned processing repos...")

	processingRepos, err := r.storageService.GetProcessingRepos()
	if err != nil {
		return nil, fmt.Errorf("failed to get processing repos: %w", err)
	}

	r.mutex.RLock()
	defer r.mutex.RUnlock()

	var orphanedRepos []*definition.AgentWikiRepo
	for _, repo := range processingRepos {
		// 在单worker模式下，检查是否有活跃的worker在处理这个repo
		if !r.activeRepos[repo.ID] {
			// 额外检查：如果repo长时间没有更新（超过1小时），认为是孤儿进程
			if time.Since(repo.GmtModified) > time.Hour {
				log.Warnf("Found orphaned processing repo: %s (last modified: %v)", repo.WorkspacePath, repo.GmtModified)
				orphanedRepos = append(orphanedRepos, repo)
			}
		}
	}

	log.Debugf("Found %d orphaned processing repos", len(orphanedRepos))
	return orphanedRepos, nil
}

// AnalyzeRecoveryPoint 分析恢复起始点
func (r *WikiRecoveryService) AnalyzeRecoveryPoint(repo *definition.AgentWikiRepo, currentCommitID string) (*RecoveryPoint, error) {
	log.Debugf("Analyzing recovery point for repo: %s", repo.WorkspacePath)

	point := &RecoveryPoint{}

	// 检查commit ID是否变化
	if currentCommitID != "" && repo.LastCommitID != "" && currentCommitID != repo.LastCommitID {
		log.Debugf("Commit ID changed from %s to %s, requires full rebuild", repo.LastCommitID, currentCommitID)
		point.CommitChanged = true
		point.RequiresFullRebuild = true
		point.Checkpoint = CheckpointNotStarted // 从头开始，什么都没完成
		return point, nil
	}

	// 按顺序检查各阶段完成情况
	checkpoint, err := r.determineCheckpoint(repo)
	if err != nil {
		return nil, fmt.Errorf("failed to determine checkpoint: %w", err)
	}

	point.Checkpoint = checkpoint
	point.RequiresFullRebuild = false
	point.CommitChanged = false

	log.Debugf("Determined recovery checkpoint: %s for repo: %s", checkpoint, repo.WorkspacePath)
	return point, nil
}

// determineCheckpoint 确定恢复检查点
func (r *WikiRecoveryService) determineCheckpoint(repo *definition.AgentWikiRepo) (string, error) {
	// 1. 检查README是否存在
	readme, err := r.storageService.GetReadmeByRepoID(repo.ID)
	if err != nil {
		return "", fmt.Errorf("failed to check readme: %w", err)
	}
	if readme == nil {
		return CheckpointNotStarted, nil // README未生成，从头开始
	}

	// 2. 检查Overview是否存在
	overview, err := r.storageService.GetOverviewByRepoID(repo.ID)
	if err != nil {
		return "", fmt.Errorf("failed to check overview: %w", err)
	}
	if overview == nil {
		return CheckpointReadmeCompleted, nil // README已完成，从Overview开始
	}

	// 3. 检查优化后的目录是否存在
	if repo.OptimizedCatalog == "" {
		return CheckpointOverviewCompleted, nil // Overview已完成，从目录过滤开始
	}

	// 4. 检查目录规划思考内容是否存在
	if repo.CatalogueThinkContent == "" {
		return CheckpointCatalogueFilterCompleted, nil // 目录过滤已完成，从目录规划开始
	}

	// 5. 检查当前文档结构是否存在
	if repo.CurrentDocumentStructure == "" {
		return CheckpointCataloguePlanCompleted, nil // 目录规划已完成，从目录生成开始
	}

	// 6. 检查wiki generation是否完成
	// 获取repo下所有catalog的状态
	statusCount, err := r.storageService.GetCatalogStatusByRepoID(repo.ID)
	if err != nil {
		return "", fmt.Errorf("failed to get catalog status: %w", err)
	}

	totalCatalogs := 0
	for _, count := range statusCount {
		totalCatalogs += count
	}

	if totalCatalogs == 0 {
		// 没有catalog，说明目录生成已完成，从wiki生成开始
		return CheckpointCatalogueGenCompleted, nil
	}

	completedCount := statusCount[definition.DeepWikiProgressStatusCompleted]
	if completedCount < totalCatalogs {
		// 有未完成的catalog，说明wiki生成未完全完成，从wiki生成开始（部分恢复）
		return CheckpointCatalogueGenCompleted, nil
	}

	// 所有阶段都完成了，这种情况理论上不应该存在failed状态
	log.Warnf("All stages completed but repo is in failed state: %s", repo.WorkspacePath)
	return CheckpointWikiGenCompleted, nil
}

// BuildRecoveryInputs 构建恢复时的输入参数
func (r *WikiRecoveryService) BuildRecoveryInputs(repo *definition.AgentWikiRepo, checkpoint string) (*RecoveryInputs, error) {
	log.Debug("Building recovery inputs for checkpoint: %s, repo: %s", checkpoint, repo.WorkspacePath)

	inputs := make(map[string]any)

	// 基础请求参数
	request := definition.CreateDeepwikiRequest{
		WorkspacePath:     repo.WorkspacePath,
		PreferredLanguage: definition.DeepwikiPreferredLanguage,
	}

	inputs[common.KeyCreateDeepwikiRequest] = request

	switch checkpoint {
	case CheckpointNotStarted:
		// 从头开始，不需要额外的输入
		break

	case CheckpointReadmeCompleted:
		// 从Overview开始，需要README内容
		readme, err := r.storageService.GetReadmeByRepoID(repo.ID)
		if err != nil {
			return nil, fmt.Errorf("failed to get readme for recovery: %w", err)
		}
		if readme != nil {
			inputs[common.KeyReadmeContent] = readme.Content
		}

	case CheckpointOverviewCompleted:
		// 从CatalogueFilter开始，需要README和Overview内容
		readme, err := r.storageService.GetReadmeByRepoID(repo.ID)
		if err != nil {
			return nil, fmt.Errorf("failed to get readme for recovery: %w", err)
		}
		if readme != nil {
			inputs[common.KeyReadmeContent] = readme.Content
		}

		overview, err := r.storageService.GetOverviewByRepoID(repo.ID)
		if err != nil {
			return nil, fmt.Errorf("failed to get overview for recovery: %w", err)
		}
		if overview != nil {
			inputs[common.KeyOverviewContent] = overview.Content
		}

	case CheckpointCatalogueFilterCompleted:
		// 从CataloguePlan开始，需要README、Overview和OptimizedCatalogue
		readme, err := r.storageService.GetReadmeByRepoID(repo.ID)
		if err != nil {
			return nil, fmt.Errorf("failed to get readme for recovery: %w", err)
		}
		if readme != nil {
			inputs[common.KeyReadmeContent] = readme.Content
		}

		overview, err := r.storageService.GetOverviewByRepoID(repo.ID)
		if err != nil {
			return nil, fmt.Errorf("failed to get overview for recovery: %w", err)
		}
		if overview != nil {
			inputs[common.KeyOverviewContent] = overview.Content
		}

		if repo.OptimizedCatalog != "" {
			inputs[common.KeyOptimizedCatalogue] = repo.OptimizedCatalog
		}

	case CheckpointCataloguePlanCompleted:
		// 从CatalogueGeneration开始，需要前面的内容 + 思考内容
		if err := r.loadPreviousStageInputs(inputs, repo); err != nil {
			return nil, fmt.Errorf("failed to load previous stage inputs: %w", err)
		}

		if repo.CatalogueThinkContent != "" {
			inputs[common.KeyCatalogueThink] = repo.CatalogueThinkContent
		}

	case CheckpointCatalogueGenCompleted:
		// 从WikiGeneration开始，需要前面的内容 + 文档结构
		if err := r.loadPreviousStageInputs(inputs, repo); err != nil {
			return nil, fmt.Errorf("failed to load previous stage inputs: %w", err)
		}

		if repo.CatalogueThinkContent != "" {
			inputs[common.KeyCatalogueThink] = repo.CatalogueThinkContent
		}

	case CheckpointWikiGenCompleted:
		// 从wiki生成开始，需要重构catalog结果
		if err := r.loadPreviousStageInputs(inputs, repo); err != nil {
			return nil, fmt.Errorf("failed to load previous stage inputs: %w", err)
		}

		if repo.CurrentDocumentStructure != "" {
			catalogueResult, err := r.reconstructCatalogueResult(repo)
			if err != nil {
				return nil, fmt.Errorf("failed to reconstruct catalogue result: %w", err)
			}
			inputs[common.KeyCatalogueResult] = catalogueResult
		}
	}

	return &RecoveryInputs{
		Inputs:     inputs,
		Checkpoint: checkpoint,
	}, nil
}

// loadPreviousStageInputs 加载前面阶段的输入
func (r *WikiRecoveryService) loadPreviousStageInputs(inputs map[string]any, repo *definition.AgentWikiRepo) error {
	// 加载README
	readme, err := r.storageService.GetReadmeByRepoID(repo.ID)
	if err != nil {
		return fmt.Errorf("failed to get readme: %w", err)
	}
	if readme != nil {
		inputs[common.KeyReadmeContent] = readme.Content
	}

	// 加载Overview
	overview, err := r.storageService.GetOverviewByRepoID(repo.ID)
	if err != nil {
		return fmt.Errorf("failed to get overview: %w", err)
	}
	if overview != nil {
		inputs[common.KeyOverviewContent] = overview.Content
	}

	// 加载优化后的目录
	if repo.OptimizedCatalog != "" {
		inputs[common.KeyOptimizedCatalogue] = repo.OptimizedCatalog
	}

	return nil
}

// reconstructCatalogueResult 重构CatalogueResult对象
func (r *WikiRecoveryService) reconstructCatalogueResult(repo *definition.AgentWikiRepo) (*definition.CatalogueResult, error) {
	if repo.CurrentDocumentStructure == "" {
		return nil, fmt.Errorf("current document structure is empty")
	}

	var documentCatalogues []definition.DocumentCatalog
	err := json.Unmarshal([]byte(repo.CurrentDocumentStructure), &documentCatalogues)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal document structure: %w", err)
	}

	catalogueResult := &definition.CatalogueResult{
		DocumentCatalogs: documentCatalogues,
		RepositoryName:   repo.Name,
	}

	return catalogueResult, nil
}

// MarkRepoAsActive 标记repo为活跃状态（正在处理中）
func (r *WikiRecoveryService) MarkRepoAsActive(repoID string) {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	r.activeRepos[repoID] = true
	log.Debugf("Marked repo as active: %s", repoID)
}

// MarkRepoAsInactive 标记repo为非活跃状态
func (r *WikiRecoveryService) MarkRepoAsInactive(repoID string) {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	delete(r.activeRepos, repoID)
	log.Debugf("Marked repo as inactive: %s", repoID)
}

// IsRepoActive 检查repo是否活跃
func (r *WikiRecoveryService) IsRepoActive(repoID string) bool {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	return r.activeRepos[repoID]
}

// ExecuteRecovery 执行恢复操作
func (r *WikiRecoveryService) ExecuteRecovery(ctx context.Context, repo *definition.AgentWikiRepo, recoveryPoint *RecoveryPoint, recoveryInputs *RecoveryInputs) error {
	log.Debugf("Starting recovery for repo: %s, checkpoint: %s", repo.WorkspacePath, recoveryPoint.Checkpoint)

	// 标记repo为活跃状态
	r.MarkRepoAsActive(repo.ID)
	defer r.MarkRepoAsInactive(repo.ID)

	// 更新恢复检查点
	err := r.storageService.UpdateRecoveryCheckpoint(repo.ID, recoveryPoint.Checkpoint)
	if err != nil {
		return fmt.Errorf("failed to update recovery checkpoint: %w", err)
	}

	// 将repo状态改为processing
	repo.ProgressStatus = definition.DeepWikiProgressStatusProcessing
	err = r.storageService.UpdateWikiRepo(repo)
	if err != nil {
		return fmt.Errorf("failed to update repo status to processing: %w", err)
	}

	log.Debugf("Successfully started recovery process for repo: %s", repo.WorkspacePath)
	return nil
}

// PerformStartupRecoveryCheck 执行启动时的恢复检查
func (r *WikiRecoveryService) PerformStartupRecoveryCheck(ctx context.Context, taskSubmitter TaskSubmitter) error {
	// 禁止remote agent模式下进行启动时的恢复检查
	if !IsEnableWiki() {
		log.Info("Skip wiki skip startup recovery check")
		return nil
	}

	log.Debugf("Starting startup recovery check...")

	// 1. 检测孤儿进程 - processing状态但没有对应的worker
	orphanedRepos, err := r.DetectOrphanedRepos(ctx, taskSubmitter)
	if err != nil {
		log.Errorf("Failed to detect orphaned repos: %v", err)
	} else if len(orphanedRepos) > 0 {
		log.Debugf("Found %d orphaned repos, re-queuing them...", len(orphanedRepos))
		for _, repo := range orphanedRepos {
			err := r.RequeueOrphanedRepo(ctx, repo, taskSubmitter)
			if err != nil {
				log.Errorf("Failed to requeue orphaned repo %s: %v", repo.ID, err)
			}
		}
	}

	// 2. 检测真正失败的repo并进行恢复
	failedRepos, err := r.storageService.GetFailedRepos()
	if err != nil {
		log.Errorf("Failed to get failed repos: %v", err)
		return fmt.Errorf("failed to get failed repos: %w", err)
	}

	if len(failedRepos) == 0 {
		log.Debugf("No failed repos found, recovery check completed")
		return nil
	}

	log.Debugf("Found %d failed repos, starting recovery process...", len(failedRepos))

	// 为每个failed repo执行恢复准备，将其状态设置为pending
	var recoveredCount int
	for _, repo := range failedRepos {
		err := r.RecoverRepo(ctx, repo)
		if err != nil {
			log.Errorf("Failed to prepare repo for recovery %s: %v", repo.ID, err)
		} else {
			recoveredCount++
		}
	}

	// 3. 为已准备好的failed repos创建任务并提交到队列
	// 注意：我们将这些repo状态已设置为pending，现在需要提交全量生成任务
	if recoveredCount > 0 {
		log.Infof("Prepared %d failed repos for recovery, now submitting recovery tasks...", recoveredCount)

		// 重新获取pending状态的repos（刚刚从failed转为pending的）
		for _, repo := range failedRepos {
			if repo.ProgressStatus == definition.DeepWikiProgressStatusPending {
				// 创建恢复任务并提交到队列
				request := definition.CreateDeepwikiRequest{
					WorkspacePath:     repo.WorkspacePath,
					RequestId:         fmt.Sprintf("recovery-%s-%d", repo.ID, time.Now().Unix()),
					PreferredLanguage: definition.DeepwikiPreferredLanguage,
				}

				task := queue.NewFullGenerationTask(ctx, request)
				err := taskSubmitter.SubmitTask(task)
				if err != nil {
					log.Errorf("Failed to submit recovery task for repo %s: %v", repo.ID, err)
					// 如果提交失败，将状态改回failed，避免丢失状态
					repo.ProgressStatus = definition.DeepWikiProgressStatusFailed
					r.storageService.UpdateWikiRepo(repo)
				} else {
					log.Infof("Successfully submitted recovery task for repo %s as task %s", repo.ID, task.ID)
				}
			}
		}
	}

	log.Debugf("Recovery check completed: %d/%d repos prepared for recovery", recoveredCount, len(failedRepos))
	return nil
}

// DetectOrphanedRepos 检测孤儿进程 - processing状态但队列中没有对应任务
func (r *WikiRecoveryService) DetectOrphanedRepos(ctx context.Context, taskSubmitter TaskSubmitter) ([]*definition.AgentWikiRepo, error) {
	// 获取所有processing状态的repo
	processingRepos, err := r.storageService.GetProcessingRepos()
	if err != nil {
		return nil, fmt.Errorf("failed to get processing repos: %w", err)
	}

	if len(processingRepos) == 0 {
		return nil, nil
	}

	var orphanedRepos []*definition.AgentWikiRepo

	// 检查每个processing repo是否在队列中有对应的任务
	for _, repo := range processingRepos {
		isOrphaned := r.isRepoOrphaned(repo, taskSubmitter)
		if isOrphaned {
			log.Warnf("Found orphaned repo: %s (workspace: %s) - processing status but no active task", repo.ID, repo.WorkspacePath)
			orphanedRepos = append(orphanedRepos, repo)
		}
	}

	return orphanedRepos, nil
}

// isRepoOrphaned 检查repo是否为孤儿进程的改进版本
func (r *WikiRecoveryService) isRepoOrphaned(repo *definition.AgentWikiRepo, taskSubmitter TaskSubmitter) bool {
	// 首先检查最后修改时间，超过阈值才可能是孤儿进程
	orphanThreshold := 5 * time.Minute
	if time.Since(repo.GmtModified) <= orphanThreshold {
		log.Debugf("Repo %s was modified recently (%v ago), not considering as orphaned", repo.ID, time.Since(repo.GmtModified))
		return false
	}

	// 使用TaskQueueManager的精确检查方法（如果可用）
	if taskQueueManager, ok := taskSubmitter.(interface {
		GetTasksByWorkspace(workspacePath string) []*queue.Task
	}); ok {
		tasks := taskQueueManager.GetTasksByWorkspace(repo.WorkspacePath)
		for _, task := range tasks {
			if !task.IsFinished() {
				log.Debugf("Repo %s has active task %s, not orphaned", repo.ID, task.ID)
				return false // 有未完成的任务，不是孤儿进程
			}
		}
		log.Debugf("Repo %s has no active tasks and was modified %v ago, considering as orphaned", repo.ID, time.Since(repo.GmtModified))
		return true
	}

	// 回退到原有的简单检查（兼容性）
	return r.hasActiveTaskInQueueLegacy(repo.WorkspacePath, taskSubmitter.GetQueueStatus())
}

// hasActiveTaskInQueueLegacy 原有的简单检查方法（保持向后兼容）
func (r *WikiRecoveryService) hasActiveTaskInQueueLegacy(workspacePath string, queueStatus queue.QueueStatus) bool {
	// 如果队列中有待处理或正在处理的任务，可能包含该workspace
	// 在单worker模式下，这个检查相对简单
	totalActiveTasks := queueStatus.Tasks[queue.TaskStatusPending] + queueStatus.Tasks[queue.TaskStatusProcessing]

	// 如果没有活跃任务，肯定是孤儿进程
	if totalActiveTasks == 0 {
		return false
	}

	log.Debugf("Queue has %d active tasks, assuming workspace %s may have active task (legacy check)", totalActiveTasks, workspacePath)
	return totalActiveTasks > 0
}

// hasActiveTaskInQueue 检查指定workspace是否在队列中有活跃任务 - 废弃，使用isRepoOrphaned替代
// todo 这是一个简化的检查，实际实现可能需要更复杂的逻辑
func (r *WikiRecoveryService) hasActiveTaskInQueue(workspacePath string, queueStatus queue.QueueStatus) bool {
	// 标记为废弃，使用新的isRepoOrphaned方法
	return r.hasActiveTaskInQueueLegacy(workspacePath, queueStatus)
}

// RequeueOrphanedRepo 将孤儿repo重新放入队列
func (r *WikiRecoveryService) RequeueOrphanedRepo(ctx context.Context, repo *definition.AgentWikiRepo, taskSubmitter TaskSubmitter) error {
	// 禁止remote agent模式下进行孤儿repo恢复任务提交
	if !IsEnableWiki() {
		log.Infof("Skip wiki requeuing orphaned repo for workspace: %s", repo.WorkspacePath)
		return nil
	}

	log.Debugf("Re-queuing orphaned repo: %s (workspace: %s)", repo.ID, repo.WorkspacePath)

	// 1. 将repo状态重置为pending，以便队列系统可以重新处理
	repo.ProgressStatus = definition.DeepWikiProgressStatusPending
	err := r.storageService.UpdateWikiRepo(repo)
	if err != nil {
		return fmt.Errorf("failed to update repo status to pending: %w", err)
	}

	// 2. 创建新的全量生成任务并提交到队列
	request := definition.CreateDeepwikiRequest{
		WorkspacePath:     repo.WorkspacePath,
		RequestId:         fmt.Sprintf("recovery-%s-%d", repo.ID, time.Now().Unix()),
		PreferredLanguage: definition.DeepwikiPreferredLanguage,
	}

	task := queue.NewFullGenerationTask(ctx, request)
	err = taskSubmitter.SubmitTask(task)
	if err != nil {
		// 如果提交失败，将状态改回processing，避免丢失状态
		repo.ProgressStatus = definition.DeepWikiProgressStatusProcessing
		r.storageService.UpdateWikiRepo(repo)
		return fmt.Errorf("failed to submit recovery task: %w", err)
	}

	log.Debugf("Successfully re-queued orphaned repo %s as task %s", repo.ID, task.ID)
	return nil
}

// RecoverRepo 处理单个repo的恢复
func (r *WikiRecoveryService) RecoverRepo(ctx context.Context, repo *definition.AgentWikiRepo) error {
	log.Infof("Handling recovery for repo: %s", repo.WorkspacePath)

	// 分析恢复点（这里暂时不检查commit变化，实际使用时可以传入当前commit）
	recoveryPoint, err := r.AnalyzeRecoveryPoint(repo, "")
	if err != nil {
		return fmt.Errorf("failed to analyze recovery point: %w", err)
	}

	// 将repo状态设置为pending，然后通过正常的GenerateUpdate流程处理

	// 1. 将repo状态重置为pending，以便正常流程可以处理
	repo.ProgressStatus = definition.DeepWikiProgressStatusPending
	err = r.storageService.UpdateWikiRepo(repo)
	if err != nil {
		return fmt.Errorf("failed to update repo status to pending: %w", err)
	}

	// 2. 更新恢复检查点，确保恢复数据可用
	err = r.storageService.UpdateRecoveryCheckpoint(repo.ID, recoveryPoint.Checkpoint)
	if err != nil {
		log.Warnf("Failed to update recovery checkpoint for repo %s: %v", repo.ID, err)
		// 不返回错误，继续处理
	}

	log.Infof("Successfully prepared repo for recovery: %s (status: pending, checkpoint: %s)",
		repo.WorkspacePath, recoveryPoint.Checkpoint)
	return nil
}

// RecoveryServiceManager 恢复服务管理器
type RecoveryServiceManager struct {
	recoveryService *WikiRecoveryService
}

// NewRecoveryServiceManager 创建恢复服务管理器
func NewRecoveryServiceManager(storageService *storage.LingmaWikiStorageService) *RecoveryServiceManager {
	return &RecoveryServiceManager{
		recoveryService: NewWikiRecoveryService(storageService),
	}
}

// StartupRecoveryCheck 启动时的恢复检查 - 在lingma进程启动时调用
func (m *RecoveryServiceManager) StartupRecoveryCheck(ctx context.Context, taskSubmitter TaskSubmitter) error {
	log.Debugf("Starting wiki recovery check on lingma startup...")

	err := m.recoveryService.PerformStartupRecoveryCheck(ctx, taskSubmitter)
	if err != nil {
		log.Errorf("Failed to perform startup recovery check: %v", err)
		return fmt.Errorf("startup recovery check failed: %w", err)
	}

	log.Debugf("Wiki recovery check completed successfully")
	return nil
}

// ManualRecoveryForRepo 手动恢复指定的repo
func (m *RecoveryServiceManager) ManualRecoveryForRepo(ctx context.Context, workspacePath, currentCommitID string) error {
	log.Debugf("Starting manual recovery for repo: %s", workspacePath)

	// 获取repo信息
	repo, err := m.recoveryService.storageService.GetWikiRepoByWorkspacePath(workspacePath)
	if err != nil {
		return fmt.Errorf("failed to get repo: %w", err)
	}

	if repo == nil {
		return fmt.Errorf("repo not found: %s", workspacePath)
	}

	// 分析恢复点
	recoveryPoint, err := m.recoveryService.AnalyzeRecoveryPoint(repo, currentCommitID)
	if err != nil {
		return fmt.Errorf("failed to analyze recovery point: %w", err)
	}

	// 构建恢复输入
	recoveryInputs, err := m.recoveryService.BuildRecoveryInputs(repo, recoveryPoint.Checkpoint)
	if err != nil {
		return fmt.Errorf("failed to build recovery inputs: %w", err)
	}

	// 执行恢复
	err = m.recoveryService.ExecuteRecovery(ctx, repo, recoveryPoint, recoveryInputs)
	if err != nil {
		return fmt.Errorf("failed to execute recovery: %w", err)
	}

	log.Debugf("Manual recovery completed for repo: %s", workspacePath)
	return nil
}

// GetRecoveryStatus 获取恢复状态信息
func (m *RecoveryServiceManager) GetRecoveryStatus(taskSubmitter TaskSubmitter) (*RecoveryStatus, error) {
	// 获取失败的repo数量
	failedRepos, err := m.recoveryService.storageService.GetFailedRepos()
	if err != nil {
		return nil, fmt.Errorf("failed to get failed repos: %w", err)
	}

	// 获取孤儿processing repo数量
	orphanedRepos, err := m.recoveryService.DetectOrphanedRepos(context.Background(), taskSubmitter)
	if err != nil {
		return nil, fmt.Errorf("failed to detect orphaned repos: %w", err)
	}

	status := &RecoveryStatus{
		FailedReposCount:   len(failedRepos),
		OrphanedReposCount: len(orphanedRepos),
		TotalNeedRecovery:  len(failedRepos) + len(orphanedRepos),
		ActiveReposCount:   len(m.recoveryService.activeRepos),
	}

	return status, nil
}

// GetRecoveryService 获取底层的恢复服务实例
func (m *RecoveryServiceManager) GetRecoveryService() *WikiRecoveryService {
	return m.recoveryService
}

// UpdateCheckpointOnSuccess 在阶段成功完成后更新检查点
// 这是一个全局辅助函数，供各个chain在成功完成后调用
func UpdateCheckpointOnSuccess(workspacePath, checkpoint string) {
	if !IsEnableWiki() {
		return
	}

	repo, err := storage.GlobalStorageService.GetWikiRepoByWorkspacePath(workspacePath)
	if err != nil {
		log.Warnf("Failed to get repo for checkpoint update: %v", err)
		return
	}

	if repo == nil {
		log.Warnf("Repo not found for checkpoint update: %s", workspacePath)
		return
	}

	// 只有在pending或processing状态下才更新检查点
	if repo.ProgressStatus != definition.DeepWikiProgressStatusPending &&
		repo.ProgressStatus != definition.DeepWikiProgressStatusProcessing {
		log.Debugf("Skipping checkpoint update for repo in %s status", repo.ProgressStatus)
		return
	}

	err = storage.GlobalStorageService.UpdateRecoveryCheckpoint(repo.ID, checkpoint)
	if err != nil {
		log.Warnf("Failed to update checkpoint to %s for workspace %s: %v", checkpoint, workspacePath, err)
	} else {
		log.Infof("Successfully updated recovery checkpoint to: %s for workspace: %s", checkpoint, workspacePath)
	}
}

// RecoveryStatus 恢复状态信息
type RecoveryStatus struct {
	FailedReposCount   int `json:"failed_repos_count"`
	OrphanedReposCount int `json:"orphaned_repos_count"`
	TotalNeedRecovery  int `json:"total_need_recovery"`
	ActiveReposCount   int `json:"active_repos_count"`
}
