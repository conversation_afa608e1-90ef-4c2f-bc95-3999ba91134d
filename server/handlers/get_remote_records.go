package handlers

import (
	"context"
	"cosy/definition"
	"cosy/log"
	"cosy/longruntask"
	"cosy/websocket"
	"sort"
)

func GetRemoteRecordsHandler(ctx context.Context, req *websocket.WireRequest) error {
	params, err := parseRequestParameter[definition.GetRemoteAgentRecordsRequest](ctx, req)
	if err != nil {
		reply(ctx, req, definition.NewErrorResponse("InvalidParams", err.<PERSON>rror(), nil))
		return err
	}

	pageNumber := params.PageNumber
	if pageNumber <= 0 {
		pageNumber = 1
	}
	pageSize := params.PageSize
	if pageSize < 10 || pageSize > 100 {
		pageSize = 10
	}

	taskMrg := longruntask.NewTaskManager()
	resp := []definition.ChatRecord{}
	for page := 1; ; page++ {
		records, err := taskMrg.GetSessionRecords(ctx, params.SessionId, pageSize, page)
		if len(records.Items) == 0 {
			break
		}
		if err != nil {
			log.Error(err)
			continue
		}
		for _, item := range records.Items {
			if item.Mode == definition.SessionModeLongRunning {
				resp = append(resp, item)
			}
		}
		if page*pageSize > records.TotalSize {
			break
		}
	}
	sort.Slice(resp, func(i, j int) bool {
		return resp[i].GmtCreate < resp[j].GmtCreate
	})

	pagedResult := longruntask.GetSessionRecordsResp{
		PageNumber: 1,
		PageSize:   len(resp),
		TotalSize:  len(resp),
		Items:      resp,
	}

	reply(ctx, req, definition.NewSuccessResponse(pagedResult))
	return nil
}
