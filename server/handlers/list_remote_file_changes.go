package handlers

import (
	"context"
	"cosy/chat/service"
	"cosy/config"
	"cosy/definition"
	"cosy/log"
	"cosy/websocket"
	"fmt"
	"strings"
)

func ListRemoteFileChangesHandler(ctx context.Context, req *websocket.WireRequest) error {
	params, err := parseRequestParameter[definition.ListRemoteFileChangesRequestParams](ctx, req)
	if err != nil {
		reply(ctx, req, definition.NewErrorResponse("InvalidParams", err.Error(), nil))
		return err
	}
	if params.SessionId == "" {
		err := fmt.Errorf("SessionId is required for remote/fileChanges/list")
		log.Error(err)
		reply(ctx, req, definition.NewErrorResponse("InvalidParams", err.Error(), nil))
		return err
	}
	fileVOList, err := service.WorkingSpaceServiceManager.ListFileChangesBySessionId(ctx, params.SessionId)
	if err != nil {
		// 可能获取某个文件有错误，不应影响整体数据返回
		log.Error(err)
		if len(fileVOList) == 0 {
			// 没有数据就返回错误
			reply(ctx, req, definition.NewErrorResponse("InternalError", err.Error(), nil))
			return err
		}
		// 若有数据则返回数据
	}
	data := definition.ListRemoteFileChangesResponse{
		FileChanges: make([]definition.RemoteFileChangeInfo, 0),
	}
	var filteredFileVOList []definition.RemoteFileChangeInfo
	for _, f := range fileVOList {
		if f.BeforeContent == f.AfterContent {
			// 过滤掉 前后改动相同的文件
			continue
		}
		if strings.HasPrefix(f.FileId, config.GetRemoteAgentWorkspacePath()) {
			// 将沙箱里的绝对路径转换成相对路径
			f.FileId = strings.Replace(f.FileId, config.GetRemoteAgentWorkspacePath(), ".", 1)
		}
		filteredFileVOList = append(filteredFileVOList, *definition.ParseRemoteFileChangeInfo(&f))
	}
	if len(filteredFileVOList) > 0 {
		data.FileChanges = filteredFileVOList
	}
	reply(ctx, req, definition.NewSuccessResponse(data))
	return nil
}
