package handlers

import (
	"context"
	"cosy/definition"
	"cosy/log"
	"cosy/longruntask"
	"cosy/websocket"
)

func DeleteTaskHandler(ctx context.Context, req *websocket.WireRequest) error {
	params, err := parseRequestParameter[definition.DeleteChatTaskParams](ctx, req)
	if err != nil {
		reply(ctx, req, definition.NewErrorResponse("InvalidParams", err.Error(), nil))
		return err
	}

	log.Info("[chat][deleteTask] params=%v", params)
	// 删除任务
	response := longruntask.DeleteChatTask(params.Id)

	reply(ctx, req, response)
	return nil
}
