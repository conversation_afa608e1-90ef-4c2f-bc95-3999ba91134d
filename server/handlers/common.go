package handlers

import (
	"context"
	"cosy/log"
	"cosy/websocket"
	"encoding/json"
)

var (
	handlerMap = map[string]HandlerFunc{}
)

type HandlerFunc func(ctx context.Context, req *websocket.WireRequest) error

func init() {
	// Remote相关
	mustRegisterHandler("remote/fileChanges/get", GetRemoteFileChangeHandler)
	mustRegisterHandler("remote/fileChanges/list", ListRemoteFileChangesHandler)

	// Session相关
	mustRegisterHandler("session/getRemoteMessages", GetRemoteMessagesHandler)
	mustRegisterHandler("session/getRemoteRecords", GetRemoteRecordsHandler)

	// Task基本CRUD操作
	mustRegisterHandler("task/list", ListTasksHandler)
	mustRegisterHandler("task/getById", GetTaskByIdHandler)
	mustRegisterHandler("task/create", CreateTaskHandler)
	mustRegister<PERSON>andler("task/update", UpdateTaskHandler)
	mustReg<PERSON><PERSON>andler("task/update/status", UpdateTaskStatusHandler)
	mustRegisterHandler("task/delete", DeleteTaskHandler)

	// Task执行相关
	mustRegisterHandler("task/execute", ExecuteTaskHandler)
	mustRegisterHandler("task/cancel", CancelTaskHandler)
	mustRegisterHandler("task/boot/log", GetTaskBootLogHandler)

	// Task内容相关
	mustRegisterHandler("task/design/update", UpdateTaskDesignHandler)
	mustRegisterHandler("task/design/content", GetTaskDesignContentHandler)
	mustRegisterHandler("task/actionFlow/content", GetTaskActionFlowContentHandler)
	mustRegisterHandler("task/report/content", GetTaskReportContentHandler)
	mustRegisterHandler("task/fileChanges/content", GetTaskFileChangesHandler)

	// Task其他功能
	mustRegisterHandler("task/websocketEndpoint", GetTaskWebsocketEndpointHandler)
	mustRegisterHandler("task/user/stats", GetUserTaskStatsHandler)
	mustRegisterHandler("task/quotas", GetTaskQuotasHandler)

	// WorkingSpaceFile相关
	mustRegisterHandler("workingSpaceFile/getFile", GetWorkspaceLatestFile)

	// Agents相关
	mustRegisterHandler("agents/master/sendNotice", SendAgentNoticeHandler)
}

// mustRegisterHandler 如果handler重复定义，则panic快速挂掉
func mustRegisterHandler(name string, h HandlerFunc) {
	_, ok := handlerMap[name]
	if ok {
		panic("handler " + name + " is already registered")
	}
	handlerMap[name] = h
}

func GetHandler(method string) HandlerFunc {
	return handlerMap[method]
}

func parseRequestParameter[T any](ctx context.Context, req *websocket.WireRequest) (*T, error) {
	var params T
	if err := json.Unmarshal(*req.Params, &params); err != nil {
		log.Errorf("Failed to parse params, err: %v", err)
		return nil, err
	}
	return &params, nil
}

func reply(ctx context.Context, request *websocket.WireRequest, response interface{}) {
	if e := websocket.WsInst.Reply(ctx, request, response, nil); e != nil {
		log.Errorf("Failed to send request to socket client, err: %v", e)
	}
}
