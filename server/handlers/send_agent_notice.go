package handlers

import (
	"context"
	"cosy/chat/agents/support"
	"cosy/definition"
	"cosy/log"
	"cosy/websocket"
)

func SendAgentNoticeHandler(ctx context.Context, req *websocket.WireRequest) error {
	params, err := parseRequestParameter[definition.AgentStateInNoticeParam](ctx, req)
	if err != nil {
		reply(ctx, req, definition.NewErrorResponse("InvalidParams", err.Error(), nil))
		return err
	}

	stateInNotice := support.StateInNotice{
		Type: params.NoticeType,
	}
	success := true
	errMsg := ""
	err = support.ProcessStateInNotice(ctx, params.RequestId, stateInNotice)
	if err != nil {
		log.Errorf("Failed to send notice to agent: %v", err)
		errMsg = err.Error()
		success = false
	}

	result := definition.NewSuccessResponse(nil)
	if !success {
		result = definition.NewErrorResponse("InternalError", errMsg, nil)
	}
	reply(ctx, req, result)
	return nil
}
