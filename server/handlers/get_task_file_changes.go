package handlers

import (
	"context"
	"cosy/definition"
	"cosy/log"
	"cosy/longruntask"
	"cosy/websocket"
)

func GetTaskFileChangesHandler(ctx context.Context, req *websocket.WireRequest) error {
	params, err := parseRequestParameter[definition.GetFileChangesContentRequestParams](ctx, req)
	if err != nil {
		reply(ctx, req, definition.NewErrorResponse("InvalidParams", err.Error(), nil))
		return err
	}
	taskMgr := longruntask.NewTaskManager()
	resp, err := taskMgr.GetFileChanges(ctx, params.TaskId, params.PageSize, params.PageNumber)
	if err != nil {
		log.Errorf("Failed to get fileChanges: %v", err)
		reply(ctx, req, definition.NewErrorResponse("InternalError", err.Error(), nil))
		return err
	}

	reply(ctx, req, definition.NewSuccessResponse(resp))
	return nil
}
