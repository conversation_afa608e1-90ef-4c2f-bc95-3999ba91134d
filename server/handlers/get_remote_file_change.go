package handlers

import (
	"context"
	"cosy/chat/service"
	"cosy/config"
	"cosy/definition"
	"cosy/log"
	"cosy/websocket"
	"fmt"
	"path/filepath"
	"strings"
)

func GetRemoteFileChangeHandler(ctx context.Context, req *websocket.WireRequest) error {
	params, err := parseRequestParameter[definition.GetRemoteFileChangesRequestParams](ctx, req)
	if err != nil {
		reply(ctx, req, definition.NewErrorResponse("InvalidParams", err.Error(), nil))
		return err
	}
	if params.SessionId == "" {
		err := fmt.Errorf("SessionId is required for remote/fileChanges/get")
		log.Error(err)
		reply(ctx, req, definition.NewErrorResponse("InvalidParams", err.Error(), nil))
		return err
	}
	if params.FileId == "" {
		err := fmt.Errorf("FileId is required for remote/fileChanges/get")
		log.Error(err)
		reply(ctx, req, definition.NewErrorResponse("InvalidParams", err.Error(), nil))
		return err
	}
	filePath := params.FileId
	if strings.HasPrefix(params.FileId, "./") {
		// 相对路径转换为绝对路径
		filePath = filepath.Join(config.GetRemoteAgentWorkspacePath(), params.FileId)
	}
	var result any
	fileVO, err := service.WorkingSpaceServiceManager.GetFileChangeBySessionId(ctx, params.SessionId, filePath)
	if err != nil {
		// 可能获取某个文件有错误，不应影响整体数据返回
		log.Error(err)
		result = definition.NewErrorResponse("InternalError", err.Error(), &definition.RemoteFileChangeInfo{})
	} else {
		result = definition.NewSuccessResponse(definition.GetRemoteFileChangesResponse{
			FileChange: definition.ParseRemoteFileChangeInfo(fileVO),
		})
	}

	reply(ctx, req, result)
	return nil
}
