package handlers

import (
	"context"
	"cosy/chat/service"
	"cosy/definition"
	"cosy/log"
	"cosy/websocket"
	"fmt"
)

func GetWorkspaceLatestFile(ctx context.Context, req *websocket.WireRequest) error {
	params, err := parseRequestParameter[definition.WorkingSpaceFileGetContentParams](ctx, req)
	if err != nil {
		reply(ctx, req, definition.NewErrorResponse("InvalidParams", err.Error(), nil))
		return err
	}
	if params.FileId == "" {
		err := fmt.Errorf("fileId is required for workingSpaceFile/getFile")
		reply(ctx, req, definition.NewErrorResponse("InvalidParams", err.Error(), nil))
		return err
	}
	filePath := params.FileId

	content, err := service.GetFileContent(filePath)
	if err != nil {
		log.Errorf("Failed to get file content: %v", err)
		reply(ctx, req, definition.NewErrorResponse("InternalError", err.Error(), nil))
		return err
	}
	data := map[string]string{
		"content": content,
		"fileId":  filePath,
	}
	reply(ctx, req, definition.NewSuccessResponse(data))
	return nil
}
