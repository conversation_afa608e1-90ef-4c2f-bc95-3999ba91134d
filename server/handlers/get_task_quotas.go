package handlers

import (
	"context"
	"cosy/definition"
	"cosy/longruntask"
	"cosy/websocket"
)

func GetTaskQuotasHandler(ctx context.Context, req *websocket.WireRequest) error {
	params, err := parseRequestParameter[definition.GetUserChatTaskQuotasRequest](ctx, req)
	if err != nil {
		reply(ctx, req, definition.NewErrorResponse("InvalidParams", err.Error(), nil))
		return err
	}

	resp := longruntask.GetUserChatTaskQuotas(ctx, params.Resource)

	reply(ctx, req, resp)
	return nil
}
