package handlers

import (
	"context"
	"cosy/definition"
	"cosy/log"
	"cosy/longruntask"
	"cosy/util"
	"cosy/websocket"
)

func ExecuteTaskHandler(ctx context.Context, req *websocket.WireRequest) error {
	params, err := parseRequestParameter[definition.ExecuteTaskParams](ctx, req)
	if err != nil {
		reply(ctx, req, definition.NewErrorResponse("InvalidParams", err.Error(), nil))
		return err
	}

	// copy context
	asyncCtx := websocket.CopyContext(ctx)
	// 由于无法直接访问cs对象，需要从上下文中获取workspace信息
	if workspace := ctx.Value(definition.ContextKeyWorkspace); workspace != nil {
		asyncCtx = context.WithValue(asyncCtx, definition.ContextKeyWorkspace, workspace)
	}

	log.Info("[chat][executeTask] params=%v", params)
	// 执行任务
	response := longruntask.ExecuteTask(asyncCtx, params)
	log.Info("[chat][executeTask] response=%s", util.ToJsonStr(response))

	reply(ctx, req, response)
	return nil
}
