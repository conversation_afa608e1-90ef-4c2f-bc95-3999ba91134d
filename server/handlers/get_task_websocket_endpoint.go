package handlers

import (
	"context"
	"cosy/definition"
	"cosy/log"
	"cosy/longruntask"
	"cosy/websocket"
)

func GetTaskWebsocketEndpointHandler(ctx context.Context, req *websocket.WireRequest) error {
	params, err := parseRequestParameter[definition.GetTaskWebsocketEndpointParams](ctx, req)
	if err != nil {
		reply(ctx, req, definition.NewErrorResponse("InvalidParams", err.Error(), nil))
		return err
	}

	log.Info("[task][websocketEndpoint] params=%v", params)
	ep, err := longruntask.GetTaskWebsocketEndpoint(ctx, params.TaskId)
	if err != nil {
		log.Errorf("Failed to get remote websocket endpoint: %v", err)
	}

	result := map[string]any{"endpoint": ep}
	reply(ctx, req, result)
	return nil
}
