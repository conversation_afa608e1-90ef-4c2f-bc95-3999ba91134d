package handlers

import (
	"context"
	"cosy/definition"
	"cosy/log"
	"cosy/longruntask"
	"cosy/websocket"
)

func GetTaskByIdHandler(ctx context.Context, req *websocket.WireRequest) error {
	params, err := parseRequestParameter[definition.GetChatTaskParams](ctx, req)
	if err != nil {
		reply(ctx, req, definition.NewErrorResponse("InvalidParams", err.Error(), nil))
		return err
	}

	log.Info("[chat][getById] params=%v", params)
	// 获取任务详情
	response := longruntask.GetChatTask(params.Id)

	reply(ctx, req, response)
	return nil
}
