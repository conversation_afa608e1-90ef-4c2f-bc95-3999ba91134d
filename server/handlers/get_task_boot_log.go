package handlers

import (
	"context"
	"cosy/definition"
	"cosy/log"
	"cosy/longruntask"
	"cosy/websocket"
)

func GetTaskBootLogHandler(ctx context.Context, req *websocket.WireRequest) error {
	params, err := parseRequestParameter[definition.GetTaskBootLogParams](ctx, req)
	if err != nil {
		reply(ctx, req, definition.NewErrorResponse("InvalidParams", err.Error(), nil))
		return err
	}

	log.Info("[chat][getTaskBootLog] params=%v", params)
	// 获取任务启动日志
	response := longruntask.GetChatTaskBootLog(params.Id)

	reply(ctx, req, response)
	return nil
}
