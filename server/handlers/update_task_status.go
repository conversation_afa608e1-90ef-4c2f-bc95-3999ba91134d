package handlers

import (
	"context"
	"cosy/definition"
	"cosy/log"
	"cosy/longruntask"
	"cosy/websocket"
)

func UpdateTaskStatusHandler(ctx context.Context, req *websocket.WireRequest) error {
	params, err := parseRequestParameter[definition.UpdateChatTaskStatusParams](ctx, req)
	if err != nil {
		reply(ctx, req, definition.NewErrorResponse("InvalidParams", err.Error(), nil))
		return err
	}

	log.Info("[chat][updateStatus] params=%v", params)
	// 更新任务状态
	response := longruntask.UpdateChatTaskStatus(ctx, params.Id, params.Status, []func(context.Context, string, string) error{longruntask.OnRemoteStatusChanged})

	reply(ctx, req, response)
	return nil
}
