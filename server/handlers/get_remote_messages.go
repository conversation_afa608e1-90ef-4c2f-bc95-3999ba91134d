package handlers

import (
	"context"
	"cosy/chat/agents/coder/common"
	"cosy/definition"
	"cosy/log"
	"cosy/longruntask"
	"cosy/websocket"
	"encoding/json"
)

type PagedAgentToolCallResult struct {
	ErrorMessage string                       `json:"errorMessage"`
	Successful   bool                         `json:"successful"`
	ToolResults  []common.ToolCallSyncRequest `json:"toolResults"`
	PageNumber   int                          `json:"pageNumber"`
	PageSize     int                          `json:"pageSize"`
	TotalSize    int                          `json:"totalSize"`
}

func GetRemoteMessagesHandler(ctx context.Context, req *websocket.WireRequest) error {
	params, err := parseRequestParameter[definition.GetRemoteAgentMessagesRequest](ctx, req)
	if err != nil {
		reply(ctx, req, definition.NewErrorResponse("InvalidParams", err.Error(), nil))
		return err
	}

	taskMrg := longruntask.NewTaskManager()
	agentToolCallResult := PagedAgentToolCallResult{}

	records, err := taskMrg.GetSessionMessages(ctx, params.SessionId, "tool", params.PageSize, params.PageNumber)
	if err != nil {
		agentToolCallResult.ErrorMessage = "failed to get session message"
		agentToolCallResult.Successful = false
		log.Error(err)
	}
	agentToolCallResult.TotalSize = records.TotalSize
	agentToolCallResult.PageNumber = records.PageNumber
	agentToolCallResult.PageSize = records.PageSize
	for _, message := range records.Items {
		toolCallResult := common.ToolCallSyncRequest{}
		if err := json.Unmarshal([]byte(message.ToolResult), &toolCallResult); err != nil {
			log.Errorf("Error unmarshalling tool result for message ID: %s, err: %s", message.Id, err.Error())
		}
		agentToolCallResult.ToolResults = append(agentToolCallResult.ToolResults, toolCallResult)
	}

	reply(ctx, req, definition.NewSuccessResponse(agentToolCallResult))
	return nil
}
