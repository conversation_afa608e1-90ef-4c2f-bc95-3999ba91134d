package handlers

import (
	"context"
	"cosy/definition"
	"cosy/log"
	"cosy/longruntask"
	"cosy/util"
	"cosy/websocket"
)

func CancelTaskHandler(ctx context.Context, req *websocket.WireRequest) error {
	params, err := parseRequestParameter[definition.CancelChatTaskParams](ctx, req)
	if err != nil {
		reply(ctx, req, definition.NewErrorResponse("InvalidParams", err.Error(), nil))
		return err
	}

	log.Info("[chat][cancelTask] params=%v", params)
	// 取消任务
	response := longruntask.CancelChatTask(params.Id)
	log.Info("[chat][cancelTask] response=%s", util.ToJsonStr(response))

	reply(ctx, req, response)
	return nil
}
