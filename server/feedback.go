package server

import (
	"cosy/client"
	"cosy/config"
	"cosy/definition"
	"cosy/log"
	"cosy/remote"
	"cosy/storage/database"
	"cosy/user"
	util "cosy/util"
	"encoding/json"
	"io"
	"net/http"
)

func SubmitFeedback(feedback definition.FeedbackDislikeParam) definition.FeedbackDislikeSubmitResult {
	cachedUserInfo := user.GetCachedUserInfo()
	if cachedUserInfo == nil || cachedUserInfo.Uid == "" {
		return definition.FeedbackDislikeSubmitResult{
			Success: false,
		}
	}
	feedback.UserId = cachedUserInfo.Uid
	feedback.OrganizationId = cachedUserInfo.OrgId

	if feedback.ShareContext {
		completeChatContext(&feedback, feedback.SessionId, feedback.RequestId)
	}

	httpPayload := remote.HttpPayload{
		Payload:       util.ToJsonStr(feedback),
		EncodeVersion: config.Remote.MessageEncode,
	}
	req, err := remote.BuildBigModelAuthRequest(http.MethodPost, definition.UrlPathFeedBackApiUrl, httpPayload)
	if err != nil {
		log.Errorf("build big model auth request error: %v", err)
		return definition.FeedbackDislikeSubmitResult{
			Success: false,
		}
	}
	httpClient := client.GetDefaultClient()
	resp, err := httpClient.Do(req)
	if err != nil {
		log.Warnf("Failed to send request: " + err.Error())
		return definition.FeedbackDislikeSubmitResult{
			Success: false,
		}
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		log.Warnf("Failed to get response: " + resp.Status)
		return definition.FeedbackDislikeSubmitResult{
			Success: false,
		}
	}
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Warnf("Failed to read response: " + err.Error())
		return definition.FeedbackDislikeSubmitResult{
			Success: false,
		}
	}
	log.Debug("submit feedback, response: ", string(responseBody))

	result := definition.FeedbackDislikeSubmitResult{}
	err = json.Unmarshal(responseBody, &result)
	if err != nil {
		log.Warnf("Failed to feedback submit response. resp=%s, error=%v", string(responseBody), err)
		return definition.FeedbackDislikeSubmitResult{
			Success: false,
		}
	}
	return result
}

func completeChatContext(feedbackParam *definition.FeedbackDislikeParam, sessionId, requestId string) {
	session, err := database.ChatSessionTemplate.GetChatSession(sessionId)
	if err != nil {
		log.Errorf("complete feedback context error. err: %v", err)
		return
	}
	if session.SessionId == "" {
		return
	}
	//已经是自然升序
	records, err := database.ChatRecordTemplate.GetSessionChats(sessionId, definition.PageInfoAll)
	if err != nil {
		log.Errorf("complete feedback context error. err: %v", err)
		return
	}
	sharedChats := make([]definition.ChatRecord, 0)
	for _, record := range records {
		sharedChats = append(sharedChats, record)
		if record.RequestId == requestId {
			break
		}
	}
	feedbackParam.Context = util.ToJsonStr(sharedChats)
}
