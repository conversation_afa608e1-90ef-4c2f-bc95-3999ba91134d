package server

import (
	"context"
	"cosy/client"
	"cosy/components"
	"cosy/config"
	"cosy/definition"
	cosyErrors "cosy/errors"
	"cosy/experiment"
	"cosy/global"
	"cosy/log"
	"cosy/prompt"
	"cosy/sls"
	"cosy/util"
	"cosy/util/encrypt"
	"cosy/util/ops"
	"cosy/websocket"
	"encoding/json"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"sort"
	"strings"
	"time"

	"github.com/robfig/cron/v3"
)

type Mission struct {
	Version int                 `json:"version"`
	Tasks   []map[string]string `json:"tasks"`
}

const taskUpdateCosyGo = "update_cosy_go"

const taskAbTestFeature = "ab_feature"

const taskAgentAbTest = "agent_ab_test"

var heartbeatTimer *cron.Cron

func InitHeartbeat() {
	// Add timer, report heartbeat periodically
	heartbeatTimer = cron.New()
	heartbeatTimer.Start()

	// Report every 15 minute
	_, _ = heartbeatTimer.AddFunc("@every 15m", ReportHeartbeat)
}

func ReportHeartbeat() {
	extInfo := map[string]string{
		"host_system": util.GetPlatform(),
		"os_version":  util.GetOsVersion(),
		"tag":         config.Remote.HeartbeatTag,
	}
	appendIdeInformation(extInfo)
	appendProfilerInfo(extInfo)
	appendDeviceInformation(extInfo)
	//log.Debugf("Reporting heartbeat with data %v", extInfo)
	resp, err := sls.Reporter.PostHeartbeat(sls.ReportData(sls.HeartBeat, "", extInfo))
	if err != nil {
		log.Debugf("Failed to send heartbeat: %s", err.Error())

		if cosyErrors.IsTimeoutError(err) {
			components.RecordNetworkError(components.NetworkError{
				OccurTime: time.Now(),
			})
		}
	} else {
		//网络恢复
		if components.GetLastNetworkError() != nil {
			//广播网络恢复事件
			go func() {
				components.ClearNetworkError()
				log.Info("network recovered from error.")
				components.BroadcastNetworkRecover()
			}()
		}
	}

	mission := Mission{Tasks: []map[string]string{}}
	if err = json.Unmarshal(resp, &mission); err != nil {
		log.Warn("Failed to parse heartbeat response")
	}
	if len(mission.Tasks) > 0 {
		doMission(mission)
		log.Debugf("Heartbeat fetched %d tasks", len(mission.Tasks))
	} else {
		log.Debugf("Heartbeat ticks")
	}
}

func appendIdeInformation(info map[string]string) {
	var ides []string
	// 使用 sync.Map 的 Range 方法遍历
	global.IdeInfo.Range(func(key, value interface{}) bool {
		name := key.(string)
		ide := value.(definition.IdeConfig)
		name = strings.ReplaceAll(strings.ToLower(name), " ", "_")
		info[fmt.Sprintf("ide_%s_version", name)] = ide.IdeVersion
		info[fmt.Sprintf("ide_%s_plugin_version", name)] = ide.PluginVersion
		ides = append(ides, name)
		return true // 继续遍历
	})
	sort.Strings(ides)
	info["ide_types"] = strings.Join(ides, ";")
}

func appendProfilerInfo(info map[string]string) {
	if !ops.GlobalProfileStat.IsValid() {
		return
	}
	profilerInfo, err := ops.GlobalProfileStat.Export()
	if err != nil {
		log.Warnf("Failed to export profiler info: %s", err.Error())
	} else {
		for k, v := range profilerInfo {
			info[k] = v
		}
	}
	ops.GlobalProfileStat.Reset()
}

func appendDeviceInformation(info map[string]string) {
	deviceInfo := util.BuildDeviceInformation()
	if deviceInfo != nil {
		for k, v := range deviceInfo {
			info[k] = v
		}
	}
}

func doMission(mission Mission) {
	for _, task := range mission.Tasks {
		name := task["name"]
		if name == taskUpdateCosyGo {
			updateCosyGo(task)
		} else if name == taskAbTestFeature {
			updateAbFeature(task)
		} else if name == taskAgentAbTest {
			checkAgentConfigChecksum(task)
		}
	}
}

func updateCosyGo(task map[string]string) {
	// 更新CosyGo二进制文件
	url := task["download_url"]
	md5 := task["file_md5"]
	// 如果参数不完整，则拒绝更新
	if url == "" || md5 == "" {
		log.Warnf("Some parameters are missing, skip update")
		return
	}
	cosyHome := util.GetCosyHomePath()
	cosyHomeCache := filepath.Join(cosyHome, "cache")
	placeholderFile := filepath.Join(cosyHomeCache, "Lingma.update")
	err := client.DownloadFile(url, placeholderFile)
	// 如果下载失败，则拒绝更新
	if err != nil {
		log.Warnf("Failed to download update package: %s, skip update", err.Error())
		return
	}
	fileMd5 := encrypt.GetFileMd5(placeholderFile)
	// 如果文件MD5不正确，则拒绝更新
	if fileMd5 != md5 {
		log.Warnf("Package MD5 unmatch, skip update")
		return
	}
	err = os.Chmod(placeholderFile, 0755)
	// 如果文件无法赋予可执行权限，则拒绝更新
	if err != nil {
		fmt.Printf("Failed set package permission: %s, skip update", err.Error())
		return
	}
	cmd := exec.Command(placeholderFile, "version")
	output, err := cmd.Output()
	// 如果文件无法执行，则拒绝更新
	if err != nil {
		fmt.Printf("Failed to execute new cosy package: %s, skip update", err.Error())
		return
	}
	version := strings.ReplaceAll(strings.ReplaceAll(string(output), " ", ""), "\n", "")
	targetFolder := filepath.Join(cosyHome, "bin", version, util.GetPlatform())
	err = os.MkdirAll(targetFolder, 0755)
	// 如果创建新版本目录失败，则拒绝更新
	if err != nil {
		fmt.Printf("Failed to create new cosy version directory: %s, skip update", err.Error())
		return
	}
	err = os.Rename(placeholderFile, filepath.Join(targetFolder, getFileName(url)))
	// 如果移动文件失败，则拒绝更新
	if err != nil {
		log.Warnf("Failed replace lingma package: %s, skip update", err.Error())
		return
	}
	configFile := filepath.Join(cosyHome, "bin", "config.json")
	configText, err := os.ReadFile(configFile)
	// 如果无法读取配置文件，则拒绝更新
	if err != nil {
		log.Warnf("Failed to read cosy config: %s, skip update", err.Error())
		return
	}
	configJson := make(map[string]any)
	err = json.Unmarshal(configText, &configJson)
	// 如果无法解析配置文件内容，则拒绝更新
	if err != nil {
		log.Warnf("Failed to parse cosy config: %s, skip update", err.Error())
		return
	}
	configJson["cosy.core.version"] = version
	configText, err = json.MarshalIndent(configJson, "", "  ")
	// 如果修改后的配置文件内容无法序列化，则拒绝更新
	if err != nil {
		log.Warnf("Failed to modified cosy config: %s, skip update", err.Error())
		return
	}
	err = os.WriteFile(configFile, configText, 0644)
	// 如果无法写入配置文件，则拒绝更新
	if err != nil {
		log.Warnf("Failed to write cosy config: %s, skip update", err.Error())
		return
	}
	// 更新完成，自动关闭当前进程
	os.Exit(0)
}

//	{
//	     "name": "ab_feature",
//	     "vscode.completion.auto.delay": "500",
//	     "xxxxx.feature1": "xxxx"
//	     ……
//	   }
func updateAbFeature(task map[string]string) {
	updateSuccess := experiment.ConfigService.UpdateAll(task)
	if updateSuccess {
		UpdateCodebaseAbFeature()
		handler := websocket.WsInst.GetHandler()
		if cosyServer, ok := handler.(*CosyServer); ok && cosyServer != nil {
			for _, wsClient := range websocket.WsInst.GetClientList() {
				if ideInfo, err := cosyServer.GetIdeInfo(wsClient); err == nil {
					go func(client *websocket.Client) {
						defer func() {
							if r := recover(); r != nil {
							}
						}()
						expConfig := experiment.ConfigService.GetExperimentConfigsByIdeConfig(ideInfo)
						if expConfig != nil && len(expConfig) > 0 {
							featuresConfig := make(map[string]interface{})
							featuresConfig["features"] = expConfig
							if err := websocket.WsInst.NotifyToClient(context.Background(), client, "textDocument/publishExperimental", featuresConfig); err != nil {
								log.Infof("Failed to send abFeatures to socket client %p, idePlatform %s", client, ideInfo.IdePlatform)
							}
							log.Debugf("Send abFeatures to socket client %p, idePlatform %s, config %s", client, ideInfo.IdePlatform, util.ToJsonStr(expConfig))
						}
					}(wsClient)
				}
			}
		}
	}
}

func checkAgentConfigChecksum(task map[string]string) {
	if promptChecksum, ok := task["prompt_checksum"]; ok && prompt.IsUpdateRequired(promptChecksum) {
		prompt.Engine.UpdateRemoteTemplate()
	}
}

func getFileName(url string) string {
	// 去除?之后的参数
	if i := strings.Index(url, "?"); i > 0 {
		url = url[:i]
	}

	// 获取最后一个/之后的部分
	if url[len(url)-1] == '/' {
		return ""
	}
	for i := len(url) - 2; i > 0; i-- {
		if url[i] == '/' && url[i-1] != '/' && url[i+1] != '/' {
			return url[i+1:]
		}
	}

	return ""
}

func UpdateCodebaseAbFeature() {
	components.UpdateEmbeddingTokenBucket()
	newMaxStorageFileNum := experiment.ConfigService.GetIntConfigValue(definition.ExperimentKeyMaxClientStorageFileNum, experiment.ConfigScopeClient, definition.DefaultClientMaxStorageFileNum)
	global.UpdateMaxClientStorageFileNum(newMaxStorageFileNum)
}
