package product

// 获取产品化的文案
type BrandingHandler interface {
	GetMessage(key string) string
}

const (
	KeyCmdName       = "cmd.name"
	KeyCmdShort      = "cmd.short"
	KeyCmdLong       = "cmd.long"
	KeyVersionPrefix = "version.prefix"
	KeyAboutMessage  = "about.message"
)

var defaultBranding = &BrandingSupporter{}

// GetBranding 获取默认的 branding 实例
func GetBranding() BrandingHandler {
	return defaultBranding
}
