//go:build qoder

package product

var messages map[string]string

type BrandingSupporter struct {
}

func init() {
	messages = map[string]string{
		KeyCmdName:       "qoder",
		KeyCmdShort:      "<PERSON><PERSON><PERSON> is an intelligent code assistant which makes your coding easy",
		KeyCmdLong:       "<PERSON><PERSON><PERSON> is an intelligent code assistant which makes your coding easy",
		KeyVersionPrefix: "Qoder",
		KeyAboutMessage:  "<PERSON><PERSON><PERSON> is an intelligent code assistant which makes your coding easy",
	}
}

func (b *BrandingSupporter) GetMessage(key string) string {
	if msg, exists := messages[key]; exists {
		return msg
	}
	return ""
}
