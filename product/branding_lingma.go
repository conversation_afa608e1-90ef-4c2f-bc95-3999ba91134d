//go:build !qoder

package product

var messages map[string]string

type BrandingSupporter struct {
}

func init() {
	messages = map[string]string{
		KeyCmdName:       "lingma",
		KeyCmdShort:      "<PERSON><PERSON> is an intelligent code assistant which makes your coding easy",
		KeyCmdLong:       "<PERSON><PERSON> is an intelligent code assistant which makes your coding easy",
		KeyVersionPrefix: "TONGYI Lingma",
		KeyAboutMessage:  "TONGY<PERSON> is an intelligent code assistant which makes your coding easy",
	}
}

func (b *BrandingSupporter) GetMessage(key string) string {
	if msg, exists := messages[key]; exists {
		return msg
	}
	return ""
}
