package components

import (
	"context"
	"cosy/client"
	"cosy/config"
	"cosy/definition"
	"cosy/remote"
	"cosy/user"
	"cosy/util"
	"cosy/websocket"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
)

type ReportQuotaResp struct {
	Success bool `json:"success"`
	Quota   int  `json:"quota"`
}

type QueryQuotaResp struct {
	IsMatchQuota bool `json:"isMatchQuota"`
	Quota        int  `json:"quota"`
	QuotaBalance int  `json:"quotaBalance"`
}

type ReportQuotaRequest struct {
	QuotaTargetType string `json:"quotaTargetType"`
	UserId          string `json:"userId"`
	QuotaPoolId     string `json:"quotaPoolId"`
	Quota           int    `json:"quota"`
}

func QueryQuota(ctx context.Context, quotaTargetTyp, quotaPoolId string) (QueryQuotaResp, error) {
	quota := QueryQuotaResp{}
	urlPath := fmt.Sprintf("%s?quotaTargetType=%s&quotaPoolId=%s", definition.UrlPathQuotaEndpoint, quotaTargetTyp, quotaPoolId)
	request, err := remote.BuildBigModelAuthRequest(http.MethodGet, urlPath, nil)
	if err != nil {
		var tokenExpiredError *definition.TokenExpiredError
		if errors.As(err, &tokenExpiredError) {
			status := definition.AuthStatusResult{
				Status:          definition.AuthStatusInit,
				Quota:           0,
				WhitelistStatus: definition.WhitelistUnknown,
			}
			websocket.SendBroadcastWithTimeout(ctx, "auth/report", status, nil)
		}
		return quota, err
	}
	resp, err := client.GetDefaultClient().Do(request)
	if err != nil {
		return quota, err
	}
	defer resp.Body.Close()
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return quota, err
	}
	if resp.StatusCode != http.StatusOK {
		return quota, fmt.Errorf("query quota failed. satatus code: %d. response body: %s", resp.StatusCode, bodyBytes)
	}
	err = json.Unmarshal(bodyBytes, &quota)
	if err != nil {
		return quota, err
	}
	return quota, nil
}

func ReportUsedQuota(quotaTargetType, quotaPoolId string, quota int) (ReportQuotaResp, error) {
	curUser := user.GetCachedUserInfo()
	reportResp := ReportQuotaResp{}
	reportRequest := ReportQuotaRequest{
		Quota:           quota,
		UserId:          curUser.Uid,
		QuotaPoolId:     quotaPoolId,
		QuotaTargetType: quotaTargetType,
	}
	httpPayload := remote.HttpPayload{
		Payload:       util.ToJsonStr(reportRequest),
		EncodeVersion: config.Remote.MessageEncode,
	}
	request, err := remote.BuildBigModelAuthRequest(http.MethodPost, definition.UrlPathQuotaEndpoint, httpPayload)
	if err != nil {
		return reportResp, err
	}
	resp, err := client.GetDefaultClient().Do(request)
	if err != nil {
		return reportResp, err
	}
	defer resp.Body.Close()
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return reportResp, err
	}
	if resp.StatusCode != http.StatusOK {
		return reportResp, fmt.Errorf("report quota failed. satatus code: %d. response body: %s", resp.StatusCode, bodyBytes)
	}
	err = json.Unmarshal(bodyBytes, &reportResp)
	if err != nil {
		return reportResp, err
	}
	return reportResp, nil
}
