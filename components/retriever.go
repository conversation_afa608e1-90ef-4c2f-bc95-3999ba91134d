package components

import (
	"context"
	"cosy/client"
	"cosy/config"
	"cosy/definition"
	"cosy/log"
	"cosy/remote"
	"cosy/util"
	"cosy/websocket"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"sync"

	"github.com/tmc/langchaingo/embeddings"
	"github.com/tmc/langchaingo/schema"
)

const (
	ScenarioDocument = "DOCUMENT"
	ScenarioCode     = "CODE"
	SceneQA          = "answer"
	SceneCompletion  = "complement"
	NoAuthorizedKB   = "no_authorized_kb"
	NoEnableKB       = "no_enabled_kb"
)

type BatchRetriever interface {
	GetRelevantDocumentsBatch(ctx context.Context, queries []string) (map[string][]schema.Document, error)
	GetRelevantDocuments(ctx context.Context, query string) ([]schema.Document, error)
	// 从指定的KBIDs中获取文档列表
	GetRelevantDocumentsByID(ctx context.Context, query string, kbIDs []string) ([]schema.Document, error)
	GetRelevantDocumentList(ctx context.Context, query RetrieveDocListRequest) ([]definition.DocInfo, int, error)
}

type LingmaDocRetriever struct {
	embedder   embeddings.Embedder
	httpClient *http.Client
}

type RetrieveDocRequest struct {
	Query string
	//文档类型，DOCUMENT或CODE
	Scenario string
	//场景，qa或complement
	Scene          string
	TopK           int
	RerankFactor   float64
	ScoreThreshold float64
	RecallWindow   []int
	Language       string
	TokensLimit    int
}

// NOTE(chenan.xxw): queryDocExt 新接口参数，支持指定KBIDs
type RetrieveDocExtRequest struct {
	Query string `json:"query"`
	// 查询的知识库列表
	KBIDs          []string `json:"kbIds"`
	TopK           int      `json:"topK"`
	ScoreThreshold float64  `json:"scoreThreshold"`
	UseRerank      bool     `json:"useRerank"`
}

type RetrieveDocResponse struct {
	RequestID string  `json:"requestId"`
	Message   string  `json:"message"`
	Status    string  `json:"status"`
	Matches   Matches `json:"matches"`
}

type Matches struct {
	MatchList []Match `json:"matchList"`
}

type Match struct {
	Content  string                 `json:"content"`
	Metadata map[string]interface{} `json:"metadata"`
	Score    float32                `json:"score"`
}

type RetrieveDocListRequest struct {
	//场景，answer或complement
	SceneType string `json:"scene_type"`
	// 搜索名称
	Query string `json:"query"`
	// 选择开启或关闭的知识库，enable or disable，为空时则返回所有
	State    string `json:"state"`
	Page     int    `json:"page"`
	PageSize int    `json:"page_size"`
}

type RetrieveDocListResponse struct {
	RequestID string               `json:"request_id"`
	Message   string               `json:"message"`
	Status    string               `json:"status"`
	DocList   []definition.DocInfo `json:"kb_list"`
	PageTotal int                  `json:"page_total"`
}

func NewLingmaDocRetrever() LingmaDocRetriever {
	return LingmaDocRetriever{
		embedder:   LingmaEmbedder{},
		httpClient: client.GetDocEmbeddingClient(),
	}
}

func (l LingmaDocRetriever) GetRelevantDocuments(ctx context.Context, query string) ([]schema.Document, error) {
	return l.GetRelevantDocumentsByID(ctx, query, []string{})
}

func (l LingmaDocRetriever) GetRelevantDocumentsByID(ctx context.Context, query string, kbIDs []string) ([]schema.Document, error) {
	docs := []schema.Document{}
	var retrieveResult RetrieveDocResponse
	retrieveReq := RetrieveDocExtRequest{
		Query: query,
		KBIDs: kbIDs,
	}
	httpPayload := remote.HttpPayload{
		Payload:       util.ToJsonStr(retrieveReq),
		EncodeVersion: config.Remote.MessageEncode,
	}
	request, err := remote.BuildBigModelAuthRequest(http.MethodPost, definition.UrlPathQueryDocEndpoint, httpPayload)
	if err != nil {
		var tokenExpiredError *definition.TokenExpiredError
		if errors.As(err, &tokenExpiredError) {
			status := definition.AuthStatusResult{
				Status:          definition.AuthStatusInit,
				Quota:           0,
				WhitelistStatus: definition.WhitelistUnknown,
			}
			websocket.SendBroadcastWithTimeout(ctx, "auth/report", status, nil)
		}
		return nil, err
	}
	resp, err := l.httpClient.Do(request)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("query documents failed. satatus code: %d. response body: %s", resp.StatusCode, bodyBytes)
	}
	err = json.Unmarshal(bodyBytes, &retrieveResult)
	if err != nil {
		return nil, err
	}
	if retrieveResult.Status != "success" {
		return nil, fmt.Errorf("query documents return non-success. message: %s", retrieveResult.Message)
	}
	if retrieveResult.Message == NoAuthorizedKB || retrieveResult.Message == NoEnableKB {
		return nil, &definition.NoVisibleKnowledgeBaseError{Msg: retrieveResult.Message}
	}
	for _, match := range retrieveResult.Matches.MatchList {
		doc := schema.Document{
			PageContent: match.Content,
			Score:       match.Score,
			Metadata:    match.Metadata,
		}
		docs = append(docs, doc)
	}
	return docs, nil
}

// GetRelevantDocumentsBatch get relevant documents in batch
func (l LingmaDocRetriever) GetRelevantDocumentsBatch(ctx context.Context, queries []string) (map[string][]schema.Document, error) {
	wg := sync.WaitGroup{}
	result := make(map[string][]schema.Document)
	errorChannel := make(chan error, len(queries))
	mutex := sync.Mutex{}

	for _, query := range queries {
		wg.Add(1)
		go func(q string) {
			defer wg.Done()
			documents, err := l.GetRelevantDocuments(ctx, q)
			if err != nil {
				errorChannel <- err
				return
			}
			mutex.Lock()
			result[q] = documents
			mutex.Unlock()
		}(query)
	}

	go func() {
		wg.Wait()
		close(errorChannel)
	}()

	for err := range errorChannel {
		if err != nil {
			log.Error(err)
		}
	}

	return result, nil
}

func (l LingmaDocRetriever) GetRelevantDocumentList(ctx context.Context, req RetrieveDocListRequest) ([]definition.DocInfo, int, error) {
	var queryResult RetrieveDocListResponse
	queryURL := fmt.Sprintf("%s?query=%s&page=%d&page_size=%d&scene_type=%s&state=%s", definition.UrlPathQueryDocListURI, url.QueryEscape(req.Query), req.Page, req.PageSize, req.SceneType, req.State)
	request, err := remote.BuildBigModelAuthRequest(http.MethodGet, queryURL, nil)
	if err != nil {
		var tokenExpiredError *definition.TokenExpiredError
		if errors.As(err, &tokenExpiredError) {
			status := definition.AuthStatusResult{
				Status:          definition.AuthStatusInit,
				Quota:           0,
				WhitelistStatus: definition.WhitelistUnknown,
			}
			websocket.SendBroadcastWithTimeout(ctx, "auth/report", status, nil)
		}
		return nil, 0, err
	}
	resp, err := l.httpClient.Do(request)
	if err != nil {
		return nil, 0, err
	}
	defer resp.Body.Close()
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, 0, err
	}
	if resp.StatusCode != http.StatusOK {
		return nil, 0, fmt.Errorf("query document list failed. satatus code: %d. response body: %s", resp.StatusCode, bodyBytes)
	}
	err = json.Unmarshal(bodyBytes, &queryResult)
	if err != nil {
		return nil, 0, err
	}
	if queryResult.Status != "success" {
		return nil, 0, fmt.Errorf("query documents return non-success. request_id:%s, message: %s", queryResult.RequestID, queryResult.Message)
	}
	if queryResult.Message == NoAuthorizedKB || queryResult.Message == NoEnableKB {
		return nil, 0, &definition.NoVisibleKnowledgeBaseError{Msg: queryResult.Message}
	}
	return queryResult.DocList, queryResult.PageTotal, nil
}
