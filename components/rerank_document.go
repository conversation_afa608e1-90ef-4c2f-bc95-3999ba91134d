package components

import (
	"fmt"
	"path/filepath"
	"strings"
)

// RerankDocument represents a structured document for reranking with metadata
type RerankDocument struct {
	Content       string  // The actual code content
	FilePath      string  // Full file path
	FileName      string  // File name only
	Language      string  // Programming language
	Category      string  // Code category (normal/test)
	Type          string  // Code type (function/class/method/etc)
	StartLine     uint32  // Start line number
	EndLine       uint32  // End line number
	Score         float64 // Initial score if any
	WorkspacePath string  // Workspace root path for relative path calculation
}

// GetRelativePath returns the file path relative to workspace, falls back to absolute path if conversion fails
func (d *RerankDocument) GetRelativePath() string {
	if d.WorkspacePath == "" {
		return d.FilePath
	}

	// Try to get relative path
	relPath, err := filepath.Rel(d.WorkspacePath, d.FilePath)
	if err != nil {
		// If relative path calculation fails, return the original path
		return d.FilePath
	}

	// If the relative path starts with "../", it means the file is outside workspace
	// In this case, return the absolute path for clarity
	if strings.HasPrefix(relPath, "..") {
		return d.FilePath
	}

	return relPath
}

// FormatForRerank formats the document into a structured string for reranking
func (d *RerankDocument) FormatForRerank() string {
	var parts []string

	// Add metadata section
	parts = append(parts, "=== METADATA ===")
	parts = append(parts, fmt.Sprintf("File: %s", d.GetRelativePath()))
	parts = append(parts, fmt.Sprintf("Language: %s", d.Language))
	parts = append(parts, fmt.Sprintf("Category: %s", d.Category))
	parts = append(parts, fmt.Sprintf("Type: %s", d.Type))
	parts = append(parts, fmt.Sprintf("Lines: %d-%d", d.StartLine, d.EndLine))

	// Add content section
	parts = append(parts, "=== CONTENT ===")
	parts = append(parts, d.Content)

	return strings.Join(parts, "\n")
}

// FormatForLLMRerank formats the document with enhanced metadata for LLM understanding
func (d *RerankDocument) FormatForLLMRerank() string {
	var parts []string

	// Use standard format with path:L<lines> (common IDE format)
	parts = append(parts, fmt.Sprintf("File Path: %s:L%d-%d", d.GetRelativePath(), d.StartLine, d.EndLine))
	parts = append(parts, fmt.Sprintf("Language: %s", d.Language))

	// Add category information with explanation
	categoryDesc := d.Category
	if d.Category == "test" {
		categoryDesc = "Test Code"
	} else if d.Category == "normal" {
		categoryDesc = "Production Code"
	}
	parts = append(parts, fmt.Sprintf("Category: %s", categoryDesc))

	// Add type with more context
	if d.Type != "" {
		parts = append(parts, fmt.Sprintf("Code Type: %s", d.Type))
	}

	parts = append(parts, "Code Content:")
	parts = append(parts, d.Content)

	return strings.Join(parts, "\n")
}

// ParseRerankDocument attempts to parse a formatted string back into RerankDocument
// This is useful for debugging and testing
func ParseRerankDocument(formatted string) (*RerankDocument, error) {
	doc := &RerankDocument{}
	lines := strings.Split(formatted, "\n")

	inContent := false
	var contentLines []string

	for _, line := range lines {
		if strings.HasPrefix(line, "=== CONTENT ===") || strings.HasPrefix(line, "【Code Content】") {
			inContent = true
			continue
		}

		if inContent {
			contentLines = append(contentLines, line)
			continue
		}

		// Parse metadata
		if strings.HasPrefix(line, "File: ") || strings.HasPrefix(line, "【File Path】") {
			doc.FilePath = strings.TrimPrefix(strings.TrimPrefix(line, "File: "), "【File Path】")
		} else if strings.HasPrefix(line, "Language: ") || strings.HasPrefix(line, "【Language】") {
			doc.Language = strings.TrimPrefix(strings.TrimPrefix(line, "Language: "), "【Language】")
		} else if strings.HasPrefix(line, "Category: ") || strings.Contains(line, "【Category】") {
			if strings.Contains(line, "Test Code") {
				doc.Category = "test"
			} else if strings.Contains(line, "Production Code") {
				doc.Category = "normal"
			} else {
				doc.Category = strings.TrimPrefix(line, "Category: ")
			}
		} else if strings.HasPrefix(line, "Type: ") || strings.HasPrefix(line, "【Code Type】") {
			doc.Type = strings.TrimPrefix(strings.TrimPrefix(line, "Type: "), "【Code Type】")
		}
	}

	doc.Content = strings.Join(contentLines, "\n")
	return doc, nil
}
