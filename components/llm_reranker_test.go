package components

import (
	"context"
	"cosy/prompt"
	"cosy/tokenizer"
	"fmt"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"strings"
	"testing"
)

func TestLLMRerankerPromptTemplates(t *testing.T) {
	// 初始化 prompt 系统
	prompt.InitializeRepo()

	// 测试系统 prompt
	systemPrompt, err := prompt.Engine.RenderLLMRerankSystemPrompt(prompt.LLMRerankPromptInput{
		BaseInput: prompt.BaseInput{},
	})
	if err != nil {
		t.Errorf("Failed to render system prompt: %v", err)
	}
	if systemPrompt == "" {
		t.Error("System prompt is empty")
	}
	t.Logf("System prompt: %s", systemPrompt)

	// 测试用户 prompt
	userPrompt, err := prompt.Engine.RenderLLMRerankUserPrompt(prompt.LLMRerankPromptInput{
		BaseInput: prompt.BaseInput{},
		Query:     "test query",
		Documents: []string{"doc1", "doc2"},
	})
	if err != nil {
		t.<PERSON><PERSON><PERSON>("Failed to render user prompt: %v", err)
	}
	if userPrompt == "" {
		t.Error("User prompt is empty")
	}
	t.Logf("User prompt: %s", userPrompt)
}

func TestLLMRerankerGetSystemPrompt(t *testing.T) {
	// 初始化 prompt 系统
	prompt.InitializeRepo()

	reranker := NewLLMReranker()
	systemPrompt := reranker.getSystemPrompt()
	if systemPrompt == "" {
		t.Error("System prompt is empty")
	}
	t.Logf("System prompt from reranker: %s", systemPrompt)
}

// TestParseRankResponse_ToleranceMechanism 测试容错机制处理混合有效/无效响应
func TestParseRankResponse_ToleranceMechanism(t *testing.T) {
	reranker := NewLLMReranker()

	// 模拟文档和索引
	filteredDocuments := []string{
		"document 1 content",
		"document 2 content",
		"document 3 content",
		"document 4 content",
		"document 5 content",
	}
	originalIndexes := []int{0, 1, 2, 3, 4}

	tests := []struct {
		name          string
		responseText  string
		expectSuccess bool
		expectedCount int
		description   string
	}{
		{
			name:          "完全有效的响应",
			responseText:  "1|2|3|4|5",
			expectSuccess: true,
			expectedCount: 5,
			description:   "所有索引都有效，应该成功解析",
		},
		{
			name:          "在容忍范围内的混合响应",
			responseText:  "1|2|3|4|$",
			expectSuccess: true,
			expectedCount: 4,
			description:   "4个有效，1个无效 (20% < 30%)，应该提取有效部分",
		},
		{
			name:          "超过容忍阈值的响应",
			responseText:  "1|2|3|$|a|",
			expectSuccess: false,
			expectedCount: 0,
			description:   "3个有效，2个无效 (40% > 30%)，应该被拒绝",
		},
		{
			name:          "包含越界索引",
			responseText:  "1|2|10|4|$",
			expectSuccess: false,
			expectedCount: 0,
			description:   "索引10越界被视为无效，3个有效2个无效 (40% > 30%)，应该被拒绝",
		},
		{
			name:          "复杂的混合响应",
			responseText:  "1|abc|2|$|3|def|4|#|5",
			expectSuccess: false,
			expectedCount: 0,
			description:   "5个有效，4个无效 (44% > 30%)，应该被拒绝",
		},
		{
			name:          "只有无效字符",
			responseText:  "$|a|#|@",
			expectSuccess: false,
			expectedCount: 0,
			description:   "全部无效，应该被拒绝",
		},
		{
			name:          "空响应",
			responseText:  "",
			expectSuccess: false,
			expectedCount: 0,
			description:   "空响应应该被拒绝",
		},
		{
			name:          "包含空格的响应",
			responseText:  " 1 | 2 | $ | 3 | 4 ",
			expectSuccess: true,
			expectedCount: 4,
			description:   "4个有效，1个无效 (20% < 30%)，应该提取有效部分",
		},
		{
			name:          "包含重复序号的相应",
			responseText:  "1|2|3|2|3|2|3|2|1|4",
			expectSuccess: true,
			expectedCount: 4,
			description:   "5个有效，0个无效 (0% < 30%)，应该提取有效部分",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			results, err := reranker.parseRankResponse(tt.responseText, filteredDocuments, originalIndexes, 10)

			if tt.expectSuccess {
				assert.NoError(t, err, "测试 '%s' 应该成功: %s", tt.name, tt.description)
				assert.Equal(t, tt.expectedCount, len(results), "测试 '%s' 结果数量不匹配: %s", tt.name, tt.description)

				// 验证所有结果的相关性分数都是1.0
				for i, result := range results {
					assert.Equal(t, 1.0, result.RelevanceScore,
						"第%d个结果的相关性分数应该是1.0", i+1)
				}

				// 验证所有索引都在有效范围内
				for _, result := range results {
					assert.GreaterOrEqual(t, result.Index, 0, "索引应该 >= 0")
					assert.Less(t, result.Index, len(originalIndexes), "索引应该在有效范围内")
				}
			} else {
				assert.Error(t, err, "测试 '%s' 应该失败: %s", tt.name, tt.description)
				assert.Equal(t, 0, len(results), "失败时应该返回空结果")
			}
		})
	}
}

// TestParseRankResponse_RelevanceScoring 测试相关性分数计算
func TestParseRankResponse_RelevanceScoring(t *testing.T) {
	reranker := NewLLMReranker()

	filteredDocuments := []string{"doc1", "doc2", "doc3"}
	originalIndexes := []int{0, 1, 2}

	// 测试相关性分数计算
	results, err := reranker.parseRankResponse("1|2|3", filteredDocuments, originalIndexes, 10)

	assert.NoError(t, err)
	assert.Equal(t, 3, len(results))

	// 验证所有分数都是1.0
	for i, result := range results {
		assert.Equal(t, 1.0, result.RelevanceScore, "第%d个结果应该得分1.0", i+1)
	}
}

// TestParseRankResponse_TopNLimit 测试TopN限制
func TestParseRankResponse_TopNLimit(t *testing.T) {
	reranker := NewLLMReranker() // 限制只返回2个结果

	filteredDocuments := []string{"doc1", "doc2", "doc3", "doc4", "doc5"}
	originalIndexes := []int{0, 1, 2, 3, 4}

	results, err := reranker.parseRankResponse("1|2|3|4|5", filteredDocuments, originalIndexes, 2)

	assert.NoError(t, err)
	assert.Equal(t, 2, len(results), "应该只返回TopN个结果")
	assert.Equal(t, 0, results[0].Index, "第一个结果应该是索引0")
	assert.Equal(t, 1, results[1].Index, "第二个结果应该是索引1")
}

// TestParseRankResponse_RealWorldScenarios 测试真实世界场景
func TestParseRankResponse_RealWorldScenarios(t *testing.T) {
	reranker := NewLLMReranker()

	filteredDocuments := []string{"doc1", "doc2", "doc3", "doc4", "doc5"}
	originalIndexes := []int{0, 1, 2, 3, 4}

	// 模拟真实的LLM幻觉场景
	realWorldCases := []struct {
		name     string
		response string
		expectOK bool
		desc     string
	}{
		{
			name:     "LLM添加了解释文字",
			response: "1|2|3|Based on relevance|4",
			expectOK: false, // 4个有效，1个无效 = 20%，但这个测试可能失败因为"Based on relevance"很长
			desc:     "LLM在响应中添加了解释",
		},
		{
			name:     "LLM使用了特殊符号",
			response: "1|2|3|★|4|5",
			expectOK: true, // 5个有效，1个无效 = 16.7% < 30%
			desc:     "LLM使用了星号等特殊符号",
		},
		{
			name:     "LLM重复了索引",
			response: "1|2|2|3|4",
			expectOK: true,
			desc:     "LLM重复了某个索引",
		},
		{
			name:     "LLM使用了字母",
			response: "1|2|a|b|3|4|5",
			expectOK: true, // 5个有效，2个无效 = 28.6% < 30%
			desc:     "LLM使用了字母代替数字",
		},
		{
			name:     "LLM完全混乱",
			response: "a|b|c|d|e|1|2",
			expectOK: false, // 2个有效，5个无效 = 71.4% > 30%
			desc:     "LLM输出完全混乱",
		},
	}

	for _, tc := range realWorldCases {
		t.Run(tc.name, func(t *testing.T) {
			results, err := reranker.parseRankResponse(tc.response, filteredDocuments, originalIndexes, 10)

			t.Logf("测试 '%s': 响应='%s', 结果数量=%d, 错误=%v",
				tc.name, tc.response, len(results), err)

			if tc.expectOK {
				if err != nil {
					t.Logf("预期成功但失败了: %s - %s", tc.desc, err.Error())
				} else {
					assert.Greater(t, len(results), 0, "应该有有效结果")
				}
			} else {
				if err == nil {
					t.Logf("预期失败但成功了: %s - 得到%d个结果", tc.desc, len(results))
				}
			}
		})
	}
}

// TestSmartTruncateDocuments 测试智能截断机制
func TestSmartTruncateDocuments(t *testing.T) {
	reranker := NewLLMReranker()

	// 设置合理的token阈值便于测试
	reranker.TokenThreshold = 500

	tests := []struct {
		name             string
		documents        []string
		expectedMaxCount int
		expectTruncation bool
		description      string
	}{
		{
			name:             "小文档不需要截断",
			documents:        []string{"short doc 1", "short doc 2", "short doc 3"},
			expectedMaxCount: 3,
			expectTruncation: false,
			description:      "总token数小于阈值，不应该截断",
		},
		{
			name: "大文档需要截断",
			documents: []string{
				strings.Repeat("This is a very long document with many words that will exceed the token threshold. ", 10),
				strings.Repeat("Another long document that contains lots of text and will contribute to token overflow. ", 10),
				strings.Repeat("Yet another lengthy document with extensive content that adds to the total token count. ", 10),
				strings.Repeat("Final long document with substantial text content that pushes us over the limit. ", 10),
			},
			expectedMaxCount: 4,
			expectTruncation: true,
			description:      "总token数超过阈值，应该进行截断",
		},
		{
			name:             "空文档列表",
			documents:        []string{},
			expectedMaxCount: 0,
			expectTruncation: false,
			description:      "空列表应该直接返回",
		},
		{
			name:             "单个文档",
			documents:        []string{"single document"},
			expectedMaxCount: 1,
			expectTruncation: false,
			description:      "单个文档应该保留",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 计算原始token数
			originalTokens, _ := reranker.calculateTotalTokens(tt.documents)

			// 执行智能截断
			result, err := reranker.smartTruncateDocuments(tt.documents)

			assert.NoError(t, err, "智能截断不应该返回错误")
			assert.LessOrEqual(t, len(result), tt.expectedMaxCount, "截断后文档数量应该不超过预期")

			if tt.expectTruncation && originalTokens > reranker.TokenThreshold {
				// 验证确实进行了截断
				assert.Less(t, len(result), len(tt.documents), "应该进行了截断: %s", tt.description)

				// 验证截断后的token数不超过阈值
				resultTokens, _ := reranker.calculateTotalTokens(result)
				assert.LessOrEqual(t, resultTokens, reranker.TokenThreshold,
					"截断后token数应该不超过阈值: %s", tt.description)
			} else {
				// 验证没有进行不必要的截断
				assert.Equal(t, len(tt.documents), len(result), "不应该进行截断: %s", tt.description)
			}

			t.Logf("测试 '%s': 原始文档=%d, 截断后=%d, 原始tokens=%d, 阈值=%d",
				tt.name, len(tt.documents), len(result), originalTokens, reranker.TokenThreshold)
		})
	}
}

// TestCalculateTotalTokens 测试token计算
func TestCalculateTotalTokens(t *testing.T) {
	reranker := NewLLMReranker()

	documents := []string{
		"hello world",
		"this is a test document",
		"another document with more content",
	}

	totalTokens, err := reranker.calculateTotalTokens(documents)

	assert.NoError(t, err, "计算token数不应该返回错误")
	assert.Greater(t, totalTokens, 0, "总token数应该大于0")

	// 验证token数是所有文档token数的总和
	expectedTotal := 0
	for _, doc := range documents {
		expectedTotal += tokenizer.GetTokenCountWithSimpleAsciiTokenizer(doc)
	}
	assert.Equal(t, expectedTotal, totalTokens, "总token数应该等于各文档token数之和")
}

// TestSmartTruncateIterations 测试迭代截断逻辑
func TestSmartTruncateIterations(t *testing.T) {
	reranker := NewLLMReranker()
	reranker.TokenThreshold = 50 // 设置很小的阈值强制多次迭代

	// 创建多个中等大小的文档
	documents := make([]string, 20)
	for i := 0; i < 20; i++ {
		documents[i] = fmt.Sprintf("Document %d with some content that has moderate length", i)
	}

	result, err := reranker.smartTruncateDocuments(documents)

	assert.NoError(t, err, "智能截断不应该返回错误")
	assert.Less(t, len(result), len(documents), "应该进行了截断")
	assert.Greater(t, len(result), 0, "应该保留至少一个文档")

	// 验证最终结果不超过token阈值
	finalTokens, _ := reranker.calculateTotalTokens(result)
	assert.LessOrEqual(t, finalTokens, reranker.TokenThreshold, "最终token数不应该超过阈值")

	t.Logf("迭代截断测试: %d -> %d 文档, 最终tokens=%d, 阈值=%d",
		len(documents), len(result), finalTokens, reranker.TokenThreshold)
}

// TestSmartTruncateMaxIterations 测试最大迭代次数限制
func TestSmartTruncateMaxIterations(t *testing.T) {
	reranker := NewLLMReranker()
	reranker.TokenThreshold = 1 // 设置极小的阈值

	// 创建很多文档，确保需要多次迭代
	documents := make([]string, 100)
	for i := 0; i < 100; i++ {
		documents[i] = fmt.Sprintf("Document %d with substantial content that will require multiple iterations to truncate properly", i)
	}

	result, err := reranker.smartTruncateDocuments(documents)

	// 由于阈值极小，应该会触发错误（要么是单文档超过阈值，要么是迭代次数限制）
	if err != nil {
		// 检查是否是预期的错误类型之一
		errorMsg := err.Error()
		isExpectedError := strings.Contains(errorMsg, "failed to truncate documents within") ||
			strings.Contains(errorMsg, "single document") && strings.Contains(errorMsg, "exceeds threshold")

		assert.True(t, isExpectedError, "错误信息应该是预期的截断失败类型: %s", errorMsg)
		assert.Nil(t, result, "出错时应该返回nil")
		t.Logf("最大迭代次数测试: 错误信息=%s", err.Error())
	} else {
		// 如果没有出错，说明在最大迭代次数内完成了截断
		assert.NotNil(t, result, "成功时应该返回结果")
		assert.Greater(t, len(result), 0, "应该至少保留1个文档")
		t.Logf("最大迭代次数测试: 成功截断到%d个文档", len(result))
	}
}

// TestGetStringFromContext 测试从context中安全获取字符串值的所有情况
func TestGetStringFromContext(t *testing.T) {
	tests := []struct {
		name        string
		setupCtx    func() context.Context
		key         string
		expectValid bool // 是否期望返回有效的字符串值（非UUID）
		expectedVal string
		description string
	}{
		{
			name: "正常情况 - 存在有效的字符串值",
			setupCtx: func() context.Context {
				return context.WithValue(context.Background(), "test-key", "test-value")
			},
			key:         "test-key",
			expectValid: true,
			expectedVal: "test-value",
			description: "应该返回context中存储的字符串值",
		},
		{
			name: "key不存在",
			setupCtx: func() context.Context {
				return context.Background()
			},
			key:         "non-existent-key",
			expectValid: false,
			expectedVal: "",
			description: "应该生成新的UUID",
		},
		{
			name: "value为nil",
			setupCtx: func() context.Context {
				return context.WithValue(context.Background(), "nil-key", nil)
			},
			key:         "nil-key",
			expectValid: false,
			expectedVal: "",
			description: "应该生成新的UUID",
		},
		{
			name: "value类型不匹配 - int类型",
			setupCtx: func() context.Context {
				return context.WithValue(context.Background(), "int-key", 123)
			},
			key:         "int-key",
			expectValid: false,
			expectedVal: "",
			description: "应该生成新的UUID",
		},
		{
			name: "value类型不匹配 - bool类型",
			setupCtx: func() context.Context {
				return context.WithValue(context.Background(), "bool-key", true)
			},
			key:         "bool-key",
			expectValid: false,
			expectedVal: "",
			description: "应该生成新的UUID",
		},
		{
			name: "value类型不匹配 - slice类型",
			setupCtx: func() context.Context {
				return context.WithValue(context.Background(), "slice-key", []string{"a", "b"})
			},
			key:         "slice-key",
			expectValid: false,
			expectedVal: "",
			description: "应该生成新的UUID",
		},
		{
			name: "value类型不匹配 - map类型",
			setupCtx: func() context.Context {
				return context.WithValue(context.Background(), "map-key", map[string]string{"a": "b"})
			},
			key:         "map-key",
			expectValid: false,
			expectedVal: "",
			description: "应该生成新的UUID",
		},
		{
			name: "value类型不匹配 - struct类型",
			setupCtx: func() context.Context {
				type TestStruct struct {
					Field string
				}
				return context.WithValue(context.Background(), "struct-key", TestStruct{Field: "test"})
			},
			key:         "struct-key",
			expectValid: false,
			expectedVal: "",
			description: "应该生成新的UUID",
		},
		{
			name: "包含特殊字符的字符串",
			setupCtx: func() context.Context {
				return context.WithValue(context.Background(), "special-key", "test-value-with-特殊字符-123!@#")
			},
			key:         "special-key",
			expectValid: true,
			expectedVal: "test-value-with-特殊字符-123!@#",
			description: "应该返回包含特殊字符的字符串",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := tt.setupCtx()
			result := getStringFromContext(ctx, tt.key)

			if tt.expectValid {
				// 期望返回特定的字符串值
				assert.Equal(t, tt.expectedVal, result, tt.description)
			} else {
				// 期望返回UUID（36个字符，包含4个连字符）
				assert.Len(t, result, 36, "应该返回UUID格式的字符串")
				assert.Regexp(t, `^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$`, result, "应该是有效的UUID格式")

				// 验证是否为有效的UUID
				_, err := uuid.Parse(result)
				assert.NoError(t, err, "应该是有效的UUID")
			}

			t.Logf("测试 '%s': 输入key='%s', 返回值='%s', %s", tt.name, tt.key, result, tt.description)
		})
	}
}
