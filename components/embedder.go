package components

import (
	"context"
	"cosy/config"
	"cosy/definition"
	"cosy/experiment"
	"cosy/log"
	"cosy/tokenizer"
	"cosy/websocket"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math"
	"math/rand"
	"net/http"
	"sort"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"cosy/client"
	"cosy/remote"
	"cosy/util"

	"github.com/tmc/langchaingo/embeddings"
)

// 定义退避策略的参数
const (
	// DefaultTextEmbeddingModel 默认使用 text-embedding-v3 模型
	DefaultTextEmbeddingModel = "text-embedding-v4"

	// DefaultDimension 默认维度
	DefaultDimension = 512

	// DefaultMaxEmbeddingChunkSize 模型侧每个chunk最多8192个Token，这里用8000Byte简化了
	DefaultMaxEmbeddingChunkSize = 8000

	// MaxLowFrequencyRetryTimes 低频操作的最大重试次数
	MaxLowFrequencyRetryTimes = 2
	// MaxHighFrequencyRetryTimes 高频操作的最大重试次数
	MaxHighFrequencyRetryTimes = 5
	BaseWaitTime               = 1 * time.Second  // 初始等待时间
	MaxWaitTime                = 60 * time.Second // 最大等待时间

	DefaultStopEmbeddingTokenBucketLimit         = 0.0
	DefaultMinimalValidEmbeddingTokenBucketLimit = 0.1
	DefaultMaximumEmbeddingTokenBucketLimit      = 100

	TextTypeDocument = "document"
	TextTypeQuery    = "query"

	// MaxLimitTimes 最大的限流次数
	MaxLimitTimes = 10
)

var EmbeddingBatchSize *atomic.Int32
var EmbeddingTokenBucket atomic.Value

// limitTimes 全局的限流次数
var limitTimes *atomic.Int32

type LingmaEmbedderTokenBucket struct {
	Closed      *atomic.Bool // 现有bucket是否已关闭
	BucketLen   int
	TokenBucket chan struct{}
	ConfigValue float64
}

func init() {
	if EmbeddingBatchSize == nil {
		EmbeddingBatchSize = &atomic.Int32{}
		EmbeddingBatchSize.Store(definition.DefaultEmbeddingBatchSize)
	}
	limitTimes = &atomic.Int32{}

	closed := atomic.Bool{}
	closed.Store(false)
	configValue := float64(definition.DefaultEmbeddingTokenBucketLimit)
	// 不从环境变量获取
	//value := os.Getenv(environment.ExpKey2EnvKey(definition.ExperimentKeyEmbeddingTokenBucketLimit, experiment.ConfigScopeClient))
	//envConfig, err := strconv.ParseFloat(value, 64)
	//if err == nil {
	//	configValue = envConfig
	//}

	var bucketLen int
	if configValue <= DefaultStopEmbeddingTokenBucketLimit {
		// 这个配置的要求是所有embedding停下，不再允许发送任何请求
		configValue = DefaultStopEmbeddingTokenBucketLimit
		// 令牌桶的数量设置为0
		bucketLen = 0
	} else if configValue < definition.DefaultEmbeddingTokenBucketLimit {
		// 配置的值小于1.0时，同时只有1个令牌，则sleep时间是1-configLimitValue
		bucketLen = 1
		if configValue < DefaultMinimalValidEmbeddingTokenBucketLimit {
			// 最小设置为0.1
			configValue = DefaultMinimalValidEmbeddingTokenBucketLimit
		}
	} else {
		if configValue > DefaultMaximumEmbeddingTokenBucketLimit {
			// 最大设置为100
			configValue = DefaultMaximumEmbeddingTokenBucketLimit
		}
		// 配置的值大于等于1.0时，同时多个令牌，则sleep时间是0
		bucketLen = int(configValue)
	}

	bucket := make(chan struct{}, bucketLen)
	for i := 0; i < bucketLen; i++ {
		bucket <- struct{}{}
	}

	EmbeddingTokenBucket.Store(&LingmaEmbedderTokenBucket{
		Closed:      &closed,
		BucketLen:   bucketLen,
		TokenBucket: bucket,
		ConfigValue: configValue,
	})
}

func UpdateEmbeddingTokenBucket() {
	// 不从环境变量获取
	//newConfigLimitValue := experiment.ConfigService.GetDoubleConfigWithEnv(definition.ExperimentKeyEmbeddingTokenBucketLimit, experiment.ConfigScopeClient, definition.DefaultEmbeddingTokenBucketLimit)
	newConfigLimitValue := experiment.ConfigService.GetDoubleConfigValue(definition.ExperimentKeyEmbeddingTokenBucketLimit, experiment.ConfigScopeClient, definition.DefaultEmbeddingTokenBucketLimit)
	originBucket := EmbeddingTokenBucket.Load().(*LingmaEmbedderTokenBucket)
	var newEmbeddingTokenBucketLimit int
	if newConfigLimitValue <= DefaultStopEmbeddingTokenBucketLimit {
		// 这个配置的要求是所有embedding停下，不再允许发送任何请求
		newConfigLimitValue = DefaultStopEmbeddingTokenBucketLimit
		// 令牌桶的数量设置为0
		newEmbeddingTokenBucketLimit = 0
	} else if newConfigLimitValue < definition.DefaultEmbeddingTokenBucketLimit {
		// 配置的值小于1.0时，同时只有1个令牌，则sleep时间是1-configLimitValue
		newEmbeddingTokenBucketLimit = 1
		if newConfigLimitValue < DefaultMinimalValidEmbeddingTokenBucketLimit {
			// 最小设置为0.1
			newConfigLimitValue = DefaultMinimalValidEmbeddingTokenBucketLimit
		}
	} else {
		if newConfigLimitValue > DefaultMaximumEmbeddingTokenBucketLimit {
			// 最大设置为100
			newConfigLimitValue = DefaultMaximumEmbeddingTokenBucketLimit
		}
		// 配置的值大于等于1.0时，同时多个令牌，则sleep时间是0
		newEmbeddingTokenBucketLimit = int(newConfigLimitValue)
	}

	if newEmbeddingTokenBucketLimit == originBucket.BucketLen {
		return
	}

	if math.Abs(newConfigLimitValue-originBucket.ConfigValue) <= 1e-6 {
		return
	}

	// 一般来讲一定会成功，因为只有这里一个地方会修改closed
	// 即使不成功，后续也会有recover兜底
	originBucket.Closed.CompareAndSwap(false, true)
	closed := atomic.Bool{}
	closed.Store(false)

	bucket := make(chan struct{}, newEmbeddingTokenBucketLimit)
	for i := 0; i < newEmbeddingTokenBucketLimit; i++ {
		bucket <- struct{}{}
	}
	EmbeddingTokenBucket.Store(&LingmaEmbedderTokenBucket{
		Closed:      &closed,
		BucketLen:   newEmbeddingTokenBucketLimit,
		TokenBucket: bucket,
		ConfigValue: newConfigLimitValue,
	})
	close(originBucket.TokenBucket)
}

// LingmaEmbedder
// 请务必使用 NewIndexLingmaEmbedder 或者 NewMemoryLingmaEmbedder
type LingmaEmbedder struct {
	HttpClient    *http.Client
	Model         string
	Dims          int
	StripNewLines bool
	EmbedderType  string
}

type EmbeddingRequest struct {
	Input []string `json:"input"`
	Type  string   `json:"type"`
	Model string   `json:"model"`
	Dims  int      `json:"dims"`
}

type Embeddings struct {
	Embeddings []EmbeddingItem `json:"embeddings"`
}

type EmbeddingItem struct {
	Embedding []float32 `json:"embedding"`
	TextIndex int       `json:"textIndex"`
}

type EmbeddingResponse struct {
	RequestId string     `json:"requestId"`
	Message   string     `json:"message"`
	Output    Embeddings `json:"output"`
	Usage     Usage      `json:"usage"`
}

type EmbeddingErrorResponse struct {
	StatusCode int    `json:"statusCode"`
	Message    string `json:"message"`
	Code       string `json:"code"`
	RequestId  string `json:"requestId"`
}

type Usage struct {
	TotalTokens int `json:"totalTokens"`
}

func NewLingmaEmbedder() *LingmaEmbedder {
	return &LingmaEmbedder{
		HttpClient: client.GetDocEmbeddingClient(),
		Dims:       DefaultDimension,
		Model:      DefaultTextEmbeddingModel,
	}
}

// 指数退避算法
func exponentialBackoff(enableTokenBucket bool, retryTimes int) time.Duration {
	if enableTokenBucket {
		// 使用令牌桶
		limitTime := limitTimes.Add(1)
		if limitTime > MaxLimitTimes {
			limitTime = MaxLimitTimes
		}
		waitTime := BaseWaitTime * (1 << limitTime) // 指数增长
		if waitTime > MaxWaitTime {
			waitTime = MaxWaitTime // 限制最大等待时间
		}
		return waitTime
	}

	// 不使用令牌桶，则使用重试次数
	waitTime := BaseWaitTime * (1 << retryTimes) // 指数增长
	if waitTime > MaxWaitTime {
		waitTime = MaxWaitTime // 限制最大等待时间
	}
	return waitTime
}

func (e LingmaEmbedder) _getEnableTokenBucket(textType string) bool {
	if textType == TextTypeQuery {
		// query类型，不启用令牌桶
		return false
	}

	// 其他不阻塞主链路的服务，使用令牌桶，防止服务端压力过大
	return true
}

func (e LingmaEmbedder) _getEndPoint(textType string) string {
	if textType == TextTypeQuery {
		// 查询类型，使用不限流endpoint
		return definition.UrlPathCodebaseEmbeddingUnlimitEndpoint
	}

	// 其他类型，使用限流endpoint
	return definition.UrlPathCodebaseEmbeddingLimitEndpoint
}

// _dealRateLimit
// 统一按照是否使用令牌桶，来处理限流
// 使用令牌桶时，endpoint一定是embedding集群
// 不使用令牌桶时，endpoint一定是lingma-embedding-api服务
func (e LingmaEmbedder) _dealRateLimit(ctx context.Context, texts []string, textType string) ([][]float32, error) {
	defer func() {
		// 有可能放回bucket时，发生panic
		if r := recover(); r != nil {
			log.Errorf("[embedding] token bucket panic, %v", r)
		}
	}()

	enableTokenBucket := e._getEnableTokenBucket(textType)
	var bucket *LingmaEmbedderTokenBucket
	var oneToken struct{}
	var getToken bool
	var embedding [][]float32
	var errResp *EmbeddingErrorResponse
	var err error

	defer func() {
		defer func() {
			// 有可能放回bucket时，发生panic
			if r := recover(); r != nil {
				log.Errorf("[embedding] token bucket panic, %v", r)
			}
		}()

		if enableTokenBucket {
			// 使用令牌桶
			// 函数返回，放回令牌，并返回错误
			if getToken && bucket != nil && !bucket.Closed.Load() {
				// 这里有可能发生panic
				bucket.TokenBucket <- oneToken
			}
		}
	}()

	// 放回令牌的时机：
	// 1. 限流结束后，单独执行一次放回
	// 2. 函数返回后，限流不限流都要执行放回
	retryTimes := 0
	for {
		if enableTokenBucket {
			// 使用令牌桶
			for {
				// 循环获取token令牌
				bucket = EmbeddingTokenBucket.Load().(*LingmaEmbedderTokenBucket)
				getToken = false
				oneToken, getToken = <-bucket.TokenBucket
				if getToken {
					// 确定正确获取到令牌后，放行，否则持续阻塞
					break
				}
			}
		}

		startTime := time.Now()
		embedding, errResp, err = e._executeEmbedRequest(ctx, texts, textType)
		costTime := time.Since(startTime)

		if err != nil {
			// 这里代表出现错误
			return nil, err
		}

		// 服务端出现错误，可能触发限流错误
		if errResp != nil {
			if errResp.StatusCode == 429 {
				retryTimes += 1

				// 这里直接返回错误，不再重试
				if (enableTokenBucket && retryTimes > MaxHighFrequencyRetryTimes) ||
					(!enableTokenBucket && retryTimes > MaxLowFrequencyRetryTimes) {
					log.Errorf("embedding rate limiting error too many times")
					return nil, errors.New("embedding rate limiting error too many times")
				}

				var timeToSleep time.Duration
				// 计算休眠时间
				if enableTokenBucket {
					// 使用令牌桶的高频操作
					timeToSleep = exponentialBackoff(enableTokenBucket, retryTimes)
					log.Debugf("[index building] embedding detect rate limiting, to sleep time: [%.2fs]", timeToSleep.Seconds())
				} else {
					// 不使用令牌桶的低频操作
					// 使用一个随机时间来进行休眠，避免瞬间的大量请求
					randomSleepTime := 1.0 + rand.Float64() // 范围 [1.0, 2.0)
					if randomSleepTime > 2.0 {
						randomSleepTime = 2.0
					}
					timeToSleep = time.Duration(randomSleepTime * float64(time.Second))
					log.Debugf("[query|memory] embedding detect rate limiting, to sleep time: [%.2fs]", timeToSleep.Seconds())
				}

				// 执行休眠
				if timeToSleep.Seconds() > 0.0 {
					time.Sleep(timeToSleep)
				}

				if enableTokenBucket {
					// 使用令牌桶的高频操作
					// 限流sleep结束，放回令牌
					if getToken && bucket != nil && !bucket.Closed.Load() {
						// 防止重复放回
						getToken = false
						// 这里有可能因为令牌桶更新而发生panic，直接丢弃这次请求结果
						bucket.TokenBucket <- oneToken
					}
				}
			} else {
				// 未知错误，不再尝试
				log.Errorf("embedding detect unknown error, error code: %d, error message: %s", errResp.StatusCode, errResp.Message)
				err = errors.New(errResp.Message)
				return nil, err
			}
		} else {
			if enableTokenBucket {
				// 只有在使用令牌桶的高频操作时，才进行qps限流控制
				// 未出现预期外错误，控制qps即可

				// 这部分是精确控制qps的核心逻辑
				var sleepTime time.Duration
				if bucket != nil && bucket.ConfigValue > DefaultStopEmbeddingTokenBucketLimit && bucket.ConfigValue < definition.DefaultEmbeddingTokenBucketLimit {
					requestInterval := 10.0 / float64(bucket.ConfigValue*10)
					sleepTime = time.Duration(requestInterval*float64(time.Second)) - costTime
				} else {
					sleepTime = time.Second - costTime
				}

				if sleepTime.Seconds() > 0.0 {
					time.Sleep(sleepTime)
				}

				if limitTimes.Add(-1) < 0 {
					limitTimes.Store(0)
				}
			}

			// 正常结束请求
			break
		}

		// 兜底逻辑，不会走到这里
		if (enableTokenBucket && retryTimes > MaxHighFrequencyRetryTimes) ||
			(!enableTokenBucket && retryTimes > MaxLowFrequencyRetryTimes) {
			log.Errorf("embedding rate limiting error too many times")
			return nil, errors.New("embedding rate limiting error too many times")
		}
	}

	return embedding, nil
}

// _executeEmbedRequest 具体执行request请求
func (e LingmaEmbedder) _executeEmbedRequest(ctx context.Context, texts []string, textType string) ([][]float32, *EmbeddingErrorResponse, error) {
	if e.Dims == 0 {
		e.Dims = DefaultDimension
	}
	if e.Model == "" {
		e.Model = DefaultTextEmbeddingModel
	}
	requestBody := EmbeddingRequest{
		Input: texts,
		Model: e.Model,
		Dims:  e.Dims,
		Type:  textType,
	}

	var result EmbeddingResponse
	httpPayload := remote.HttpPayload{
		Payload:       util.ToJsonStr(requestBody),
		EncodeVersion: config.Remote.MessageEncode,
	}
	embedRequest, err := remote.BuildBigModelAuthRequest(http.MethodPost, e._getEndPoint(textType), httpPayload)
	if err != nil {
		var tokenExpiredError *definition.TokenExpiredError
		if errors.As(err, &tokenExpiredError) {
			status := definition.AuthStatusResult{
				Status:          definition.AuthStatusInit,
				Quota:           0,
				WhitelistStatus: definition.WhitelistUnknown,
			}
			websocket.SendBroadcastWithTimeout(ctx, "auth/report", status, nil)
		}
		return nil, nil, err
	}

	resp, err := e.HttpClient.Do(embedRequest)
	if err != nil {
		return nil, nil, err
	}

	defer resp.Body.Close()
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, nil, err
	}
	if resp.StatusCode != http.StatusOK {
		return nil, nil, fmt.Errorf("create embedding failed. status code: %d. response body: %s", resp.StatusCode, bodyBytes)
	}

	err = json.Unmarshal(bodyBytes, &result)
	if err != nil {
		return nil, nil, err
	}

	if result.Message != "" {
		var errResp EmbeddingErrorResponse
		if err = json.Unmarshal([]byte(result.Message), &errResp); err != nil {
			return nil, nil, err
		}
		return nil, &errResp, nil
	}

	return SortOutput(len(texts), result), nil, nil
}

type embeddingParam struct {
	embedding []float32
	index     int
}

// CutTextLimit 切割文本
// 使用token数量进行智能截断，优先保留开头内容用于embedding
func CutTextLimit(texts []string, limit int) []string {
	if len(texts) <= 0 {
		return texts
	}
	result := make([]string, 0)
	for _, text := range texts {
		tokenCount := tokenizer.GetTokenCountWithSimpleAsciiTokenizer(text)
		if tokenCount > limit {
			// 按token数量截断，保留开头内容用于embedding
			// 使用简单的字符比例估算，通常1个token约等于4个字符
			estimatedCharLimit := limit * 4
			if len(text) > estimatedCharLimit {
				// 在合适的位置截断，避免截断到单词中间
				text = text[:estimatedCharLimit]
				// 尝试在换行或空格处截断
				if lastNewline := strings.LastIndex(text, "\n"); lastNewline > estimatedCharLimit/2 {
					text = text[:lastNewline]
				} else if lastSpace := strings.LastIndex(text, " "); lastSpace > estimatedCharLimit/2 {
					text = text[:lastSpace]
				}
			}
		}
		result = append(result, text)
	}
	return result
}

// _fitBatchSizeAndExecuteEmbeddingRequest
// 将texts按照BatchSize拆分成为可以执行的batch
func (e LingmaEmbedder) _fitBatchSizeAndExecuteEmbeddingRequest(ctx context.Context, texts []string, textType string) ([][]float32, error) {
	// 截断输入
	texts = CutTextLimit(texts, DefaultMaxEmbeddingChunkSize)

	var err error
	finalResult := make([][]float32, len(texts))
	batchSize := int(EmbeddingBatchSize.Load())
	wg := sync.WaitGroup{}
	batchParam := make([]embeddingParam, 0)
	for i := 0; i < len(texts); i++ {
		if len(batchParam) < batchSize && finalResult[i] == nil {
			batchParam = append(batchParam, embeddingParam{
				embedding: nil,
				index:     i,
			})
		}

		// 满足一个batchSize或者已遍历到最后一个text时，执行一次embedding请求
		if len(batchParam) == batchSize || (i == len(texts)-1 && len(batchParam) > 0) {
			wg.Add(1)
			go func(params []embeddingParam) {
				defer wg.Done()

				batchText := make([]string, 0)
				for _, param := range params {
					batchText = append(batchText, texts[param.index])
				}
				batchEmbedding, internalErr := e._dealRateLimit(ctx, batchText, textType)

				if internalErr != nil {
					err = internalErr
					return
				}

				for idx, embedding := range batchEmbedding {
					resultIdx := params[idx].index
					if finalResult[resultIdx] == nil {
						finalResult[resultIdx] = embedding
					}
				}
			}(batchParam)
			batchParam = make([]embeddingParam, 0)
		}
	}

	wg.Wait()

	if err != nil {
		return nil, err
	}

	return finalResult, nil
}

// CreateEmbedding
// 以创建的embedding为基准，来判断是否使用令牌桶限流
// textType: 用于判断是否使用限流endpoint
// TextTypeQuery，query类型，使用不限流endpoint，不会阻塞
// TextTypeDocument，document类型，使用限流endpoint，可能会阻塞
func (e LingmaEmbedder) CreateEmbedding(ctx context.Context, texts []string, textType string) ([][]float32, error) {
	return e._fitBatchSizeAndExecuteEmbeddingRequest(ctx, texts, textType)
}

func (e LingmaEmbedder) EmbedDocuments(ctx context.Context, texts []string) ([][]float32, error) {
	batchedTexts := embeddings.BatchTexts(
		embeddings.MaybeRemoveNewLines(texts, e.StripNewLines),
		definition.DefaultEmbeddingBatchSize,
	)

	emb := make([][]float32, 0, len(texts))
	for _, batch := range batchedTexts {
		curBatchEmbeddings, err := e.CreateEmbedding(ctx, batch, TextTypeDocument)
		if err != nil {
			return nil, err
		}
		emb = append(emb, curBatchEmbeddings...)
	}

	return emb, nil
}

func (e LingmaEmbedder) EmbedQuery(ctx context.Context, text string) ([]float32, error) {
	if e.StripNewLines {
		text = strings.ReplaceAll(text, "\n", " ")
	}

	emb, err := e.CreateEmbedding(ctx, []string{text}, TextTypeQuery)
	if err != nil {
		return nil, err
	}

	return emb[0], nil
}

func SortOutput(inputLen int, embedResult EmbeddingResponse) [][]float32 {
	embeds := embedResult.Output.Embeddings
	sort.Slice(embeds, func(i, j int) bool {
		return embeds[i].TextIndex < embeds[j].TextIndex
	})
	finalResult := make([][]float32, inputLen)
	for _, item := range embeds {
		// TODO 这里有个莫名其妙的BUG，一直查不出来是为什么，报的错是数组越界
		if item.TextIndex >= inputLen {
			continue
		}
		finalResult[item.TextIndex] = item.Embedding
	}
	return finalResult
}
