package components

import (
	"bytes"
	"cosy/client"
	"cosy/definition"
	"cosy/log"
	"cosy/remote"
	"cosy/user"
	"encoding/json"
	"errors"
	"fmt"
	"image"
	"image/jpeg"
	"image/png"
	"io"
	"math"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/chai2010/webp"
	"golang.org/x/image/tiff"

	"github.com/disintegration/imaging"
	"github.com/google/uuid"
)

const (
	jpegMagic             = "\xFF\xD8"
	pngMagic              = "\x89PNG\r\n\x1A\n"
	tiffLittleEndianMagic = "\x49\x49\x2A\x00"
	tiffBigEndianMagic    = "\x4D\x4D\x00\x2A"
	webpFirstPartMagic    = "\x52\x49\x46\x46"
	webpSecondPartMagic   = "\x57\x45\x42\x50"

	JpgExt  string = ".jpg"
	PngExt  string = ".png"
	TiffExt string = ".tiff"
	WebpExt string = ".webp"
)

// resizeImage
// 三方库缩放图像
func resizeImage(img image.Image, scale float32) image.Image {
	width := int(float32(img.Bounds().Dx()) * scale)
	height := int(float32(img.Bounds().Dy()) * scale)
	return imaging.Resize(img, width, height, imaging.Lanczos)
}

// CompressImage 压缩图片，并存储在lingma目录的tmp目录下
// imagePath参数是绝对路径
// target 单位为B，默认压缩至3MB下，3 * 1024 * 1024
// 返回压缩后的图片绝对路径，该图片为最终上传至OSS的图片
func CompressImage(imagePath string, targetSize int64) (*bytes.Buffer, error) {
	imageFile, err := os.Open(imagePath)
	if err != nil {
		return nil, err
	}
	defer imageFile.Close()

	imageExt, err := DetectImageExt(imagePath)
	if err != nil {
		log.Errorf("detect image type error: %+v", err)
		return nil, err
	}
	log.Infof("image detect success, ext is %s", imageExt)

	// 解码图像
	var img image.Image
	switch imageExt {
	case JpgExt:
		img, err = jpeg.Decode(imageFile)
	case PngExt:
		img, err = png.Decode(imageFile)
	case TiffExt:
		img, err = tiff.Decode(imageFile)
	case WebpExt:
		img, err = webp.Decode(imageFile)
	default:
		return nil, err
	}

	// 检查图片分辨率
	if img.Bounds().Max.X-img.Bounds().Min.X <= 12 || img.Bounds().Max.Y-img.Bounds().Min.Y <= 12 {
		log.Errorf("image resolution is too small, the minimal resolution is 12x12")
		return nil, errors.New("illegal image resolution")
	}

	stat, err := os.Stat(imagePath)
	if err != nil {
		return nil, err
	}
	if stat.Size() <= targetSize {
		// 原图大小合适，不需要压缩
		log.Infof("image size is %d, less than %d, no need to compress", stat.Size(), targetSize)

		file, fileOpenErr := os.Open(imagePath)
		if fileOpenErr != nil {
			return nil, fileOpenErr
		}
		defer file.Close()

		// 创建一个足够大的字节切片来存储文件内容
		data := make([]byte, stat.Size())

		// 读取文件内容到字节切片
		_, fileReadErr := io.ReadFull(file, data)
		if fileReadErr != nil {
			return nil, fileReadErr
		}

		buf := bytes.Buffer{}
		buf.Write(data)
		return &buf, nil
	}

	log.Infof("image size is %d, larger than %d, start to compress", stat.Size(), targetSize)
	originImageSize := float32(stat.Size())
	// 开始图片缩放压缩
	scale := float32(int(float32(targetSize)/originImageSize*10)) / 10.0 // 初始缩放比例
	if scale <= 0.0 {
		// 图片过大，30M以上
		// 0.1可以保证至少有2次的压缩
		scale = 0.1
	}
	// 循环压缩，直至满足要求，或者无法压缩
	buf := bytes.Buffer{}
	compressTimes := 0

	for {
		compressTimes += 1
		buf.Reset()
		// 缩放图片
		resizedImg := resizeImage(img, scale)

		// 编码为指定格式
		switch imageExt {
		case JpgExt:
			err = jpeg.Encode(&buf, resizedImg, &jpeg.Options{Quality: 85})
		case PngExt:
			err = png.Encode(&buf, resizedImg)
		case TiffExt:
			err = tiff.Encode(&buf, resizedImg, &tiff.Options{Compression: tiff.Deflate})
		case WebpExt:
			err = webp.Encode(&buf, resizedImg, &webp.Options{Quality: 85})
		}
		if err != nil {
			return nil, err
		}

		// 检查当前大小
		if int64(buf.Len()) <= targetSize {
			log.Infof("under %d rounds of compression, compressed image size is %d", compressTimes, buf.Len())
			return &buf, err
		}

		scale *= definition.CompressImageStep // 每次缩小比例
		if scale < definition.MinCompressScale {
			return nil, fmt.Errorf("failed to compress image, under %d rounds of compression, size still larger than target", compressTimes)
		}
	}
}

// DetectImageExt
// 根据图片magicNum来检测文件类型
func DetectImageExt(filePath string) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	// 创建一个 20 字节的缓冲区
	buffer := make([]byte, 20)

	// 读取前 20 个字节
	n, err := io.ReadFull(file, buffer)
	if err != nil && err != io.EOF {
		return "", err
	}

	// 如果读取的字节数少于 20，则说明文件长度不足 20 字节
	if n < 20 {
		return "", errors.New("file length is less than 20 bytes")
	}

	if bytes.HasPrefix(buffer, []byte(jpegMagic)) {
		// 检查JPEG
		return JpgExt, nil
	} else if bytes.HasPrefix(buffer, []byte(pngMagic)) {
		// 检查PNG
		return PngExt, nil
	} else if bytes.HasPrefix(buffer, []byte(tiffLittleEndianMagic)) || bytes.HasPrefix(buffer, []byte(tiffBigEndianMagic)) {
		// 检查TIFF
		return TiffExt, nil
	} else if bytes.HasPrefix(buffer, []byte(webpFirstPartMagic)) && bytes.Contains(buffer, []byte(webpSecondPartMagic)) {
		// 检查WebP
		return WebpExt, nil
	}

	return "", errors.New("unknown image type")
}

// UploadImage 将本地压缩后的图片上传后端
// imagePath参数是绝对路径
// 返回URL地址，可能是oss_url，也可能是其他url地址
func UploadImage(imagePath string) (*definition.UploadImageResponse, error) {
	userInfo := user.GetCachedUserInfo()
	if userInfo == nil {
		return nil, errors.New("user not login")
	}

	beginTimeStamp := time.Now()
	imageBuf, err := CompressImage(imagePath, definition.TargetCompressSize)
	if err != nil {
		log.Errorf("when compressing image error: %+v", err)
		return nil, err
	}
	finishTimeStamp := time.Now()
	log.Infof("compress image cost time %fs", finishTimeStamp.Sub(beginTimeStamp).Seconds())

	httpPayload := &bytes.Buffer{}
	writer := multipart.NewWriter(httpPayload)

	formFile, err := writer.CreateFormFile("file", filepath.Base(imagePath))
	if err != nil {
		log.Errorf("when uploading, create form file error: %+v", err)
		return nil, err
	}

	_, err = io.Copy(formFile, imageBuf)
	if err != nil {
		log.Errorf("when uploading, copy image error: %+v", err)
		return nil, err
	}

	err = writer.Close()
	if err != nil {
		log.Errorf("when uploading, writer from file error: %+v", err)
		return nil, err
	}

	requestUrl := definition.UrlPathUploadImageEndpoint + "?request_id=" + strings.ReplaceAll(uuid.NewString(), "-", "")
	req, err := remote.BuildBigModelAuthUploadFileRequest(http.MethodPut, requestUrl, httpPayload.Bytes())

	if err != nil {
		log.Errorf("upload image build big model auth request error: %v", err)
		return nil, err
	}
	req.Header.Set("AI-CLIENT-TIMESTAMP", strconv.FormatInt(time.Now().Unix(), 10))
	req.Header.Set("Content-Type", writer.FormDataContentType())
	//req.Header.Set("Content-Type", "application/octet-stream")

	beginTimeStamp = time.Now()
	resp, err := client.GetUploadFileClient().Do(req)
	finishTimeStamp = time.Now()
	log.Infof("upload image cost time %fs", finishTimeStamp.Sub(beginTimeStamp).Seconds())

	if err != nil {
		log.Errorf("do big model auth request error when uploading image, the err: %v", err)
		return nil, err
	}
	defer resp.Body.Close()

	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Errorf("read response body error when uploading image, the err: %v", err)
		return nil, err
	}

	//{"success":false,"traceId":"","msgCode":500,"msgInfo":"Internal Server Error","message":"Internal Server Error"}
	var result definition.UploadImageResponse
	err = json.Unmarshal(bodyBytes, &result)
	if err != nil {
		log.Errorf("unmarshal response body error when uploading image, the err: %v", err)
		return nil, err
	}
	if result.Success {
		log.Infof("upload image success, requestId: %s", result.RequestId)
		log.Debugf("> imageUrl: %+v", result.Result.Url)
	} else {
		log.Errorf("upload image failed, requestId: %s, errorMsg: %+v", result.RequestId, result.Message)
	}

	return &result, nil
}

// CalculateTokens calculates the number of tokens based on the image URL and the new ratio.
func CalculateTokens(imgURL string) (int, error) {
	// 下载图片
	resp, err := http.Get(imgURL)
	if err != nil {
		return 0, fmt.Errorf("failed to download image: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return 0, fmt.Errorf("bad status: %s", resp.Status)
	}

	// 解析图片
	img, _, err := image.Decode(resp.Body)
	if err != nil {
		return 0, fmt.Errorf("failed to decode image: %v", err)
	}

	// 获取图片尺寸
	bounds := img.Bounds()
	width, height := bounds.Max.X, bounds.Max.Y

	// 定义每个Token对应的像素大小
	tokenSize := 28

	// 计算向上取整后的宽度和高度
	paddedWidth := int(math.Ceil(float64(width)/float64(tokenSize))) * tokenSize
	paddedHeight := int(math.Ceil(float64(height)/float64(tokenSize))) * tokenSize

	// 计算Token数量
	tokens := (paddedWidth * paddedHeight) / (tokenSize * tokenSize)

	return tokens, nil
}
