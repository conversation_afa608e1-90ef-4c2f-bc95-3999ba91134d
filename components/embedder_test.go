package components

import (
	"context"
	"cosy/config"
	"cosy/definition"
	"cosy/definition/environment"
	"cosy/experiment"
	"cosy/util/distance"
	"crypto/rand"
	"encoding/json"
	"fmt"
	"math/big"
	"os"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"cosy/client"
)

func TestLingmaEmbedder_Multi_Workspace(t *testing.T) {
	config.InitLocalConfig()
	client.InitClients()

	textLen := 100
	texts := make([]string, 0)
	for i := 0; i < textLen; i++ {
		texts = append(texts, fmt.Sprintf("这是一段测试文本%d", i))
	}
	fmt.Println("textLen: ", textLen)

	requestCnt := 0

	env := environment.ExpKey2EnvKey(definition.ExperimentKeyEmbeddingTokenBucketLimit, experiment.ConfigScopeClient)
	os.Setenv(env, strconv.FormatFloat(100.0, 'f', -1, 64))
	UpdateEmbeddingTokenBucket()

	wg := sync.WaitGroup{}
	times := 1
	wg.Add(times)
	for i := 0; i < times; i++ {
		go func() {
			defer wg.Done()
			e := NewLingmaEmbedder()

			embedding, err := e.CreateEmbedding(context.Background(), texts, TextTypeDocument)
			if err != nil {
				t.Errorf("CreateEmbedding() error = %v", err)
				return
			}
			requestCnt += 1

			cnt := 0
			for idx := range embedding {
				if embedding[idx] != nil {
					cnt++
				}
			}
			assert.Equal(t, cnt, textLen)
			//fmt.Printf("embedding count: %d\n", cnt)
		}()
	}
	wg.Wait()

}

func TestLingmaEmbedder_WithoutTokenBucket(t *testing.T) {
	config.InitLocalConfig()
	client.InitClients()

	textLen := 100
	texts := make([]string, 0)
	for i := 0; i < textLen; i++ {
		texts = append(texts, fmt.Sprintf("这是一段测试文本%d", i))
	}
	fmt.Println("textLen: ", textLen)

	requestCnt := 0

	e := NewLingmaEmbedder()
	wg := sync.WaitGroup{}
	times := 1000
	wg.Add(times)
	for i := 0; i < times; i++ {
		go func() {
			defer wg.Done()
			embedding, err := e.CreateEmbedding(context.Background(), texts[0:1], TextTypeQuery)
			if err != nil {
				t.Errorf("CreateEmbedding() error = %v", err)
				return
			}
			requestCnt += 1

			cnt := 0
			for idx := range embedding {
				if embedding[idx] != nil {
					cnt++
				}
			}
			//assert.Equal(t, cnt, textLen)
			//fmt.Printf("embedding count: %d\n", cnt)
		}()
	}
	wg.Wait()

}

func TestLingmaEmbedder_TokenBucket(t *testing.T) {
	config.InitLocalConfig()
	client.InitClients()

	textLen := 100
	texts := make([]string, 0)
	for i := 0; i < textLen; i++ {
		texts = append(texts, fmt.Sprintf("这是一段测试文本%d", i))
	}
	fmt.Println("textLen: ", textLen)

	requestCnt := 0

	go func() {
		// 随机变更环境变量配置
		env := environment.ExpKey2EnvKey(definition.ExperimentKeyEmbeddingTokenBucketLimit, experiment.ConfigScopeClient)
		fmt.Println("env:", env)

		configLimit := []float64{
			0.0,
			0.1,
			0.5,
			1.0,
			3.0,
		}

		var limitIdx int

		for {
			fmt.Println("==========配置的值为：", configLimit[limitIdx], "时, 10s内总共请求次数为：", requestCnt)
			requestCnt = 0
			limitIdx = (limitIdx + 1) % len(configLimit)
			limit := configLimit[limitIdx]
			//fmt.Println("修改的limit为：", limit)
			os.Setenv(env, strconv.FormatFloat(limit, 'f', -1, 64))
			UpdateEmbeddingTokenBucket()
			time.Sleep(time.Second * 10)
		}
	}()

	e := NewLingmaEmbedder()
	wg := sync.WaitGroup{}
	times := 100
	wg.Add(times)
	for i := 0; i < times; i++ {
		go func() {
			defer wg.Done()
			embedding, err := e.CreateEmbedding(context.Background(), texts[0:1], TextTypeDocument)
			if err != nil {
				t.Errorf("CreateEmbedding() error = %v", err)
				return
			}
			requestCnt += 1

			cnt := 0
			for idx := range embedding {
				if embedding[idx] != nil {
					cnt++
				}
			}
			//assert.Equal(t, cnt, textLen)
			//fmt.Printf("embedding count: %d\n", cnt)
		}()
	}
	wg.Wait()

}

func TestLingmaEmbedder_Similar(t *testing.T) {
	t.Skip("manually")
	config.InitLocalConfig()
	client.InitClients()
	e := NewLingmaEmbedder()
	embedding, err := e.CreateEmbedding(context.Background(), []string{"生成注释", "新增批量添加收藏到购物车并清空收藏夹功能 在实现批量添加收藏商品到购物车并清空收藏夹的功能时，应该同步编写单元测试，以确保功能的正确性和稳定性。", "代码注释规范 为方法添加详细的 JavaDoc 注释，包括方法的主要功能说明、参数说明和详细描述，以帮助其他开发者快速理解代码功能并支持后续维护工作。"}, TextTypeQuery)
	assert.Nil(t, err)
	fmt.Println(distance.CosineSimilarity(embedding[0], embedding[1]))
	fmt.Println(distance.CosineSimilarity(embedding[0], embedding[2]))
	fmt.Println(distance.CosineSimilarity(embedding[0], embedding[3]))
	jsonBytes, err := json.Marshal(embedding)
	if err != nil {
		return
	}
	fmt.Println(string(jsonBytes))
}

// 测试带有退避算法的新embedding向量是否与原来的embedding流程相同
func TestLingmaEmbedder_Correction(t *testing.T) {
	config.InitLocalConfig()
	client.InitClients()

	textLen := 10
	texts := make([]string, 0)
	for i := 0; i < textLen; i++ {
		n, err := rand.Int(rand.Reader, big.NewInt(int64(textLen)))
		if err != nil {
			t.Errorf("rand.Int() error = %v", err)
			return
		}
		texts = append(texts, fmt.Sprintf("这是一段测试文本%d", n))
	}

	e := NewLingmaEmbedder()
	newEmbeddings, err := e.CreateEmbedding(context.Background(), texts, TextTypeDocument)
	if err != nil {
		t.Errorf("CreateEmbedding() error = %v", err)
		return
	}
	cnt := 0
	for i := range newEmbeddings {
		if newEmbeddings[i] != nil {
			cnt++
			fmt.Println(len(newEmbeddings[i]))
		}
	}
	assert.Equal(t, textLen, cnt)

	oldEmbeddings, resp, err := e._executeEmbedRequest(context.Background(), texts, TextTypeDocument)
	if err != nil {
		t.Errorf("_dealRateLimit() error = %v", err)
		return
	}
	assert.Nil(t, resp)

	//var oldEmbeddings [][]float32
	//for i := 0; i < textLen; i++ {
	//	time.Sleep(1 * time.Second)
	//	oldEmbedding, resp, err := e._dealRateLimit(context.Background(), []string{texts[i]})
	//	if err != nil {
	//		t.Errorf("_dealRateLimit() error = %v", err)
	//		return
	//	}
	//
	//	assert.Nil(t, resp)
	//	oldEmbeddings = append(oldEmbeddings, oldEmbedding[0])
	//}

	cnt = 0
	for i := range oldEmbeddings {
		if oldEmbeddings[i] != nil {
			cnt++
			fmt.Println(len(oldEmbeddings[i]))
		}
	}
	assert.Equal(t, textLen, cnt)

	for i := 0; i < textLen; i++ {
		oldEmbedding := oldEmbeddings[i]
		newEmbedding := newEmbeddings[i]
		assert.InDeltaSlice(t, oldEmbedding, newEmbedding, 0.001)
	}
}

func TestLingmaEmbedder_CreateEmbedding_StressTest(t *testing.T) {
	config.InitLocalConfig()
	client.InitClients()

	textLen := 5000
	texts := make([]string, 0)
	for i := 0; i < textLen; i++ {
		n, err := rand.Int(rand.Reader, big.NewInt(int64(textLen)))
		if err != nil {
			t.Errorf("rand.Int() error = %v", err)
			return
		}
		texts = append(texts, fmt.Sprintf("这是一段测试文本%d", n))
	}

	e := NewLingmaEmbedder()
	wg := sync.WaitGroup{}
	times := 1
	wg.Add(times)
	for i := 0; i < times; i++ {
		go func() {
			defer wg.Done()
			embedding, err := e.CreateEmbedding(context.Background(), texts, TextTypeDocument)
			if err != nil {
				t.Errorf("CreateEmbedding() error = %v", err)
				return
			}

			cnt := 0
			for i := range embedding {
				if embedding[i] != nil {
					cnt++
				}
			}
			assert.Equal(t, cnt, textLen)
			fmt.Printf("embedding count: %d", cnt)
		}()
	}
	wg.Wait()

}

func TestLingmaEmbedder_CreateEmbedding(t *testing.T) {
	tests := []struct {
		name          string
		StripNewLines bool
		BatchSize     int
		ctx           context.Context
		texts         []string
		want          [][]float32
		wantErr       bool
	}{
		{
			name:          "create embedding",
			StripNewLines: true,
			BatchSize:     10,
			texts:         []string{"衣服质量很好", "衣服质量不太行"},
			ctx:           context.Background(),
		},
	}
	config.InitLocalConfig()
	client.InitClients()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			e := NewLingmaEmbedder()
			embeddings, err := e.CreateEmbedding(tt.ctx, tt.texts, TextTypeDocument)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateEmbedding() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if len(embeddings) <= 0 {
				t.Errorf("CreateEmbedding() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func Test_sortOutput(t *testing.T) {
	tests := []struct {
		name        string
		inputLen    int
		embedResult EmbeddingResponse
		want        [][]float32
	}{
		{
			name:     "length equal",
			inputLen: 5,
			embedResult: EmbeddingResponse{Output: Embeddings{
				[]EmbeddingItem{
					{Embedding: []float32{1, 2, 3, 4, 5}, TextIndex: 0},
					{Embedding: []float32{2, 3, 4, 5, 6}, TextIndex: 1},
					{Embedding: []float32{3, 4, 5, 6, 7}, TextIndex: 2},
					{Embedding: []float32{4, 5, 6, 7, 8}, TextIndex: 3},
					{Embedding: []float32{5, 6, 7, 8, 9}, TextIndex: 4},
				},
			}},
			want: [][]float32{{1, 2, 3, 4, 5}, {2, 3, 4, 5, 6}, {3, 4, 5, 6, 7}, {4, 5, 6, 7, 8}, {5, 6, 7, 8, 9}},
		},
		{
			name:     "lack first",
			inputLen: 5,
			embedResult: EmbeddingResponse{Output: Embeddings{
				[]EmbeddingItem{
					{Embedding: []float32{2, 3, 4, 5, 6}, TextIndex: 1},
					{Embedding: []float32{3, 4, 5, 6, 7}, TextIndex: 2},
					{Embedding: []float32{4, 5, 6, 7, 8}, TextIndex: 3},
					{Embedding: []float32{5, 6, 7, 8, 9}, TextIndex: 4},
				},
			}},
			want: [][]float32{nil, {2, 3, 4, 5, 6}, {3, 4, 5, 6, 7}, {4, 5, 6, 7, 8}, {5, 6, 7, 8, 9}},
		},
		{
			name:     "lack middle",
			inputLen: 5,
			embedResult: EmbeddingResponse{Output: Embeddings{
				[]EmbeddingItem{
					{Embedding: []float32{1, 2, 3, 4, 5}, TextIndex: 0},
					{Embedding: []float32{2, 3, 4, 5, 6}, TextIndex: 1},
					{Embedding: []float32{4, 5, 6, 7, 8}, TextIndex: 3},
					{Embedding: []float32{5, 6, 7, 8, 9}, TextIndex: 4},
				},
			}},
			want: [][]float32{{1, 2, 3, 4, 5}, {2, 3, 4, 5, 6}, nil, {4, 5, 6, 7, 8}, {5, 6, 7, 8, 9}},
		},
		{
			name:     "lack last",
			inputLen: 5,
			embedResult: EmbeddingResponse{Output: Embeddings{
				[]EmbeddingItem{
					{Embedding: []float32{1, 2, 3, 4, 5}, TextIndex: 0},
					{Embedding: []float32{2, 3, 4, 5, 6}, TextIndex: 1},
					{Embedding: []float32{3, 4, 5, 6, 7}, TextIndex: 2},
					{Embedding: []float32{4, 5, 6, 7, 8}, TextIndex: 3},
				},
			}},
			want: [][]float32{{1, 2, 3, 4, 5}, {2, 3, 4, 5, 6}, {3, 4, 5, 6, 7}, {4, 5, 6, 7, 8}, nil},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := SortOutput(tt.inputLen, tt.embedResult); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("sortOutput() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_CreateEmbeddingRT(t *testing.T) {
	tests := []struct {
		name          string
		concurrency   int
		texts         []string
		StripNewLines bool
		BatchSize     int
		ctx           context.Context
	}{
		{
			concurrency:   1,
			texts:         []string{"以下是一个Go的单元测试示例"},
			BatchSize:     10,
			StripNewLines: true,
			ctx:           context.Background(),
		},
		{
			concurrency: 10,
			texts: []string{"以下是一个Go的单元测试示例", "展示了如何编写测试函数和测试用例", "提供了一些方法来报告和记录测试失败", "最常用的是Error，Errorf，Fail和FailNow", "t.Run允许在测试函数内部运行“子测试”",
				"t.Run接收两个参数：子测试的名称和一个函数", "这个函数本身符合测试函数的签名", "这种机制允许更好的测试用例组织和更易读的测试输出。", "测试用例通常通过定义一个包含测试参数和期望结果的匿名结构体切片来表示。",
				"反馈问题 : root登录报错\n原因分析：判断用户多次密码输错后锁住\n解决方法：通过内置的命令行工具解锁root并重置密码。\n\n反馈问题： 站点管理后台发现进行发现大量用户被三方APIserver同步删除，\n原因分析：在API服务端返回数据时，由于API请求被设计为分两次执行，其中一次返回部门信息，另一次返回用户信息。但云效的设计要求一次性返回部门和用户信息。这种设计差异导致在获取用户信息时返回结果为空。此外，在API同步配置中，当双方用户差异时，配置为删除云效账号，这最终导致大量用户被错误地删除。\n解决方法：关闭三方同步的配置。并通过sql临时恢复数据，恢复页面端登录。\n\n反馈问题：部分镜像拉取不到\n原因分析：版本升级中，引入了新的镜像和模型镜像，镜像内容较大，由于镜像在本地的存储默认是放着系统卷上，由于系统卷空间不足，部分镜像没有导入，应用容器在拉取镜像时就无法找到镜像。\n解决方法：手动调整镜像存储目录到数据卷上，并重新导入所有镜像。\n\n反馈问题： 鉴权服务中报csrf错误\n原因分析：排查日志发现，鉴权服务中报csrf错误，此问题曾在家里环境碰到过，第一时间想到是不是镜像问题， 跟运维同学确认此时镜像版本号，发现新给的镜像跟建行版本中镜像不一致，询问运维同学为啥没用建行大版本中的镜像，反馈是因为本地镜像导入不成功，后面又单独要了一个其他镜像，导致了不一致。\n解决办法：把镜像更正成建行正确版本镜像就修复了。\n\n反馈问题：Jetbrans IDea端灵码无法登录账号\n原因分析：经日志排查分析，发现后端服务lingma-server在调用lingma-web的api时超时，超时原因是因为域名解析超时，怀疑是k8s的网络以及dns服务存在问题，具体原因还需本周五晚上进一步排查。\n解决方法：由于没有找到根因，采用了临时解决方案，经发现只有lingma-server和lingma-web在不同的机器上，会遇到域名解析超时问题，因为通过调度调整，将lingma-server和lingma-web部署在同一个机器上就解决了问题。\n\n反馈问题： WEB网页登录的显示仍然是通义灵码\n原因分析：运维步骤更换名称跟图片还需要额外配置，跟运维同学视频沟通分析\n解决办法：按运维配置文档把建行的方舟灵码，描述，logo配置好重启更新镜像即可，此过程中也碰到上面dns网络问题，已经有前面经验后，直接调度让登录页面节点也飘走到lingma2同一个节点解决。"},
			BatchSize:     10,
			StripNewLines: true,
			ctx:           context.Background(),
		},
		{
			concurrency: 50,
			texts: []string{"以下是一个Go的单元测试示例", "展示了如何编写测试函数和测试用例", "提供了一些方法来报告和记录测试失败", "最常用的是Error，Errorf，Fail和FailNow", "t.Run允许在测试函数内部运行“子测试”",
				"t.Run接收两个参数：子测试的名称和一个函数", "这个函数本身符合测试函数的签名", "这种机制允许更好的测试用例组织和更易读的测试输出。", "测试用例通常通过定义一个包含测试参数和期望结果的匿名结构体切片来表示。",
				"反馈问题 : root登录报错\n原因分析：判断用户多次密码输错后锁住\n解决方法：通过内置的命令行工具解锁root并重置密码。\n\n反馈问题： 站点管理后台发现进行发现大量用户被三方APIserver同步删除，\n原因分析：在API服务端返回数据时，由于API请求被设计为分两次执行，其中一次返回部门信息，另一次返回用户信息。但云效的设计要求一次性返回部门和用户信息。这种设计差异导致在获取用户信息时返回结果为空。此外，在API同步配置中，当双方用户差异时，配置为删除云效账号，这最终导致大量用户被错误地删除。\n解决方法：关闭三方同步的配置。并通过sql临时恢复数据，恢复页面端登录。\n\n反馈问题：部分镜像拉取不到\n原因分析：版本升级中，引入了新的镜像和模型镜像，镜像内容较大，由于镜像在本地的存储默认是放着系统卷上，由于系统卷空间不足，部分镜像没有导入，应用容器在拉取镜像时就无法找到镜像。\n解决方法：手动调整镜像存储目录到数据卷上，并重新导入所有镜像。\n\n反馈问题： 鉴权服务中报csrf错误\n原因分析：排查日志发现，鉴权服务中报csrf错误，此问题曾在家里环境碰到过，第一时间想到是不是镜像问题， 跟运维同学确认此时镜像版本号，发现新给的镜像跟建行版本中镜像不一致，询问运维同学为啥没用建行大版本中的镜像，反馈是因为本地镜像导入不成功，后面又单独要了一个其他镜像，导致了不一致。\n解决办法：把镜像更正成建行正确版本镜像就修复了。\n\n反馈问题：Jetbrans IDea端灵码无法登录账号\n原因分析：经日志排查分析，发现后端服务lingma-server在调用lingma-web的api时超时，超时原因是因为域名解析超时，怀疑是k8s的网络以及dns服务存在问题，具体原因还需本周五晚上进一步排查。\n解决方法：由于没有找到根因，采用了临时解决方案，经发现只有lingma-server和lingma-web在不同的机器上，会遇到域名解析超时问题，因为通过调度调整，将lingma-server和lingma-web部署在同一个机器上就解决了问题。\n\n反馈问题： WEB网页登录的显示仍然是通义灵码\n原因分析：运维步骤更换名称跟图片还需要额外配置，跟运维同学视频沟通分析\n解决办法：按运维配置文档把建行的方舟灵码，描述，logo配置好重启更新镜像即可，此过程中也碰到上面dns网络问题，已经有前面经验后，直接调度让登录页面节点也飘走到lingma2同一个节点解决。"},
			BatchSize:     25,
			StripNewLines: true,
			ctx:           context.Background(),
		},
	}
	client.InitClients()
	config.InitLocalConfig()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			wg := sync.WaitGroup{}
			wg.Add(tt.concurrency)
			for i := 0; i < tt.concurrency; i++ {
				go func(i int) {
					defer wg.Done()
					start := time.Now()
					e := NewLingmaEmbedder()
					_, err := e.CreateEmbedding(tt.ctx, []string{tt.texts[i%len(tt.texts)]}, TextTypeQuery)
					if err != nil {
						fmt.Printf("%d err: %s\n", i, err.Error())
					} else {
						end := time.Now()
						fmt.Printf("%d RT: %v\n", i, end.Sub(start))
					}
				}(i)
			}
			wg.Wait()
		})
	}
}

func Benchmark_Embedding(b *testing.B) {
	text := "这是测试文本。"
	ctx := context.Background()
	concurrency := 30
	duration := 60 * time.Second
	config.InitLocalConfig()
	client.InitClients()
	tokens := make(chan struct{}, concurrency)
	const letters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"

	b.Run(fmt.Sprintf("Concurrency%d", concurrency), func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < concurrency; i++ {
			go func() {
				for {
					tokens <- struct{}{}
					time.Sleep(1 * time.Second)
				}
			}()
		}

		for i := 0; i < concurrency; i++ {
			go func(i int) {
				for {
					<-tokens
					e := NewLingmaEmbedder()
					random := ""
					for n := 0; n < b.N; n++ {
						for i := 0; i < 10; i++ {
							num, _ := rand.Int(rand.Reader, big.NewInt(int64(len(letters))))
							random += string(letters[num.Int64()])
						}
						_, err := e.CreateEmbedding(ctx, []string{text + random}, TextTypeQuery)
						if err != nil {
							b.Error(err)
						}
						fmt.Printf("routine %d embedding done\n", i)
					}
				}
			}(i)
		}

		time.Sleep(duration)
		b.StopTimer()
	})

}

func TestCutTextLimit(t *testing.T) {
	// 创建一个超过限制的长文本
	longText := strings.Repeat("这是一个很长的代码块，包含了大量的代码内容。", 200)

	// 测试截断功能
	texts := []string{longText}
	limit := 100 // 设置较小的限制

	result := CutTextLimit(texts, limit)

	if len(result) != 1 {
		t.Errorf("Expected 1 result, got %d", len(result))
	}

	if len(result[0]) >= len(longText) {
		t.Errorf("Expected text to be truncated, original length: %d, result length: %d",
			len(longText), len(result[0]))
	}

	// 测试短文本不被截断
	shortText := "这是一个短文本"
	shortTexts := []string{shortText}
	shortResult := CutTextLimit(shortTexts, 1000)

	if shortResult[0] != shortText {
		t.Errorf("Short text should not be modified")
	}
}

func TestCutTextLimitWithCodeBlock(t *testing.T) {
	// 模拟一个长代码块
	codeBlock := `
func LongFunction() {
	// 这是一个非常长的函数
	for i := 0; i < 1000; i++ {
		fmt.Println("这是第", i, "行代码")
		// 更多的代码内容...
		if i%10 == 0 {
			fmt.Println("每十行输出一次")
		}
	}
}
` + strings.Repeat("// 更多的注释和代码...\n", 200)

	texts := []string{codeBlock}
	limit := 50 // 很小的限制

	result := CutTextLimit(texts, limit)

	if len(result[0]) >= len(codeBlock) {
		t.Errorf("Code block should be truncated")
	}

	// 验证截断后的内容仍然以换行符结尾或包含完整的行
	if !strings.Contains(result[0], "func LongFunction()") {
		t.Errorf("Should preserve the beginning of the code block")
	}
}
