package graph

import (
	"code.alibaba-inc.com/cosy/lingma-codebase-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-codebase-graph/storage"
	"context"
	"cosy/ide"
	"strconv"
)

const DefaultSymbolMaxCount = 15
const DefaultRelationMaxCount = 10

type LocateNodeQuery struct {
	WorkspacePath string
	FilePath      string
	StartLine     int32
	EndLine       int32
}

type TravelGraphQuery struct {
	WorkspacePath string
	FilePath      string
	StartOffset   int32
	EndOffset     int32
	Reverse       bool
}

type ExpandGraphQuery struct {
	WorkspacePath string
	FilePath      string
	StartLine     int32
	EndLine       int32
}

type Node struct {
	NodeId      string `json:"nodeId"`
	NodeName    string `json:"nodeName"`
	NodeType    string `json:"nodeType"`
	WorkSpace   string `json:"workSpace"`
	Filepath    string `json:"filepath"`
	StartLine   int32  `json:"startLine"`
	EndLine     int32  `json:"endLine"`
	StartOffset int32  `json:"startOffset"`
	EndOffset   int32  `json:"endOffset"`
	Snippet     string `json:"snippet"`
}

type Edge struct {
	SourceId string `json:"sourceId"`
	TargetId string `json:"targetId"`
	EdgeType string `json:"edgeType"`
}

type Graph struct {
	Nodes map[string]Node `json:"nodes"`
	Edges []Edge          `json:"edges"`
}

type GraphPath struct {
	Nodes map[string]Node `json:"nodes"`
	Paths [][]string      `json:"paths"`
}

var nodeTypeMap = map[string]string{
	"file":          "file",
	"module":        definition.ModuleMetaType,
	"namespace":     definition.ModuleMetaType,
	"package":       definition.ModuleMetaType,
	"class":         definition.ClassMetaType,
	"method":        definition.MethodMetaType,
	"property":      definition.FieldMetaType,
	"field":         definition.FieldMetaType,
	"constructor":   definition.MethodMetaType,
	"enum":          definition.EnumMetaType,
	"interface":     definition.InterfaceMetaType,
	"function":      definition.FunctionMetaType,
	"variable":      definition.VariableMetaType,
	"constant":      definition.ConstantMetaType,
	"string":        definition.ClassMetaType,
	"number":        definition.ClassMetaType,
	"boolean":       definition.ClassMetaType,
	"array":         definition.ClassMetaType,
	"object":        definition.ClassMetaType,
	"key":           definition.ClassMetaType,
	"null":          definition.ClassMetaType,
	"enumMember":    definition.EnumInstanceMetaType,
	"struct":        definition.StructMetaType,
	"event":         definition.FieldMetaType,
	"operator":      "operator",
	"typeParameter": "typeParameter",
}

func convertFromIdeSymbol(symbol ide.Symbol) Node {
	if nodeType, ok := nodeTypeMap[symbol.SymbolType]; ok {
		symbol.SymbolType = nodeType
	}
	return Node{
		NodeId:      symbol.Filepath + "#L" + strconv.Itoa(int(symbol.StartLine)) + "-L" + strconv.Itoa(int(symbol.EndLine)),
		NodeName:    symbol.SymbolName,
		NodeType:    symbol.SymbolType,
		WorkSpace:   symbol.WorkspacePath,
		Filepath:    symbol.Filepath,
		StartLine:   symbol.StartLine - 1,
		EndLine:     symbol.EndLine - 1,
		StartOffset: symbol.StartOffset,
		EndOffset:   symbol.EndOffset,
		Snippet:     symbol.Snippet,
	}
}

func convertFromBuiltinSymbol(node storage.GraphNode) Node {
	return Node{
		NodeId:      node.NodeId,
		NodeName:    node.SimpleName,
		NodeType:    node.NodeType,
		WorkSpace:   node.WorkspaceDir,
		Filepath:    node.FileAbsPath,
		StartLine:   node.Position.StartLine,
		EndLine:     node.Position.EndLine,
		StartOffset: node.Position.StartOffset,
		EndOffset:   node.Position.EndOffset,
		Snippet:     "",
	}
}

func convertFromBuiltinEdge(edge storage.GraphEdge) Edge {
	return Edge{
		SourceId: edge.SourceId,
		TargetId: edge.TargetId,
		EdgeType: edge.EdgeType,
	}
}

func convertFromIdeSourceAndTarget(sources []ide.Symbol, targets []ide.Symbol, graph *Graph, relaType string) {
	for _, source := range sources {
		for _, target := range targets {
			sourceId := source.Filepath + "#L" + strconv.Itoa(int(source.StartLine)) + "-L" + strconv.Itoa(int(source.EndLine))
			targetId := target.Filepath + "#L" + strconv.Itoa(int(target.StartLine)) + "-L" + strconv.Itoa(int(target.EndLine))
			if relaType == definition.MethodReferenceVariable {
				if nodeType, ok := nodeTypeMap[target.SymbolType]; ok {
					target.SymbolType = nodeType
				}
				if target.SymbolType == definition.ClassMetaType || target.SymbolType == definition.InterfaceMetaType ||
					target.SymbolType == definition.EnumMetaType || target.SymbolType == definition.StructMetaType {
					relaType = definition.MethodReferenceClass
				}
			}

			edge := Edge{
				SourceId: sourceId,
				TargetId: targetId,
				EdgeType: relaType,
			}
			graph.Edges = append(graph.Edges, edge)
			graph.Nodes[sourceId] = convertFromIdeSymbol(source)
			graph.Nodes[targetId] = convertFromIdeSymbol(target)
		}
	}
}

func convertFromIdeGraph(graph ide.IdeSearchRelationResponse) Graph {
	res := Graph{
		Nodes: make(map[string]Node),
		Edges: make([]Edge, 0),
	}
	convertFromIdeSourceAndTarget([]ide.Symbol{graph.CenterSymbol}, graph.Relationships.Extend, &res, definition.Extend)
	convertFromIdeSourceAndTarget(graph.Relationships.ExtendBy, []ide.Symbol{graph.CenterSymbol}, &res, definition.Extend)
	convertFromIdeSourceAndTarget([]ide.Symbol{graph.CenterSymbol}, graph.Relationships.Implement, &res, definition.Implement)
	convertFromIdeSourceAndTarget(graph.Relationships.ImplementBy, []ide.Symbol{graph.CenterSymbol}, &res, definition.Implement)
	convertFromIdeSourceAndTarget([]ide.Symbol{graph.CenterSymbol}, graph.Relationships.MethodCall, &res, definition.MethodCall)
	convertFromIdeSourceAndTarget(graph.Relationships.MethodCallBy, []ide.Symbol{graph.CenterSymbol}, &res, definition.MethodCall)
	convertFromIdeSourceAndTarget([]ide.Symbol{graph.CenterSymbol}, graph.Relationships.Reference, &res, definition.MethodReferenceVariable)
	convertFromIdeSourceAndTarget(graph.Relationships.ReferenceBy, []ide.Symbol{graph.CenterSymbol}, &res, definition.MethodReferenceVariable)
	return res
}

type GraphSearcher interface {
	// 搜索节点
	LocateNode(ctx context.Context, params LocateNodeQuery) ([]Node, error)

	// 图遍历
	TravelGraph(ctx context.Context, params TravelGraphQuery) (GraphPath, error)

	// 子图提取
	ExpandGraph(ctx context.Context, params ExpandGraphQuery) (Graph, error)

	// 合并图
	MergeGraph(ctx context.Context, graphs []Graph) (Graph, error)
}
