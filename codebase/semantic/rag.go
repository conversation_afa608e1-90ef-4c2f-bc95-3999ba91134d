package semantic

import (
	"context"
	"cosy/components"
	"cosy/definition"
	"cosy/global"
	"cosy/indexing"
	"cosy/lang/indexer"
	"cosy/lang/indexer/rag"
	"cosy/log"
	"cosy/tokenizer"
	"os"
	"path/filepath"

	"errors"
	"fmt"
	"sort"
	"strings"
)

// 检索策略常量
const (
	// TextOnly 仅使用文本检索
	TextOnly = "text_only"
	// VectorOnly 仅使用向量检索
	VectorOnly = "vector_only"
	// HybridMerge 混合检索，合并文本和向量检索结果
	HybridMerge = "hybrid_merge"
	// HybridRerank 混合检索，使用向量重排
	HybridRerank = "hybrid_rerank"
	// HybridLLMRerank 混合检索，使用LLM重排
	HybridLLMRerank = "hybrid_llm_rerank"

	ChatMode = "chat"
	// TODO mode要改一下
	MemoryMode = "memory"
)

// RagOperator 实现了 Operator 接口，使用 RAG 检索引擎进行语义检索
type RagOperator struct {
	fileIndexer *indexing.ProjectFileIndex
	embedder    *components.LingmaEmbedder
	reranker    interface{} // 支持 *components.LingmaReranker 或 *components.LLMReranker
}

// NewRagOperator 创建一个新的 RagOperator 实例使用默认 LingmaReranker
func NewRagOperator(fileIndexer *indexing.ProjectFileIndex, embedder *components.LingmaEmbedder) *RagOperator {
	return &RagOperator{
		fileIndexer: fileIndexer,
		embedder:    embedder,
		reranker:    components.NewLingmaReranker(), // 默认使用 LingmaReranker
	}
}

// NewRagOperatorWithReranker 创建一个新的 RagOperator 实例使用指定的 reranker
func NewRagOperatorWithReranker(fileIndexer *indexing.ProjectFileIndex, embedder *components.LingmaEmbedder, reranker interface{}) *RagOperator {
	return &RagOperator{
		fileIndexer: fileIndexer,
		embedder:    embedder,
		reranker:    reranker,
	}
}

// Retrieve 使用 RAG 检索引擎进行语义检索
func (r *RagOperator) Retrieve(ctx context.Context, query RetrieveQuery, workspaceURI string, topK int, options RetrieveOptions) ([]indexer.CodeChunk, error) {
	switch options.Strategy {
	case TextOnly:
		return r.retrieveText(ctx, query, topK, options.ModeType)
	case VectorOnly:
		return r.retrieveVector(ctx, query, topK, options.VectorScoreThreshold, options.ModeType)
	case HybridMerge:
		return r.retrieveHybridMerge(ctx, query, topK, options)
	case HybridRerank:
		return r.retrieveHybridRerank(ctx, query, topK, options)
	case HybridLLMRerank:
		return r.retrieveHybridLLMRerank(ctx, query, workspaceURI, topK, options)
	default:
		// 默认使用混合LLM重排策略
		return r.retrieveHybridLLMRerank(ctx, query, workspaceURI, topK, options)
	}
}

type RetrieveQuery struct {
	RawQuery     string
	RefinedQuery string
	Keywords     []string
	CodeCategory string

	// 这个参数是评测用的
	// 评测要用到不同模型，因此不能使用lingma内实现的embedder
	// 外部计算好query的Embedding，直接传入Embedding
	Embedding []float32
}

func (r *RagOperator) getTextRetrieveEngine(modeType string) (rag.TextRetrieveEngine, error) {
	switch modeType {
	case MemoryMode:
		textRetrieveIdx, hasTextRetrieveIdx := r.fileIndexer.GetMemoryRetrieveFileTextIndexer()
		if !hasTextRetrieveIdx {
			return nil, errors.New("memory mode text retrieve file indexer not found")
		}
		return textRetrieveIdx.GetTextRetrieveEngine(true)
	default:
		textRetrieveIdx, hasTextRetrieveIdx := r.fileIndexer.GetChatRetrieveFileTextIndexer()
		if !hasTextRetrieveIdx {
			return nil, errors.New("text retrieve file indexer not found")
		}
		return textRetrieveIdx.GetTextRetrieveEngine(true)
	}
}

// retrieveText 仅使用文本检索
func (r *RagOperator) retrieveText(ctx context.Context, query RetrieveQuery, topK int, modeType string) ([]indexer.CodeChunk, error) {
	// 获取文本检索引擎
	textEngine, err := r.getTextRetrieveEngine(modeType)
	if err != nil {
		return nil, err
	}

	// 构建文本检索查询
	textQuery := rag.TextQuery{
		Conditions: []rag.TextCondition{
			{
				FieldName: "index_content",
				Query:     query.RawQuery,
				Boost:     1.5,
			},
			{
				FieldName: "index_focus",
				Query:     query.RawQuery,
				Boost:     3.0,
			},
			{
				FieldName: "index_content",
				Query:     query.RefinedQuery,
				Boost:     1.0,
			},
			{
				FieldName: "index_focus",
				Query:     query.RefinedQuery,
				Boost:     2.0,
			},
			{
				FieldName: "index_focus",
				Query:     strings.Join(query.Keywords, " "),
				Boost:     2.0,
			},
			{
				FieldName: "code_category",
				Query:     query.CodeCategory,
				Boost:     1.0,
			},
		},
		Operator: rag.MatchOr,
		Size:     topK,
		From:     0,
		Fields:   []string{"*"},
	}

	// 执行文本检索
	textResult, err := textEngine.Retrieve(ctx, textQuery)
	if err != nil {
		return nil, err
	}

	return convertChunksToDocuments(textResult.Chunks), nil
}

func (r *RagOperator) getVectorRetrieveEngine(modeType string) (rag.VectorRetrieveEngine, error) {
	switch modeType {
	case MemoryMode:
		vectorRetrieveIdx, hasVectorRetrieveIdx := r.fileIndexer.GetMemoryRetrieveFileVectorIndexer()
		if !hasVectorRetrieveIdx {
			return nil, errors.New("vector retrieve file indexer not found")
		}
		return vectorRetrieveIdx.GetVectorRetrieveEngine()
	default:
		vectorRetrieveIdx, hasVectorRetrieveIdx := r.fileIndexer.GetChatRetrieveFileVectorIndexer()
		if !hasVectorRetrieveIdx {
			return nil, errors.New("vector retrieve file indexer not found")
		}
		return vectorRetrieveIdx.GetVectorRetrieveEngine()
	}
}

// retrieveVector 仅使用向量检索
func (r *RagOperator) retrieveVector(ctx context.Context, query RetrieveQuery, topK int, scoreThreshold float64, modeType string) ([]indexer.CodeChunk, error) {
	// 获取向量检索引擎
	vectorEngine, err := r.getVectorRetrieveEngine(modeType)
	if err != nil {
		return nil, err
	}
	if vectorEngine == nil {
		return nil, errors.New("vector retrieve engine not found")
	}

	var queryEmbedding []float32
	if global.ClientVectorIndexEnable() || modeType == MemoryMode {
		if len(query.Embedding) == 0 {
			// 如果query.Embedding为空，则使用lingma内置的embedder
			queryEmbeddings, err := r.embedder.CreateEmbedding(ctx, []string{query.RefinedQuery}, components.TextTypeQuery)
			if err != nil {
				return nil, err
			}
			queryEmbedding = queryEmbeddings[0]
		} else {
			queryEmbedding = query.Embedding
		}
	}

	// 执行向量检索
	vectorResult, err := vectorEngine.Retrieve(definition.QueryCondition{
		Query:          query.RefinedQuery,
		QueryEmbedding: queryEmbedding,
		TopK:           topK,
		ScoreThreshold: scoreThreshold,
	})
	if err != nil {
		return nil, err
	}

	return convertChunksToDocuments(vectorResult.Chunks), nil
}

// getAdditionalFileChunks 获取额外文件的代码块
func (r *RagOperator) getAdditionalFileChunks(filePaths []string) ([]indexer.CodeChunk, error) {
	if len(filePaths) == 0 {
		return nil, nil
	}

	log.Infof("[codebase] get additional file chunks. filePaths: %v", filePaths)

	textRetrieveIdx, hasTextRetrieveIdx := r.fileIndexer.GetChatRetrieveFileTextIndexer()
	if !hasTextRetrieveIdx {
		return nil, errors.New("text retrieve file indexer not found")
	}

	textEngine, err := textRetrieveIdx.GetTextRetrieveEngine(true)
	if err != nil {
		return nil, err
	}

	// 文件总数超过100， 则取前50个
	if len(filePaths) > 100 {
		filePaths = filePaths[:50]
	}

	topNPerFile := 100 / len(filePaths)

	// 直接获取文件的所有chunks，设置较大的limit确保获取所有chunks
	return textEngine.BatchGetFileChunks(filePaths, topNPerFile)
}

// retrieveHybridMerge 使用混合检索，简单合并文本和向量检索结果
func (r *RagOperator) retrieveHybridMerge(ctx context.Context, query RetrieveQuery, topK int, options RetrieveOptions) ([]indexer.CodeChunk, error) {
	// 获取文本检索结果
	textDocs, err := r.retrieveText(ctx, query, topK, options.ModeType)
	if err != nil {
		return nil, err
	}

	// 获取向量检索结果
	vectorDocs, err := r.retrieveVector(ctx, query, topK, options.VectorScoreThreshold, options.ModeType)
	if err != nil {
		return textDocs, nil // 如果向量检索失败，返回文本检索结果
	}

	// 合并结果，去重
	resultMap := make(map[string]indexer.CodeChunk)
	filePathMap := make(map[string]bool)

	// 记录已有的文件路径
	for _, doc := range textDocs {
		resultMap[doc.Id] = doc
		filePathMap[doc.FilePath] = true
	}

	for _, doc := range vectorDocs {
		if _, exists := resultMap[doc.Id]; !exists {
			resultMap[doc.Id] = doc
			filePathMap[doc.FilePath] = true
		}
	}

	// 处理额外的相关文件
	if len(options.RelevantFiles) > 0 {
		log.Infof("[codebase] relevant files found %d", len(options.RelevantFiles))
		needAddFiles := make([]string, 0)
		for _, relevantFile := range options.RelevantFiles {
			if _, exists := filePathMap[relevantFile]; !exists {
				needAddFiles = append(needAddFiles, relevantFile)
			}
		}

		additionalChunks, err := r.getAdditionalFileChunks(needAddFiles)
		if err == nil && additionalChunks != nil {
			for _, chunk := range additionalChunks {
				if _, exists := resultMap[chunk.Id]; !exists {
					resultMap[chunk.Id] = chunk
				}
			}
		}
	}

	// 转换回切片
	mergedDocs := make([]indexer.CodeChunk, 0, len(resultMap))
	for _, doc := range resultMap {
		mergedDocs = append(mergedDocs, doc)
	}

	// 限制返回数量
	if len(mergedDocs) > topK {
		mergedDocs = mergedDocs[:topK]
	}

	return mergedDocs, nil
}

// retrieveHybridLLMRerank 使用混合检索，通过LLM重排合并结果，如果LLM重排失败则降级为向量重排
func (r *RagOperator) retrieveHybridLLMRerank(ctx context.Context, query RetrieveQuery, workspaceURI string, topK int, options RetrieveOptions) ([]indexer.CodeChunk, error) {
	textEngine, err := r.getTextRetrieveEngine(options.ModeType)
	if err != nil {
		return nil, err
	}

	textQueryConditions := []rag.TextCondition{
		{
			FieldName: "index_content",
			Query:     query.RawQuery,
			Boost:     1.5,
		},
		{
			FieldName: "index_focus",
			Query:     query.RawQuery,
			Boost:     3.0,
		},
	}

	if query.RefinedQuery != "" {
		textQueryConditions = append(textQueryConditions, rag.TextCondition{
			FieldName: "index_content",
			Query:     query.RefinedQuery,
			Boost:     1.0,
		})

		textQueryConditions = append(textQueryConditions, rag.TextCondition{
			FieldName: "index_focus",
			Query:     query.RefinedQuery,
			Boost:     2.0,
		})
	}

	if len(query.Keywords) > 0 {
		textQueryConditions = append(textQueryConditions, rag.TextCondition{
			FieldName: "index_focus",
			Query:     strings.Join(query.Keywords, " "),
			Boost:     2.0,
		})

	}

	if query.CodeCategory != "" {
		textQueryConditions = append(textQueryConditions, rag.TextCondition{
			FieldName: "code_category",
			Query:     query.CodeCategory,
			Boost:     1.0,
		})
	}

	// 构建文本检索查询
	textQuery := rag.TextQuery{
		Conditions: textQueryConditions,
		Must:       []rag.TextCondition{},
		Operator:   rag.MatchOr,
		Size:       topK * 2,
		From:       0,
		Fields:     []string{"*"},
		KeyWords:   query.Keywords,
	}

	// 如果有文件路径过滤条件，添加到 Must 条件中
	if options.FilePathPattern != "" {
		textQuery.Must = append(textQuery.Must, rag.TextCondition{
			FieldName: "file_path",
			Query:     options.FilePathPattern,
			Boost:     1.0,
		})
	}

	var textResult rag.RetrieveResult
	textResult, err = textEngine.Retrieve(ctx, textQuery)
	if err != nil {
		// 混合检索场景文本检索非强依赖，检索失败后自动降级
		log.Errorf("text retrieve error on hybrid search mode: %v", err)
		textResult = rag.RetrieveResult{
			Chunks: make([]rag.RetrieveChunk, 0),
		}
	}

	verboseLogf("[codebase] text retrieve result: %+v", textResult)

	vectorEngine, err := r.getVectorRetrieveEngine(options.ModeType)
	if err != nil {
		// 如果向量引擎获取失败，仅使用文本检索结果
		log.Errorf("[codebase] vector retrieve engine not found. error: %v", err)
		return convertChunksToDocuments(textResult.Chunks), nil
	}

	// 这里要防呆一下，如果refinedquery没有处理，或者模型返回为空，需要使用rawquery
	if query.RefinedQuery == "" {
		query.RefinedQuery = query.RawQuery
	}

	var queryEmbedding []float32
	if global.ClientVectorIndexEnable() || options.ModeType == MemoryMode {
		if len(query.Embedding) == 0 {
			// 如果query.Embedding为空，则使用lingma内置的embedder
			queryEmbeddings, err := r.embedder.CreateEmbedding(ctx, []string{query.RefinedQuery}, components.TextTypeQuery)
			if err != nil {
				// 如果向量嵌入失败，仅使用文本检索结果
				log.Errorf("[codebase] vector embedding failed. error: %v", err)
				return convertChunksToDocuments(textResult.Chunks), nil
			}
			queryEmbedding = queryEmbeddings[0]
		} else {
			queryEmbedding = query.Embedding
		}
	}

	vectorResult, err := vectorEngine.Retrieve(definition.QueryCondition{
		Query:           query.RefinedQuery,
		QueryEmbedding:  queryEmbedding,
		TopK:            topK * 4,
		ScoreThreshold:  options.VectorScoreThreshold,
		FilePathPattern: options.FilePathPattern,
	})
	if err != nil {
		// 如果向量检索失败，仅使用文本检索结果
		log.Errorf("[codebase] vector retrieve failed. error: %v", err)
		return convertChunksToDocuments(textResult.Chunks), nil
	}

	verboseLogf("[codebase] vector retrieve success. result: %v", vectorResult)

	// 处理额外的相关文件
	// 记录已有的文件路径
	filePathMap := make(map[string]bool)
	for _, chunk := range textResult.Chunks {
		filePathMap[chunk.FilePath] = true
	}
	for _, chunk := range vectorResult.Chunks {
		filePathMap[chunk.FilePath] = true
	}

	var additionalChunks []rag.RetrieveChunk
	if len(options.RelevantFiles) > 0 {
		log.Infof("[codebase] relevant files found %d", len(options.RelevantFiles))
		needAddFiles := make([]string, 0)
		for _, relevantFile := range options.RelevantFiles {
			if _, exists := filePathMap[relevantFile]; !exists {
				needAddFiles = append(needAddFiles, relevantFile)
			}
		}

		additions, err := r.getAdditionalFileChunks(needAddFiles)
		if err == nil && additions != nil {
			for _, chunk := range additions {
				additionalChunks = append(additionalChunks, rag.RetrieveChunk{
					CodeChunk: chunk,
					Score:     1.0, // 给一个默认分数
				})
			}
		}
	}

	// 对所有chunks进行去重
	uniqueChunks := make(map[string]rag.RetrieveChunk)
	var allChunks []rag.RetrieveChunk

	// 使用文件路径+起始行+结束行作为唯一标识
	getChunkKey := func(chunk rag.RetrieveChunk) string {
		return fmt.Sprintf("%s:%d:%d", chunk.FilePath, chunk.StartLine, chunk.EndLine)
	}

	// 添加向量检索结果
	for _, chunk := range vectorResult.Chunks {
		key := getChunkKey(chunk)
		if existing, ok := uniqueChunks[key]; !ok || chunk.Score > existing.Score {
			uniqueChunks[key] = chunk
		}
	}

	// 添加文本检索结果
	for _, chunk := range textResult.Chunks {
		key := getChunkKey(chunk)
		if existing, ok := uniqueChunks[key]; !ok || chunk.Score > existing.Score {
			uniqueChunks[key] = chunk
		}
	}

	// 添加额外文件的chunks
	for _, chunk := range additionalChunks {
		key := getChunkKey(chunk)
		if _, ok := uniqueChunks[key]; !ok {
			uniqueChunks[key] = chunk
		}
	}

	// 转换为切片
	for _, chunk := range uniqueChunks {
		allChunks = append(allChunks, chunk)
	}

	// 准备重排数据
	var documents []string
	chunkMap := make(map[int]rag.RetrieveChunk) // 存储从documents索引到chunk的映射
	documentIndex := 0

	for _, chunk := range allChunks {
		if chunk.Content == "" {
			continue
		}
		// 使用结构化文档格式
		rankDoc := components.RerankDocument{
			Content:       chunk.Content,
			FilePath:      chunk.FilePath,
			FileName:      filepath.Base(chunk.FilePath),
			Language:      chunk.Language,
			Category:      chunk.CodeCategory,
			Type:          chunk.Type,
			StartLine:     chunk.StartLine,
			EndLine:       chunk.EndLine,
			Score:         chunk.Score,
			WorkspacePath: workspaceURI,
		}
		documents = append(documents, rankDoc.FormatForLLMRerank())
		chunkMap[documentIndex] = chunk // 使用documents的索引作为key
		documentIndex++
	}

	if len(documents) == 0 {
		log.Infof("[codebase] rerank documents is empty, query: `%s`", query.RawQuery)
		return nil, nil
	}

	// 使用配置的 reranker 执行重排序
	rerankResponse, err := r.executeRerank(ctx, query.RawQuery, documents, topK)
	if err != nil {
		return nil, fmt.Errorf("[codebase] rerank failed: %w", err)
	}
	if err == nil && rerankResponse != nil {
		// LLM重排成功，处理结果
		var rerankedChunks []rag.RetrieveChunk
		count := 0
		for _, result := range rerankResponse.Output.Results {
			if chunk, ok := chunkMap[result.Index]; ok {
				if result.RelevanceScore < options.LLMRerankScoreThreshold {
					continue
				}
				chunk.Score = result.RelevanceScore
				rerankedChunks = append(rerankedChunks, chunk)
				count++
				if count >= topK {
					break
				}
			}
		}

		sort.Slice(rerankedChunks, func(i, j int) bool {
			return rerankedChunks[i].Score > rerankedChunks[j].Score
		})

		return convertChunksToDocuments(rerankedChunks), nil
	}

	// LLM重排失败，降级为向量重排
	vectorReranker := rag.VectorBasedReranker{
		VectorEngine: vectorEngine,
	}

	rerankOption := &rag.RerankOption{
		TopK:           topK,
		ThresholdScore: options.RerankScoreThreshold,
	}

	// 构建重排结果
	retrieveResult := rag.RetrieveResult{
		Source: rag.ClientVectorRetrieveSource,
		Chunks: allChunks,
	}

	mergedResult := vectorReranker.MergeRerank(query.RawQuery, rerankOption, []rag.RetrieveResult{retrieveResult})
	return convertChunksToDocuments(mergedResult.Chunks), nil
}

// retrieveHybridRerank 使用混合检索，通过向量重排合并结果
func (r *RagOperator) retrieveHybridRerank(ctx context.Context, query RetrieveQuery, topK int, options RetrieveOptions) ([]indexer.CodeChunk, error) {
	textEngine, err := r.getTextRetrieveEngine(options.ModeType)
	if err != nil {
		return nil, err
	}

	textQueryConditions := []rag.TextCondition{
		{
			FieldName: "index_content",
			Query:     query.RawQuery,
			Boost:     1.5,
		},
		{
			FieldName: "index_focus",
			Query:     query.RawQuery,
			Boost:     3.0,
		},
	}

	if query.RefinedQuery != "" {
		textQueryConditions = append(textQueryConditions, rag.TextCondition{
			FieldName: "index_content",
			Query:     query.RefinedQuery,
			Boost:     1.0,
		})

		textQueryConditions = append(textQueryConditions, rag.TextCondition{
			FieldName: "index_focus",
			Query:     query.RefinedQuery,
			Boost:     2.0,
		})
	}

	if len(query.Keywords) > 0 {
		textQueryConditions = append(textQueryConditions, rag.TextCondition{
			FieldName: "index_focus",
			Query:     strings.Join(query.Keywords, " "),
			Boost:     2.0,
		})
	}

	if query.CodeCategory != "" {
		textQueryConditions = append(textQueryConditions, rag.TextCondition{
			FieldName: "code_category",
			Query:     query.CodeCategory,
			Boost:     1.0,
		})
	}

	textQuery := rag.TextQuery{
		Conditions: textQueryConditions,
		Must:       []rag.TextCondition{},
		Operator:   rag.MatchOr,
		Size:       topK * 3,
		From:       0,
		Fields:     []string{"*"},
	}

	var textResult rag.RetrieveResult
	textResult, err = textEngine.Retrieve(ctx, textQuery)
	if err != nil {
		// 混合检索场景文本检索非强依赖，检索失败后自动降级
		log.Errorf("text retrieve error on hybrid search mode: %v", err)
		textResult = rag.RetrieveResult{
			Chunks: make([]rag.RetrieveChunk, 0),
		}
	}

	vectorEngine, err := r.getVectorRetrieveEngine(options.ModeType)
	if err != nil {
		return convertChunksToDocuments(textResult.Chunks), nil
	}

	// 这里要防呆一下，如果refinedquery没有处理，或者模型返回为空，需要使用rawquery
	if query.RefinedQuery == "" {
		query.RefinedQuery = query.RawQuery
	}

	var queryEmbedding []float32
	if global.ClientVectorIndexEnable() || options.ModeType == MemoryMode {
		if len(query.Embedding) == 0 {
			// 如果query.Embedding为空，则使用lingma内置的embedder
			queryEmbeddings, err := r.embedder.CreateEmbedding(ctx, []string{query.RefinedQuery}, components.TextTypeQuery)
			if err != nil {
				return convertChunksToDocuments(textResult.Chunks), nil
			}
			queryEmbedding = queryEmbeddings[0]
		} else {
			queryEmbedding = query.Embedding
		}
	}

	vectorResult, err := vectorEngine.Retrieve(definition.QueryCondition{
		Query:          query.RefinedQuery,
		QueryEmbedding: queryEmbedding,
		TopK:           topK * 2,
		ScoreThreshold: options.VectorScoreThreshold,
	})
	if err != nil {
		return convertChunksToDocuments(textResult.Chunks), nil
	}

	// 使用RRF重排器进行混合排序，而不是向量重排
	// 创建RRF重排器，设置通用权重为1，文本检索权重为2，向量检索权重为1
	reranker := rag.NewReciprocalRankFusionReranker(1, map[string]float64{
		rag.TextRetrieveSource:         2.0,
		rag.ClientVectorRetrieveSource: 1.0,
	})

	rerankOption := &rag.RerankOption{
		TopK:           topK,
		ThresholdScore: options.RerankScoreThreshold,
	}

	mergedResult := reranker.MergeRerank(query.RawQuery, rerankOption, []rag.RetrieveResult{textResult, vectorResult})

	return convertChunksToDocuments(mergedResult.Chunks), nil
}

// convertChunksToDocuments 将检索结果转换为文档格式
func convertChunksToDocuments(chunks []rag.RetrieveChunk) []indexer.CodeChunk {
	docs := make([]indexer.CodeChunk, 0, len(chunks))
	for _, chunk := range chunks {
		doc := indexer.CodeChunk{
			Id:               chunk.Id,
			Content:          chunk.Content,
			FilePath:         chunk.FilePath,
			Language:         chunk.Language,
			StartLine:        chunk.StartLine,
			EndLine:          chunk.EndLine,
			FileName:         chunk.FileName,
			DocType:          chunk.DocType,
			FileExtension:    chunk.FileExtension,
			CommentStartLine: chunk.CommentStartLine,
			Score:            chunk.Score,
		}
		docs = append(docs, doc)
	}
	return docs
}

// batchLLMRerank 实现分批LLM重排以处理token限制问题
func (r *RagOperator) batchLLMRerank(ctx context.Context, query string, chunks []rag.RetrieveChunk, topK int, threshold float64, workspaceURI string) ([]rag.RetrieveChunk, error) {
	const maxTokenPerBatch = 30000

	if len(chunks) == 0 {
		return nil, fmt.Errorf("no chunks to rerank")
	}

	// 计算总token数，如果不超过限制则直接重排
	totalTokens := r.calculateTotalTokens(query, chunks)
	if totalTokens <= maxTokenPerBatch {
		return r.singleLLMRerank(ctx, query, chunks, topK, threshold, workspaceURI)
	}

	// 需要分批处理
	batches := r.splitChunksIntoBatches(query, chunks, maxTokenPerBatch)
	if len(batches) == 0 {
		return nil, fmt.Errorf("failed to split chunks into batches")
	}

	verboseLogf("[codebase] LLM rerank: splitting %d chunks into %d batches", len(chunks), len(batches))

	// 对每个批次进行重排
	var allBatchResults []rag.RetrieveChunk
	for i, batch := range batches {
		verboseLogf("[codebase] LLM rerank: processing batch %d/%d with %d chunks", i+1, len(batches), len(batch))

		batchResults, err := r.singleLLMRerank(ctx, query, batch, len(batch), threshold, workspaceURI)
		if err != nil {
			log.Warnf("[codebase] LLM rerank: batch %d failed: %v", i+1, err)
			continue
		}

		allBatchResults = append(allBatchResults, batchResults...)
	}

	if len(allBatchResults) == 0 {
		return nil, fmt.Errorf("all batches failed to rerank")
	}

	// 对分批结果按分数排序
	sort.Slice(allBatchResults, func(i, j int) bool {
		return allBatchResults[i].Score > allBatchResults[j].Score
	})

	// 如果分批结果较多，取前70%进行二次重排
	if len(allBatchResults) > topK*2 {
		secondRoundCount := int(float64(len(allBatchResults)) * 0.7)
		if secondRoundCount > topK*3 {
			secondRoundCount = topK * 3
		}

		verboseLogf("[codebase] LLM rerank: second round rerank with %d chunks", secondRoundCount)

		secondRoundChunks := allBatchResults[:secondRoundCount]
		secondRoundTokens := r.calculateTotalTokens(query, secondRoundChunks)

		// 如果二次重排的token数仍然超过限制，则只返回当前排序结果
		if secondRoundTokens <= maxTokenPerBatch {
			finalResults, err := r.singleLLMRerank(ctx, query, secondRoundChunks, topK, threshold, workspaceURI)
			if err == nil {
				return finalResults, nil
			}
			log.Warnf("[codebase] LLM rerank: second round failed: %v", err)
		} else {
			log.Warnf("[codebase] LLM rerank: second round would exceed token limit (%d), skipping", secondRoundTokens)
		}
	}

	// 返回前topK个结果
	if len(allBatchResults) > topK {
		allBatchResults = allBatchResults[:topK]
	}

	return allBatchResults, nil
}

// singleLLMRerank 执行单次LLM重排
func (r *RagOperator) singleLLMRerank(ctx context.Context, query string, chunks []rag.RetrieveChunk, topN int, threshold float64, workspaceURI string) ([]rag.RetrieveChunk, error) {
	var documents []string
	chunkMap := make(map[int]rag.RetrieveChunk)

	for i, chunk := range chunks {
		// 使用结构化文档格式
		rankDoc := components.RerankDocument{
			Content:       chunk.Content,
			FilePath:      chunk.FilePath,
			FileName:      filepath.Base(chunk.FilePath),
			Language:      chunk.Language,
			Category:      chunk.CodeCategory,
			Type:          chunk.Type,
			StartLine:     chunk.StartLine,
			EndLine:       chunk.EndLine,
			Score:         chunk.Score,
			WorkspacePath: workspaceURI,
		}
		documents = append(documents, rankDoc.FormatForLLMRerank())
		chunkMap[i] = chunk
	}

	// 使用配置的 reranker 执行重排序
	rerankResponse, err := r.executeRerank(ctx, query, documents, topN)
	if err != nil {
		return nil, fmt.Errorf("rerank failed: %v", err)
	}

	if rerankResponse == nil {
		return nil, fmt.Errorf("LLM rerank returned nil response")
	}

	var rerankedChunks []rag.RetrieveChunk
	count := 0
	for _, result := range rerankResponse.Output.Results {
		if chunk, ok := chunkMap[result.Index]; ok {
			if result.RelevanceScore < threshold {
				continue
			}
			chunk.Score = result.RelevanceScore
			rerankedChunks = append(rerankedChunks, chunk)
			count++
			if count >= topN {
				break
			}
		}
	}

	sort.Slice(rerankedChunks, func(i, j int) bool {
		return rerankedChunks[i].Score > rerankedChunks[j].Score
	})

	return rerankedChunks, nil
}

// calculateTotalTokens 估算总token数（使用简单估算：query + 所有chunk content）
func (r *RagOperator) calculateTotalTokens(query string, chunks []rag.RetrieveChunk) int {
	totalTokens := tokenizer.GetTokenCountWithSimpleAsciiTokenizer(query)

	for _, chunk := range chunks {
		chunkTokens := tokenizer.GetTokenCountWithSimpleAsciiTokenizer(chunk.Content)
		totalTokens += chunkTokens
	}

	return totalTokens
}

// splitChunksIntoBatches 将chunks分批，确保每批不超过token限制
func (r *RagOperator) splitChunksIntoBatches(query string, chunks []rag.RetrieveChunk, maxTokenPerBatch int) [][]rag.RetrieveChunk {
	var batches [][]rag.RetrieveChunk
	var currentBatch []rag.RetrieveChunk

	queryTokens := tokenizer.GetTokenCountWithSimpleAsciiTokenizer(query)
	currentBatchTokens := queryTokens

	for _, chunk := range chunks {
		chunkTokens := tokenizer.GetTokenCountWithSimpleAsciiTokenizer(chunk.Content)

		// 如果单个chunk就超过限制，跳过它
		if queryTokens+chunkTokens > maxTokenPerBatch {
			log.Warnf("[codebase] LLM rerank: chunk too large (%d tokens), skipping", chunkTokens)
			continue
		}

		// 如果加入这个chunk会超过限制，先保存当前批次
		if currentBatchTokens+chunkTokens > maxTokenPerBatch {
			if len(currentBatch) > 0 {
				batches = append(batches, currentBatch)
			}
			currentBatch = []rag.RetrieveChunk{chunk}
			currentBatchTokens = queryTokens + chunkTokens
		} else {
			currentBatch = append(currentBatch, chunk)
			currentBatchTokens += chunkTokens
		}
	}

	// 保存最后一个批次
	if len(currentBatch) > 0 {
		batches = append(batches, currentBatch)
	}

	return batches
}

// verboseLogf 根据 LINGMA_CODEBASE_FUSION_LOG_VERBOSE 环境变量控制debug日志输出
// 如果开启了 LINGMA_CODEBASE_FUSION_LOG_VERBOSE 环境变量，则展示更多debug信息
func verboseLogf(format string, args ...interface{}) {
	if os.Getenv("LINGMA_CODEBASE_FUSION_LOG_VERBOSE") != "" {
		log.Debugf("[codebase-verbose] "+format, args...)
	}
}

// executeRerank 执行重排序操作，根据 reranker 类型选择合适的重排序策略
func (r *RagOperator) executeRerank(ctx context.Context, query string, documents []string, limit int) (*components.RerankResponse, error) {
	switch reranker := r.reranker.(type) {
	case *components.LLMReranker:
		// LLMReranker 内部已包含降级逻辑
		return reranker.RerankDocuments(ctx, query, documents, limit)
	case *components.LingmaReranker:
		// 使用 LingmaReranker
		return reranker.RerankDocuments(ctx, query, documents, limit)
	default:
		// 默认使用 LingmaReranker
		lingmaReranker := components.NewLingmaReranker()
		return lingmaReranker.RerankDocuments(ctx, query, documents, limit)
	}
}
