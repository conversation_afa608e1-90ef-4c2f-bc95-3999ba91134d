package overview

import (
	"context"
	"cosy/tree"
	"os"
	"path/filepath"
	"testing"
)

func TestGetStructure(t *testing.T) {
	// 创建临时目录作为测试工作空间
	tempDir, err := os.MkdirTemp("", "overview-test")
	if err != nil {
		t.Fatalf("创建临时目录失败: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 创建测试目录结构
	dirs := []string{
		"src/main/java",
		"src/test/java",
		"resources",
		"docs",
	}

	for _, dir := range dirs {
		err := os.MkdirAll(filepath.Join(tempDir, dir), 0755)
		if err != nil {
			t.Fatalf("创建目录失败: %v", err)
		}
	}

	// 创建一些测试文件
	files := map[string]string{
		"src/main/java/Main.java":     "public class Main { public static void main(String[] args) {} }",
		"src/test/java/MainTest.java": "public class MainTest { void testMain() {} }",
		"resources/config.properties": "app.name=TestApp",
		"docs/README.md":              "# Test Project",
	}

	for path, content := range files {
		fullPath := filepath.Join(tempDir, path)
		err := os.WriteFile(fullPath, []byte(content), 0644)
		if err != nil {
			t.Fatalf("创建文件失败: %v", err)
		}
	}

	treeManager := tree.NewWorkTreeManager(tree.DefaultWeightConfig(), func(s string) int {
		return len(s)
	})

	// 创建OverviewOperator实例
	operator := NewOverviewOperator(treeManager)

	// 测试获取整个工作空间结构
	ctx := context.Background()
	structure, err := operator.GetStructure(ctx, tempDir, "", 1000)
	if err != nil {
		t.Fatalf("GetStructure失败: %v", err)
	}

	if structure == "" {
		t.Error("GetStructure返回空字符串")
	}

	// 测试获取子目录结构
	subDirStructure, err := operator.GetStructure(ctx, tempDir, "src", 1000)
	if err != nil {
		t.Fatalf("GetStructure(subDir)失败: %v", err)
	}

	if subDirStructure == "" {
		t.Error("GetStructure(subDir)返回空字符串")
	}
}

func TestGuessFramework(t *testing.T) {
	// 创建临时目录作为测试工作空间
	tempDir, err := os.MkdirTemp("", "framework-test")
	if err != nil {
		t.Fatalf("创建临时目录失败: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 测试React框架
	reactFiles := map[string]string{
		"package.json": `{
			"name": "react-app",
			"dependencies": {
				"react": "^17.0.2",
				"react-dom": "^17.0.2"
			}
		}`,
		"src/App.js": `import React from 'react';
		function App() {
			return <div>Hello World</div>;
		}
		export default App;`,
	}

	// 创建React测试文件
	for path, content := range reactFiles {
		fullPath := filepath.Join(tempDir, path)
		dir := filepath.Dir(fullPath)
		if err := os.MkdirAll(dir, 0755); err != nil {
			t.Fatalf("创建目录失败: %v", err)
		}
		if err := os.WriteFile(fullPath, []byte(content), 0644); err != nil {
			t.Fatalf("创建文件失败: %v", err)
		}
	}

	treeManager := tree.NewWorkTreeManager(tree.DefaultWeightConfig(), func(s string) int {
		return len(s)
	})

	// 创建OverviewOperator实例
	operator := NewOverviewOperator(treeManager)

	// 测试框架识别
	ctx := context.Background()
	framework, err := operator.GuessFramework(ctx, tempDir, 1000)
	if err != nil {
		t.Fatalf("GuessFramework失败: %v", err)
	}

	if framework == "" {
		t.Error("GuessFramework返回空字符串")
	}

	// 检查是否识别出React框架
	if framework == "未能识别出明确的框架" {
		t.Error("未能识别出React框架")
	}
}
