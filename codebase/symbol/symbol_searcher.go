package symbol

import (
	"code.alibaba-inc.com/cosy/lingma-codebase-graph/storage"
	"context"
	"cosy/definition"
	"cosy/ide"
	"strconv"
)

type SymbolSearchParams struct {
	Language      string
	WorkspacePath string
	SymbolKey     string
	MaxCount      int
	RankResult    bool
}

type SymbolSearchResult struct {
	WorkspacePath string
	FilePath      string
	LineRange     definition.LineRange
	OffsetRange   definition.OffsetRange
	SymbolKey     string
	SymbolName    string
	SymbolType    string
	Content       string
}

func ConvertFromIdeSymbol(symbol ide.Symbol) SymbolSearchResult {
	return SymbolSearchResult{
		WorkspacePath: symbol.WorkspacePath,
		FilePath:      symbol.Filepath,
		LineRange: definition.LineRange{
			StartLine: uint32(symbol.StartLine - 1),
			EndLine:   uint32(symbol.EndLine - 1),
		},
		OffsetRange: definition.OffsetRange{
			StartOffset: uint32(symbol.StartOffset),
			EndOffset:   uint32(symbol.EndOffset),
		},
		SymbolName: symbol.SymbolName,
		SymbolType: symbol.SymbolType,
		SymbolKey:  symbol.Filepath + "#L" + strconv.Itoa(int(symbol.StartLine)) + "-L" + strconv.Itoa(int(symbol.EndLine)),
		Content:    symbol.Snippet,
	}
}

func ConvertFromBuiltinNode(node storage.GraphNode) SymbolSearchResult {
	return SymbolSearchResult{
		WorkspacePath: node.WorkspaceDir,
		FilePath:      node.FileAbsPath,
		LineRange: definition.LineRange{
			StartLine: uint32(node.Position.StartLine),
			EndLine:   uint32(node.Position.EndLine),
		},
		OffsetRange: definition.OffsetRange{
			StartOffset: uint32(node.Position.StartOffset),
			EndOffset:   uint32(node.Position.EndOffset),
		},
		SymbolName: node.SimpleName,
		SymbolType: node.NodeType,
		SymbolKey:  node.NodeId,
	}
}

type SymbolSearcher interface {
	SearchSymbol(ctx context.Context, params *SymbolSearchParams) ([]SymbolSearchResult, error)
	SearchSymbolByIde(ctx context.Context, params *SymbolSearchParams) ([]SymbolSearchResult, error)
	SearchSymbolByBuiltin(ctx context.Context, params *SymbolSearchParams) ([]SymbolSearchResult, error)
}
