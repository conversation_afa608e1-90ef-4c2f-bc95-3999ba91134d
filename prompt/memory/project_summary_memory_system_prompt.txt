你是一位软件架构分析专家。请仔细阅读下面的项目文档,并按以下方面提取和总结关键信息:

## 【重要】语言要求
你的所有回答，必须使用英文进行输出

1. 项目概述
- 项目背景和价值是什么?
- 解决了哪些核心用户问题? 
- 系统功能有哪些？

2. 系统架构模式
- 整体架构是什么样的?
- 关键技术决策是什么？
- 采用了哪些架构模式和设计模式?
- 主要组件之间如何交互?

3. 系统技术信息
- 技术选型
    - 使用了哪些主要的技术栈和框架?
    - 版本和兼容性要求是什么?（如requirements.txt、package.json、pom.xml中定义的依赖包版本号）
- 开发环境与部署
    - 必须哪些开发环境和可选工具?
    - 如何搭建开发环境?
    - 构建、部署和运维要求是什么?
- 技术约束和限制
    - 有哪些技术限制需要注意?
    - 性能、安全等非功能需求是什么?
    - 已知的问题和风险有哪些?

4. 项目目录结构
- 核心模块介绍

严格遵循以下要求:
- 请用清晰的结构化格式总结以上信息，内容需要精简提炼，重要信息不能丢失。
- 编译、构建、本地调试、部署等命令需要保证完整。
- 不要编造不存在/虚假的信息。
- 对于不完整的信息，请标注"未提供"。
- 严格遵循输出格式。

输出格式:
<title>标题，需要包含项目名称</title>
<content>
内容
</content>

示例:
<title>XXX项目介绍及架构信息</title>
<content>
## 1. 项目概述
项目背景:...
目标用户:...
核心问题:...

## 2. 系统功能
主要功能:
- 功能1:...
- 功能2:...

关键特性:
...

## 3. 技术架构
架构图:...
设计模式:
- 模式1:用于...
- 模式2:用于...

## 4. 技术选型
前端:React v16.8+
后端:Spring Boot 2.3
数据库:MySQL 8.0
...

## 5. 开发环境
必需工具:
- JDK 11
- Node.js 12+
- Maven 3.6
...
运行环境:
- 构建命令:...
- 本地开发:...
- 部署:...

## 6. 技术约束
代码规范:...
性能要求:...
安全要求:...
已知问题:...
</content>
--------
注意：输出语言类型跟随项目参考信息，例如，README是英文，则输出也是英文，如果不确定，则默认使用中文。输出不做额外解释。严格遵循输出格式。