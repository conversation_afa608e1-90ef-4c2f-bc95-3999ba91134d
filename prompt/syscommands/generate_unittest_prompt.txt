{{- if .HasRulesContext}}
下面是一组规则信息，你应该仔细阅读并理解以下所有规则，并正确遵守需要遵守的规则。
如果规则的详细内容未提供，请使用 fetch_rules 工具获取。
<rules>
{{- if .AlwaysAppliedRules }}
<always_on_rules>
    {{- range $i, $detail := .AlwaysAppliedRules }}
    <rule name="{{ $detail.Name }}">
        <rule_content>{{ $detail.Content }}</rule_content>
    </rule>
{{- end }}
</always_on_rules>
{{- end }}
{{- if .ModelDecisionRules }}
<model_decision_rules>
    {{- range $i, $detail := .ModelDecisionRules }}
    <rule name="{{ $detail.Name }}" description="{{ $detail.Description }}"/>
    {{- end }}
</model_decision_rules>
{{- end }}
{{- if .GlobRulesExcludeAlreadyMatched }}
<glob_rules>
    {{- range $i, $detail := .GlobRulesExcludeAlreadyMatched }}
    <rule name="{{ $detail.Name }}" glob="{{ $detail.Glob }}"/>
    {{- end }}
</glob_rules>
{{- end }}
{{- if .UserRules }}
<user_rules>
    {{- range $i, $detail := .UserRules }}
    <rule name="{{ $detail.Name }}" description="{{ $detail.Description }}">
        <rule_content>{{- $detail.Content }}</rule_content>
    </rule>
    {{- end }}
</user_rules>
{{- end }}
</rules>
{{- end }}

<user_query>
为下面的{{.Language}}代码片段生成单元测试:
{{- if and (ne .FilePath "")}}文件路径: {{.FilePath}}{{- end }}
```{{ .Language }}
{{ .ContentForTest }}
```
{{ if ne .Text "" }}
附加要求：必须遵循以下原则：{{ .Text }}
{{ end }}
</user_query>