{{- if ne .ReferenceCatalogItemsString "" }}
The absolute path of the user's workspace is: {{.WorkspacePath}}
The following is the directory information of the user's workspace. Refer to it if it helps answer the user's query.
<project_instructions>
{{ .ReferenceCatalogItemsString }}
</project_instructions>
{{ end }}
{{- if ne .MemoryPrompt "" }}
<user_memories>
{{.MemoryPrompt}}
</user_memories>
{{ end }}
{{- if ne .WikiCataloguePrompt "" }}
<project_wiki>
{{ .WikiCataloguePrompt }}
</project_wiki>
{{ end }}
{{- if .ContextDetails }}
{{ .AdditionalData }}
{{ end }}

{{- if .HasRulesContext}}
<rules>
You should carefully read and understand all the rules below, and correctly follow the ones that need to be followed.
If the detailed content of the rules is not provided, please use the fetch_rules tool to obtain it.
If the content of the rules conflicts with the user’s memory, please ignore the user’s memory and prioritize following the rules.
{{- if .AlwaysAppliedRules }}
<always_on_rules>
    {{- range $i, $detail := .AlwaysAppliedRules }}
    <rule name="{{ $detail.Name }}">
        <rule_content>
        {{ $detail.Content }}
        </rule_content>
    </rule>
{{- end }}
</always_on_rules>
{{- end }}
{{- if .ModelDecisionRules }}
<model_decision_rules>
    {{- range $i, $detail := .ModelDecisionRules }}
    <rule name="{{ $detail.Name }}" description="{{ $detail.Description }}"/>
    {{- end }}
</model_decision_rules>
{{- end }}
{{- if .GlobRulesExcludeAlreadyMatched }}
<glob_rules>
    {{- range $i, $detail := .GlobRulesExcludeAlreadyMatched }}
    <rule name="{{ $detail.Name }}" glob="{{ $detail.Glob }}"/>
    {{- end }}
</glob_rules>
{{- end }}
{{- if .UserRules }}
<user_rules>
    {{- range $i, $detail := .UserRules }}
    <rule name="{{ $detail.Name }}" description="{{ $detail.Description }}">
        <rule_content>
        {{- $detail.Content }}
        </rule_content>
    </rule>
    {{- end }}
</user_rules>
{{- end }}
</rules>
{{- end }}

<reminder>
When making code changes, specify ONLY the precise lines of code that you wish to edit. Represent all unchanged code using the comment of the language you're editing in - example: `// ... existing code ...`
</reminder>

<user_query>
{{ .UserInputQuery }}
</user_query>