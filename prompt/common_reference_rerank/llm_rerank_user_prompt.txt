Query: {{ .Query }}

Code Documents:
{{ range $index, $doc := .Documents }}
Index: {{ add $index 1 }}
{{ $doc }}
---

{{ end }}

Task: Rank the above code documents based on their relevance to the user's query. Consider the metadata (file path, language, category, type, location) along with the code content to make informed ranking decisions.

Return only the indexes of the code documents in descending order of relevance (most relevant first), separated by the | character. Do not provide any explanation or additional text.
DO NOT DISCARD ANY CODE ITEM - include all {{ len .Documents }} items in your ranking.