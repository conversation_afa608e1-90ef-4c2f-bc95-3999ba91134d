首先，我会给你一些有帮助的上下文信息。
{{- if .EditFullFile }}
然后，我会向你展示我当前正在编辑的文件代码，并给出相应的指令
{{- else }}
{{- if eq .OperationType "gen_code" }}
然后，我会向你展示插入点，并给出相应的指令，插入点将在 {{ .UserCurrentlyEditFile }} 中。
{{- else }}
然后，我会向你展示我当前选中的代码，并给出相应的指令，选中的内容将在 {{ .UserCurrentlyEditFile }} 中。
{{- end }}
{{- end }}

-------

### 上下文信息
{{- if .ReferenceContext }}
### 可参考的文件
<reference_context>
{{- range $i, $detail := .ReferenceContext }}
{{ $detail.RenderedContent}}
{{- end }}
</reference_context>
{{- end }}

{{- if .LintsError }}
### 当前文件存在的语法错误
<lints_error>
{{- .LintsError}}
</lints_error>
{{ end }}

{{- if .EditFullFile }}
这是我当前正在编辑的文件。
{{- else }}
{{- if eq .OperationType "gen_code" }}
这是我当前正在编辑的文件。插入点将由以下注释标识：//Start Generation Here 和 //End Generation Here
{{- else }}
这是我当前正在编辑的文件，选中的代码将由以下注释标识：//Start of Selection 和 //End of Selection
{{- end }}
{{- end }}
```{{ .UserCurrentlyEditFile }}
{{ .UserCurrentlyEditFileCode }}
```

{{- if .InlineAskHistory}}
### 对话历史
下面是我之前和智能编码助手的对话历史，参考这些对话历史来回答问题
{{- range $i, $history := .InlineAskHistory }}
*User:*
{{ $history.Question}}
*Assistant:*
{{ $history.Answer}}
{{- end }}
{{- end }}

### 我的指令
{{ .UserInputQueryWithHistory }}
{{- if .ExtraRequire }}
{{ .ExtraRequire}}
{{- end}}
## 你的任务

{{- if .EditFullFile }}
{{- if eq .OperationType "gen_code" }}
我目前光标所在位置如下，用<<<CURSOR_IS_HERE>>>标记，如果你需要新增代码，请尽量在我光标所在位置新增，
特别注意：**`<<<CURSOR_IS_HERE>>>`**：这个标记仅用于指示光标位置，它不存在原代码中，请不要在 `SEARCH` 块中包含这个标记。
{{- else }}
我选中的代码如下：
{{- end }}
```{{ .UserCurrentlyEditFile }}
{{ .UserSelectedCodeWithCommentAndIndent }}
```

请结合上下文和我的指令, 帮我修改我正在编辑的文件，你的回答需要符合SEARCH/REPLACE代码块的格式。

{{- else }}
{{- if eq .OperationType "gen_code" }}
结合上下文和我的指令，在插入点生成要插入的代码。
你的回答需要按照以下的格式返回:
```
{{ .GenerationWithCommentAndIndent }}
```
{{- else }}
结合上下文和我的指令，重写选中的代码。
## 选中的代码如下
```{{ .UserCurrentlyEditFile }}
{{ .UserSelectedCodeWithCommentAndIndent }}
```
注意：你只能重写选中的代码。

你的回答需要按照以下的格式返回:
```
{{ .GenerationWithCommentAndIndent }}
```
{{- end }}
{{- end }}

你的回答需要马上以```开始。