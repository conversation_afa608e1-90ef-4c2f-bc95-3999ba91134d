package common

import (
	"cosy/global"
	"cosy/log"
	"cosy/util"
	"os"
	"path/filepath"
	"runtime/debug"
	"strings"
	"sync"
	"time"
)

const (
	DefaultIgnoreFileName = ".gitignore"
	TongyiIgnoreFileName  = ".tongyiignore"
	QoderIgnoreFileName   = ".qoderignore"

	DetectIgnoreFileInterval = time.Second * 10
)

var (
	// 允许在workspace之外识别的文件
	allowedWithoutInWorkspace = []string{
		"sql",
	}

	ignoreMap sync.Map
	ignoreMtx sync.Mutex
)

func init() {
	ignoreMap = sync.Map{}
	ignoreMtx = sync.Mutex{}
}

// ProjectIgnore
// 根据.gitignore文件识别指定文件路径是否应当被忽略
type ProjectIgnore struct {
	WorkspacePath    string
	GitIgnore        *util.GitRuleIgnore
	gitFileMtime     time.Time
	ServiceIgnore    *util.GitRuleIgnore // 国内版是.tongyiignore 国外ide是.qoderignore
	serviceFileMtime time.Time
	mutex            sync.RWMutex
}

// IgnoreRule
// 执行ignore的匹配规则
type IgnoreRule struct {
	IsDir              bool // 是否为Dir
	GlobalIgnoreEnable bool // 是否开启全局过滤
}

func NewProjectIgnore(workspacePath string) *ProjectIgnore {
	if workspacePath == "" {
		return &ProjectIgnore{
			WorkspacePath: workspacePath,
			GitIgnore:     nil,
			ServiceIgnore: nil,
			mutex:         sync.RWMutex{},
		}
	}

	if projectIgnore, ok := ignoreMap.Load(workspacePath); ok && projectIgnore != nil {
		return projectIgnore.(*ProjectIgnore)
	}

	ignoreMtx.Lock()
	defer ignoreMtx.Unlock()

	if projectIgnore, ok := ignoreMap.Load(workspacePath); ok && projectIgnore != nil {
		return projectIgnore.(*ProjectIgnore)
	}

	projectIgnore := &ProjectIgnore{
		WorkspacePath: workspacePath,
		GitIgnore:     nil,
		ServiceIgnore: nil,
		mutex:         sync.RWMutex{},
	}

	projectIgnore.LoadWorkspace()

	go func() {
		defer func() {
			// 恢复panic
			if r := recover(); r != nil {
				log.Errorf("monitor ignore file panic: %v", r)
				log.Debugf("monitor ignore file panic stack: %s", string(debug.Stack()))
			}
		}()

		for {
			time.Sleep(DetectIgnoreFileInterval)
			projectIgnore.detectAndReload()
		}
	}()

	ignoreMap.Store(workspacePath, projectIgnore)

	return projectIgnore

}

// LoadIgnoreFile 加载.gitignore文件内容
func (p *ProjectIgnore) LoadIgnoreFile(filePath string) (*util.GitRuleIgnore, error) {
	log.Info("load ignore file:", filePath)
	gitRuleIgnore, err := util.NewGitRuleIgnore(p.WorkspacePath, filePath)
	if err != nil {
		return nil, err
	}
	return gitRuleIgnore, nil
}

// LoadWorkspace
// 查找.gitignore文件，并加载到GitIgnore中
// 查找.tongyiignore文件，并加载到TongyiIgnore中
func (p *ProjectIgnore) LoadWorkspace() {
	// 优先加载自定义的忽略文件
	p.mutex.Lock()
	defer p.mutex.Unlock()

	serviceIgnoreFileName := ""
	if global.IsQoderProduct() {
		serviceIgnoreFileName = QoderIgnoreFileName
	}

	if global.IsLingmaProduct() {
		serviceIgnoreFileName = TongyiIgnoreFileName
	}

	if serviceIgnoreFileName != "" {
		// 加载业务方的ignore文件
		serviceIgnoreFile := filepath.Join(p.WorkspacePath, serviceIgnoreFileName)
		if stat, err := os.Stat(serviceIgnoreFile); err == nil {
			p.ServiceIgnore, err = p.LoadIgnoreFile(serviceIgnoreFile)
			if err != nil {
				log.Errorf("load %s failed: %v", serviceIgnoreFile, err)
			}
			p.serviceFileMtime = stat.ModTime()
		}
	}

	// 加载默认的gitignore
	gitIgnoreFile := filepath.Join(p.WorkspacePath, DefaultIgnoreFileName)
	if stat, err := os.Stat(gitIgnoreFile); err == nil {
		p.GitIgnore, err = p.LoadIgnoreFile(gitIgnoreFile)
		if err != nil {
			log.Error("load .gitignore failed:", err)
		}
		p.gitFileMtime = stat.ModTime()
	}
}

func (p *ProjectIgnore) detectAndReload() {
	serviceIgnoreFileName := ""
	if global.IsQoderProduct() {
		serviceIgnoreFileName = QoderIgnoreFileName
	}

	if global.IsLingmaProduct() {
		serviceIgnoreFileName = TongyiIgnoreFileName
	}

	if serviceIgnoreFileName != "" {
		// 加载业务方的ignore文件
		serviceIgnoreFile := filepath.Join(p.WorkspacePath, serviceIgnoreFileName)
		if stat, err := os.Stat(serviceIgnoreFile); err == nil {
			if stat.ModTime() != p.serviceFileMtime {
				log.Infof("detect %s changed, reload", serviceIgnoreFile)

				p.mutex.Lock()
				p.ServiceIgnore, err = p.LoadIgnoreFile(serviceIgnoreFile)
				if err != nil {
					log.Error("load .tongyiignore failed:", err)
				}
				p.serviceFileMtime = stat.ModTime()
				p.mutex.Unlock()
			}
		}
	}

	gitIgnoreFile := filepath.Join(p.WorkspacePath, DefaultIgnoreFileName)
	if stat, err := os.Stat(gitIgnoreFile); err == nil {
		if stat.ModTime() != p.gitFileMtime {
			log.Infof("detect %s changed, reload", gitIgnoreFile)

			p.mutex.Lock()
			p.GitIgnore, err = p.LoadIgnoreFile(gitIgnoreFile)
			if err != nil {
				log.Error("load .gitignore failed:", err)
			}
			p.gitFileMtime = stat.ModTime()
			p.mutex.Unlock()
		}
	}
}

// IsIgnored
// 根据gitignore文件判断是否忽略
// 当开启globalIgnoreEnable时，会先执行全局ignore的规则检查
// mcp/extension 可以考虑将globalIgnoreEnable置为false，只做gitignore文件忽略
func (p *ProjectIgnore) IsIgnored(workspacePath string, filePath string, rule *IgnoreRule) bool {
	if !strings.HasPrefix(filePath, workspacePath) {
		return !isKeepingFile(filePath)
	}

	if rule == nil {
		return false
	}

	p.mutex.RLock()
	defer p.mutex.RUnlock()

	if rule.GlobalIgnoreEnable {
		// 开启全局检查时，先执行全局检查
		if rule.IsDir && util.IsGlobalIgnoreDir(workspacePath, filePath) {
			return true
		} else if !rule.IsDir && (util.IsGlobalIgnoreFile(filePath) || util.IsLargeFileByPath(filePath)) {
			return true
		}
	}

	isIgnored := false
	if p.GitIgnore != nil {
		paths := make([]string, 0)
		for _, path := range strings.Split(filePath, string(os.PathSeparator)) {
			if path == "" {
				continue
			}
			paths = append(paths, path)
		}
		isIgnored = p.GitIgnore.Matcher.Match(paths, rule.IsDir)
	}
	if isIgnored {
		return true
	}

	if p.ServiceIgnore != nil {
		paths := make([]string, 0)
		for _, path := range strings.Split(filePath, string(os.PathSeparator)) {
			if path == "" {
				continue
			}
			paths = append(paths, path)
		}
		isIgnored = p.ServiceIgnore.Matcher.Match(paths, rule.IsDir)
	}

	return isIgnored
}

func isKeepingFile(filePath string) bool {
	fileName := filepath.Base(filePath)
	for _, lang := range allowedWithoutInWorkspace {
		if strings.Contains(fileName, lang) {
			return true
		}
	}
	return false
}
