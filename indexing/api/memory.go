package api

import (
	"context"
	"cosy/deepwiki/storage"
	"cosy/definition"
	"cosy/indexing"
	"cosy/lang/indexer"
	"cosy/lang/indexer/rag"
	"cosy/log"
	"strings"

	// "cosy/storage/vector"
	// "cosy/util"
	"fmt"
	"path/filepath"
)

func MemoryIndexAll(ctx context.Context, wikiId string, workspace string) {
	log.Infof("[memory index] start to index wiki %s in workspace %s", wikiId, workspace)

	// GetWikiItemIdsByRepoAndWorkspace
	mdIds, err := storage.GlobalStorageService.GetWikiItemIdsByRepoAndWorkspace(workspace)
	// 给每个md后加上 .md 用于切块的时候获得正确splitter
	for i, mdId := range mdIds {
		mdIds[i] = mdId + ".md"
	}
	if err != nil {
		log.Errorf("[memory index] failed to get wiki item ids by repo and workspace: %v", err)
		return
	}

	memoryIndex(ctx, wikiId, workspace, mdIds)
}

func memoryIndex(ctx context.Context, wikiId string, workspace string, mdIds []string) {
	if len(mdIds) == 0 {
		log.Warnf("[memory index] no mdIds provided for indexing")
		return
	}
	MemoryIndexText(ctx, wikiId, workspace, mdIds)
	MemoryIndexVector(ctx, wikiId, workspace, mdIds)

	log.Infof("[memory index] successfully indexed wiki %s in workspace %s", wikiId, workspace)
}

// getMemoryTextEngine 获取记忆文本引擎
// 优先从ctx中的fileIndexer获取，失败则使用原来的方式
func getMemoryTextEngine(ctx context.Context, workspace string) rag.TextRetrieveEngine {
	// 尝试从上下文中获取 fileIndexer
	if fileIndexerValue := ctx.Value(definition.ContextKeyFileIndexer); fileIndexerValue != nil {
		// 使用接口类型断言避免循环依赖
		if fileIndexer, ok := fileIndexerValue.(*indexing.ProjectFileIndex); ok {
			if textIndexer, hasTextIndexer := fileIndexer.GetMemoryRetrieveFileTextIndexer(); hasTextIndexer {
				if engine, err := textIndexer.GetTextRetrieveEngine(true); err == nil {
					log.Debugf("[memory index] successfully got text engine from fileIndexer for workspace: %s", workspace)
					return engine
				} else {
					log.Warnf("[memory index] failed to get text engine from fileIndexer: %v", err)
				}
			} else {
				log.Warnf("[memory index] memory text indexer not found in fileIndexer")
			}
		} else {
			log.Debugf("[memory index] fileIndexer type assertion failed, using fallback method")
		}
	} else {
		log.Debugf("[memory index] fileIndexer not found in context, using fallback method")
	}

	// 回退到原来的方式
	engine := rag.NewMemoryBleveRetrieveEngine(workspace)
	if engine == nil {
		log.Errorf("[memory index] failed to create memory bleve retrieve engine")
		return nil
	}

	err := engine.Initialize()
	if err != nil {
		log.Errorf("[memory index] failed to initialize memory bleve retrieve engine: %v", err)
		return nil
	}

	return engine
}

// getMemoryVectorEngine 获取记忆向量引擎
// 优先从ctx中的fileIndexer获取，失败则使用原来的方式
func getMemoryVectorEngine(ctx context.Context, workspace string) rag.VectorRetrieveEngine {
	// 回退到原来的方式
	engine, err := rag.GetClientChatMemoVectorRetrieveEngine(workspace)
	if err != nil {
		log.Errorf("[memory index] failed to get memo vector retrieve engine: %v", err)
		return nil
	}

	return engine
}

func MemoryIndexText(ctx context.Context, wikiId string, workspace string, mdIds []string) {
	engine := getMemoryTextEngine(ctx, workspace)
	if engine == nil {
		log.Errorf("[memory index] failed to get memory text engine")
		return
	}

	results, err := engine.BatchIndex(mdIds, false)
	log.Debugf("[memory index] batch index text results: %v", results)
	if err != nil {
		log.Errorf("[memory index] failed to batch index text: %v", err)
	}
}

func MemoryIndexVector(ctx context.Context, wikiId string, workspace string, mdIds []string) {
	// 获取memo向量引擎
	engine := getMemoryVectorEngine(ctx, workspace)
	if engine == nil {
		log.Errorf("[memory index] failed to get memory vector engine")
		return
	}

	// 创建任务列表
	var tasks []*definition.Task

	for _, mdId := range mdIds {
		// 去掉 .md
		handleMdId := strings.TrimSuffix(mdId, ".md")
		// 根据mdId调用GetWikiItemByID获得内容
		wikiItem, err := storage.GlobalStorageService.GetWikiItemByID(handleMdId)
		if err != nil {
			log.Errorf("[memory index] failed to get wiki item by ID %s: %v", mdId, err)
			continue
		}

		if wikiItem == nil {
			log.Warnf("[memory index] wiki item not found for ID: %s", mdId)
			continue
		}

		virtualFile := definition.NewVirtualFileWithContent(mdId, []byte(wikiItem.Content))

		// 创建任务
		task := definition.NewTask(virtualFile, definition.VectorFileChangeIndexSource, workspace, -1)
		tasks = append(tasks, task)

		log.Debugf("[memory index] prepared wiki item %s (title: %s) for indexing", mdId, wikiItem.Title)
	}

	if len(tasks) == 0 {
		log.Warnf("[memory index] no valid wiki items found for indexing")
		return
	}

	// 创建批量任务并执行
	batchTask := definition.NewVectorBatchTask(workspace, tasks)
	log.Debugf("[memory index] start to build index, task num: %v", len(batchTask.Tasks))

	// 调用ExecuteBatchTask建立索引
	// enableCheckChange设为false，因为这些是wiki内容，不需要检查文件变更
	engine.ExecuteBatchTask(batchTask, false, true)
}
func GetBuiltRecords(ctx context.Context, workspace string, wikiIds []string) []string {
	vecRes := GetAllVecRecords(workspace)
	textRes := GetAllTextRecords(ctx, workspace, wikiIds)

	// 创建一个map来存储textRes中的ID，便于快速查找
	textMap := make(map[string]bool)
	for _, id := range textRes {
		textMap[id] = true
	}

	// 找出两个结果中都有的ID
	var intersection []string
	for _, id := range vecRes {
		if textMap[id] {
			intersection = append(intersection, id)
		}
	}
	for i, id := range intersection {
		intersection[i] = strings.TrimSuffix(id, ".md")
	}

	// 创建成功ID的map，便于查找
	successMap := make(map[string]bool)
	for _, id := range intersection {
		successMap[id] = true
	}

	// 返回失败的ID（传入的wikiIds减去成功的ID）
	var failedIds []string
	for _, wikiId := range wikiIds {
		if !successMap[wikiId] {
			failedIds = append(failedIds, wikiId)
		}
	}

	return failedIds
}
func GetAllVecRecords(workspace string) []string {
	vecEngine, err := rag.GetClientChatMemoVectorRetrieveEngine(workspace)
	if err != nil {
		log.Errorf("[memory index] failed to get memo vector retrieve engine: %v", err)
		return nil
	}

	engine, ok := vecEngine.(*rag.SqliteVecRetrieveEngine)
	if !ok {
		log.Errorf("[memory index] vector engine is not SqliteVecRetrieveEngine type")
		return nil
	}

	res, err := engine.Client.QueryAllStorageRecords()

	mdIds := make([]string, 0)
	for _, record := range res {
		mdIds = append(mdIds, record.FilePath)
	}
	return mdIds
}
func GetAllTextRecords(ctx context.Context, workspace string, wikiIds []string) []string {
	textEngine := getMemoryTextEngine(ctx, workspace)
	if textEngine == nil {
		log.Errorf("[memory index] failed to get memory text engine")
		return nil
	}

	engine, ok := textEngine.(*rag.BleveRetrieveEngine)
	if !ok {
		log.Errorf("[memory index] text engine is not SqliteTextRetrieveEngine type")
		return nil
	}
	mdIds := make([]string, 0)
	for _, wikiId := range wikiIds {
		tempId := wikiId + ".md"
		tempRes, _ := engine.GetFileChunks(tempId, 10)
		if len(tempRes) > 0 {
			mdIds = append(mdIds, tempId)
		}
	}
	return mdIds
}
func MemoryIndexUpdate(ctx context.Context, wikiId string, workspace string, mdIdsToAdd []string, mdIdsToDelete []string) {
	// 处理删除的项目
	if len(mdIdsToDelete) > 0 {
		engine := getMemoryVectorEngine(ctx, workspace)
		if engine == nil {
			log.Errorf("[memory index] failed to get memo vector retrieve engine for deletion")
		} else {
			var deletePatterns []string
			for _, mdId := range mdIdsToDelete {
				// 构建要删除的文件路径模式
				pattern := filepath.Join(workspace, fmt.Sprintf("%s.md", mdId))
				deletePatterns = append(deletePatterns, pattern)
			}

			deleteVirtualFiles := definition.BuildBatchVirtualFile(deletePatterns)
			err := engine.BatchDeleteIndex(deleteVirtualFiles, true)
			if err != nil {
				log.Errorf("[memory index] failed to delete index for wiki items: %v", err)
			} else {
				log.Infof("[memory index] successfully deleted index for %d wiki items", len(mdIdsToDelete))
			}
		}
	}

	// 处理添加的项目
	if len(mdIdsToAdd) > 0 {
		for i, mdId := range mdIdsToAdd {
			mdIdsToAdd[i] = mdId + ".md"
		}
		memoryIndex(ctx, wikiId, workspace, mdIdsToAdd)
	}

	// 处理文本索引
	textEngine := getMemoryTextEngine(ctx, workspace)
	if textEngine == nil {
		log.Errorf("[memory index] failed to get memory text engine for update")
		return
	}

	// 删除文本索引
	if len(mdIdsToDelete) > 0 {
		log.Warnf("[memory index] text index removal not implemented yet for %d items", len(mdIdsToDelete))
	}
	textEngine.BatchRemove(mdIdsToDelete)
	log.Infof("[memory index] successfully removed text index for %d wiki items", len(mdIdsToDelete))

	// 添加文本索引
	if len(mdIdsToAdd) > 0 {
		_, err := textEngine.BatchIndex(mdIdsToAdd, true)
		if err != nil {
			log.Errorf("[memory index] failed to batch index text for update: %v", err)
		}
		log.Infof("[memory index] successfully updated text index for %d wiki items", len(mdIdsToAdd))
	}
}

func MemeoryQuery(wikiId string, workspace string, query string) ([]indexer.CodeChunk, error) {
	return nil, nil
}

// MemoryDeleteWiki 删除指定工程的wiki向量化数据
func MemoryDeleteWiki(ctx context.Context, wikiId string, workspace string) error {
	log.Infof("[memory index] start to delete wiki %s in workspace %s", wikiId, workspace)

	// 获取所有wiki项目ID
	mdIds, err := storage.GlobalStorageService.GetWikiItemIdsByRepoAndWorkspace(workspace)
	if err != nil {
		log.Errorf("[memory index] failed to get wiki item ids for deletion: %v", err)
		return err
	}

	// 删除向量索引
	vectorEngine := getMemoryVectorEngine(ctx, workspace)
	if vectorEngine == nil {
		log.Errorf("[memory index] failed to get vector engine for deletion")
		return fmt.Errorf("failed to get vector engine")
	}

	var deletePatterns []string
	for _, mdId := range mdIds {
		// 构建要删除的文件路径模式
		pattern := fmt.Sprintf("%s.md", mdId)
		deletePatterns = append(deletePatterns, pattern)
	}

	if len(deletePatterns) > 0 {
		deleteVirtualFiles := definition.BuildBatchVirtualFile(deletePatterns)
		err = vectorEngine.BatchDeleteIndex(deleteVirtualFiles, true)
		if err != nil {
			log.Errorf("[memory index] failed to delete vector index: %v", err)
			return err
		}
	}

	// 删除文本索引
	textEngine := getMemoryTextEngine(ctx, workspace)
	if textEngine == nil {
		log.Errorf("[memory index] failed to get text engine for deletion")
		return fmt.Errorf("failed to get text engine")
	}

	if len(mdIds) > 0 {
		// 为每个mdId添加.md后缀
		for i, mdId := range mdIds {
			mdIds[i] = mdId + ".md"
		}
		textEngine.BatchRemove(mdIds)
	}

	log.Infof("[memory index] successfully deleted wiki %s in workspace %s", wikiId, workspace)
	return nil
}

// MemoryRebuildWiki 重建指定工程的wiki向量化数据
func MemoryRebuildWiki(ctx context.Context, wikiId string, workspace string) error {
	log.Infof("[memory index] start to rebuild wiki %s in workspace %s", wikiId, workspace)

	// 先删除现有的索引
	err := MemoryDeleteWiki(ctx, wikiId, workspace)
	if err != nil {
		log.Errorf("[memory index] failed to delete existing wiki index before rebuild: %v", err)
		// 即使删除失败，也继续尝试重建
	}

	// 重新建立索引
	MemoryIndexAll(ctx, wikiId, workspace)

	log.Infof("[memory index] successfully rebuilt wiki %s in workspace %s", wikiId, workspace)
	return nil
}
