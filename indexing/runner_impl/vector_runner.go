package runner

import (
	"cosy/definition"
	"cosy/indexing/common"
	"cosy/lang/indexer/rag"
	"cosy/log"
	"cosy/util/collection"
	"sync"
	"sync/atomic"
	"time"
)

const (
	DefaultMaxTaskRetryTimes = 3

	DefaultBatchTaskSize = 10

	// VectorIndexMaxWorkerNum 最大worker数量
	VectorIndexMaxWorkerNum = 1

	MinScanQueueTime  = 2 * time.Second
	MaxScanQueueTime  = 10 * time.Second
	ScanQueueTimeStep = 2 * time.Second
)

func NewVectorIndexBuilderRunner() common.Runner {
	runner := &VectorIndexRunner{
		Mtx:                 sync.Mutex{},
		ReadyBatchTasks:     make(chan definition.VectorBatchTask, VectorIndexMaxWorkerNum),
		Tasks:               collection.NewPriorityQueue[*definition.Task](),
		WorkspaceReportData: make(map[string]*definition.VectorRunnerReportData),
		IsStop:              atomic.Bool{},
	}
	runner.IsStop.Store(false)

	return runner
}

// VectorIndexRunner 向量索引的执行器
type VectorIndexRunner struct {
	Mtx                 sync.Mutex
	Tasks               *collection.PriorityQueue[*definition.Task] // 存储所有进来的任务
	ReadyBatchTasks     chan definition.VectorBatchTask
	WorkspaceReportData map[string]*definition.VectorRunnerReportData
	IsStop              atomic.Bool
}

func (runner *VectorIndexRunner) Start() {
	go func() {
		timeToSleep := MaxScanQueueTime
		// 监控优先队列中的任务，执行任务
		for {
			if runner.IsStop.Load() {
				log.Debugf("[codebase]-[system] vector runner stop")
				return
			}

			runner.Mtx.Lock()
			if runner.Tasks.Len() == 0 ||
				len(runner.ReadyBatchTasks) >= VectorIndexMaxWorkerNum {
				// 队列为空，或者已准备好的任务队列已满，或者失败的任务队列过多，则等待
				// 没有任务，此时可以多等待，减少cpu占用
				runner.Mtx.Unlock()
				if timeToSleep > MaxScanQueueTime {
					timeToSleep = MaxScanQueueTime
				}
				time.Sleep(timeToSleep)
				timeToSleep += ScanQueueTimeStep
				//log.Debugf("[codebase]-[system] runner wait")
			} else {
				var tasks []*definition.Task

				// 同一个batch只做workspace完全一致的ask
				workspacePath := ""
				for idx := 0; idx < DefaultBatchTaskSize && runner.Tasks.Len() > 0; idx++ {
					item := runner.Tasks.PopItem()
					if workspacePath == "" {
						// workspacePath初始化为第一个取出的task的workspacePath
						workspacePath = item.Value().WorkspacePath
					} else if workspacePath != item.Value().WorkspacePath {
						// 保证每个batchTask都是同一个workspace的任务，因此这里将取出的task放回队列中
						runner.Tasks.PushItem(item.Value(), item.Value().Priority)
						break
					}
					tasks = append(tasks, item.Value())
					item.Value().Status = definition.TaskStatusRunning
				}

				if workspacePath != "" {
					batchTask := definition.NewVectorBatchTask(workspacePath, tasks)
					log.Debugf("[codebase]-[system] runner get task: %d, workspace: %s", len(tasks), workspacePath)

					// 保证不阻塞
					if len(runner.ReadyBatchTasks) < VectorIndexMaxWorkerNum {
						// 这里是唯一的放入队列的逻辑
						runner.ReadyBatchTasks <- batchTask
					} else {
						log.Errorf("[codebase]-[system] the ready channel is full, workspace: %s", batchTask.WorkspacePath)
						go func() {
							// 未添加进队列的任务放回队列中
							runner.PushTasks(tasks, false)
						}()
					}

					// 存在任务被真实放入了执行队列，因此需要减少扫描时间间隔，加速任务执行
					timeToSleep -= ScanQueueTimeStep
					if timeToSleep < MinScanQueueTime {
						timeToSleep = MinScanQueueTime
					}
				}

				runner.Mtx.Unlock()
			}
		}
	}()

	for idx := 0; idx < VectorIndexMaxWorkerNum; idx++ {
		go func() {
			runner.executeBatchTask()
		}()
	}
}

func (runner *VectorIndexRunner) executeBatchTask() {
	if runner.IsStop.Load() {
		return
	}
	// 真实执行的batchTask
	for batchTask := range runner.ReadyBatchTasks {
		workspacePath := batchTask.WorkspacePath

		// 获取向量引擎
		engine, err := rag.GetClientChatVectorRetrieveEngine(workspacePath)
		if err != nil {
			log.Errorf("[codebase]-[system] runner get engine failed, err:%v", err)
			// 出现失败，就直接丢弃当前的全部任务
			continue
		}
		if engine == nil {
			log.Errorf("[codebase]-[system] runner get engine failed, workspacePath:%s", workspacePath)
			// 出现失败，就直接丢弃当前的全部任务
			continue
		}

		reachMaxStorage := engine.ExecuteBatchTask(batchTask, true, true)
		if reachMaxStorage {
			go func() {
				runner.SkipRemainingTask(workspacePath)
			}()
			continue
		}
		// 统计失败的Task，将失败的Task放回
		var failedTasks []*definition.Task
		finishedTaskCnt := 0
		discardTaskCnt := 0
		skipTaskCnt := 0
		doEmbeddingChunkCnt := 0
		shareChunkHitCnt := 0
		for _, task := range batchTask.Tasks {
			if task.Status == definition.TaskStatusFinish {
				if task.Source == definition.VectorInitialFullIndexSource {
					finishedTaskCnt += 1
					doEmbeddingChunkCnt += task.DoEmbeddingChunkCnt
					shareChunkHitCnt += task.ShareChunkHitCnt
				}
			} else if task.Status == definition.TaskStatusFailed {
				// 降低失败task的优先级，等待后续放回任务队列中
				task.Priority -= 1
				task.FailedTimes += 1
				// 失败次数过多的任务不再放回队列
				if task.FailedTimes > DefaultMaxTaskRetryTimes {
					log.Warnf("[codebase]-[system] task discard, filePath:%s", task.GetFilePath())
					if task.Source == definition.VectorInitialFullIndexSource {
						discardTaskCnt += 1
					}
					continue
				}
				failedTasks = append(failedTasks, task)
			} else if task.Status == definition.TaskStatusDiscard {
				if task.Source == definition.VectorInitialFullIndexSource {
					discardTaskCnt += 1
				}
			} else if task.Status == definition.TaskStatusSkip {
				if task.Source == definition.VectorInitialFullIndexSource {
					skipTaskCnt += 1
				}
			} else {
				log.Errorf("[codebase]-[runner] task status unknown error, filePath:%s", task.GetFilePath())
				if task.Source == definition.VectorInitialFullIndexSource {
					discardTaskCnt += 1
				}
			}
		}

		//log.Debugf("[codebase]-[system] %d tasks success, %d tasks failed", successTaskCnt, failedTaskCnt)
		if len(failedTasks) > 0 {
			go func() {
				// 放回失败的task
				// 将失败的task放回任务队列中
				log.Warnf("[codebase]-[system] %d failed task will be pushed back", len(failedTasks))
				runner.PushTasks(failedTasks, true)
			}()
		}

		runner.Mtx.Lock()
		if record, ok := runner.WorkspaceReportData[workspacePath]; ok && record != nil {
			runner.WorkspaceReportData[workspacePath].FinishedTaskCnt += finishedTaskCnt
			runner.WorkspaceReportData[workspacePath].DiscardTaskCnt += discardTaskCnt
			runner.WorkspaceReportData[workspacePath].SkipTaskCnt += skipTaskCnt
			runner.WorkspaceReportData[workspacePath].DoEmbeddingChunkCnt += doEmbeddingChunkCnt
			runner.WorkspaceReportData[workspacePath].ShareChunkHitCnt += shareChunkHitCnt
		}
		runner.Mtx.Unlock()
	}
}

// Stop 停止执行器
// 业务不调用，评测调用
func (runner *VectorIndexRunner) Stop() {
	runner.Mtx.Lock()
	defer runner.Mtx.Unlock()

	runner.IsStop.Store(true)
}

// PushTasks 添加任务
func (runner *VectorIndexRunner) PushTasks(tasks []*definition.Task, retry bool) {
	if runner.IsStop.Load() {
		return
	}

	runner.Mtx.Lock()
	defer runner.Mtx.Unlock()

	for _, task := range tasks {
		if !retry && task.Source == definition.VectorInitialFullIndexSource {
			if _, ok := runner.WorkspaceReportData[task.WorkspacePath]; ok {
				runner.WorkspaceReportData[task.WorkspacePath].TotalTaskCnt += 1
			}
		}
		runner.Tasks.PushItem(task, task.Priority)
	}
}

// PopTasks 移除任务
// 按照workspace移除任务，移除workspace下所有任务
func (runner *VectorIndexRunner) PopTasks(workspacePath string) {
	if workspacePath == "" {
		// workspace不能为空
		return
	}

	if runner.IsStop.Load() {
		return
	}

	runner.Mtx.Lock()
	defer runner.Mtx.Unlock()

	queue := collection.NewPriorityQueue[*definition.Task]()
	for runner.Tasks.Len() > 0 {
		newTask := runner.Tasks.PopItem().Value()
		if newTask.WorkspacePath != workspacePath {
			queue.PushItem(newTask, newTask.Priority)
		}
	}

	runner.Tasks = queue
}

// UpdatePriority 更新任务优先级
func (runner *VectorIndexRunner) UpdatePriority(workspacePath string, priority int) {
	if runner.IsStop.Load() {
		return
	}

	runner.Mtx.Lock()
	defer runner.Mtx.Unlock()

	queue := collection.NewPriorityQueue[*definition.Task]()
	for runner.Tasks.Len() > 0 {
		newTask := runner.Tasks.PopItem().Value()
		if newTask.WorkspacePath == workspacePath {
			queue.PushItem(newTask, priority)
			newTask.Priority = priority
			continue
		} else {
			queue.PushItem(newTask, newTask.Priority)
		}
	}

	runner.Tasks = queue
}

// RegisterWorkspace
// 注册workspace的进度
func (runner *VectorIndexRunner) RegisterWorkspace(workspacePath string, reportData *definition.VectorRunnerReportData) {
	if workspacePath == "" {
		log.Errorf("[codebase]-[runner] delete tasks progress failed, workspacePath is empty")
		return
	}

	if runner.IsStop.Load() {
		log.Errorf("[codebase]-[runner] delete tasks progress failed, runner is stop")
		// 代表执行器已经停止，返回-2
		// 目前代码中不会主动停止runner
		return
	}

	runner.Mtx.Lock()
	defer runner.Mtx.Unlock()
	if _, ok := runner.WorkspaceReportData[workspacePath]; !ok {
		runner.WorkspaceReportData[workspacePath] = reportData
	}
	return
}

// UnRegisterWorkspace
// 根据workspacePath删除进度
func (runner *VectorIndexRunner) UnRegisterWorkspace(workspacePath string) {
	if workspacePath == "" {
		log.Errorf("[codebase]-[runner] delete tasks progress failed, workspacePath is empty")
		return
	}

	if runner.IsStop.Load() {
		log.Errorf("[codebase]-[runner] delete tasks progress failed, runner is stop")
		// 代表执行器已经停止，返回-2
		// 目前代码中不会主动停止runner
		return
	}

	runner.Mtx.Lock()
	defer runner.Mtx.Unlock()
	delete(runner.WorkspaceReportData, workspacePath)
	return
}

// ReportTasksProgress
// 根据workspacePath查询用户当前执行任务的进度
func (runner *VectorIndexRunner) ReportTasksProgress(workspacePath string) *definition.VectorRunnerReportData {
	if workspacePath == "" {
		log.Errorf("[codebase]-[runner] report tasks progress failed, workspacePath is empty")
		// 这是一个错误请求，workspacePath不应该为空
		return nil
	}

	if runner.IsStop.Load() {
		log.Errorf("[codebase]-[runner] report tasks progress failed, runner is stop")
		// 代表执行器已经停止，返回-2
		// 目前代码中不会主动停止runner
		return nil
	}

	runner.Mtx.Lock()
	defer runner.Mtx.Unlock()
	return runner.WorkspaceReportData[workspacePath]
}

// SkipRemainingTask
// 出现全量skip后，代表当前数据库存储已达到上限，需要更新任务进度
// 标记任务全部已完成
// 当全库索引数据已满时，删除在Runner中同一个WorkspacePath的剩余未执行Task
func (runner *VectorIndexRunner) SkipRemainingTask(workspacePath string) {
	if workspacePath == "" {
		log.Errorf("[codebase]-[runner] update tasks progress failed, workspacePath is empty")
		// 这是一个错误请求，workspacePath不应该为空
		return
	}

	if runner.IsStop.Load() {
		return
	}

	runner.Mtx.Lock()
	defer runner.Mtx.Unlock()

	if _, ok := runner.WorkspaceReportData[workspacePath]; ok {
		reportData := runner.WorkspaceReportData[workspacePath]
		skipTodoTaskCnt := reportData.TotalTaskCnt -
			reportData.FinishedTaskCnt -
			reportData.DiscardTaskCnt -
			reportData.SkipTaskCnt
		reportData.SkipTodoTaskCnt = skipTodoTaskCnt
	}

	queue := collection.NewPriorityQueue[*definition.Task]()
	for runner.Tasks.Len() > 0 {
		newTask := runner.Tasks.PopItem().Value()
		if newTask.WorkspacePath == workspacePath &&
			newTask.Source == definition.VectorInitialFullIndexSource {
			// 同一个库且为全库索引任务，跳过
			continue
		}
		queue.PushItem(newTask, newTask.Priority)
	}

	runner.Tasks = queue
}
