package manager

import (
	"cosy/definition"
	"cosy/global"
	"cosy/indexing/common"
	runner_impl "cosy/indexing/runner_impl"
	"cosy/lang/indexer/rag"
	"cosy/log"
	"cosy/sls"
	"cosy/storage/vector"
	"cosy/user"
	"cosy/util"
	"os"
	"path/filepath"
	"strconv"
	"sync"
	"sync/atomic"
	"time"

	"github.com/google/uuid"
)

const (
	// DefaultFileChangedPriority 文件修改/文件打开 建立索引时的优先级
	// 文件修改/文件打开 考虑为最优先
	DefaultFileChangedPriority = 10000000
)

var (
	GlobalIndexBuilderManager IndexRunnerManager

	//FileChangedPriority
	// 文件变更的优先级也需要有变化，先来的优先级高，后来的优先级低，因为后执行的结果会将先执行的结果覆盖，优先级高的先执行
	// 因此优先级需要逐渐降低
	FileChangedPriority atomic.Int32
)

// IndexRunnerManager 用于建立索引的Manager
// 管理着索引建立过程中不同的Runner
type IndexRunnerManager interface {
	Start()                                                                                      // 启动
	RegisterWorkspace(workspacePath string)                                                      // 初始化workspace优先级，具备一定随机性
	UnRegisterWorkspace(workspacePath string)                                                    // 将workspace任务的优先级调低，但不会删除任务
	PushVirtualFiles(virtualFiles []definition.VirtualFile, source string, workspacePath string) // 批量构建并将Task分发给runner
	UpdatePriority(workspacePath string, priority int)                                           // 更新workspace的优先级
	// ReportIndexBuildingProgress 只有批量建立索引时，才需要汇报进度
	// 返回值含义分别是：总任务数量，已完成任务数量
	ReportIndexBuildingProgress(workspacePath string) *IndexBuildingRecord
	WaitForWorkspaceFinish(workspacePath string)
}

func init() {
	if GlobalIndexBuilderManager == nil {
		GlobalIndexBuilderManager = NewIndexBuilderManager()
	}
	FileChangedPriority.Store(DefaultFileChangedPriority)
}

type IndexBuildingManager struct {
	Mtx               sync.Mutex
	WorkspacePriority map[string]int                     // 每个workspace的task对应的优先级
	VectorRunner      common.Runner                      // 向量执行器
	TodoTaskChan      chan definition.VirtualFileWrapper // 将其构建成为task，初始化为1000，长度保证能不阻塞即可
	Priority          *atomic.Int32                      // 优先级按照顺序递增，具备一定随机性
	WorkspaceRecords  map[string]*IndexBuildingRecord
}

type IndexBuildingRecord struct {
	TotalTaskCnt         int     // 扫库后，实际进入索引队列的文件数量
	TotalValidTaskCnt    int     // 真实有效的文件数量，如果TotalTaskCnt大于文件存储最大值，则使用存储文件最大值
	FinishedTaskCnt      int     // 建立索引过程中，执行embedding而建立索引的文件数量
	SkipTaskCnt          int     // 建立索引过程中，因为建立过索引的文件而skip的文件数量
	DiscardTaskCnt       int     // 建立索引过程中，因为失败次数过多而放弃的文件数量
	SkipTodoTaskCnt      int     // 因存储已满而跳过的剩余任务数量
	TotalStorageChunkCnt int     // 总共存储的chunk数量
	ChunkSize            int     // 切块大小
	ChunkOverlap         int     // 切块重叠大小
	ShareChunkHitCnt     int     // 共享chunk命中次数
	DoEmbeddingChunkCnt  int     // 共享chunk未命中，执行embedding的chunk数量
	FinishedPercent      float64 // 实际执行embedding而完成的任务数量占比，用于判定究竟是全量索引还是增量索引
	Finished             bool
	StartTime            time.Time
	EndTime              time.Time
	WorkspacePath        string
}

func (record *IndexBuildingRecord) GetActualDoneTaskCnt() int {
	return record.FinishedTaskCnt + record.SkipTaskCnt + record.DiscardTaskCnt + record.SkipTodoTaskCnt
}

func NewIndexBuilderManager() IndexRunnerManager {
	manager := &IndexBuildingManager{
		Mtx:               sync.Mutex{},
		VectorRunner:      runner_impl.NewVectorIndexBuilderRunner(),
		TodoTaskChan:      make(chan definition.VirtualFileWrapper, 1000),
		WorkspacePriority: make(map[string]int),
		Priority:          &atomic.Int32{},
		WorkspaceRecords:  make(map[string]*IndexBuildingRecord),
	}
	manager.Priority.Store(0)
	manager.Start()

	return manager
}

// RegisterWorkspace
// 注册workspace，并更新workspace的优先级
func (manager *IndexBuildingManager) RegisterWorkspace(workspacePath string) {
	manager.Mtx.Lock()
	defer manager.Mtx.Unlock()

	if _, ok := manager.WorkspacePriority[workspacePath]; !ok {
		newPriority := int(manager.Priority.Add(100))
		manager.WorkspacePriority[workspacePath] = newPriority
	}
}

// UnRegisterWorkspace 将这个workspace的任务优先级调低
func (manager *IndexBuildingManager) UnRegisterWorkspace(workspacePath string) {
	if _, ok := manager.WorkspacePriority[workspacePath]; !ok {
		return
	}

	manager.Mtx.Lock()
	defer manager.Mtx.Unlock()
	// 删除的workspace优先级置为0，表示最低
	manager.VectorRunner.UnRegisterWorkspace(workspacePath)
	delete(manager.WorkspacePriority, workspacePath)
}

func (manager *IndexBuildingManager) UpdatePriority(workspacePath string, priority int) {
	manager.Mtx.Lock()
	defer manager.Mtx.Unlock()

	if _, ok := manager.WorkspacePriority[workspacePath]; !ok {
		return
	}

	manager.WorkspacePriority[workspacePath] = priority
	go func() {
		manager.VectorRunner.UpdatePriority(workspacePath, priority)
	}()
}

func (manager *IndexBuildingManager) PushVirtualFiles(virtualFiles []definition.VirtualFile, source string, workspacePath string) {
	if source == definition.VectorInitialFullIndexSource {
		// 全量索引请求，注册workspace
		manager.RegisterWorkspace(workspacePath)
	}

	manager.Mtx.Lock()
	defer manager.Mtx.Unlock()

	if source == definition.VectorInitialFullIndexSource {
		// 代表来源是全量建立索引的请求
		engine, err := rag.GetClientChatVectorRetrieveEngine(workspacePath)
		if err != nil {
			log.Errorf("[codebase]-[manager] get chat vector retrieve engine failed, err: %v", err)
			return
		}

		// 如果达到最大文件数量，则拒绝请求
		storageFileNum := engine.GetStorageFileNum()
		if storageFileNum >= global.GetMaxClientStorageFileNum() {
			log.Warnf("[codebase]-[manager] storage file num is %d, reject index builing request", storageFileNum)
			return
		}

		totalTaskCnt := len(virtualFiles) + storageFileNum
		totalValidTaskCnt := len(virtualFiles) + storageFileNum
		if totalValidTaskCnt >= global.GetMaxClientStorageFileNum() {
			totalValidTaskCnt = global.GetMaxClientStorageFileNum()
		}

		if record, ok := manager.WorkspaceRecords[workspacePath]; !ok {
			runnerReportData := &definition.VectorRunnerReportData{
				TotalTaskCnt:        len(virtualFiles),
				SkipTaskCnt:         storageFileNum,
				FinishedTaskCnt:     0,
				DiscardTaskCnt:      0,
				SkipTodoTaskCnt:     0,
				ShareChunkHitCnt:    0,
				DoEmbeddingChunkCnt: 0,
			}

			manager.VectorRunner.RegisterWorkspace(workspacePath, runnerReportData)
			newRecord := &IndexBuildingRecord{
				StartTime:         time.Now(),
				TotalTaskCnt:      totalTaskCnt,
				TotalValidTaskCnt: totalValidTaskCnt,
				SkipTaskCnt:       storageFileNum,
				Finished:          false,
				WorkspacePath:     workspacePath,
				ChunkSize:         definition.DefaultChunkSize,
				ChunkOverlap:      definition.DefaultChunkSize / 10,
			}
			manager.WorkspaceRecords[workspacePath] = newRecord
		} else {
			record.TotalTaskCnt += len(virtualFiles)
			record.TotalValidTaskCnt += len(virtualFiles)
			maxStorageFileNum := global.GetMaxClientStorageFileNum()
			if record.TotalValidTaskCnt >= maxStorageFileNum {
				record.TotalValidTaskCnt = maxStorageFileNum
			}
		}
	}

	wrapper := definition.VirtualFileWrapper{
		VirtualFiles:  virtualFiles,
		WorkspacePath: workspacePath,
		Source:        source,
	}
	manager.TodoTaskChan <- wrapper
}

func (manager *IndexBuildingManager) Start() {
	manager.VectorRunner.Start()

	go func() {
		manager.pushWrapperIntoRunner()
	}()
}

// pushWrapperIntoRunner 将virtualFileWrapper放入runner的chan中
func (manager *IndexBuildingManager) pushWrapperIntoRunner() {
	for {
		virtualFileWrapper := <-manager.TodoTaskChan
		workspacePath := virtualFileWrapper.WorkspacePath
		source := virtualFileWrapper.Source
		var priority int
		if source == definition.VectorFileChangeIndexSource {
			// 代表这是从文件变更来的索引建立请求
			// 增量索引请求
			priority = int(FileChangedPriority.Load())
		} else {
			// 全量索引请求
			priority = manager.WorkspacePriority[workspacePath]
		}
		var tasks []*definition.Task
		for _, virtualFile := range virtualFileWrapper.VirtualFiles {
			// 这里要过滤掉所有的目录filePath
			fileInfo, err := os.Stat(virtualFile.GetFilePath())
			if err != nil || fileInfo == nil || (fileInfo != nil && fileInfo.IsDir()) {
				// 文件不存在或无法访问
				// 当前文件为目录
				continue
			}
			taskPriority := util.GetFilePriority(virtualFile.GetFilePath()) + priority
			task := definition.NewTask(virtualFile, source, workspacePath, taskPriority)
			tasks = append(tasks, task)
		}
		manager.VectorRunner.PushTasks(tasks, false)
	}
}

func (manager *IndexBuildingManager) ReportIndexBuildingProgress(workspacePath string) *IndexBuildingRecord {
	if workspacePath == "" {
		log.Errorf("[codebase] [manager] report index building progress failed, workspace path is empty")
		return nil
	}

	manager.Mtx.Lock()
	defer manager.Mtx.Unlock()

	if record, ok := manager.WorkspaceRecords[workspacePath]; !ok || record == nil {
		// 如果没有记录，直接返回，默认是已建立成功
		return nil
	}
	runnerReportData := manager.VectorRunner.ReportTasksProgress(workspacePath)
	if runnerReportData == nil {
		// 正常链路不能为空，为空时直接停止此次索引
		// 返回nil就停止了此次索引
		return nil
	}

	record := manager.WorkspaceRecords[workspacePath]
	if record.Finished {
		return record
	}

	record.FinishedTaskCnt = runnerReportData.FinishedTaskCnt
	record.SkipTaskCnt = runnerReportData.SkipTaskCnt
	record.DiscardTaskCnt = runnerReportData.DiscardTaskCnt
	record.SkipTodoTaskCnt = runnerReportData.SkipTodoTaskCnt
	// 本次全量索引中，实际真实已执行完毕的文件数量
	actualDoneTaskCnt := runnerReportData.GetActualDoneTaskCnt()
	if record.TotalValidTaskCnt <= actualDoneTaskCnt {
		record.ShareChunkHitCnt = runnerReportData.ShareChunkHitCnt
		record.DoEmbeddingChunkCnt = runnerReportData.DoEmbeddingChunkCnt
		record.FinishedPercent = float64(record.FinishedTaskCnt) / float64(record.TotalValidTaskCnt)
		record.Finished = true
		// 任务全部完成，记录当前完成时间
		record.EndTime = time.Now()

		engine, err := rag.GetClientChatVectorRetrieveEngine(record.WorkspacePath)
		if err == nil && engine != nil {
			record.TotalStorageChunkCnt = engine.GetStorageChunkNum()
		}

		go func() {
			// 等待一段时间后，删除记录
			// 这个记录是跳过全量扫库的参考
			time.Sleep(definition.ScanRepoInterval)
			manager.Mtx.Lock()
			defer manager.Mtx.Unlock()
			manager.UnRegisterWorkspace(record.WorkspacePath)
			delete(manager.WorkspaceRecords, workspacePath)
		}()
	}
	return record
}

// WaitForWorkspaceFinish 等待workspace的任务全部完成
// 评测时使用
// 业务中暂不使用，业务上考虑使用异步的方式建索引
func (manager *IndexBuildingManager) WaitForWorkspaceFinish(workspacePath string) {
	// 全量索引触发埋点
	go func() {
		record := manager.ReportIndexBuildingProgress(workspacePath)
		if record == nil {
			// 索引记录已经不存在放弃记录
			return
		}
		data := make(map[string]string)
		maxStorageFileNum := global.GetMaxClientStorageFileNum()
		data["total_file_cnt"] = strconv.FormatInt(int64(record.TotalTaskCnt), 10)
		data["total_valid_file_cnt"] = strconv.FormatInt(int64(record.TotalValidTaskCnt), 10)
		data["max_storage_file_limit"] = strconv.FormatInt(int64(maxStorageFileNum), 10)
		data["chunk_size"] = strconv.FormatInt(int64(record.ChunkSize), 10)
		data["chunk_overlap"] = strconv.FormatInt(int64(record.ChunkOverlap), 10)
		data["full_workspace_path"] = record.WorkspacePath
		data["repo_name"] = filepath.Base(record.WorkspacePath)
		data["vector_version"] = definition.DatabaseVersion
		userType := user.GetUserType()
		data["user_type"] = userType
		sls.Report(sls.EventTypeChatCodebaseFullVectorIndexTrigger, uuid.NewString(), data)
	}()

	player := rag.NewIndexDisplayer()
	displayData := rag.DisplayData{
		WorkspacePath: workspacePath,
		IndexType:     rag.DisplayIndexTypeClientVector,
		OutputMode:    rag.DisplayOutputModeLog,
	}
	for {
		// 等待向量索引完成构建
		record := manager.ReportIndexBuildingProgress(workspacePath)
		if record == nil {
			// 索引记录已经不存在，break
			break
		}
		maxStorageFileNum := global.GetMaxClientStorageFileNum()
		if record.TotalValidTaskCnt > 0 {
			actualDoneTaskCnt := record.GetActualDoneTaskCnt()
			if actualDoneTaskCnt > maxStorageFileNum {
				actualDoneTaskCnt = maxStorageFileNum
			}

			totalValidTaskCnt := record.TotalValidTaskCnt
			if totalValidTaskCnt > maxStorageFileNum {
				totalValidTaskCnt = maxStorageFileNum
			}

			displayData.FinishedNum = actualDoneTaskCnt
			displayData.TotalNum = totalValidTaskCnt
			player.Display(displayData)
		}

		if record.Finished {
			// 全量索引完成埋点
			// 耗时、文件数、sqlite大小、仓库大小
			go func() {
				data := make(map[string]string)
				costTime := record.EndTime.Sub(record.StartTime).Seconds()
				data["cost_time"] = strconv.FormatInt(int64(costTime), 10)
				data["max_storage_file_limit"] = strconv.FormatInt(int64(maxStorageFileNum), 10)
				data["total_file_cnt"] = strconv.FormatInt(int64(record.TotalTaskCnt), 10)
				data["total_valid_file_cnt"] = strconv.FormatInt(int64(record.TotalValidTaskCnt), 10)
				data["finished_file_cnt"] = strconv.FormatInt(int64(record.FinishedTaskCnt), 10)
				data["skip_file_cnt"] = strconv.FormatInt(int64(record.SkipTaskCnt), 10)
				data["discard_file_cnt"] = strconv.FormatInt(int64(record.DiscardTaskCnt), 10)
				data["skip_todo_file_cnt"] = strconv.FormatInt(int64(record.SkipTodoTaskCnt), 10)
				data["finished_percent"] = strconv.FormatFloat(record.FinishedPercent, 'f', 3, 64)
				data["chunk_size"] = strconv.FormatInt(int64(record.ChunkSize), 10)
				data["chunk_overlap"] = strconv.FormatInt(int64(record.ChunkOverlap), 10)
				data["storage_chunk_cnt"] = strconv.FormatInt(int64(record.TotalStorageChunkCnt), 10)
				data["do_embedding_chunk_cnt"] = strconv.FormatInt(int64(record.DoEmbeddingChunkCnt), 10)
				data["share_chunk_hit_cnt"] = strconv.FormatInt(int64(record.ShareChunkHitCnt), 10)
				data["full_workspace_path"] = record.WorkspacePath
				data["repo_name"] = filepath.Base(record.WorkspacePath)
				data["vector_version"] = definition.DatabaseVersion
				repoSize, err := GetDirectorySize(record.WorkspacePath)
				if err != nil {
					return
				}
				data["repo_size_byte"] = strconv.FormatInt(repoSize, 10)

				dbPath := vector.GetSqliteVecDbPath(record.WorkspacePath, definition.DatabaseVersion, vector.DatabaseModeChat)
				dbDir := filepath.Dir(dbPath)
				sqliteSizeByte, err := GetDirectorySize(dbDir)
				if err != nil {
					return
				}
				userType := user.GetUserType()
				data["user_type"] = userType
				data["sqlite_size_byte"] = strconv.FormatInt(sqliteSizeByte, 10)
				sls.Report(sls.EventTypeChatCodebaseFullVectorIndexFinish, uuid.NewString(), data)
			}()
			break
		}
		time.Sleep(10 * time.Second)
	}
}

// GetDirectorySize 计算目录的总大小（单位：字节）
func GetDirectorySize(path string) (int64, error) {
	var totalSize int64

	var sleepIntervalCnt int

	// Walk 函数会递归遍历目录
	err := filepath.Walk(path, func(filePath string, info os.FileInfo, err error) error {
		if err != nil {
			return err // 返回错误以终止遍历
		}
		sleepIntervalCnt += 1
		if sleepIntervalCnt >= 200 {
			time.Sleep(1 * time.Second)
			sleepIntervalCnt = 0
		}

		// 如果是普通文件，则累加其大小
		if !info.IsDir() {
			totalSize += info.Size()
		}

		return nil
	})

	if err != nil {
		return 0, err
	}

	return totalSize, nil
}
