package chat_indexing

import (
	"cosy/definition"
	"cosy/indexing/common"
	"cosy/lang/indexer/rag"
	"cosy/log"
	"sync"
)

const (
	MemoryRetrieveFileVectorIndexerName = "memory_retrieve_vector_indexing"
)

var MemoryRetrieveFileVectorIndexers = common.NewDefaultWorkspaceFileIndexer[*MemoryRetrieveFileVectorIndexer](func(env *common.IndexEnvironment) *MemoryRetrieveFileVectorIndexer {
	if workspacePath, ok := env.WorkspaceInfo.GetWorkspaceFolder(); ok {
		engine, err := rag.GetClientChatMemoVectorRetrieveEngine(workspacePath)
		if err != nil {
			log.Errorf("Error to initialize memory vector indexer for %s", workspacePath)
			return nil
		}

		return &MemoryRetrieveFileVectorIndexer{
			workspacePath:        workspacePath,
			env:                  env,
			vectorRetrieveMutex:  &sync.Mutex{},
			vectorRetrieveEngine: engine,
		}
	}
	return nil
})

type MemoryRetrieveFileVectorIndexer struct {
	workspacePath        string
	env                  *common.IndexEnvironment
	vectorRetrieveMutex  *sync.Mutex
	vectorRetrieveEngine rag.VectorRetrieveEngine // 向量检索引擎，用于基于向量的相似性检索
}

func (v *MemoryRetrieveFileVectorIndexer) Init(env *common.IndexEnvironment) error {
	// 还不确定向量引擎需要在这方面做什么
	return nil
}

func (v *MemoryRetrieveFileVectorIndexer) PrepareIndexing(env *common.IndexEnvironment) {
}

func (v *MemoryRetrieveFileVectorIndexer) CompleteIndexing(env *common.IndexEnvironment) {
}

func (v *MemoryRetrieveFileVectorIndexer) IndexFiles(env *common.IndexEnvironment, virtualFiles []definition.VirtualFile) {
	engine, err := v.GetVectorRetrieveEngine()
	if engine == nil {
		log.Errorf("Error to get vector retrieve engine: %v", err)
		return
	}
	filePaths := definition.BuildBatchFilePath(virtualFiles)
	batchTask := definition.NewVectorBatchTaskWithFilePaths(v.workspacePath, filePaths, definition.VectorFileChangeIndexSource)
	log.Infof("[codebase]-[memory indexer] [index files] indexing %d files", len(filePaths))
	engine.ExecuteBatchTask(batchTask, true, false)
}

func (v *MemoryRetrieveFileVectorIndexer) DispatchEvents(env *common.IndexEnvironment, events definition.FileChangeEvents) {
	wg := sync.WaitGroup{}
	if len(events.SaveFiles) > 0 {
		wg.Add(1)
		go func() {
			wg.Done()
			v.OnFileSave(env, events.SaveFiles)
		}()
	}

	if len(events.ModifyFiles) > 0 {
		wg.Add(1)
		go func() {
			wg.Done()
			v.OnFileModify(env, events.ModifyFiles)
		}()
	}

	if len(events.DeleteFiles) > 0 {
		wg.Add(1)
		go func() {
			wg.Done()
			v.OnFileDelete(env, events.DeleteFiles, false)
		}()
	}

	if len(events.DeleteDirs) > 0 {
		wg.Add(1)
		go func() {
			wg.Done()
			v.OnFileDelete(env, events.DeleteDirs, true)
		}()
	}
	wg.Wait()
}

func (v *MemoryRetrieveFileVectorIndexer) OnFileSave(env *common.IndexEnvironment, virtualFiles []definition.VirtualFile) {
	v.IndexFiles(env, virtualFiles)
}

func (v *MemoryRetrieveFileVectorIndexer) OnFileModify(env *common.IndexEnvironment, virtualFiles []definition.VirtualFile) {
	v.IndexFiles(env, virtualFiles)
}

func (v *MemoryRetrieveFileVectorIndexer) OnFileDelete(env *common.IndexEnvironment, virtualFiles []definition.VirtualFile, isDir bool) {
	engine, err := v.GetVectorRetrieveEngine()
	if err != nil {
		log.Errorf("Error to get vector retrieve engine: %v", err)
		return
	}
	if engine != nil {
		err = engine.BatchDeleteIndex(virtualFiles, isDir)
		if err != nil {
			log.Errorf("Error to delete vector retrieve file: %d, err: %v", len(virtualFiles), err)
		}
	}
}

// GetVectorRetrieveEngine 用于获取向量检索引擎，懒加载
func (v *MemoryRetrieveFileVectorIndexer) GetVectorRetrieveEngine() (rag.VectorRetrieveEngine, error) {
	v.vectorRetrieveMutex.Lock()
	defer v.vectorRetrieveMutex.Unlock()
	if v.vectorRetrieveEngine != nil {
		return v.vectorRetrieveEngine, nil
	}
	var err error
	workspacePath, _ := v.env.WorkspaceInfo.GetWorkspaceFolder()
	v.vectorRetrieveEngine, err = rag.GetClientChatMemoVectorRetrieveEngine(workspacePath)
	if err != nil {
		log.Errorf("Error to initialize memory vector engine for %s", workspacePath)
		return nil, err // 初始化失败，返回错误
	}
	return v.vectorRetrieveEngine, nil
}

func (v *MemoryRetrieveFileVectorIndexer) Close() {
	v.vectorRetrieveMutex.Lock()
	defer v.vectorRetrieveMutex.Unlock()

	if v.env != nil {
		v.env.KvStore.Close()
	}

	if v.vectorRetrieveEngine != nil {
		v.vectorRetrieveEngine.Close()
	}
}

func (v *MemoryRetrieveFileVectorIndexer) GetIndexerEnv() *common.IndexEnvironment {
	return v.env
}
