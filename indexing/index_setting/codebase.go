package index_setting

import (
	"cosy/definition"
	"cosy/global"
	"cosy/log"
	"cosy/storage/bblotdb"
	"cosy/tree"
	"cosy/util"
	"os"
	"path/filepath"
	"sync"
)

const (
	IndexProgressNone    = "none"
	IndexProgressRunning = "running"
	IndexProgressFinish  = "finish"

	IndexModeVector = "vector"
	IndexModeGraph  = "graph"

	SettingTableName = "codebase_settings"

	SettingsVersion = "v1"
)

type CodebaseSettings struct {
	Mtx         sync.RWMutex
	StartEvents map[string]struct{}
	Settings    *bblotdb.BboltStore
}

var codebaseSettings *CodebaseSettings

func init() {
	codebaseSettings = &CodebaseSettings{
		Mtx:         sync.RWMutex{},
		StartEvents: make(map[string]struct{}),
		Settings:    nil,
	}
}

func InitCodebaseSettings() {
	codebaseSettings.Mtx.Lock()
	defer codebaseSettings.Mtx.Unlock()

	codebaseSettingsStore, err := bblotdb.NewBboltStore(filepath.Join(util.GetCosyHomePath(), "index", ".settings", SettingsVersion))
	if err != nil {
		log.Errorf("[codebase]-[setting] create index setting store failed: %v", err)
		return
	}
	codebaseSettings.Settings = codebaseSettingsStore
	codebaseSettings.Settings.AddTable(SettingTableName)
	log.Infof("[codebase]-[setting] init codebase settings success")
}

func GetCodebaseIndexSetting(workspacePath string) definition.CodebaseIndexSetting {
	codebaseSettings.Mtx.RLock()
	defer codebaseSettings.Mtx.RUnlock()

	setting := getCodebaseIndexSetting(workspacePath)
	newSetting := *setting
	if newSetting.IsCanceled {
		newSetting.Progress = IndexProgressNone
	}
	return newSetting
}

func getCodebaseIndexSetting(workspacePath string) *definition.CodebaseIndexSetting {
	workspaceTree := tree.NewWorkspaceMerkleTree(workspacePath)
	if codebaseSettings.Settings == nil {
		log.Errorf("[codebase]-[setting] codebase settings storage table is nil")
		InitCodebaseSettings()
		return &definition.CodebaseIndexSetting{
			AutoIndex:      false,
			Progress:       IndexProgressNone,
			FileNumber:     workspaceTree.GetLeafNodeCount(),
			IsCanceled:     false,
			IsDeleted:      false,
			GraphProgress:  IndexProgressNone,
			VectorProgress: IndexProgressNone,
		}
	}

	setting := &definition.CodebaseIndexSetting{}
	err := codebaseSettings.Settings.Get(workspacePath, SettingTableName, setting)
	if err == nil {
		setting.FileNumber = workspaceTree.GetLeafNodeCount()
		return setting
	}

	// 如果设置不存在，则创建默认设置
	setting = &definition.CodebaseIndexSetting{
		AutoIndex:      true,
		Progress:       IndexProgressRunning,
		FileNumber:     workspaceTree.GetLeafNodeCount(),
		IsCanceled:     false,
		IsDeleted:      false,
		GraphProgress:  IndexProgressRunning,
		VectorProgress: IndexProgressRunning,
	}
	if setting.FileNumber >= global.GetMaxAutoIndexFileNum() {
		setting.AutoIndex = false
		setting.Progress = IndexProgressRunning
		// 超过自动文件限制，向量默认是不做的
		setting.VectorProgress = IndexProgressNone
		// 图谱默认是执行的
		setting.GraphProgress = IndexProgressRunning
	}

	bigRepoAutoIndex := os.Getenv(definition.MockBigRepoAutoIndexEnvKey)
	if bigRepoAutoIndex == "true" {
		// 开启了大库默认index的环境变量
		setting.AutoIndex = true
		setting.Progress = IndexProgressRunning
		setting.GraphProgress = IndexProgressRunning
		setting.VectorProgress = IndexProgressRunning
	}

	err = codebaseSettings.Settings.Set(workspacePath, SettingTableName, setting)
	if err != nil {
		log.Errorf("[codebase]-[setting] create index setting failed: %v", err)
	}
	log.Debugf("[codebase]-[setting] create index setting, workspacePath: %s, setting: %v", workspacePath, setting)
	return setting
}

func SetAutoIndexSetting(workspacePath string, autoIndex bool) definition.CodebaseIndexSetting {
	codebaseSettings.Mtx.Lock()
	defer codebaseSettings.Mtx.Unlock()

	setting := getCodebaseIndexSetting(workspacePath)
	setting.AutoIndex = autoIndex
	err := codebaseSettings.Settings.Set(workspacePath, SettingTableName, setting)
	if err != nil {
		log.Errorf("[codebase]-[setting] set auto index setting failed: %v", err)
	}
	log.Debugf("[codebase]-[setting] set auto index setting, workspacePath: %s, autoIndex: %v", workspacePath, autoIndex)
	return *setting
}

func StartBuildIndex(workspacePath string) definition.CodebaseIndexSetting {
	codebaseSettings.Mtx.Lock()
	defer codebaseSettings.Mtx.Unlock()

	setting := getCodebaseIndexSetting(workspacePath)
	setting.Progress = IndexProgressRunning
	setting.IsCanceled = false
	setting.IsDeleted = false
	setting.GraphProgress = IndexProgressRunning
	setting.VectorProgress = IndexProgressRunning
	err := codebaseSettings.Settings.Set(workspacePath, SettingTableName, setting)
	if err != nil {
		log.Errorf("[codebase]-[setting] set index setting failed: %v", err)
	}
	codebaseSettings.StartEvents[workspacePath] = struct{}{}
	updateProgress(workspacePath, setting)
	log.Debugf("[codebase]-[setting] start build index, workspacePath: %s", workspacePath)
	return *setting
}

// CheckAndStartIndex
// 这个接口看起来很蠢，实际上是因为解决循环引用的问题
// websocket接口无法获取fileIndexer，因此只能通过事件机制绕开
func CheckAndStartIndex(workspacePath string) bool {
	codebaseSettings.Mtx.Lock()
	defer codebaseSettings.Mtx.Unlock()
	_, ok := codebaseSettings.StartEvents[workspacePath]
	if ok {
		delete(codebaseSettings.StartEvents, workspacePath)
		log.Debugf("[codebase]-[setting] receive start index event, continue to start index, workspacePath: %s", workspacePath)
		return true
	}
	return false
}

func FinishBuildIndex(workspacePath string, mode string) {
	codebaseSettings.Mtx.Lock()
	defer codebaseSettings.Mtx.Unlock()

	setting := getCodebaseIndexSetting(workspacePath)
	if mode == IndexModeGraph {
		setting.GraphProgress = IndexProgressFinish
	} else if mode == IndexModeVector {
		setting.VectorProgress = IndexProgressFinish
	}
	updateProgress(workspacePath, setting)
	err := codebaseSettings.Settings.Set(workspacePath, SettingTableName, setting)
	if err != nil {
		log.Errorf("[codebase]-[setting] set index setting failed: %v", err)
	}
	log.Debugf("[codebase]-[setting] finish build index, workspacePath: %s, mode: %s", workspacePath, mode)
}

func updateProgress(workspacePath string, setting *definition.CodebaseIndexSetting) {
	if setting.GraphProgress == IndexProgressNone || setting.VectorProgress == IndexProgressNone {
		setting.Progress = IndexProgressNone
	} else if setting.GraphProgress == IndexProgressRunning || setting.VectorProgress == IndexProgressRunning {
		setting.Progress = IndexProgressRunning
	} else if setting.GraphProgress == IndexProgressFinish && setting.VectorProgress == IndexProgressFinish {
		setting.Progress = IndexProgressFinish
	} else {
		setting.Progress = IndexProgressNone
	}
}

func CancelBuildIndex(workspacePath string) definition.CodebaseIndexSetting {
	codebaseSettings.Mtx.Lock()
	defer codebaseSettings.Mtx.Unlock()

	setting := getCodebaseIndexSetting(workspacePath)
	setting.IsCanceled = true
	setting.AutoIndex = false
	// setting.GraphProgress = IndexProgressNone
	setting.VectorProgress = IndexProgressNone
	updateProgress(workspacePath, setting)
	err := codebaseSettings.Settings.Set(workspacePath, SettingTableName, setting)
	if err != nil {
		log.Errorf("[codebase]-[setting] cancel build index failed: %v", err)
	}
	log.Debugf("[codebase]-[setting] cancel build index, workspacePath: %s", workspacePath)
	return *setting
}

func GetAutoIndexSetting(workspacePath string) bool {
	codebaseSettings.Mtx.RLock()
	defer codebaseSettings.Mtx.RUnlock()
	setting := getCodebaseIndexSetting(workspacePath)
	return setting.AutoIndex
}

func IndexIsCanceled(workspacePath string) bool {
	codebaseSettings.Mtx.RLock()
	defer codebaseSettings.Mtx.RUnlock()
	setting := getCodebaseIndexSetting(workspacePath)
	return setting.IsCanceled
}

func IndexIsDeleted(workspacePath string) bool {
	codebaseSettings.Mtx.RLock()
	defer codebaseSettings.Mtx.RUnlock()
	setting := getCodebaseIndexSetting(workspacePath)
	return setting.IsDeleted
}
