package lang

import (
	"context"
	"cosy/definition"
	"cosy/lang/provider"
	"cosy/log"
	"cosy/model/base"
	"cosy/model/inline_edit"
	"cosy/sls"
	"cosy/stable"
	"cosy/util"
	"cosy/websocket"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"sync"
	"time"
)

var inlineEditTriggerHistory = NewInlineEditTriggerHistory()

// InlineEdit 处理内联编辑请求
func (dp *DefaultProcessor) InlineEdit(ctx context.Context, params *definition.InlineEditParams) definition.InlineEditResult {
	startTime := time.Now()
	log.Infof("Processing inline edit request, requestId: %s, sessionId: %s", params.RequestId, params.SessionId)
	if !verifyTrigger(ctx, params) {
		return definition.InlineEditResult{
			Success: false,
			Message: "Invalid trigger",
		}
	}
	params.Preprocess()

	// 创建结果对象
	result := definition.InlineEditResult{
		Success: false,
	}

	// 获取单例工厂实例
	factory := provider.GetInlineEditContextProviderFactory()

	// 获取所有上下文信息
	timeRecorder := util.NewTimeRecorder()
	performance := base.PerformanceCollect{
		ClientEntryTime: time.Now().UnixMilli(),
	}
	performance.Init()
	ctx = context.WithValue(ctx, definition.ContextKeyRemoteRag, dp.remoteRag)
	ctx = context.WithValue(ctx, definition.ContextKeyTimeRecorder, timeRecorder)
	ctx = context.WithValue(ctx, definition.ContextKeyPerformance, performance)
	contextData := factory.GetContext(ctx, params)
	if contextData == nil || len(contextData) == 0 {
		return definition.InlineEditResult{
			Success: false,
			Message: "Empty context",
		}
	}
	jsonContextData, err := json.Marshal(contextData)
	if err == nil {
		log.Debugf("inline edit context data: %s", string(jsonContextData))
	}
	if err := verifyContext(ctx, contextData); err != nil {
		return definition.InlineEditResult{
			Success: false,
			Message: "Invalid context",
		}
	}
	if params.Version == "2" {
		go func() {
			err, actionCount, actionTimes := dp.callPredictModelService(ctx, params, contextData)
			log.Debugf("inline edit next predict finish, requestId: %s", params.RequestId)
			if err != nil {
				log.Errorf("Failed to call model service: %v", err)
			} else {
				timeRecorder.Record("finish_total_cost", time.Since(startTime))
				timeRecorder.PrintWithTitle(fmt.Sprintf("Inline Edit Performance:%s", params.RequestId))
				eventData := timeRecorder.Export()
				eventData["type"] = "next_predict_all"
				eventData["action_count"] = strconv.Itoa(actionCount)
				if actionTimes != nil {
					eventData["action_times"] = util.ToJsonStr(actionTimes)
				}
				sls.Report(sls.EventTypeInlineEditPerformance, params.RequestId, eventData)
			}
		}()
	} else {
		stable.GoSafe(ctx, func() {
			err = dp.callModelService(ctx, params, contextData)
			if err != nil {
				log.Errorf("Failed to call model service: %v", err)
			} else {
				timeRecorder.Record("finish_total_cost", time.Since(startTime))
				timeRecorder.PrintWithTitle(fmt.Sprintf("Inline Edit Performance:%s", params.RequestId))
				eventData := timeRecorder.Export()
				eventData["type"] = "inline_edit_all"
				sls.Report(sls.EventTypeInlineEditPerformance, params.RequestId, eventData)
			}
		}, stable.SceneInlineEdit, params)
	}

	// 填充结果
	result.Success = true

	// 记录处理时间
	processingTime := time.Since(startTime)
	log.Infof("Inline edit request completed, time cost: %v", processingTime)

	return result
}

// verifyContext 验证上下文数据
func verifyContext(ctx context.Context, contextData map[string]interface{}) error {
	rewriteNewCode, ok := contextData[definition.InlineEditContextKeyCodeToRewrite].(definition.CodeToRewriteData)
	if !ok {
		return errors.New("failed to get code to rewrite from context data")
	}
	if rewriteNewCode.Content == "" {
		return errors.New("failed to get code to rewrite content from context data")
	}
	return nil
}

// callModelService 调用模型服务进行内联编辑
func (dp *DefaultProcessor) callModelService(ctx context.Context, params *definition.InlineEditParams, contextData map[string]interface{}) error {
	// 检查获取重写代码
	codeToRewrite, ok := contextData[definition.InlineEditContextKeyCodeToRewrite].(definition.CodeToRewriteData)
	if !ok {
		return errors.New("failed to get code to rewrite from context data")
	}
	if codeToRewrite.Content == "" {
		return errors.New("failed to get code to rewrite content from context data")
	}

	// 尝试从缓存获取结果并处理
	filePath := string(params.TextDocument.URI)
	if err := dp.handleInlineEditCache(ctx, params, contextData, codeToRewrite, filePath); err == nil {
		// 缓存处理成功，直接返回
		return nil
	}

	// 缓存未命中，需要调用模型服务
	log.Debugf("Cache miss for inline edit request, requestId: %s, sessionId: %s", params.RequestId, params.SessionId)

	// 调用模型服务获取结果
	var rewriteCodeResult definition.RewriteCodeActionMessage
	var nextEditResult definition.NextEditLocationActionMessage
	var wg sync.WaitGroup
	wg.Add(2)
	stable.GoSafe(ctx, func() {
		defer wg.Done()
		rewriteCodeResult, _ = inline_edit.CallRewriteCode(ctx, params, contextData)
	}, stable.SceneInlineEdit, params)
	stable.GoSafe(ctx, func() {
		defer wg.Done()
		nextEditResult = inline_edit.CallNextEditAction(ctx, params, contextData)
	}, stable.SceneInlineEdit, params)
	wg.Wait()

	if !nextEditResult.Success && !rewriteCodeResult.Success {
		return nil
	}

	hasRewriteCode := false

	// 判断是否需要显示重写代码
	if rewriteCodeResult.Data.Content == "" {
		nextEditResult.Data.HasRewriteCode = false
	} else if rewriteCodeResult.Data.Content == codeToRewrite.Content {
		log.Debugf("Rewrite result is same as original code, ignore it, requestId: %s, sessionId: %s", params.RequestId, params.SessionId)

		nextEditResult.Data.HasRewriteCode = false
	} else {
		hasRewriteCode = true
		nextEditResult.Data.HasRewriteCode = true
	}

	if nextEditResult.Success {
		if !hasRewriteCode {
			nextEditResult = calFirstValidNextEditLocation(*params, nextEditResult)
		} else {
			//查找相同intent的location预测
			nextEditResult = calValidNextEditLocation(*params, codeToRewrite, rewriteCodeResult, nextEditResult)
		}
	}

	//验证后处理rewrite结果
	if hasRewriteCode {
		isValid := ValidateRewriteResult(ctx, *params, codeToRewrite.Content, rewriteCodeResult, contextData, false)
		if !isValid {
			log.Debugf("Rewrite result is invalid, requestId: %s, sessionId: %s", params.RequestId, params.SessionId)
			hasRewriteCode = false
			if nextEditResult.Success && nextEditResult.HasValidAction() {
				nextEditResult.Data.HasRewriteCode = false
			}
		}
	}

	isNextEditLocationValid := false
	if nextEditResult.HasValidAction() {
		isNextEditLocationValid = ValidateNextEditResult(ctx, *params, codeToRewrite.Content, nextEditResult, contextData)
	}

	//记录历史记录
	inlineEditTriggerItem := InlineEditTriggerHistoryItem{
		FilePath:          filePath,
		NextEditResult:    nextEditResult,
		RequestId:         params.RequestId,
		Params:            *params,
		RewriteCodeResult: rewriteCodeResult,
		TriggerTime:       time.Now(),
	}
	inlineEditTriggerHistory.Push(inlineEditTriggerItem)

	if rewriteCodeResult.Success && nextEditResult.Success && hasRewriteCode {
		// 将结果存入缓存
		cacheItem := &inline_edit.InlineEditCacheItem{
			FilePath:       filePath,
			SessionId:      params.SessionId,
			RequestId:      params.RequestId,
			OriginalCode:   codeToRewrite,
			RewriteResult:  rewriteCodeResult,
			NextEditResult: nextEditResult,
			EditRange:      rewriteCodeResult.Data.EditRange,
			CursorLine:     params.Position.Line,
		}
		inline_edit.GetInlineEditCache().Set(cacheItem)
	}

	if hasRewriteCode {
		// 发送结果到客户端
		if err := websocket.WsInst.NotifyClient(websocket.CopyContext(ctx), "textDocument/nextEditAction", rewriteCodeResult); err != nil {
			log.Errorf("Failed to send next edit action to socket client: %v", err)
		}
	}

	if nextEditResult.Success && nextEditResult.HasValidAction() && isNextEditLocationValid {
		if err := websocket.WsInst.NotifyClient(websocket.CopyContext(ctx), "textDocument/nextEditAction", nextEditResult); err != nil {
			log.Errorf("Failed to send next edit action to socket client: %v", err)
		}
	}
	return nil
}

// callPredictModelService nes v2 版本，调用next_action_predict接口返回action list
func (dp *DefaultProcessor) callPredictModelService(ctx context.Context, params *definition.InlineEditParams,
	contextData map[string]interface{}) (error, int, []int64) {
	// 检查获取重写代码
	codeToRewrite, ok := contextData[definition.InlineEditContextKeyCodeToRewrite].(definition.CodeToRewriteData)
	if !ok {
		return errors.New("failed to get code to rewrite from context data"), 0, nil
	}
	if codeToRewrite.Content == "" {
		return errors.New("failed to get code to rewrite content from context data"), 0, nil
	}

	filePath := string(params.TextDocument.URI)
	// TODO 先不加缓存
	// 调用模型服务获取结果
	var nextEditPredictResult definition.RewriteCodeActionMessage
	actionCount := 0
	actionTimes := []int64{}
	nextEditPredictResult, _, actionCount, actionTimes = inline_edit.CallNextPredict(ctx, params, contextData,
		func(ctx context.Context,
			params definition.InlineEditParams, codeToRewrite string, rewriteCodeResult definition.RewriteCodeActionMessage, contextData map[string]interface{}) bool {
			return ValidateRewriteResult(ctx, params, codeToRewrite, rewriteCodeResult, contextData, true)
		})

	if !nextEditPredictResult.Success {
		return nil, actionCount, actionTimes
	}

	//记录历史记录
	inlineEditTriggerItem := InlineEditTriggerHistoryItem{
		FilePath:              filePath,
		RequestId:             params.RequestId,
		Params:                *params,
		NextEditPredictResult: nextEditPredictResult,
		TriggerTime:           time.Now(),
	}
	inlineEditTriggerHistory.Push(inlineEditTriggerItem)

	// 将结果存入缓存
	//if err := websocket.WsInst.NotifyClient(websocket.CopyContext(ctx), "textDocument/nextEditAction", nextEditPredictResult); err != nil {
	//	log.Errorf("Failed to send next edit action to socket client: %v", err)
	//}
	return nil, actionCount, actionTimes
}

// handleInlineEditCache 处理内联编辑的缓存逻辑
// 如果缓存命中则处理并返回nil，否则返回错误表示需要调用模型服务
func (dp *DefaultProcessor) handleInlineEditCache(
	ctx context.Context,
	params *definition.InlineEditParams,
	contextData map[string]interface{},
	rewriteNewCode definition.CodeToRewriteData,
	filePath string) error {

	// 获取缓存实例
	cache := inline_edit.GetInlineEditCache()

	// 尝试从缓存获取结果
	cacheItem, cacheHit := cache.Get(filePath, params.SessionId, params.Position.Line, rewriteNewCode.Content, params.FileContent)

	if !cacheHit {
		// 缓存未命中
		return errors.New("cache missing")
	}

	log.Debugf("Cache hit for inline edit request, requestId: %s, sessionId: %s", params.RequestId, params.SessionId)

	// 命中缓存，直接使用缓存的结果
	rewriteCodeResult := cacheItem.RewriteResult
	rewriteCodeResult.RequestId = params.RequestId // 更新请求ID
	rewriteCodeResult.SessionId = params.SessionId
	rewriteCodeResult.CacheId = cacheItem.RequestId

	nextEditResult := cacheItem.NextEditResult
	nextEditResult.RequestId = params.RequestId // 更新请求ID
	nextEditResult.SessionId = params.SessionId
	nextEditResult.CacheId = cacheItem.RequestId

	hasRewriteCode := false

	// 判断是否需要显示重写代码
	if rewriteCodeResult.Data.Content == rewriteNewCode.Content {
		nextEditResult.Data.HasRewriteCode = false
	} else {
		hasRewriteCode = true
		nextEditResult.Data.HasRewriteCode = true
	}

	if hasRewriteCode {
		isValid := ValidateRewriteResult(ctx, *params, rewriteNewCode.Content, rewriteCodeResult, contextData, false)
		if !isValid {
			log.Debugf("Cached Rewrite result is invalid, requestId: %s, sessionId: %s", params.RequestId, params.SessionId)
			hasRewriteCode = false
		}
	}

	isNextEditLocationValid := ValidateNextEditResult(ctx, *params, rewriteNewCode.Content, nextEditResult, contextData)

	// 发送结果到客户端
	if hasRewriteCode {
		if err := websocket.WsInst.NotifyClient(websocket.CopyContext(ctx), "textDocument/nextEditAction", rewriteCodeResult); err != nil {
			log.Errorf("Failed to send rewrite code action to socket client: %v", err)
			return err
		}
	}

	if isNextEditLocationValid {
		if err := websocket.WsInst.NotifyClient(websocket.CopyContext(ctx), "textDocument/nextEditAction", nextEditResult); err != nil {
			log.Errorf("Failed to send next edit action to socket client: %v", err)
			return err
		}
	}

	return nil
}

func calFirstValidNextEditLocation(params definition.InlineEditParams, nextEditResult definition.NextEditLocationActionMessage) definition.NextEditLocationActionMessage {
	nextEditAction := &nextEditResult.Data
	if len(nextEditResult.PossibleNextCodeAction) > 0 {
		editTriggerHistory := inlineEditTriggerHistory.GetHistory(string(params.TextDocument.URI), time.Now().Add(time.Minute*5), 1)

		for _, action := range nextEditResult.PossibleNextCodeAction {
			//TODO 严格校验跳转地址的有效性
			if IsNextEditActionInRewriteAreaHistorys(action.SourceLineNumber, params, editTriggerHistory) {
				continue
			} else {
				nextEditAction.NextLineNumber = action.SourceLineNumber
				break
			}
		}
	}
	return nextEditResult
}

func calValidNextEditLocation(params definition.InlineEditParams, codeToRewrite definition.CodeToRewriteData, rewriteCodeResult definition.RewriteCodeActionMessage, nextEditResult definition.NextEditLocationActionMessage) definition.NextEditLocationActionMessage {
	nextEditAction := &nextEditResult.Data
	if len(nextEditResult.PossibleNextCodeAction) > 0 {
		editTriggerHistory := inlineEditTriggerHistory.GetHistory(string(params.TextDocument.URI), time.Now().Add(time.Minute*5), 1)

		for _, action := range nextEditResult.PossibleNextCodeAction {
			if rewriteCodeResult.Data.UserIntent != "" && action.PredictedEditIntent == rewriteCodeResult.Data.UserIntent {
				//查找相同intent
				if IsNextEditActionInRewriteArea(action.SourceLineNumber, params, codeToRewrite) {
					continue
				}
				if IsNextEditActionInRewriteAreaHistorys(action.SourceLineNumber, params, editTriggerHistory) {
					continue
				}
				nextEditAction.NextLineNumber = action.SourceLineNumber
				break
			}
		}
	}
	return nextEditResult
}
