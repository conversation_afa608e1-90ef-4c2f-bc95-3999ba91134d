package parser

import (
	"container/list"
	"cosy/lang/indexer"
	"cosy/lang/indexer/unified"
	"cosy/log"
	"cosy/util"
	"errors"
	"fmt"
	sitter "github.com/smacker/go-tree-sitter"
	"path/filepath"
	"strings"
)

type ParseMetaContext struct {
	PackageName     string
	ImportDefs      map[string]indexer.UnifiedImport
	ClassPaths      []string
	ImportPaths     []string
	RootNode        *sitter.Node
	innerClassDefs  map[string]string
	ParseRefContext *unified.ParseContext // 用于在解析引用时
}

var (
	KotlinTypeMapping = map[string]string{
		"Map":           unified.MapBasicType,
		"HashMap":       unified.MapBasicType,
		"ArrayList":     unified.ArrayBasicType,
		"List":          unified.ArrayBasicType,
		"Set":           unified.ArrayBasicType,
		"Array":         unified.SliceBasicType,
		"arrayListOf":   unified.ArrayBasicType,
		"listOf":        unified.ArrayBasicType,
		"mutableListOf": unified.ArrayBasicType,
		"setOf":         unified.ArrayBasicType,
		"mutableSetOf":  unified.ArrayBasicType,
		"mapOf":         unified.MapBasicType,
		"mutableMapOf":  unified.MapBasicType,
		"arrayOf":       unified.SliceBasicType,
	}
)

func (p *ParseMetaContext) GetImportDef(typeName string) (string, bool) {
	if len(p.ImportDefs) > 0 {
		if importInfo, ok := p.ImportDefs[typeName]; ok {
			return importInfo.ModulePath, true
		}
	}
	if len(p.innerClassDefs) > 0 {
		if classFullName, ok := p.innerClassDefs[typeName]; ok {
			return classFullName, true
		}
	}
	return "", false
}

// PendingClassNode 待解析的类节点
type PendingClassNode struct {
	ClassNode *sitter.Node
	Ctx       ParseMetaContext
}

func (p *KotlinLangParser) GetMeta() (map[string]indexer.UnifiedMeta, *PostHandlerData, error) {
	classMetas := make(map[string]indexer.UnifiedMeta)
	postData := &PostHandlerData{
		Methods: make(map[string][]indexer.UnifiedMethod),
	}
	queue := list.List{}
	queue.PushBack(ParseMetaContext{
		PackageName: "",
		ImportDefs:  make(map[string]indexer.UnifiedImport, 16),
		ClassPaths:  make([]string, 0, 1),
		ImportPaths: make([]string, 0, 16),
		RootNode:    p.Tree.RootNode(),
	})
	innerClassDefs := make(map[string]string)
	pendingClassNodes := make([]PendingClassNode, 0, 1)
	for queue.Len() > 0 {
		ctx := queue.Remove(queue.Front()).(ParseMetaContext)
		if ctx.RootNode == nil {
			continue
		}
		var declNodes []*sitter.Node
		if ctx.RootNode.Type() == "source_file" {
			declNodes = util.FindFirstChildNodesByTypes(ctx.RootNode, "package_header", "import_list", "statement",
				"class_declaration", "function_declaration", "property_declaration", "type_alias", "object_declaration")
		} else {
			// 处理类嵌套子类的情况
			declNodes = util.FindFirstChildNodesByTypes(ctx.RootNode, "class_declaration", "object_declaration")
		}
		for _, declNode := range declNodes {
			nodeType := declNode.Type()
			if nodeType == "class_declaration" || nodeType == "object_declaration" || nodeType == "type_alias" ||
				nodeType == "function_declaration" || nodeType == "property_declaration" {
				className, classFullName, err := p.parseNodeName(ctx, declNode)
				if err != nil {
					continue
				}
				// 这里只记录当前文件的所有类信息
				innerClassDefs[className] = classFullName
				pendingClassNodes = append(pendingClassNodes, PendingClassNode{
					ClassNode: declNode,
					Ctx:       ctx,
				})
				if nodeType == "class_declaration" || nodeType == "object_declaration" {
					bodyNode := util.FindFirstChildNodeByTypes(declNode, "class_body")
					if bodyNode == nil {
						continue
					}
					// 处理类嵌套子类的情况
					queue.PushBack(ParseMetaContext{
						PackageName: ctx.PackageName,
						ImportDefs:  ctx.ImportDefs,
						ClassPaths:  util.AppendArray(ctx.ClassPaths, className),
						ImportPaths: ctx.ImportPaths,
						RootNode:    bodyNode,
					})
				}
			} else if nodeType == "statement" {
				newClassPaths := make([]string, len(ctx.ClassPaths))
				copy(newClassPaths, ctx.ClassPaths)
				queue.PushBack(ParseMetaContext{
					PackageName: ctx.PackageName,
					ImportDefs:  ctx.ImportDefs,
					ClassPaths:  newClassPaths,
					ImportPaths: ctx.ImportPaths,
					RootNode:    declNode,
				})
			} else if declNode.Type() == "package_header" {
				ctx.PackageName = strings.TrimPrefix(declNode.Content(p.Code), "package ")
			} else if declNode.Type() == "import_list" {
				importHeaderNodes := util.FindFirstChildNodesByTypes(declNode, "import_header")
				for _, importHeaderNode := range importHeaderNodes {
					info, paths, err := p.ParseImport(importHeaderNode)
					if err != nil {
						continue
					}
					ctx.ImportDefs[info.Name] = info
					if info.Alias != "" {
						ctx.ImportDefs[info.Alias] = info
					}
					ctx.ImportPaths = append(ctx.ImportPaths, paths...)
				}
			}
		}
	}
	for _, pendingClassNode := range pendingClassNodes {
		pendingClassNode.Ctx.innerClassDefs = innerClassDefs
		nodeType := pendingClassNode.ClassNode.Type()
		var err error
		if nodeType == "type_alias" {
			// TODO
		} else if nodeType == "function_declaration" {
			err = p.parseFunction(classMetas, pendingClassNode.Ctx, postData, pendingClassNode.ClassNode)
		} else if nodeType == "property_declaration" {
			err = p.parseGlobalVariable(classMetas, pendingClassNode.Ctx, pendingClassNode.ClassNode)
		} else if nodeType == "class_declaration" || nodeType == "object_declaration" {
			err = p.parseClass(classMetas, pendingClassNode.Ctx, pendingClassNode.ClassNode)
		}
		if err != nil {
			log.Debugf("parse meta failed: %s", err)
		}
	}
	return classMetas, postData, nil
}

func (p *KotlinLangParser) parseNodeName(ctx ParseMetaContext, node *sitter.Node) (string, string, error) {
	var nameNode *sitter.Node
	if node.Type() == "property_declaration" {
		varNode := util.FindFirstChildNodeByType(node, "variable_declaration")
		if varNode != nil {
			nameNode = util.FindFirstChildNodeByType(varNode, "simple_identifier")
		}
	} else if node.Type() == "typealias" {
		nameNode = node.ChildByFieldName("type")
	} else if node.Type() == "class_declaration" || node.Type() == "object_declaration" {
		nameNode = util.FindFirstChildNodeByType(node, "type_identifier")
	} else if node.Type() == "function_declaration" {
		nameNode = util.FindFirstChildNodeByType(node, "simple_identifier")
	}
	if nameNode == nil {
		return "", "", fmt.Errorf("parse class name failed")
	}
	className := nameNode.Content(p.Code)
	classPaths := append(ctx.ClassPaths, className)
	classFullName := strings.Join(classPaths, ".")
	if ctx.PackageName != "" {
		classFullName = fmt.Sprintf("%s.%s", ctx.PackageName, classFullName)
	}
	return className, classFullName, nil
}

// parseClass 解析类、接口、枚举
func (p *KotlinLangParser) parseClass(classMetas map[string]indexer.UnifiedMeta, ctx ParseMetaContext, node *sitter.Node) error {
	nodeType := node.Type()
	className, classFullName, err := p.parseNodeName(ctx, node)
	if err != nil {
		return err
	}
	classSignature := util.GetNodeHeaderCode(p.Code, node, false)
	metaType := indexer.ClassMetaType
	if nodeType == "object_declaration" {
		metaType = indexer.ObjectMetaType
		if !strings.Contains(classSignature, "object ") {
			classSignature = "object " + classSignature
		}
	} else if strings.Contains(classSignature, "enum class ") {
		metaType = indexer.EnumMetaType
	} else if strings.Contains(classSignature, "interface ") {
		metaType = indexer.InterfaceMetaType
	} else if strings.Contains(classSignature, "data class") {
		metaType = indexer.RecordMetaType
	} else if strings.Contains(classSignature, "annotation class") {
		metaType = indexer.AnnotationMetaType
	}
	meta := indexer.UnifiedMeta{
		Name:            className,
		FullName:        classFullName,
		IsData:          metaType == indexer.RecordMetaType,
		IsInterface:     metaType == indexer.InterfaceMetaType,
		MetaType:        metaType,
		PackageName:     ctx.PackageName,
		Imports:         ctx.ImportDefs,
		Fields:          map[string]indexer.UnifiedField{},
		Methods:         map[string][]indexer.UnifiedMethod{},
		HeaderSignature: classSignature,
		BaseMetas:       map[string]indexer.UnifiedType{},
		Aliases:         []string{},
		MetaFile:        indexer.MetaFile{FileName: filepath.Base(p.FilePath), FileFullPath: p.FilePath},
	}
	meta.UpdateRange(node)
	bodyNode := util.FindFirstChildNodeByTypes(node, "class_body", "enum_class_body")
	classFirstChildNodes := util.FindFirstChildNodesByTypes(node, "delegation_specifier", "primary_constructor")
	if len(classFirstChildNodes) > 0 {
		for _, childNode := range classFirstChildNodes {
			if childNode.Type() == "delegation_specifier" {
				// 处理基类
				specChildNode := util.FindFirstChildNodeByTypes(childNode, "constructor_invocation", "user_type")
				if specChildNode != nil && specChildNode.Type() == "constructor_invocation" {
					specChildNode = util.FindFirstChildNodeByType(specChildNode, "user_type")
				}
				if specChildNode == nil {
					continue
				}
				baseClassName := specChildNode.Content(p.Code)
				typeFullName := ""
				if baseFullName, ok := ctx.GetImportDef(baseClassName); ok {
					typeFullName = baseFullName
				}
				meta.BaseMetas[baseClassName] = indexer.UnifiedType{TypeName: baseClassName, TypeFullName: typeFullName}
			} else if childNode.Type() == "primary_constructor" {
				p.appendPrimaryConstructorMethodAndFields(ctx, &meta, className, childNode, true)
			}
		}
	}
	if bodyNode != nil {
		p.parseClassMembers(ctx, bodyNode, &meta, false)
	}
	classMetas[className] = meta
	return nil
}

// parseFunction 解析函数
func (p *KotlinLangParser) parseFunction(classMetas map[string]indexer.UnifiedMeta, ctx ParseMetaContext, postData *PostHandlerData, node *sitter.Node) error {
	meta := indexer.UnifiedMeta{
		Name:            "",
		FullName:        "",
		IsData:          false,
		IsInterface:     false,
		MetaType:        indexer.FunctionMetaType,
		PackageName:     ctx.PackageName,
		Imports:         ctx.ImportDefs,
		Fields:          map[string]indexer.UnifiedField{},
		Methods:         map[string][]indexer.UnifiedMethod{},
		HeaderSignature: "",
		BaseMetas:       map[string]indexer.UnifiedType{},
		Aliases:         []string{},
		MetaFile:        indexer.MetaFile{FileName: filepath.Base(p.FilePath), FileFullPath: p.FilePath},
	}
	meta.UpdateRange(node)
	method, receiveTarget, err := p.parseClassMethod(ctx, node, &meta, false)
	if err != nil {
		return err
	}

	if receiveTarget != "" {
		if recvMeta, ok := classMetas[receiveTarget]; ok {
			recvMeta.Methods[method.MethodName] = append(recvMeta.Methods[method.MethodName], *method)
		} else {
			if fullName, ok := ctx.GetImportDef(receiveTarget); ok {
				postData.Methods[fullName] = append(postData.Methods[fullName], *method)
			} else {
				postData.Methods[receiveTarget] = append(postData.Methods[receiveTarget], *method)
			}
		}
	} else {
		meta.Name = method.MethodName
		classPaths := append(ctx.ClassPaths, meta.Name)
		classFullName := strings.Join(classPaths, ".")
		if ctx.PackageName != "" {
			classFullName = fmt.Sprintf("%s.%s", ctx.PackageName, classFullName)
		}
		meta.FullName = classFullName
		meta.HeaderSignature = method.MethodSignature
		meta.Methods[method.MethodName] = append(meta.Methods[method.MethodName], *method)
		classMetas[method.MethodName] = meta
	}
	return nil
}

// parseGlobalVariable 解析顶层变量
func (p *KotlinLangParser) parseGlobalVariable(classMetas map[string]indexer.UnifiedMeta, ctx ParseMetaContext, node *sitter.Node) error {
	meta := indexer.UnifiedMeta{
		Name:            "",
		FullName:        "",
		IsData:          false,
		IsInterface:     false,
		MetaType:        indexer.VariableMetaType,
		PackageName:     ctx.PackageName,
		Imports:         ctx.ImportDefs,
		Fields:          map[string]indexer.UnifiedField{},
		Methods:         map[string][]indexer.UnifiedMethod{},
		HeaderSignature: "",
		BaseMetas:       map[string]indexer.UnifiedType{},
		Aliases:         []string{},
		MetaFile:        indexer.MetaFile{FileName: filepath.Base(p.FilePath), FileFullPath: p.FilePath},
	}
	meta.UpdateRange(node)
	field, err := p.parseClassField(ctx, node, &meta, false)
	if err != nil {
		return err
	}
	meta.Name = field.Name
	classPaths := append(ctx.ClassPaths, meta.Name)
	classFullName := strings.Join(classPaths, ".")
	if ctx.PackageName != "" {
		classFullName = fmt.Sprintf("%s.%s", ctx.PackageName, classFullName)
	}
	meta.FullName = classFullName
	meta.HeaderSignature = field.FieldSignature
	classMetas[field.Name] = meta
	return nil
}

// appendPrimaryConstructorMethod 针对默认构造函数，需要mock一个方法, primary_constructor节点
//
//	primary_constructor [1, 12] - [1, 44]
//	      class_parameters [1, 12] - [1, 44]
//	        class_parameter [1, 13] - [1, 29]
//	          identifier [1, 17] - [1, 21]
//	          user_type [1, 23] - [1, 29]
//	            identifier [1, 23] - [1, 29]
//	        class_parameter [1, 31] - [1, 43]
//	          identifier [1, 35] - [1, 38]
//	          user_type [1, 40] - [1, 43]
//	            identifier [1, 40] - [1, 43]
func (p *KotlinLangParser) appendPrimaryConstructorMethodAndFields(ctx ParseMetaContext, meta *indexer.UnifiedMeta,
	className string, primaryNode *sitter.Node, appendFields bool) {
	targetMethod := indexer.UnifiedMethod{
		MethodName:      className,
		MethodSignature: "",
		Args:            map[string][]indexer.UnifiedType{},
		ReturnType:      []indexer.UnifiedType{},
		Scope:           indexer.PublicScope,
		IsStatic:        false,
		IsConstructor:   true,
		MetaFile:        indexer.MetaFile{FileName: filepath.Base(p.FilePath), FileFullPath: p.FilePath},
	}
	targetMethod.UpdateRange(primaryNode)
	classParamChildNodes := util.FindFirstChildNodesByTypes(primaryNode, "class_parameter")
	for _, classParamChildNode := range classParamChildNodes {
		content := classParamChildNode.Content(p.Code)
		paramName, _, paramTypes, err := p.parseParameter(ctx, classParamChildNode)
		if err != nil {
			continue
		}
		targetMethod.Args[paramName] = paramTypes
		if appendFields {
			defaultScope := indexer.PublicScope
			if meta.MetaType == indexer.EnumMetaType {
				defaultScope = indexer.PrivateScope
			}
			field := indexer.UnifiedField{
				FieldSignature: "",
				Scope:          indexer.GetDefinitionScopeWithDefault(content, defaultScope),
				Types:          paramTypes,
				IsStatic:       false,
				FieldType:      indexer.PropertyType,
			}
			field.UpdateRange(classParamChildNode)
			meta.Fields[paramName] = field
		}
	}
	meta.Methods[className] = append(meta.Methods[className], targetMethod)
}

// parseClassMembers 解析类成员变量及方法
func (p *KotlinLangParser) parseClassMembers(ctx ParseMetaContext, classBodyNode *sitter.Node, classMeta *indexer.UnifiedMeta, isCompanion bool) {
	memberNodes := util.FindFirstChildNodesByTypes(classBodyNode, "property_declaration", "variable_declaration",
		"function_declaration", "secondary_constructor", "enum_entry", "anonymous_initializer", "companion_object")
	for _, memberNode := range memberNodes {
		nodeType := memberNode.Type()
		if nodeType == "property_declaration" || nodeType == "variable_declaration" {
			p.parseClassField(ctx, memberNode, classMeta, isCompanion)
		} else if nodeType == "function_declaration" || nodeType == "secondary_constructor" || nodeType == "anonymous_initializer" {
			method, _, err := p.parseClassMethod(ctx, memberNode, classMeta, isCompanion)
			if method != nil && err == nil {
				classMeta.Methods[method.MethodName] = append(classMeta.Methods[method.MethodName], *method)
			}
		} else if nodeType == "enum_entry" {
			p.parseEnumField(ctx, memberNode, classMeta)
		} else if nodeType == "companion_object" {
			companionObjectNode := util.FindFirstChildNodeByTypes(memberNode, "class_body")
			if companionObjectNode != nil {
				p.parseClassMembers(ctx, companionObjectNode, classMeta, true)
			}
		}
	}
}

// parseClassField 解析类属性、接口常量
func (p *KotlinLangParser) parseClassField(ctx ParseMetaContext, fieldNode *sitter.Node, classMeta *indexer.UnifiedMeta, isCompanion bool) (indexer.UnifiedField, error) {
	fieldSignature := util.GetNodeHeaderCode(p.Code, fieldNode, false)
	fieldName, _, fieldTypes, err := p.parseVariableDef(ctx, fieldNode)
	if err != nil {
		return indexer.UnifiedField{}, err
	}
	classField := indexer.UnifiedField{
		Name:           fieldName,
		FieldSignature: fieldSignature,
		Scope:          indexer.GetDefinitionScopeWithDefault(fieldSignature, indexer.PublicScope),
		Types:          fieldTypes,
		IsStatic:       isCompanion,
	}
	classField.UpdateRange(fieldNode)
	classMeta.Fields[fieldName] = classField
	return classField, nil
}

// parseEnumField 解析枚举属性及枚举方法,
func (p *KotlinLangParser) parseEnumField(ctx ParseMetaContext, enumEntryNode *sitter.Node, enumMeta *indexer.UnifiedMeta) {
	enumFieldSignature := util.GetNodeHeaderCode(p.Code, enumEntryNode, true)
	enumFieldNameNode := util.FindFirstChildNodeByType(enumEntryNode, "simple_identifier")
	if enumFieldNameNode == nil {
		return
	}
	enumFieldName := enumFieldNameNode.Content(p.Code)
	field := indexer.UnifiedField{
		Name:           enumFieldName,
		FieldSignature: enumFieldSignature,
		Scope:          indexer.PublicScope,
		IsStatic:       true,
		Types:          []indexer.UnifiedType{},
	}
	field.UpdateRange(enumEntryNode)
	enumMeta.Fields[enumFieldName] = field
}

// parseClassMethods 解析类方法、接口方法、枚举方法
//
// 构造函数:constructor(name: String, age: Int) : this(name)
// (secondary_constructor (parameter (simple_identifier) (user_type (type_identifier))) (parameter (simple_identifier) (user_type (type_identifier))) (constructor_delegation_call (value_arguments (value_argument (simple_identifier)))) (statements (assignment (directly_assignable_expression (this_expression) (navigation_suffix (simple_identifier))) (simple_identifier))))
// 函数fun greet()
// (function_declaration (simple_identifier) (function_body (statements (call_expression (simple_identifier) (call_suffix (value_arguments (value_argument (line_string_literal (interpolated_identifier) (interpolated_identifier)))))))))
func (p *KotlinLangParser) parseClassMethod(ctx ParseMetaContext, node *sitter.Node, classMeta *indexer.UnifiedMeta, isCompanion bool) (*indexer.UnifiedMethod, string, error) {
	methodSignature := util.GetNodeHeaderCodeExt(p.Code, node, false, []string{"statements"})
	methodSignature = strings.TrimRight(methodSignature, " \t\n{")
	method := indexer.UnifiedMethod{
		MethodName:      "",
		MethodSignature: methodSignature,
		Args:            map[string][]indexer.UnifiedType{},
		ReturnType:      []indexer.UnifiedType{},
		Scope:           indexer.GetDefinitionScopeWithDefault(methodSignature, indexer.PublicScope),
		IsStatic:        isCompanion,
		IsConstructor:   node.Type() == "secondary_constructor" || node.Type() == "anonymous_initializer",
		MetaFile:        indexer.MetaFile{FileName: filepath.Base(p.FilePath), FileFullPath: p.FilePath},
	}
	method.UpdateRange(node)
	childNodes := util.FindFirstChildNodesByTypes(node, "simple_identifier", "parameter", "user_type")
	// 是否出现过函数名
	hasName := false
	receiveTarget := ""
	for _, childNode := range childNodes {
		if childNode.Type() == "simple_identifier" {
			method.MethodName = childNode.Content(p.Code)
			hasName = true
		} else if childNode.Type() == "parameter" {
			paramName, _, paramTypes, err := p.parseParameter(ctx, childNode)
			if err != nil {
				continue
			}
			method.Args[paramName] = paramTypes
		} else if childNode.Type() == "user_type" {
			if !hasName {
				// 如果没有出现函数名，说明是一个extension function的接受者
				recvTypes := p.parseMetaTypes(ctx, childNode)
				if len(recvTypes) > 0 {
					receiveTarget = recvTypes[0].TypeName
					// 去除函数签名中的接受者类型
					methodSignature = strings.Replace(methodSignature, receiveTarget+".", "", 1)
					method.MethodSignature = methodSignature
				}
			} else {
				// 函数的返回值
				method.ReturnType = p.parseMetaTypes(ctx, childNode)
			}
		}
	}
	if method.MethodName == "" && method.IsConstructor {
		method.MethodName = classMeta.Name
	}
	if method.MethodName == "" {
		return nil, "", errors.New("method name is empty")
	}
	return &method, receiveTarget, nil
}

// parseVariableDef 解析变量定义
// private val outerProperty = "Outer Property"
// (property_declaration (modifiers (visibility_modifier)) (variable_declaration (simple_identifier)) (line_string_literal))
// val age: Int = 0
// (property_declaration (variable_declaration (simple_identifier) (user_type (type_identifier))) (integer_literal))
// lateinit var user2: User
// (variable_declaration (simple_identifier) (user_type (type_identifier)))
// var user3 = User("name")
// (property_declaration (variable_declaration (simple_identifier)) (call_expression (simple_identifier) (call_suffix (value_arguments (value_argument (line_string_literal))))))
func (p *KotlinLangParser) parseVariableDef(ctx ParseMetaContext, node *sitter.Node) (string, []*sitter.Node, []indexer.UnifiedType, error) {
	varDeclNode := node
	if varDeclNode.Type() == "property_declaration" {
		varDeclNode = util.FindFirstChildNodeByType(node, "variable_declaration")
	}
	if varDeclNode == nil {
		return "", nil, nil, errors.New("invalid node")
	}
	fieldName, typeNodes, fieldType, err := p.parseParameter(ctx, varDeclNode)
	if err != nil {
		return "", nil, nil, err
	}
	if len(fieldType) == 0 && node.Type() == "property_declaration" && node.NamedChildCount() > 1 {
		// 处理隐式类型定义,切带默认值
		secondChildNode := node.NamedChild(int(node.NamedChildCount() - 1))
		if secondChildNode.Type() == "call_expression" {
			// 通过函数调用或者类初始化定义一个变量
			fieldType = p.getVarTypeByCallChain(ctx, secondChildNode)
		} else if secondChildNode.Type() == "line_string_literal" {
			fieldType = append(fieldType, indexer.UnifiedType{TypeName: "String"})
		} else if secondChildNode.Type() == "integer_literal" {
			fieldType = append(fieldType, indexer.UnifiedType{TypeName: "Int"})
		} else if secondChildNode.Type() == "float_literal" {
			fieldType = append(fieldType, indexer.UnifiedType{TypeName: "Float"})
		} else if secondChildNode.Type() == "boolean_literal" {
			fieldType = append(fieldType, indexer.UnifiedType{TypeName: "Boolean"})
		} else if secondChildNode.Type() == "null_literal" {
			fieldType = append(fieldType, indexer.UnifiedType{TypeName: "Null"})
		}
	}
	return fieldName, typeNodes, fieldType, nil
}

// getVarTypeByCallChain 通过计算链式调用的返回类型获得隐式变量的类型
func (p *KotlinLangParser) getVarTypeByCallChain(ctx ParseMetaContext, node *sitter.Node) []indexer.UnifiedType {
	var result []indexer.UnifiedType
	if p.LangIndex != nil {
		// 用于引用查询阶段
		parseCtx := &unified.ParseContext{
			ImportDefs: ctx.innerClassDefs,
			LangIndex:  p.LangIndex,
		}
		types, err := p.ParseAssignmentReturnTypes(parseCtx, node)
		if err == nil {
			result = types
		}
	}
	if len(result) == 0 {
		// 用于索引阶段，没有索引信息的情况
		result = p.parseCallTypes(ctx, node)
	}
	return result
}

// parseParameter 解析参数
func (p *KotlinLangParser) parseParameter(ctx ParseMetaContext, node *sitter.Node) (string, []*sitter.Node, []indexer.UnifiedType, error) {
	varName := ""
	var varTypes []indexer.UnifiedType
	var typeNodes []*sitter.Node
	childNodes := util.FindFirstChildNodesByTypes(node, "simple_identifier", "user_type")
	for _, childNode := range childNodes {
		if childNode.Type() == "simple_identifier" {
			// 参数/变量名
			varName = childNode.Content(p.Code)
		} else {
			// 参数/变量类型
			genericTypes := p.parseMetaTypes(ctx, childNode)
			varTypes = append(varTypes, genericTypes...)
			typeNodes = append(typeNodes, childNode)
		}
	}
	if varName == "" {
		return "", nil, nil, errors.New("parse parameter failed")
	}
	return varName, typeNodes, varTypes, nil
}

// parseCallTypes 解析初始化一个类型的时候，通过获取类型
// val xx = HashMap<String, Person>()
// (call_expression (simple_identifier) (call_suffix (type_arguments (type_projection (user_type (type_identifier))) (type_projection (user_type (type_identifier)))) (value_arguments)))
func (p *KotlinLangParser) parseCallTypes(ctx ParseMetaContext, node *sitter.Node) []indexer.UnifiedType {
	callType := indexer.UnifiedType{}
	callNode := util.FindFirstChildNodeByTypes(node, "simple_identifier", "navigation_expression")
	if callNode != nil {
		callName := callNode.Content(p.Code)
		callType.TypeName = callName
		if fullName, ok := ctx.GetImportDef(callName); ok {
			callType.TypeFullName = fullName
		} else if mappingType, ok := KotlinTypeMapping[callType.TypeName]; ok {
			callType.TypeName = mappingType
		}
	}
	callSuffixNode := util.FindFirstChildNodeByType(node, "call_suffix")
	if callSuffixNode != nil {
		userTypes := p.parseMetaTypes(ctx, callSuffixNode)
		// 泛型, 例如：xxx = HashMap<String, Person>()
		if len(userTypes) > 0 && len(userTypes[0].GenericTypes) > 0 {
			callType.GenericTypes = userTypes[0].GenericTypes
		} else {
			// 使用系统函数，xxx = arrayListOf(XXXX(), xxxx)
			valueArgumentsNode := util.FindFirstChildNodeByType(callSuffixNode, "value_arguments")
			if valueArgumentsNode != nil {
				valueArgumentNodes := util.FindFirstChildNodesByTypes(valueArgumentsNode, "value_argument")
				for _, valueArgumentNode := range valueArgumentNodes {
					childNode := util.FindFirstChildNodeByTypes(valueArgumentNode, "call_expression", "simple_identifier")
					if childNode != nil && childNode.Type() == "call_expression" {
						// 是函数调用或者类初始化的情况
						childTypes := p.parseCallTypes(ctx, childNode)
						if len(childTypes) > 0 {
							callType.GenericTypes = childTypes
							break
						}
					} else if childNode != nil && childNode.Type() == "simple_identifier" {
						// 是变量的情况
						objectName := childNode.Content(p.Code)
						if fullName, ok := ctx.GetImportDef(objectName); ok {
							callType.GenericTypes = append(callType.GenericTypes, indexer.UnifiedType{TypeFullName: fullName, TypeName: objectName})
						} else if ctx.ParseRefContext != nil && ctx.ParseRefContext.VarDefs != nil {
							if varTypeDefs, ok := ctx.ParseRefContext.VarDefs[objectName]; ok {
								for _, varTypeDef := range varTypeDefs {
									callType.GenericTypes = append(callType.GenericTypes, varTypeDef.UnifiedType)
								}
							}
						}
					}
				}
			}
		}
	}
	if callType.TypeName == "" {
		return nil
	}
	return []indexer.UnifiedType{callType}
}

// parseMetaTypes 解析类属性的类型,user_type节点
func (p *KotlinLangParser) parseMetaTypes(ctx ParseMetaContext, typeNode *sitter.Node) []indexer.UnifiedType {
	results := make([]indexer.UnifiedType, 0, 2)
	if typeNode != nil {
		// 获取泛型时引用的多个类
		typeNodes := util.FindFirstChildNodesByTypes(typeNode, "type_identifier", "type_arguments")
		var unifiedType indexer.UnifiedType
		for _, n := range typeNodes {
			if n.Type() == "type_arguments" {
				typeProjectNodes := util.FindFirstChildNodesByTypes(n, "type_projection")
				if len(typeProjectNodes) > 0 {
					for _, typeProjectNode := range typeProjectNodes {
						userTypeNode := util.FindFirstChildNodeByType(typeProjectNode, "user_type")
						if userTypeNode != nil {
							genericTypes := p.parseMetaTypes(ctx, userTypeNode)
							unifiedType.GenericTypes = append(unifiedType.GenericTypes, genericTypes...)
						}
					}
				}
			} else {
				unifiedType = newUnifiedType(ctx, newUnifiedType(ctx, indexer.UnifiedType{
					TypeName: n.Content(p.Code),
				}))
			}
		}
		if unifiedType.TypeName == "" {
			// 如果没有泛型，取单个类型
			unifiedType.TypeName = typeNode.Content(p.Code)
			results = append(results, newUnifiedType(ctx, unifiedType))
		} else {
			results = append(results, unifiedType)
		}
	}
	return results
}

func newUnifiedType(ctx ParseMetaContext, unifiedType indexer.UnifiedType) indexer.UnifiedType {
	if unifiedType.TypeFullName != "" {
		return unifiedType
	}
	if fullName, ok := ctx.GetImportDef(unifiedType.TypeName); ok {
		unifiedType.TypeFullName = fullName
	} else if mappingType, ok := KotlinTypeMapping[unifiedType.TypeName]; ok {
		unifiedType.TypeName = mappingType
	}
	return unifiedType
}
