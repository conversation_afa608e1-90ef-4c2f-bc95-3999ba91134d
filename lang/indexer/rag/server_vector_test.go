package rag

import (
	"cosy/client"
	"cosy/config"
	"cosy/definition"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestServerVecRetrieveEngine_executeBuildServerIndex_fullIndex(t *testing.T) {
	client.InitClients()
	config.InitLocalConfig()

	workspacePath := "/Users/<USER>/workspace/lingma/cosy"
	//workspacePath := "/Users/<USER>/workspace/lingma/cosy/auth/static/yunxiao-fe/cosy-client-assets/0.1.9"
	engine, err := newChatServerVecRetrieveEngine(workspacePath)
	assert.NotNil(t, engine)
	assert.Nil(t, err)
	engine.executeBuildServerIndex(nil, definition.VectorFullIndexSource)
}

func TestServerVecRetrieveEngine_executeBuildServerIndex_incrementalIndex(t *testing.T) {
	client.InitClients()
	config.InitLocalConfig()

	workspacePath := "/Users/<USER>/workspace/lingma/cosy"
	//workspacePath := "/Users/<USER>/workspace/lingma/cosy/auth/static/yunxiao-fe/cosy-client-assets/0.1.9"
	engine, err := newChatServerVecRetrieveEngine(workspacePath)
	assert.NotNil(t, engine)
	assert.Nil(t, err)
	filePaths := []string{
		"/Users/<USER>/workspace/lingma/cosy/lang/indexer/rag/server_vector.go",
		"/Users/<USER>/workspace/lingma/cosy/indexing/project_indexer.go",
		"/Users/<USER>/workspace/lingma/cosy/lang/inline_edit.go",
	}
	virtualFiles := definition.BuildBatchVirtualFile(filePaths)
	engine.executeBuildServerIndex(virtualFiles, definition.VectorFileChangeIndexSource)
}
