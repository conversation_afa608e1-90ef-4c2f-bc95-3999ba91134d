package rag

import (
	"errors"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestableRipGrepEngine implements the testable version with mocking capability
type TestableRipGrepEngine struct {
	workspaceUri               string
	options                    *RipGrepRetrieveOptions
	mockExecuteRgSearchCommand func(rgPath string, maxSearchLine int, args ...string) ([]string, bool, error)
	mockExecuteBackfillSearch  func(rgPath string, args []string, keyWords []string, searchResults []searchResult, remainingCapacity int) []string
}

func NewTestableRipGrepEngine(workspaceUri string, options *RipGrepRetrieveOptions) *TestableRipGrepEngine {
	return &TestableRipGrepEngine{
		workspaceUri: workspaceUri,
		options:      options,
	}
}

// Mock the executeRgSearchCommand method
func (t *TestableRipGrepEngine) executeRgSearchCommand(rgPath string, maxSearchLine int, args ...string) ([]string, bool, error) {
	if t.mockExecuteRgSearchCommand != nil {
		return t.mockExecuteRgSearchCommand(rgPath, maxSearchLine, args...)
	}
	return []string{}, true, nil
}

// Mock the executeBackfillSearch method
func (t *TestableRipGrepEngine) executeBackfillSearch(rgPath string, args []string, keyWords []string, searchResults []searchResult, remainingCapacity int) []string {
	if t.mockExecuteBackfillSearch != nil {
		return t.mockExecuteBackfillSearch(rgPath, args, keyWords, searchResults, remainingCapacity)
	}
	return []string{}
}

// Copy the methods needed from RipGrepRetrieveEngine
func (t *TestableRipGrepEngine) calculateAllocationRatios(keyWordCount int) []float64 {
	if keyWordCount <= 0 {
		return []float64{}
	}

	ratios := make([]float64, keyWordCount)

	switch keyWordCount {
	case 1:
		ratios[0] = 1.0
	case 2:
		ratios[0] = 0.7
		ratios[1] = 0.3
	case 3:
		ratios[0] = 0.6
		ratios[1] = 0.3
		ratios[2] = 0.1
	default:
		ratios[0] = 0.5
		ratios[1] = 0.2
		remaining := 0.3
		for i := 2; i < keyWordCount; i++ {
			ratios[i] = remaining / float64(keyWordCount-2)
		}
	}

	return ratios
}

func (t *TestableRipGrepEngine) deduplicateAndTruncate(output []string, maxCount int) []string {
	seen := make(map[string]bool)
	var result []string

	for _, line := range output {
		if len(result) >= maxCount {
			break
		}
		if !seen[line] {
			seen[line] = true
			result = append(result, line)
		}
	}

	return result
}

// ExecuteProportionalSearchForTest is the actual method under test
func (t *TestableRipGrepEngine) ExecuteProportionalSearchForTest(rgPath string, args []string, keyWords []string) ([]string, error) {
	var allOutput []string

	// 根据优先级设定分配比例
	allocationRatios := t.calculateAllocationRatios(len(keyWords))

	// 第一轮：按比例分配检索
	searchResults := make([]searchResult, len(keyWords))
	for i, keyWord := range keyWords {
		targetCount := int(float64(DefaultTotalMaxChunk) * allocationRatios[i])
		if targetCount < 1 {
			targetCount = 1
		}

		tmpArgs := append(args, fmt.Sprintf("(%s)", generateWordBoundaryPattern(keyWord)))
		tmpArgs = append(tmpArgs, t.workspaceUri)

		outputLine, finishedSearch, err := t.executeRgSearchCommand(rgPath, targetCount, tmpArgs...)
		if err != nil {
			searchResults[i] = searchResult{
				actualCount: 0,
				targetCount: targetCount,
				finished:    false,
			}
			continue
		}

		actualCount := len(outputLine)
		searchResults[i] = searchResult{
			actualCount: actualCount,
			targetCount: targetCount,
			finished:    finishedSearch,
		}
		allOutput = append(allOutput, outputLine...)
	}

	// 第二轮：回填机制
	remainingCapacity := DefaultTotalMaxChunk - len(allOutput)
	if remainingCapacity > 0 {
		allOutput = append(allOutput, t.executeBackfillSearch(rgPath, args, keyWords, searchResults, remainingCapacity)...)
	}

	// 去重和截断
	allOutput = t.deduplicateAndTruncate(allOutput, DefaultTotalMaxChunk)

	return allOutput, nil
}

func TestExecuteProportionalSearch(t *testing.T) {
	tests := []struct {
		name             string
		keyWords         []string
		mockResponses    []mockResponse
		expectedOutput   int
		expectedError    bool
		backfillResponse []string
	}{
		{
			name:     "single keyword success",
			keyWords: []string{"test"},
			mockResponses: []mockResponse{
				{output: []string{"line1", "line2"}, finished: true, err: nil},
			},
			expectedOutput: 2,
		},
		{
			name:     "multiple keywords proportional allocation",
			keyWords: []string{"keyword1", "keyword2", "keyword3"},
			mockResponses: []mockResponse{
				{output: []string{"result1", "result2"}, finished: true, err: nil},
				{output: []string{"result3"}, finished: true, err: nil},
				{output: []string{"result4"}, finished: true, err: nil},
			},
			expectedOutput: 4,
		},
		{
			name:     "keyword search failure handling",
			keyWords: []string{"keyword1", "keyword2"},
			mockResponses: []mockResponse{
				{output: []string{"result1"}, finished: true, err: nil},
				{output: nil, finished: false, err: errors.New("search failed")},
			},
			expectedOutput: 1,
		},
		{
			name:           "empty keywords",
			keyWords:       []string{},
			mockResponses:  []mockResponse{},
			expectedOutput: 0,
		},
		{
			name:     "four keywords allocation",
			keyWords: []string{"kw1", "kw2", "kw3", "kw4"},
			mockResponses: []mockResponse{
				{output: []string{"r1", "r2"}, finished: true, err: nil}, // 50% allocation
				{output: []string{"r3"}, finished: true, err: nil},       // 20% allocation
				{output: []string{"r4"}, finished: true, err: nil},       // 15% allocation
				{output: []string{"r5"}, finished: true, err: nil},       // 15% allocation
			},
			expectedOutput: 5,
		},
		{
			name:     "backfill mechanism test",
			keyWords: []string{"kw1", "kw2"},
			mockResponses: []mockResponse{
				{output: []string{"r1"}, finished: false, err: nil}, // Incomplete search
				{output: []string{"r2"}, finished: false, err: nil}, // Incomplete search
			},
			backfillResponse: []string{"backfill1", "backfill2"},
			expectedOutput:   4, // 2 from initial + 2 from backfill
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create testable engine
			testEngine := NewTestableRipGrepEngine("/test/workspace", &RipGrepRetrieveOptions{})

			// Setup mock responses
			callCount := 0
			testEngine.mockExecuteRgSearchCommand = func(rgPath string, maxSearchLine int, args ...string) ([]string, bool, error) {
				if callCount < len(tt.mockResponses) {
					resp := tt.mockResponses[callCount]
					callCount++
					return resp.output, resp.finished, resp.err
				}
				return []string{}, true, nil
			}

			// Mock backfill with test-specific response
			testEngine.mockExecuteBackfillSearch = func(rgPath string, args []string, keyWords []string, searchResults []searchResult, remainingCapacity int) []string {
				if tt.backfillResponse != nil {
					return tt.backfillResponse
				}
				return []string{}
			}

			// Execute test
			result, err := testEngine.ExecuteProportionalSearchForTest("/usr/bin/rg", []string{"-i"}, tt.keyWords)

			// Assertions
			if tt.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedOutput, len(result))
			}
		})
	}
}

type mockResponse struct {
	output   []string
	finished bool
	err      error
}

func Test_riggrep_Retrieve(t *testing.T) {
	retrieveEngine := NewRipGrepRetrieveEngine("/Users/<USER>/codeup/cilium#ebpf", &RipGrepRetrieveOptions{})
	textQuery := TextQuery{
		KeyWords: []string{"DATA RACE", "cilium/ebpf"},
	}
	retrieve, err := retrieveEngine.Retrieve(textQuery)
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println("Test result:", len(retrieve.Chunks))
}
