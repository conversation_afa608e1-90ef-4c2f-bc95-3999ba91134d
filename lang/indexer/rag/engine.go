package rag

import (
	"context"
	"cosy/definition"
	"cosy/global"
	"cosy/lang/indexer"
	"cosy/log"
	"errors"
	"fmt"
	"os"
	"sync"
)

const (
	// MatchOr 表示使用 OR 运算符进行匹配
	MatchOr = "OR"
	// MatchAnd 表示使用 AND 运算符进行匹配
	MatchAnd = "and"

	RRFMergeSource             = "rrf_merge"
	VectorMergeSource          = "vector_merge"
	TextRetrieveSource         = "text_retrieve"
	ClientVectorRetrieveSource = "client_vector_retrieve"
	ServerVectorRetrieveSource = "server_vector_retrieve"

	VectorEngineTypeClient = "client"
	VectorEngineTypeServer = "server"
)

// RetrieveChunk 结构体定义了一个检索块
type RetrieveChunk struct {
	indexer.CodeChunk         // CodeChunk 用于存储代码片段的具体信息
	Score             float64 `json:"score,omitempty"` // Score 表示该代码片段与查询的相关性分数
}

// RetrieveResult 结构体定义了检索的结果
type RetrieveResult struct {
	Source string          // Source 表示检索结果的来源
	Chunks []RetrieveChunk // Chunks 存储了所有检索到的代码块及其相关性分数
}

// TextCondition 定义了一个文本查询条件
type TextCondition struct {
	FieldName string  // FieldName 表示要查询的字段名
	Query     string  // Query 表示查询的文本内容
	Boost     float64 // Boost 表示该条件的权重值
}

// TextQuery 定义了一个文本查询结构体
type TextQuery struct {
	// 采用 AND/OR 模式
	// Conditions 和 Operator 需要填值
	Conditions []TextCondition // Conditions 是查询条件的集合，每个条件通过 TextCondition 来定义
	Operator   string          // Operator 表示查询条件之间的逻辑操作符（如AND，OR等）
	// 采用 must/should/mustNot 模式
	// Conditions 和 Operator 不能填值，互斥的
	Must    []TextCondition
	Should  []TextCondition
	MustNot []TextCondition
	// 公共属性
	Size     int
	From     int
	Fields   []string
	KeyWords []string
}

// TextRetrieveEngine 接口定义了一个文本检索引擎
type TextRetrieveEngine interface {
	Initialize() error                                                       // Initialize 初始化方法，用于初始化文本检索引擎
	BatchIndex(filePaths []string, enableCheckChange bool) ([]string, error) // BatchIndex 批量索引方法，将一组文件路径索引到引擎中
	BatchRemove(filePaths []string) error                                    // BatchRemove 批量移除方法，从引擎中移除一组文件路径对应的索引
	Retrieve(ctx context.Context, query TextQuery) (RetrieveResult, error)   // Retrieve 检索方法，根据查询字符串返回相关的结果
	GetFileChunks(filePath string, topN int) ([]indexer.CodeChunk, error)    // GetFileChunks 获取文件对应的代码块
	BatchGetFileChunks(filePaths []string, topNPerFile int) ([]indexer.CodeChunk, error)
	GetDocumentTotalCount() int64 // GetDocumentTotalCount 获取文档总数
	Close()                       // 关闭文本引擎
}

// VectorRetrieveEngine 向量检索引擎
// 每个workspace都有一个全局唯一的engine
type VectorRetrieveEngine interface {
	// EngineType
	// 返回当前向量引擎为客户端引擎还是服务端引擎
	EngineType() string

	// ExecuteBatchTask
	// 返回是否skip，skip时返回true
	ExecuteBatchTask(tasks definition.VectorBatchTask, enableCheckChange bool, limitRequire bool) bool

	// BatchDeleteIndex 删除索引
	// 先删除索引记录，然后删除索引数据块
	// isDir传入删除是否为目录，如果isDir为true，virtualFiles均为目录类型File
	BatchDeleteIndex(virtualFiles []definition.VirtualFile, isDir bool) error

	// Retrieve 召回
	Retrieve(queryCondition definition.QueryCondition) (RetrieveResult, error)

	// GetStorageFileNum 获取当前引擎中存储的总共索引的文件数量
	GetStorageFileNum() int

	// GetStorageChunkNum 获取当前引擎中存储的总共索引的数据块数量
	GetStorageChunkNum() int

	// GetStorageFileChunks 获取当前库中某个文件的全部代码块
	// 查询的数据不包含存储的embedding
	GetStorageFileChunks(filePath string) ([]definition.StorageChunk, error)

	// Close 关闭向量引擎
	Close()

	// AsyncBuildIndex
	// 异步索引接口
	// 服务端、客户端异步索引接口都是这个
	AsyncBuildIndex(virtualFiles []definition.VirtualFile, source string)
}

type IndexProgress interface {
	// 输出索引进度
	Display(data DisplayData)
}

const (
	clientChatVectorRetrieveEngineName     = "client_chat_vector_engine"
	serverChatVectorRetrieveEngineName     = "server_chat_vector_engine"
	clientChatTextRetrieveEngineName       = "client_chat_text_engine"
	clientChatMemoVectorRetrieveEngineName = "client_chat_memo_vector_engine"
)

var globalEngineCreator engineCreator

type engineCreator struct {
	creatorMtx sync.Mutex
	engineMap  sync.Map
}

func getEngineCreatorKey(workspacePath string, engineName string) string {
	return fmt.Sprintf("%s_%s", workspacePath, engineName)
}

func init() {
	globalEngineCreator = engineCreator{
		creatorMtx: sync.Mutex{},
		engineMap:  sync.Map{},
	}
}

// GetClientChatVectorRetrieveEngine
// 根据workspacePath获取客户端vector检索引擎
func GetClientChatVectorRetrieveEngine(workspacePath string) (VectorRetrieveEngine, error) {
	if !global.ClientVectorIndexEnable() {
		return nil, errors.New("client vector index is not enable")
	}

	if workspacePath == "" {
		return nil, errors.New("workspace path is empty")
	}

	if stat, err := os.Stat(workspacePath); err != nil || !stat.IsDir() {
		return nil, errors.New("workspace path is not a directory")
	}

	key := getEngineCreatorKey(workspacePath, clientChatVectorRetrieveEngineName)
	if v, ok := globalEngineCreator.engineMap.Load(key); ok {
		return v.(VectorRetrieveEngine), nil
	}

	// 加锁，确保单例创建
	globalEngineCreator.creatorMtx.Lock()
	defer globalEngineCreator.creatorMtx.Unlock()

	// double check
	if v, ok := globalEngineCreator.engineMap.Load(key); ok {
		return v.(VectorRetrieveEngine), nil
	}

	engine, err := newChatSqliteVecRetrieveEngine(workspacePath)
	if err != nil {
		log.Error("Error to initialize client chat vector engine for ", workspacePath)
		return nil, err
	}

	if engine != nil {
		globalEngineCreator.engineMap.Store(key, engine)
		return engine, nil
	}

	return nil, errors.New("failed to create client vector retrieve engine")
}

// GetServerChatVectorRetrieveEngine
// 根据workspacePath获取服务端vector检索引擎
func GetServerChatVectorRetrieveEngine(workspacePath string) (VectorRetrieveEngine, error) {
	if !global.ServerVectorIndexEnable() {
		return nil, errors.New("server vector index is not enable")
	}

	if workspacePath == "" {
		return nil, errors.New("workspace path is empty")
	}

	if stat, err := os.Stat(workspacePath); err != nil || !stat.IsDir() {
		return nil, errors.New("workspace path is not a directory")
	}

	key := getEngineCreatorKey(workspacePath, serverChatVectorRetrieveEngineName)
	if v, ok := globalEngineCreator.engineMap.Load(key); ok {
		return v.(VectorRetrieveEngine), nil
	}

	// 加锁，确保单例创建
	globalEngineCreator.creatorMtx.Lock()
	defer globalEngineCreator.creatorMtx.Unlock()

	// double check
	if v, ok := globalEngineCreator.engineMap.Load(key); ok {
		return v.(VectorRetrieveEngine), nil
	}

	engine, err := newChatServerVecRetrieveEngine(workspacePath)
	if err != nil {
		log.Error("Error to initialize server chat vector engine for ", workspacePath)
		return nil, err
	}

	if engine != nil {
		globalEngineCreator.engineMap.Store(key, engine)
		return engine, nil
	}

	return nil, errors.New("failed to create server vector retrieve engine")
}

func GetClientChatTextRetrieveEngine(workspacePath string) (TextRetrieveEngine, error) {
	if workspacePath == "" {
		return nil, errors.New("workspace path is empty")
	}

	if stat, err := os.Stat(workspacePath); err != nil || !stat.IsDir() {
		return nil, errors.New("workspace path is not a directory")
	}

	key := getEngineCreatorKey(workspacePath, clientChatTextRetrieveEngineName)
	if v, ok := globalEngineCreator.engineMap.Load(key); ok {
		return v.(TextRetrieveEngine), nil
	}

	// 加锁，确保单例创建
	globalEngineCreator.creatorMtx.Lock()
	defer globalEngineCreator.creatorMtx.Unlock()

	// double check
	if v, ok := globalEngineCreator.engineMap.Load(key); ok {
		return v.(TextRetrieveEngine), nil
	}

	var engine TextRetrieveEngine
	engine = NewRipGrepRetrieveEngine(workspacePath, &RipGrepRetrieveOptions{})
	err := engine.Initialize()
	if err != nil {
		log.Error("Error to initialize client chat text engine for ", workspacePath)
		return nil, err
	}

	if engine != nil {
		globalEngineCreator.engineMap.Store(key, engine)
		return engine, nil
	}

	return nil, errors.New("failed to create client text retrieve engine")
}

// GetClientChatMemoVectorRetrieveEngine
// 根据workspacePath获取memo vector检索引擎
func GetClientChatMemoVectorRetrieveEngine(workspacePath string) (VectorRetrieveEngine, error) {
	if workspacePath == "" {
		return nil, errors.New("workspace path is empty")
	}

	if stat, err := os.Stat(workspacePath); err != nil || !stat.IsDir() {
		return nil, errors.New("workspace path is not a directory")
	}

	key := getEngineCreatorKey(workspacePath, clientChatMemoVectorRetrieveEngineName)
	if v, ok := globalEngineCreator.engineMap.Load(key); ok {
		return v.(VectorRetrieveEngine), nil
	}

	// 加锁，确保单例创建
	globalEngineCreator.creatorMtx.Lock()
	defer globalEngineCreator.creatorMtx.Unlock()

	// double check
	if v, ok := globalEngineCreator.engineMap.Load(key); ok {
		return v.(VectorRetrieveEngine), nil
	}

	engine, err := newClientChatMemoSqliteVecRetrieveEngine(workspacePath)
	if err != nil {
		log.Error("Error to initialize client chat vector memo engine for ", workspacePath)
		return nil, err
	}

	if engine != nil {
		globalEngineCreator.engineMap.Store(key, engine)
		return engine, nil
	}

	return nil, errors.New("failed to create memo vector retrieve engine")
}

type RerankOption struct {
	//门槛分数
	ThresholdScore float64
	//取topK
	TopK int
}
