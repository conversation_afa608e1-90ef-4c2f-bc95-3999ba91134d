package rag

import (
	"os"
	"path/filepath"
	"strings"
	"testing"
)

func TestExpandAndMergeChunks(t *testing.T) {
	engine := &RipGrepRetrieveEngine{
		WorkspaceUri: "/test/workspace",
		options: &RipGrepRetrieveOptions{
			MaxOneLineLength:        512,
			TopN:                    20,
			ContextExpandLineLength: 2,
			MaxChunkSizeBytes:       2000, // Large enough to not interfere with test
		},
	}

	// Create a temporary test file
	tempDir := t.TempDir()
	testFile := filepath.Join(tempDir, "test.go")

	testContent := `package main

import "fmt"

func main() {
	fmt.Println("Hello")
	fmt.Println("World")
	fmt.Println("Test")
	fmt.Println("Context")
	fmt.Println("Expansion")
}`

	err := os.WriteFile(testFile, []byte(testContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}

	// Test case 1: Non-overlapping matches (lines 3 and 8)
	matchedLines := []MatchedLine{
		{FilePath: testFile, LineNum: 3, Content: `import "fmt"`},
		{FilePath: testFile, LineNum: 8, Content: `	fmt.Println("Test")`},
	}

	expandedChunks, err := engine.expandAndMergeChunks(matchedLines, 2)
	if err != nil {
		t.Fatalf("expandAndMergeChunks failed: %v", err)
	}

	// Should have 1 chunk due to size limiting and range merging
	// Line 3 expands to 1-5, line 8 expands to 6-10, which are adjacent so they merge to 1-10
	if len(expandedChunks) != 1 {
		t.Errorf("Expected 1 chunk due to merging, got %d", len(expandedChunks))
		for i, chunk := range expandedChunks {
			t.Logf("Chunk %d: %d-%d", i, chunk.StartLine, chunk.EndLine)
		}
	}

	// Merged chunk: lines 1-10 (after merging adjacent ranges 1-5 and 6-10)
	if len(expandedChunks) > 0 {
		chunk := expandedChunks[0]
		// Due to size limiting, the actual range might be truncated
		if chunk.StartLine < 1 || chunk.EndLine > 10 {
			t.Errorf("Unexpected chunk range %d-%d", chunk.StartLine, chunk.EndLine)
		}
		// Check that both matched content is present
		if !strings.Contains(chunk.Content, `import "fmt"`) {
			t.Error("Expected first matched line to be present")
		}
		if !strings.Contains(chunk.Content, `fmt.Println("Test")`) {
			t.Error("Expected second matched line to be present")
		}
	}

	t.Logf("Test case 1 passed: %d non-overlapping chunks created", len(expandedChunks))
}

func TestExpandAndMergeChunksOverlapping(t *testing.T) {
	engine := &RipGrepRetrieveEngine{
		WorkspaceUri: "/test/workspace",
		options: &RipGrepRetrieveOptions{
			MaxOneLineLength:        512,
			TopN:                    20,
			ContextExpandLineLength: 2,
			MaxChunkSizeBytes:       2000, // Large enough to not interfere with test
		},
	}

	// Create a temporary test file
	tempDir := t.TempDir()
	testFile := filepath.Join(tempDir, "test.go")

	testContent := `package main

import "fmt"

func main() {
	fmt.Println("Hello")
	fmt.Println("World")
	fmt.Println("Test")
	fmt.Println("Context")
	fmt.Println("Expansion")
}`

	err := os.WriteFile(testFile, []byte(testContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}

	// Test case 2: Overlapping matches (lines 6 and 7)
	matchedLines := []MatchedLine{
		{FilePath: testFile, LineNum: 6, Content: `	fmt.Println("Hello")`},
		{FilePath: testFile, LineNum: 7, Content: `	fmt.Println("World")`},
	}

	expandedChunks, err := engine.expandAndMergeChunks(matchedLines, 2)
	if err != nil {
		t.Fatalf("expandAndMergeChunks failed: %v", err)
	}

	// Should have 1 chunk after merging overlapping ranges
	// Line 6 expands to 4-8, line 7 expands to 5-9
	// These overlap, so they should merge into one chunk: 4-9
	if len(expandedChunks) != 1 {
		t.Errorf("Expected 1 chunk after merging, got %d", len(expandedChunks))
		for i, chunk := range expandedChunks {
			t.Logf("Chunk %d: %d-%d", i, chunk.StartLine, chunk.EndLine)
		}
	}

	if len(expandedChunks) > 0 {
		chunk := expandedChunks[0]
		if chunk.StartLine != 4 || chunk.EndLine != 9 {
			t.Errorf("Expected merged chunk to be lines 4-9, got %d-%d", chunk.StartLine, chunk.EndLine)
		}
	}

	t.Logf("Test case 2 passed: %d merged chunks created", len(expandedChunks))
}

func TestParseRipGrepOutputWithContextExpansion(t *testing.T) {
	engine := &RipGrepRetrieveEngine{
		WorkspaceUri: "/test/workspace",
		options: &RipGrepRetrieveOptions{
			MaxOneLineLength:        512,
			TopN:                    20,
			ContextExpandLineLength: 2,
		},
	}

	// Create a temporary test file
	tempDir := t.TempDir()
	testFile := filepath.Join(tempDir, "test.go")

	testContent := `package main

import "fmt"

func main() {
	fmt.Println("Hello")
	fmt.Println("World")
	fmt.Println("Test")
	fmt.Println("Context")
	fmt.Println("Expansion")
}`

	err := os.WriteFile(testFile, []byte(testContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}

	// Sample rg output with matches on lines 3 and 6
	sampleOutput := testFile + ":3:import \"fmt\"\n" +
		testFile + ":6:\tfmt.Println(\"Hello\")\n"

	result, err := engine.parseRipGrepOutput(strings.Split(sampleOutput, "\n"))
	if err != nil {
		t.Fatalf("parseRipGrepOutput failed: %v", err)
	}

	// Verify the result structure
	if result.Source != TextRetrieveSource {
		t.Errorf("Expected source to be %s, got %s", TextRetrieveSource, result.Source)
	}

	// Should have 1 chunk after merging: line 3 expanded (1-5), line 6 expanded (4-8)
	// These ranges overlap (both include lines 4-5), so they merge into one chunk: 1-8
	expectedChunks := 1
	if len(result.Chunks) != expectedChunks {
		t.Errorf("Expected %d chunks, got %d", expectedChunks, len(result.Chunks))
		for i, chunk := range result.Chunks {
			t.Logf("Chunk %d: %d-%d", i, chunk.StartLine, chunk.EndLine)
		}
	}

	if len(result.Chunks) > 0 {
		firstChunk := result.Chunks[0]
		if firstChunk.Type != "context" {
			t.Errorf("Expected chunk type to be 'context', got '%s'", firstChunk.Type)
		}
		if firstChunk.FilePath != testFile {
			t.Errorf("Expected chunk file path to be '%s', got '%s'", testFile, firstChunk.FilePath)
		}
		if firstChunk.FileName != "test.go" {
			t.Errorf("Expected chunk file name to be 'test.go', got '%s'", firstChunk.FileName)
		}
		if firstChunk.Id == "" {
			t.Error("Expected chunk to have a non-empty ID")
		}
	}

	// Print all chunks for debugging
	t.Logf("Parsed %d chunks with context expansion:", len(result.Chunks))
	for i, chunk := range result.Chunks {
		t.Logf("Chunk %d: File=%s, Lines=%d-%d, Content length=%d",
			i+1, chunk.FileName, chunk.StartLine, chunk.EndLine, len(chunk.Content))
	}
}

func TestMergeOverlappingRanges(t *testing.T) {
	engine := &RipGrepRetrieveEngine{}

	testCases := []struct {
		name     string
		input    []struct{ startLine, endLine uint32 }
		expected []struct{ startLine, endLine uint32 }
	}{
		{
			name: "No overlap",
			input: []struct{ startLine, endLine uint32 }{
				{1, 3}, {5, 7}, {9, 11},
			},
			expected: []struct{ startLine, endLine uint32 }{
				{1, 3}, {5, 7}, {9, 11},
			},
		},
		{
			name: "Adjacent ranges",
			input: []struct{ startLine, endLine uint32 }{
				{1, 3}, {4, 6}, {7, 9},
			},
			expected: []struct{ startLine, endLine uint32 }{
				{1, 9},
			},
		},
		{
			name: "Overlapping ranges",
			input: []struct{ startLine, endLine uint32 }{
				{1, 5}, {3, 7}, {6, 10},
			},
			expected: []struct{ startLine, endLine uint32 }{
				{1, 10},
			},
		},
		{
			name: "Mixed overlapping and non-overlapping",
			input: []struct{ startLine, endLine uint32 }{
				{1, 3}, {2, 5}, {7, 9}, {11, 13},
			},
			expected: []struct{ startLine, endLine uint32 }{
				{1, 5}, {7, 9}, {11, 13},
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := engine.mergeOverlappingRanges(tc.input)
			if len(result) != len(tc.expected) {
				t.Errorf("Expected %d ranges, got %d", len(tc.expected), len(result))
				return
			}

			for i, expected := range tc.expected {
				if i >= len(result) {
					t.Errorf("Missing expected range %d: %d-%d", i, expected.startLine, expected.endLine)
					continue
				}
				if result[i].startLine != expected.startLine || result[i].endLine != expected.endLine {
					t.Errorf("Range %d: expected %d-%d, got %d-%d",
						i, expected.startLine, expected.endLine, result[i].startLine, result[i].endLine)
				}
			}
		})
	}
}
