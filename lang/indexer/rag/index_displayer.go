package rag

import (
	"cosy/log"
	"strings"
)

const (
	DisplayOutputModeLog = "log"
	DisplayOutputModeIde = "ide"

	DisplayIndexTypeClientVector            = "Client Vector Index"
	DisplayIndexTypeServerVectorFull        = "Server Vector Index [Full]"
	DisplayIndexTypeServerVectorFullSkipped = "Server Vector Index [Full Skipped]"
	DisplayIndexTypeServerVectorIncremental = "Server Vector Index [Incremental]"

	DefaultMaxArrowLen = 25
)

func NewIndexDisplayer() *IndexDisplayer {
	return &IndexDisplayer{
		MaxArrowLen: DefaultMaxArrowLen,
	}
}

type IndexDisplayer struct {
	MaxArrowLen int
}

type DisplayData struct {
	WorkspacePath string
	IndexType     string
	FinishedNum   int
	TotalNum      int
	OutputMode    string
}

func (d *IndexDisplayer) Display(data DisplayData) {
	if data.OutputMode == DisplayOutputModeLog {
		if data.TotalNum > 0 && data.FinishedNum < data.TotalNum {
			maxProgressLen := d.MaxArrowLen
			maxArrowLen := d.MaxArrowLen - 1
			percent := float64(data.FinishedNum) / float64(data.TotalNum)
			equ := strings.Repeat("=", int(percent*float64(maxArrowLen)))
			if len(equ) > maxArrowLen {
				equ = equ[:maxArrowLen]
			}
			arrow := equ + ">"
			progressStr := arrow
			if len(arrow) <= maxProgressLen {
				space := strings.Repeat(" ", maxProgressLen-len(arrow))
				progressStr = arrow + space
			}
			log.Warnf("[%s] %.2f%%, IndexType: %s, Finished/Total: %d/%d, WorkspacePath: %s", progressStr, percent*100, data.IndexType, data.FinishedNum, data.TotalNum, data.WorkspacePath)
		} else {
			// 添加非常显眼的成功标识
			log.Warnf("##########################################")
			log.Warnf("######  REPOSITORY INDEX COMPLETE   ######")
			log.Warnf("##########################################")
			log.Warnf("###### IndexType: %s", data.IndexType)
			log.Warnf("###### Workspace: '%s'", data.WorkspacePath)
			log.Warnf("###### Finished/Total: %d/%d", data.FinishedNum, data.TotalNum)
			log.Warnf("##########################################")
		}
	}

	return
}
