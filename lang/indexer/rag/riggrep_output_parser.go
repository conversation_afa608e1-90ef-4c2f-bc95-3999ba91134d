package rag

import (
	"bufio"
	"cosy/lang/indexer"
	"cosy/log"
	"crypto/md5"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
)

// MatchedLine 匹配行信息
type MatchedLine struct {
	FilePath string
	LineNum  uint32
	Content  string
}

// ExpandedChunk 扩展后的代码块
type ExpandedChunk struct {
	FilePath  string
	StartLine uint32
	EndLine   uint32
	Content   string
}

// parseRipGrepOutput
// 解析rg命令输出并转化为RetrieveResult格式
// 格式: 每行格式为 文件路径:行号:文件内容
func (r *RipGrepRetrieveEngine) parseRipGrepOutput(lines []string) (RetrieveResult, error) {
	if len(lines) == 0 {
		return RetrieveResult{}, nil
	}
	var result RetrieveResult
	result.Source = TextRetrieveSource
	result.Chunks = []RetrieveChunk{}

	// 1. 收集所有匹配行
	var matchedLines []MatchedLine

	// 2. 按行分割输出
	if len(lines) > DefaultTotalMaxChunk {
		lines = lines[:DefaultTotalMaxChunk]
	}

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// 3. 解析行格式: 文件路径:行号:文件内容
		// 使用SplitN限制分割次数为3，因为文件内容中可能包含冒号
		parts := strings.SplitN(line, ":", 3)
		if len(parts) != 3 {
			continue
		}

		filePath := strings.TrimSpace(parts[0])
		lineNumStr := strings.TrimSpace(parts[1])
		content := parts[2] // 文件内容不trim，保持原始格式

		// 4. 检查文件路径和行号
		if filePath == "" {
			continue
		}

		// 5. 检查是否是被忽略的长行
		if strings.Contains(content, IgnoreOutOfLimitPrefix) || strings.Contains(content, OutOfLimitDesc) {
			log.Debugf("file %s lineNumStr %s out of limit or omitted", filePath, lineNumStr)
			continue
		}

		// 6. 解析行号
		lineNum, err := strconv.ParseUint(lineNumStr, 10, 32)
		if err != nil {
			log.Errorf("failed to parse lineNumStr %s, line %s", lineNumStr, line)
			// 如果解析行号失败，跳过这一行
			continue
		}

		// 7. 收集匹配行信息
		matchedLines = append(matchedLines, MatchedLine{
			FilePath: filePath,
			LineNum:  uint32(lineNum),
			Content:  content,
		})
	}

	// 9. 扩展上下文并合并重叠的代码块
	contextLines := DefaultContextLines // 默认扩展2行
	if r.options != nil && r.options.ContextExpandLineLength > 0 {
		contextLines = r.options.ContextExpandLineLength
	}
	expandedChunks, err := r.expandAndMergeChunks(matchedLines, contextLines)
	if err != nil {
		return result, fmt.Errorf("failed to expand and merge chunks: %w", err)
	}

	// 10. 转化为RetrieveChunk
	for _, expandedChunk := range expandedChunks {
		chunk := RetrieveChunk{
			CodeChunk: indexer.CodeChunk{
				Content:   expandedChunk.Content,
				FilePath:  expandedChunk.FilePath,
				FileName:  extractFileName(expandedChunk.FilePath),
				StartLine: expandedChunk.StartLine,
				EndLine:   expandedChunk.EndLine,
				Type:      "context", // 标识为上下文匹配
			},
			Score: 1.0, // 默认分数，后续可根据匹配相关性调整
		}

		// 11. 生成唯一ID（使用整个chunk内容）
		chunk.Id = generateChunkId(expandedChunk.FilePath, expandedChunk.Content, expandedChunk.StartLine)

		result.Chunks = append(result.Chunks, chunk)
	}

	return result, nil
}

// expandAndMergeChunks
// 扩展上下文并合并重叠的代码块
func (r *RipGrepRetrieveEngine) expandAndMergeChunks(matchedLines []MatchedLine, contextLines int) ([]ExpandedChunk, error) {
	// 按文件路径分组
	fileGroups := make(map[string][]MatchedLine)
	for _, line := range matchedLines {
		fileGroups[line.FilePath] = append(fileGroups[line.FilePath], line)
	}

	var result []ExpandedChunk

	for filePath, lines := range fileGroups {
		// 按行号排序
		sort.Slice(lines, func(i, j int) bool {
			return lines[i].LineNum < lines[j].LineNum
		})

		// 获取文件最大行数
		maxLineNum, err := getFileLineCount(filePath)
		if err != nil {
			log.Errorf("Failed to get line count for file %s: %v", filePath, err)
			continue
		}

		// 扩展上下文（各择contextLines行）
		var expandedRanges []struct {
			startLine, endLine uint32
		}

		for _, line := range lines {
			var startLine uint32
			if line.LineNum > uint32(contextLines) {
				startLine = line.LineNum - uint32(contextLines)
			} else {
				startLine = 1
			}

			// 确保endLine不超过文件最大行数
			endLine := line.LineNum + uint32(contextLines)
			if endLine > maxLineNum {
				endLine = maxLineNum
			}

			expandedRanges = append(expandedRanges, struct {
				startLine, endLine uint32
			}{startLine, endLine})
		}

		// 合并重叠的范围
		mergedRanges := r.mergeOverlappingRanges(expandedRanges)

		// 生成最终的chunk
		for _, mergedRange := range mergedRanges {
			// 收集该范围内的匹配行
			var matchedLinesInRange []uint32
			for _, line := range lines {
				if line.LineNum >= mergedRange.startLine && line.LineNum <= mergedRange.endLine {
					matchedLinesInRange = append(matchedLinesInRange, line.LineNum)
				}
			}

			// 获取大小限制
			maxSizeBytes := DefaultMaxChunkSizeBytes
			if r.options != nil && r.options.MaxChunkSizeBytes > 0 {
				maxSizeBytes = r.options.MaxChunkSizeBytes
			}

			// 按大小限制截断内容
			content, actualStartLine, actualEndLine, err := r.truncateChunkBySize(
				filePath, mergedRange.startLine, mergedRange.endLine, matchedLinesInRange, maxSizeBytes)
			if err != nil {
				log.Errorf("Failed to truncate chunk for file %s lines %d-%d: %v", filePath, mergedRange.startLine, mergedRange.endLine, err)
				continue
			}

			if content != "" {
				result = append(result, ExpandedChunk{
					FilePath:  filePath,
					StartLine: actualStartLine,
					EndLine:   actualEndLine,
					Content:   content,
				})
			}
		}
	}

	return result, nil
}

// mergeOverlappingRanges
// 合并重叠的行范围
func (r *RipGrepRetrieveEngine) mergeOverlappingRanges(ranges []struct{ startLine, endLine uint32 }) []struct{ startLine, endLine uint32 } {
	if len(ranges) == 0 {
		return nil
	}

	// 按起始行排序
	sort.Slice(ranges, func(i, j int) bool {
		return ranges[i].startLine < ranges[j].startLine
	})

	var merged []struct{ startLine, endLine uint32 }
	current := ranges[0]

	for i := 1; i < len(ranges); i++ {
		next := ranges[i]
		// 检查是否重叠或相邻
		if next.startLine <= current.endLine+1 {
			// 合并范围
			if next.endLine > current.endLine {
				current.endLine = next.endLine
			}
		} else {
			// 不重叠，添加当前范围并更新
			merged = append(merged, current)
			current = next
		}
	}

	// 添加最后一个范围
	merged = append(merged, current)
	return merged
}

// truncateChunkBySize
// 按大小限制截断代码块，优先保留匹配行
func (r *RipGrepRetrieveEngine) truncateChunkBySize(filePath string, startLine, endLine uint32, matchedLines []uint32, maxSizeBytes int) (string, uint32, uint32, error) {
	// 读取所有行
	allLines, err := readFileLines(filePath, startLine, endLine)
	if err != nil {
		return "", startLine, endLine, err
	}

	if len(allLines) == 0 {
		return "", startLine, endLine, nil
	}

	// 先计算匹配行的大小
	matchedLinesSet := make(map[uint32]bool)
	for _, lineNum := range matchedLines {
		matchedLinesSet[lineNum] = true
	}

	var matchedContent []string
	matchedSize := 0
	for i, line := range allLines {
		currentLineNum := startLine + uint32(i)
		if matchedLinesSet[currentLineNum] {
			matchedContent = append(matchedContent, line)
			matchedSize += len(line) + 1 // +1 for newline
		}
	}

	// 如果匹配行已超出限制，只返回匹配行
	if matchedSize > maxSizeBytes {
		content := strings.Join(matchedContent, "\n")
		// 找到匹配行的范围
		minMatchedLine := ^uint32(0)
		maxMatchedLine := uint32(0)
		for lineNum := range matchedLinesSet {
			if lineNum < minMatchedLine {
				minMatchedLine = lineNum
			}
			if lineNum > maxMatchedLine {
				maxMatchedLine = lineNum
			}
		}
		return content, minMatchedLine, maxMatchedLine, nil
	}

	// 按距离排序的上下文行
	type ContextLine struct {
		index    int
		lineNum  uint32
		content  string
		distance int // 到最近匹配行的距离
	}

	var contextLines []ContextLine
	for i, line := range allLines {
		currentLineNum := startLine + uint32(i)
		if !matchedLinesSet[currentLineNum] {
			// 计算到最近匹配行的距离
			minDistance := int(^uint(0) >> 1) // max int
			for matchedLine := range matchedLinesSet {
				dist := int(currentLineNum) - int(matchedLine)
				if dist < 0 {
					dist = -dist
				}
				if dist < minDistance {
					minDistance = dist
				}
			}
			contextLines = append(contextLines, ContextLine{
				index:    i,
				lineNum:  currentLineNum,
				content:  line,
				distance: minDistance,
			})
		}
	}

	// 按距离排序，距离近的优先级高
	sort.Slice(contextLines, func(i, j int) bool {
		return contextLines[i].distance < contextLines[j].distance
	})

	// 逐行添加上下文，直到超出限制
	includedLines := make(map[int]bool)
	// 先标记匹配行
	for i, _ := range allLines {
		currentLineNum := startLine + uint32(i)
		if matchedLinesSet[currentLineNum] {
			includedLines[i] = true
		}
	}

	currentSize := matchedSize
	for _, contextLine := range contextLines {
		lineSize := len(contextLine.content) + 1 // +1 for newline
		if currentSize+lineSize <= maxSizeBytes {
			includedLines[contextLine.index] = true
			currentSize += lineSize
		} else {
			break // 超出限制，停止添加
		}
	}

	// 构建最终内容和范围
	var finalLines []string
	finalStartLine := startLine
	finalEndLine := startLine

	for i, line := range allLines {
		if includedLines[i] {
			finalLines = append(finalLines, line)
			currentLineNum := startLine + uint32(i)
			if currentLineNum < finalStartLine {
				finalStartLine = currentLineNum
			}
			if currentLineNum > finalEndLine {
				finalEndLine = currentLineNum
			}
		}
	}

	// 重新排序行号以保持顺序
	type LineWithNum struct {
		content string
		lineNum uint32
	}
	var sortedLines []LineWithNum
	for i, line := range allLines {
		if includedLines[i] {
			sortedLines = append(sortedLines, LineWithNum{
				content: line,
				lineNum: startLine + uint32(i),
			})
		}
	}
	sort.Slice(sortedLines, func(i, j int) bool {
		return sortedLines[i].lineNum < sortedLines[j].lineNum
	})

	finalLines = nil
	for _, item := range sortedLines {
		finalLines = append(finalLines, item.content)
	}

	return strings.Join(finalLines, "\n"), finalStartLine, finalEndLine, nil
}

// getLineRangeContent
// 获取指定行范围的内容
func (r *RipGrepRetrieveEngine) getLineRangeContent(filePath string, startLine, endLine uint32) (string, error) {
	lines, err := readFileLines(filePath, startLine, endLine)
	if err != nil {
		return "", err
	}

	return strings.Join(lines, "\n"), nil
}

// readFileLines
// 读取文件指定行范围的内容
func readFileLines(filePath string, startLine, endLine uint32) ([]string, error) {
	// 边界检查
	if startLine < 1 {
		startLine = 1
	}
	if startLine > endLine {
		return nil, fmt.Errorf("invalid line range: start %d > end %d", startLine, endLine)
	}

	// 读取文件
	file, err := os.Open(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to open file %s: %w", filePath, err)
	}
	defer file.Close()

	var result []string
	scanner := bufio.NewScanner(file)
	currentLine := uint32(0)

	// 读取文件并筛选指定范围的行
	for scanner.Scan() {
		currentLine++
		if currentLine >= startLine && currentLine <= endLine {
			result = append(result, scanner.Text())
		}
	}

	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("failed to read file %s: %w", filePath, err)
	}

	return result, nil
}

// extractFileName
// 从文件路径中提取文件名
func extractFileName(filePath string) string {
	return filepath.Base(filePath)
}

// generateChunkId
// 生成代码块的唯一ID（使用文件路径+内容+行号的MD5）
func generateChunkId(filePath, content string, lineNum uint32) string {
	idSource := fmt.Sprintf("%s_%s_%d", filePath, content, lineNum)
	hash := md5.Sum([]byte(idSource))
	return fmt.Sprintf("%x", hash)
}

// getFileLineCount
// 获取文件的总行数
func getFileLineCount(filePath string) (uint32, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return 0, fmt.Errorf("failed to open file %s: %w", filePath, err)
	}
	defer file.Close()

	var lineCount uint32
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		lineCount++
	}

	if err := scanner.Err(); err != nil {
		return 0, fmt.Errorf("failed to scan file %s: %w", filePath, err)
	}

	return lineCount, nil
}
