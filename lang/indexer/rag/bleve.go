package rag

import (
	"context"
	"cosy/deepwiki/storage"
	"cosy/definition"
	"cosy/lang/indexer"
	"cosy/log"
	"cosy/storage/blevesearch"
	"cosy/tokenizer"
	"cosy/util"
	"cosy/util/rag"
	"crypto/md5"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync/atomic"

	"github.com/blevesearch/bleve/v2"
	"github.com/blevesearch/bleve/v2/analysis/analyzer/custom"
	"github.com/blevesearch/bleve/v2/analysis/analyzer/keyword"
	"github.com/blevesearch/bleve/v2/analysis/lang/cjk"
	"github.com/blevesearch/bleve/v2/analysis/lang/en"
	"github.com/blevesearch/bleve/v2/analysis/token/camelcase"
	"github.com/blevesearch/bleve/v2/analysis/token/lowercase"
	"github.com/blevesearch/bleve/v2/analysis/tokenizer/unicode"
	"github.com/blevesearch/bleve/v2/mapping"
	"github.com/blevesearch/bleve/v2/search"
	"github.com/blevesearch/bleve/v2/search/query"
	"github.com/pkg/errors"
)

const (
	ChatIndexType       = "chat"
	CompletionIndexType = "completion"
	MemoryIndexType     = "memory"
)

const (
	CjkAnalyzerName  = "custom_cjk"
	CodeAnalyzerName = "code"

	// 索引时每次批量索引的文档数量
	MaxChunkCountEachBatch = 100

	ChatBleveVersion       = "v4"
	CompletionBleveVersion = "v4"
	MemoryBleveVersion     = "v4"
)

const (
	ChunkDocType = "code_chunk"
	FileDocType  = "file_info"
)

type FileIndexFile struct {
	FilePath string `json:"file_path"`
	DocType  string `json:"doc_type"`
	Md5      string `json:"md5"`
}

type BleveRetrieveEngine struct {
	WorkspaceUri string
	client       *blevesearch.BevelSearchClient
	indexType    string
	indexCount   atomic.Int64
}

func NewChatBleveRetrieveEngine(workspaceUri string) *BleveRetrieveEngine {
	return newBleveRetrieveEngine(workspaceUri, ChatIndexType, ChatBleveVersion)
}

func NewCompletionBleveRetrieveEngine(workspaceUri string) *BleveRetrieveEngine {
	return newBleveRetrieveEngine(workspaceUri, CompletionIndexType, CompletionBleveVersion)
}

func NewMemoryBleveRetrieveEngine(workspaceUri string) *BleveRetrieveEngine {
	return newBleveRetrieveEngine(workspaceUri, MemoryIndexType, MemoryBleveVersion)
}

func newBleveRetrieveEngine(workspaceUri, indexType, version string) *BleveRetrieveEngine {
	indexRootPath := filepath.Join(util.GetCosyHomePath(), "index", indexType)
	rag.ClearIndexDir(indexRootPath, version)
	hash := fmt.Sprintf("%x", md5.Sum([]byte(workspaceUri)))
	dirName := filepath.Base(workspaceUri)
	indexPath := filepath.Join(indexRootPath, version, fmt.Sprintf("%s_%s", dirName, hash))
	log.Debug("index text retrieve path: ", indexPath)
	client := blevesearch.NewBevelSearchClient(indexPath, CjkAnalyzerName)
	return &BleveRetrieveEngine{
		WorkspaceUri: workspaceUri,
		client:       client,
		indexType:    indexType,
		indexCount:   atomic.Int64{},
	}
}

func NewBleveRetrieveEngineWithPath(workspaceUri, indexType, indexPath string) *BleveRetrieveEngine {
	log.Debug("index text retrieve path: ", indexPath)
	client := blevesearch.NewBevelSearchClient(indexPath, CjkAnalyzerName)
	return &BleveRetrieveEngine{
		WorkspaceUri: workspaceUri,
		client:       client,
		indexType:    indexType,
	}
}

func (b *BleveRetrieveEngine) Initialize() error {
	indexMapping, err := b.buildBleveIndexMapping()
	if err != nil {
		return err
	}
	_, err = b.client.ConfigIndexMapping(indexMapping).CreateIndex()
	if err != nil {
		return err
	}
	return nil
}

func (b *BleveRetrieveEngine) BatchIndex(filePaths []string, enableCheckChange bool) ([]string, error) {
	_, files, err := b.IndexBatchChunks(filePaths, enableCheckChange)
	return files, err
}

func (b *BleveRetrieveEngine) IndexBatchChunks(filePaths []string, enableCheckChange bool) ([]indexer.CodeChunk, []string, error) {
	if b.client == nil || !b.client.IsValid() {
		return nil, nil, errors.New("retrieve client isn't initialized.")
	}
	chunks := []indexer.CodeChunk{}
	batchChunks := map[string]any{}
	indexFiles := make([]FileIndexFile, 0, 50)
	// changeFiles 用于记录所有存在变更的文件，用于在外面删除向量库的已变更文件
	changeFiles := make([]string, 0, 32)
	for _, filePath := range filePaths {
		defer func() {
			if r := recover(); r != nil {
				err := errors.Errorf("Recovered from panic: %v", r)
				log.Error("index file ", filePath, " chunk crash:", r, " panic:", fmt.Sprintf("%+v", err)) // 获取堆栈信息
			}
		}()

		code, err := b.ReadFile(filePath)
		if err != nil || isNeedFilterContent(code) {
			continue
		}
		b.indexCount.Add(1)
		if enableCheckChange {
			// 检查文件是否被修改，如果被修改需要删除老chunk
			fileExist, fileChanged := b.checkFileChanged(filePath, code)
			if fileChanged {
				// 立即删除旧索引，避免新旧索引并存
				err := b.BatchRemove([]string{filePath})
				if err != nil {
					log.Error("failed to remove old index for file ", filePath, " error: ", err)
					// 删除失败时跳过该文件的重新索引，避免新旧索引并存
					continue
				}
				changeFiles = append(changeFiles, filePath)
			}
			if fileExist && !fileChanged {
				continue
			}
		}
		result, err := SplitCodeChunksByIndexType(b.indexType, b.WorkspaceUri, filePath, code)
		if err != nil {
			if !errors.Is(err, definition.ErrNotSupportLanguage) {
				log.Warn("get file ", filePath, " chunk error: ", err)
			}
			continue
		}
		if result.Chunks != nil {
			indexFiles = append(indexFiles, FileIndexFile{
				FilePath: filePath,
				DocType:  FileDocType,
				Md5:      fmt.Sprintf("%x", md5.Sum(code)),
			})
			for _, chunk := range result.Chunks {
				if chunk.Language == "" {
					chunk.Language = util.GetLanguageByFilePath(chunk.FilePath)
				}
				if chunk.FileExtension == "" {
					chunk.FileExtension = filepath.Ext(chunk.FilePath)
				}
				chunk.DocType = ChunkDocType
				chunk.CodeCategory = indexer.DetectCodeCategory(chunk.FilePath)
				batchChunks[chunk.Id] = chunk

				// TODO 这里添加了filepath进入chunk和focus，看看是否能够增强召回效果
				chunkStringFilePath := strings.Join([]string{"filepath:", chunk.FilePath}, " ")
				chunk.Content = strings.Join([]string{chunkStringFilePath, chunk.Content}, "\n")
				chunk.IndexContent = strings.Join([]string{chunkStringFilePath, chunk.IndexContent}, "\n")
				chunk.IndexFocus = strings.Join([]string{chunkStringFilePath, chunk.IndexFocus}, "\n")
				chunks = append(chunks, chunk)
			}
			if len(batchChunks) >= MaxChunkCountEachBatch {
				err := b.client.AddBatchDoc(batchChunks)
				if err != nil {
					log.Error("failed to add batch chunks error: ", err)
					continue
				}
				b.recordFileIndex(indexFiles)
				batchChunks = map[string]any{}
				indexFiles = make([]FileIndexFile, 0, 50)
			}
		}
	}
	if len(batchChunks) > 0 {
		err := b.client.AddBatchDoc(batchChunks)
		if err != nil {
			return chunks, changeFiles, err
		}
		b.recordFileIndex(indexFiles)
	}
	return chunks, changeFiles, nil
}

func (b *BleveRetrieveEngine) ReadFile(filePath string) ([]byte, error) {
	if b.indexType == MemoryIndexType {
		// 去掉 .md
		filePath = strings.TrimSuffix(filePath, ".md")
		wikiItems, err := storage.GlobalStorageService.GetWikiItemByID(filePath)
		if err != nil {
			return nil, err
		}
		return []byte(wikiItems.Content), nil
	}
	return os.ReadFile(filePath)
}

func (b *BleveRetrieveEngine) recordFileIndex(indexFiles []FileIndexFile) {
	if b.client == nil || !b.client.IsValid() {
		return
	}
	if len(indexFiles) == 0 {
		return
	}
	batchFiles := map[string]any{}
	for _, indexFile := range indexFiles {
		batchFiles[indexFile.FilePath] = indexFile
	}
	err := b.client.AddBatchDoc(batchFiles)
	if err != nil {
		log.Error("failed to add batch file index error: ", err)
	}
}

// checkFileChanged 校验文件变更
// 1. 索引文件不存在，则新增索引
// 2. 索引文件存在，且md5不同，则更新索引
// 3. 索引文件存在，且md5相同，则不更新索引
func (b *BleveRetrieveEngine) checkFileChanged(filePath string, codeBytes []byte) (bool, bool) {
	if b.client == nil || !b.client.IsValid() {
		return false, false
	}
	targetMd5 := fmt.Sprintf("%x", md5.Sum(codeBytes))
	request := bleve.NewSearchRequestOptions(bleve.NewDocIDQuery([]string{filePath}), 1, 0, false)
	request.Fields = []string{"*"}
	sr := b.client.Search(request)
	if sr.Total > 0 && len(sr.Hits) > 0 {
		hit := sr.Hits[0]
		hitMd5 := getStringValue(hit.Fields, "md5")
		return true, hitMd5 != targetMd5
	} else {
		return false, false
	}
}

func (b *BleveRetrieveEngine) BatchRemove(filePaths []string) error {
	if b.client == nil || !b.client.IsValid() {
		return errors.New("retrieve client isn't initialized.")
	}

	successCount := 0
	failCount := 0
	var lastErr error
	successRecords := map[string]int{}
	failRecords := map[string]int{}

	for _, filePath := range filePaths {
		fileSuccessCount := 0
		fileFailCount := 0

		// 检查是否是文件夹：通过检查是否有以该路径开头的其他文件
		// 使用filepath.Separator确保跨平台兼容性
		dirCheckPath := filePath
		if !strings.HasSuffix(dirCheckPath, string(filepath.Separator)) {
			dirCheckPath = dirCheckPath + string(filepath.Separator)
		}
		sr := b.matchFieldWithWildcard(ChunkDocType, "file_path", dirCheckPath, 1)
		isDir := sr.Status.Total > 0

		if isDir {
			// 如果是文件夹，使用通配符匹配该文件夹下的所有文件
			wildcardPath := dirCheckPath + "*"

			// 循环删除，直到所有匹配的文档都被删除
			for {
				sr := b.matchFieldWithWildcard(ChunkDocType, "file_path", wildcardPath, 1000)
				if sr.Total == 0 || len(sr.Hits) == 0 {
					break // 没有更多的匹配项，退出循环
				}

				// 收集所有匹配文档的ID
				ids := make([]string, 0, len(sr.Hits))
				for _, hit := range sr.Hits {
					ids = append(ids, hit.ID)
				}

				// 使用BatchDelete批量删除代码块
				err := b.client.BatchDelete(ids)
				if err != nil {
					log.Warn("failed to batch delete chunks for path ", wildcardPath, " error: ", err)
					fileFailCount += len(ids)
					lastErr = err
				} else {
					fileSuccessCount += len(ids)
				}
			}
		} else {
			// 处理单个文件：先删除代码块，再删除文件记录
			var chunkIds []string
			var fileRecordExists bool

			// 1. 查找所有相关的代码块
			sr := b.matchField(ChunkDocType, "file_path", filePath, 100)
			if sr.Total > 0 {
				chunkIds = make([]string, 0, len(sr.Hits))
				for _, hit := range sr.Hits {
					chunkIds = append(chunkIds, hit.ID)
				}
			}

			// 2. 检查文件记录是否存在
			request := bleve.NewSearchRequestOptions(bleve.NewDocIDQuery([]string{filePath}), 1, 0, false)
			request.Fields = []string{"*"}
			fileSearchResult := b.client.Search(request)
			fileRecordExists = fileSearchResult.Total > 0

			// 3. 删除代码块
			if len(chunkIds) > 0 {
				err := b.client.BatchDelete(chunkIds)
				if err != nil {
					log.Warn("failed to batch delete chunks for file ", filePath, " error: ", err)
					fileFailCount += len(chunkIds)
					lastErr = err
				} else {
					fileSuccessCount += len(chunkIds)
				}
			}

			// 4. 如果代码块删除成功（或者没有代码块），再删除文件记录
			if fileFailCount == 0 && fileRecordExists {
				err := b.client.Delete(filePath)
				if err != nil {
					log.Warn("failed to delete file index ", filePath, " error: ", err)
					// 如果文件记录删除失败，但代码块已经删除，记录这个错误但不回滚
					// 因为Bleve不支持事务，回滚会更复杂
					lastErr = err
				} else {
					log.Debug("successfully deleted file record for ", filePath)
				}
			}

			failCount += fileFailCount
			successCount += fileSuccessCount
		}

		// 记录每个文件的删除结果
		if fileFailCount > 0 {
			failRecords[filePath] = fileFailCount
		}
		if fileSuccessCount > 0 {
			successRecords[filePath] = fileSuccessCount
		}

		successCount += fileSuccessCount
		failCount += fileFailCount
	}

	// 更新索引计数：只减去成功删除的文件数量
	if successCount > 0 {
		b.indexCount.Add(int64(-len(filePaths)))
	}

	if failCount > 0 || successCount > 0 {
		log.Infof("batch remove file chunks success: %d, fail: %d, successRecords: %v, failRecords: %v",
			successCount, failCount, successRecords, failRecords)
	}

	return lastErr
}

func (b *BleveRetrieveEngine) Retrieve(ctx context.Context, textQuery TextQuery) (RetrieveResult, error) {
	if b.client == nil || !b.client.IsValid() {
		return RetrieveResult{}, errors.New("retrieve client isn't initialized.")
	}
	result := RetrieveResult{
		Chunks: []RetrieveChunk{},
		Source: TextRetrieveSource,
	}
	var searchQuery query.Query
	if len(textQuery.Conditions) > 0 && textQuery.Operator != "" {
		searchQuery = b.buildQueryByConjunctionMode(textQuery)
	} else {
		searchQuery = b.buildQueryByMustShouldMode(textQuery)
	}
	search := bleve.NewSearchRequestOptions(searchQuery, textQuery.Size, textQuery.From, false)
	if textQuery.Fields == nil || len(textQuery.Fields) == 0 {
		textQuery.Fields = []string{"*"}
	}
	search.Fields = textQuery.Fields
	search.Highlight = bleve.NewHighlight()
	sr := b.client.Search(search)
	if sr.Total > 0 {
		for _, hit := range sr.Hits {
			chunk := b.hitToChunk(hit)
			// 过滤掉文件不存在的 chunk（FilePath 为空表示文件不存在）
			if chunk.FilePath != "" {
				result.Chunks = append(result.Chunks, chunk)
			}
		}
	}
	return result, nil
}

func (b *BleveRetrieveEngine) buildQueryByMustShouldMode(textQuery TextQuery) query.Query {
	mustQuery := buildConditionQuery(textQuery.Must)
	shouldQuery := buildConditionQuery(textQuery.Should)
	mustNotQuery := buildConditionQuery(textQuery.MustNot)
	q := bleve.NewTermQuery(ChunkDocType)
	q.SetField("doc_type")
	mustQuery = append(mustQuery, q)
	return query.NewBooleanQuery(mustQuery, shouldQuery, mustNotQuery)
}

func (b *BleveRetrieveEngine) buildQueryByConjunctionMode(textQuery TextQuery) query.Query {
	// 处理普通条件
	var textCondition query.Query
	if len(textQuery.Conditions) > 0 {
		subQueries := buildConditionQuery(textQuery.Conditions)
		if textQuery.Operator == MatchOr {
			textCondition = bleve.NewDisjunctionQuery(subQueries...)
		} else {
			textCondition = bleve.NewConjunctionQuery(subQueries...)
		}
	}

	// 处理路径过滤条件
	var pathCondition query.Query
	if len(textQuery.Must) > 0 {
		pathQueries := buildConditionQuery(textQuery.Must)
		pathCondition = bleve.NewConjunctionQuery(pathQueries...)
	}

	// 组合条件
	var finalCondition query.Query
	q := bleve.NewTermQuery(ChunkDocType)
	q.SetField("doc_type")

	queries := []query.Query{q}

	if textCondition != nil {
		queries = append(queries, textCondition)
	}

	if pathCondition != nil {
		queries = append(queries, pathCondition)
	}

	// 所有条件必须同时满足
	finalCondition = bleve.NewConjunctionQuery(queries...)

	return finalCondition
}

func buildConditionQuery(conditions []TextCondition) []query.Query {
	if len(conditions) == 0 {
		return nil
	}
	queries := make([]query.Query, 0, 4)
	for _, cond := range conditions {
		var q query.Query
		if cond.FieldName == "file_path" && strings.HasSuffix(cond.Query, "*") {
			// 处理通配符查询
			wildcardQuery := bleve.NewWildcardQuery(cond.Query)
			wildcardQuery.SetField(cond.FieldName)
			if cond.Boost > 0 {
				wildcardQuery.SetBoost(cond.Boost)
			}
			q = wildcardQuery
		} else if cond.FieldName == "file_path" {
			// 添加通配符以支持前缀匹配
			wildcardQuery := bleve.NewWildcardQuery(cond.Query + "*")
			wildcardQuery.SetField(cond.FieldName)
			if cond.Boost > 0 {
				wildcardQuery.SetBoost(cond.Boost)
			}
			q = wildcardQuery
		} else {
			// 对于其他字段，使用普通匹配
			matchQuery := bleve.NewMatchQuery(cond.Query)
			matchQuery.SetField(cond.FieldName)
			if cond.Boost > 0 {
				matchQuery.SetBoost(cond.Boost)
			}
			q = matchQuery
		}
		queries = append(queries, q)
	}
	return queries
}

func (b *BleveRetrieveEngine) BatchGetFileChunks(filePaths []string, topN int) ([]indexer.CodeChunk, error) {
	if b.client == nil || !b.client.IsValid() {
		return nil, errors.New("retrieve client isn't initialized.")
	}
	totalChunks := []indexer.CodeChunk{}
	for _, path := range filePaths {
		absPath := filepath.Join(b.WorkspaceUri, path)
		if filePath, ok := rag.RepairFilepath(absPath); ok {
			chunks, err := b.GetFileChunks(filePath, topN)
			if err == nil && len(chunks) > 0 {
				totalChunks = append(totalChunks, chunks...)
			}
		} else {
			log.Debugf("GetFileChunk file not exist. path: %s", path)
		}
	}
	return totalChunks, nil
}

// GetFileChunks 获取指定文件的chunks
// 找不到chunk时，重新索引后返回切块
func (b *BleveRetrieveEngine) GetFileChunks(filePath string, topN int) ([]indexer.CodeChunk, error) {
	if b.client == nil || !b.client.IsValid() {
		return nil, errors.New("retrieve client isn't initialized.")
	}
	chunks := make([]indexer.CodeChunk, 0, 8)
	sr := b.matchField(ChunkDocType, "file_path", filePath, topN)
	if sr.Total > 0 {
		for _, hit := range sr.Hits {
			chunk := b.hitToChunk(hit)
			// 过滤掉文件不存在的 chunk（FilePath 为空表示文件不存在）
			if chunk.FilePath != "" {
				chunks = append(chunks, chunk.CodeChunk)
			}
		}
	} else {
		log.Warn("no chunks found for file: ", filePath)
		fileChunks, _, err := b.IndexBatchChunks([]string{filePath}, true)
		if err != nil {
			log.Error("index file ", filePath, " chunks error: ", err)
		}
		if fileChunks != nil && len(fileChunks) > 0 {
			chunks = append(chunks, fileChunks...)
		}
	}
	return chunks, nil
}

func (b *BleveRetrieveEngine) GetDocumentTotalCount() int64 {
	if b == nil {
		return 0
	}
	if b.client != nil {
		return b.client.DocCount()
	}
	return 0
}

func (b *BleveRetrieveEngine) Close() {
	if b.client != nil {
		b.client.Close()
	}
}

// matchField 匹配指定字段
func (b *BleveRetrieveEngine) matchField(docType string, fieldName string, query string, topN int) *bleve.SearchResult {
	fileQuery := bleve.NewTermQuery(query)
	fileQuery.SetField(fieldName)
	typeQuery := bleve.NewTermQuery(docType)
	typeQuery.SetField("doc_type")
	condition := bleve.NewConjunctionQuery(fileQuery, typeQuery)
	search := bleve.NewSearchRequestOptions(condition, topN, 0, false)
	search.Fields = []string{"*"}
	return b.client.Search(search)
}

// matchFieldWithWildcard 匹配指定字段，支持通配符
func (b *BleveRetrieveEngine) matchFieldWithWildcard(docType string, fieldName string, query string, topN int) *bleve.SearchResult {
	fileQuery := bleve.NewWildcardQuery(query)
	fileQuery.SetField(fieldName)
	typeQuery := bleve.NewTermQuery(docType)
	typeQuery.SetField("doc_type")
	condition := bleve.NewConjunctionQuery(fileQuery, typeQuery)
	search := bleve.NewSearchRequestOptions(condition, topN, 0, false)
	search.Fields = []string{"*"}
	return b.client.Search(search)
}

// hitToChunk 将hit转换为chunk
// 如果文件不存在，返回的 chunk 中 FilePath 会被设置为空字符串，调用方应过滤掉这些 chunk
func (b *BleveRetrieveEngine) hitToChunk(hit *search.DocumentMatch) RetrieveChunk {
	filePath := getStringValue(hit.Fields, "file_path")
	startLine := getUint32Value(hit.Fields, "start_line")
	endLine := getUint32Value(hit.Fields, "end_line")

	// 根据索引类型选择不同的内容获取方式
	content := ""
	if b.indexType == MemoryIndexType {
		// Memory模式：直接从Bleve存储的content字段读取内容
		content = getStringValue(hit.Fields, "content")
	} else {
		// 其他模式：按照记录的起始行号从文件中获取内容
		if filePath != "" && endLine >= startLine {
			fileContent, err := rag.GetFileContentByLine(filePath, startLine, endLine)
			if err != nil {
				// 文件不存在或读取失败，标记为需要过滤的 chunk
				log.Debugf("Failed to read file content for %s: %v", filePath, err)
				// TODO: 应该有通知机制来删除这个文件的索引
				filePath = "" // 标记文件不存在
			} else {
				content = fileContent
			}
		}
	}

	return RetrieveChunk{
		CodeChunk: indexer.CodeChunk{
			Id:               hit.ID,
			FilePath:         filePath,
			FileName:         getStringValue(hit.Fields, "file_name"),
			Content:          content,
			IndexContent:     getStringValue(hit.Fields, "index_content"),
			IndexFocus:       getStringValue(hit.Fields, "index_focus"),
			Type:             getStringValue(hit.Fields, "type"),
			StartOffset:      getUint32Value(hit.Fields, "start_offset"),
			EndOffset:        getUint32Value(hit.Fields, "end_offset"),
			StartLine:        startLine,
			EndLine:          endLine,
			DocType:          getStringValue(hit.Fields, "doc_type"),
			CodeCategory:     getStringValue(hit.Fields, "code_category"),
			Language:         getStringValue(hit.Fields, "language"),
			FileExtension:    getStringValue(hit.Fields, "file_extension"),
			CommentStartLine: getInt32Value(hit.Fields, "comment_start_line"),
		},
		Score: hit.Score,
	}
}

func getStringValue(fields map[string]interface{}, fieldName string) string {
	if value, ok := fields[fieldName]; ok {
		return value.(string)
	}
	return ""
}

func getUint32Value(fields map[string]interface{}, fieldName string) uint32 {
	if value, ok := fields[fieldName]; ok {
		if fVal, ok2 := value.(float64); ok2 {
			return uint32(fVal)
		}
	}
	return 0
}
func getInt32Value(fields map[string]interface{}, fieldName string) int32 {
	if value, ok := fields[fieldName]; ok {
		if fVal, ok2 := value.(float64); ok2 {
			return int32(fVal)
		}
	}
	return 0
}
func getUint32PtrValue(fields map[string]interface{}, fieldName string) *uint32 {
	if value, ok := fields[fieldName]; ok {
		if fVal, ok2 := value.(float64); ok2 {
			ret := new(uint32)
			*ret = uint32(fVal)
			return ret
		}
	}
	return nil
}

func (b *BleveRetrieveEngine) buildBleveIndexMapping() (mapping.IndexMapping, error) {
	// a generic reusable mapping for english text
	cjkTextFieldMapping := bleve.NewTextFieldMapping()
	cjkTextFieldMapping.Analyzer = CjkAnalyzerName

	codeTextFieldMapping := bleve.NewTextFieldMapping()
	codeTextFieldMapping.Analyzer = CodeAnalyzerName

	// a generic reusable mapping for keyword text
	keywordFieldMapping := bleve.NewTextFieldMapping()
	keywordFieldMapping.Analyzer = keyword.Name
	numberFieldMapping := bleve.NewNumericFieldMapping()
	numberFieldMapping.Analyzer = keyword.Name

	// 用于存储代码元数据
	metaMapping := bleve.NewDocumentMapping()
	metaMapping.AddFieldMappingsAt("file_name", keywordFieldMapping)
	metaMapping.AddFieldMappingsAt("file_path", keywordFieldMapping)
	metaMapping.AddFieldMappingsAt("content", codeTextFieldMapping)
	metaMapping.AddFieldMappingsAt("index_content", codeTextFieldMapping)
	metaMapping.AddFieldMappingsAt("index_focus", codeTextFieldMapping)
	metaMapping.AddFieldMappingsAt("type", keywordFieldMapping)
	metaMapping.AddFieldMappingsAt("doc_type", keywordFieldMapping)
	metaMapping.AddFieldMappingsAt("start_offset", numberFieldMapping)
	metaMapping.AddFieldMappingsAt("end_offset", numberFieldMapping)
	metaMapping.AddFieldMappingsAt("start_line", numberFieldMapping)
	metaMapping.AddFieldMappingsAt("end_line", numberFieldMapping)
	metaMapping.AddFieldMappingsAt("code_category", keywordFieldMapping)
	metaMapping.AddFieldMappingsAt("language", keywordFieldMapping)
	metaMapping.AddFieldMappingsAt("file_extension", keywordFieldMapping)
	metaMapping.AddFieldMappingsAt("comment_start_line", numberFieldMapping)

	// 用于存储代码文件级信息，判断文件是否被修改
	fileMapping := bleve.NewDocumentMapping()
	fileMapping.AddFieldMappingsAt("md5", keywordFieldMapping)
	fileMapping.AddFieldMappingsAt("file_path", keywordFieldMapping)
	fileMapping.AddFieldMappingsAt("doc_type", keywordFieldMapping)

	indexMapping := bleve.NewIndexMapping()
	err := indexMapping.AddCustomAnalyzer(CjkAnalyzerName,
		map[string]interface{}{
			"type":      custom.Name,
			"tokenizer": unicode.Name,
			"token_filters": []string{
				camelcase.Name,
				cjk.WidthName,
				lowercase.Name,
				en.StopName,
				cjk.BigramName,
			},
		})
	if err != nil {
		return nil, err
	}
	codeFilter := []string{}
	if b.indexType == ChatIndexType {
		// 只有问答的再进一步拆驼峰，减少补全rag的误召回
		codeFilter = append(codeFilter, blevesearch.CamelCaseExtName)
	}
	codeFilter = append(codeFilter, cjk.WidthName)
	codeFilter = append(codeFilter, lowercase.Name)
	codeFilter = append(codeFilter, en.StopName)
	err = indexMapping.AddCustomAnalyzer(CodeAnalyzerName,
		map[string]interface{}{
			"type":          custom.Name,
			"tokenizer":     tokenizer.JiebaTokenizerName,
			"token_filters": codeFilter,
		})
	if err != nil {
		return nil, err
	}
	indexMapping.AddDocumentMapping(ChunkDocType, metaMapping)
	indexMapping.AddDocumentMapping(FileDocType, fileMapping)
	indexMapping.DefaultAnalyzer = CjkAnalyzerName
	indexMapping.TypeField = "DocType"
	return indexMapping, nil
}

func (b *BleveRetrieveEngine) GetClient() *blevesearch.BevelSearchClient {
	return b.client
}

// 判断是否需要过滤内容
func isNeedFilterContent(content []byte) bool {
	if len(content) > 300000 || len(content) < 20 {
		return true
	}
	lines := strings.Split(string(content), "\n")
	// 过滤行数下于3行
	if len(lines) < 3 {
		return true
	}
	// 过滤单行超过512个字节
	for _, line := range lines {
		if len(line) > 2048 {
			return true
		}
	}
	return false
}
