package rag

import (
	"bufio"
	"context"
	cosyDefinition "cosy/definition"
	"cosy/lang/indexer"
	"cosy/log"
	"cosy/util"
	"errors"
	"fmt"
	"io"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strconv"
	"strings"
)

const (
	DefaultMaxOneLineLength = 512

	DefaultTopN = 20

	DefaultContextLines = 5

	DefaultMaxChunkSizeBytes = 1000

	DefaultTotalMaxChunk = 50
)

const (
	IgnoreOutOfLimitPrefix = "[Omitted long line with"

	OutOfLimitDesc = "[Omitted long matching line]"
)

// RipGrepRetrieveEngine
// ripgrep 关键字检索工具检索引擎
type RipGrepRetrieveEngine struct {
	WorkspaceUri string

	options *RipGrepRetrieveOptions
}

type RipGrepRetrieveOptions struct {
	// rg检索的单行字符限制，若超出最大字符限制，则丢弃此代码行
	MaxOneLineLength int

	// 检索返回的最大片段个数
	TopN int

	// 匹配片段上下文的扩展行数量
	ContextExpandLineLength int

	// 每个chunk的最大字节数限制，默认1000字节
	MaxChunkSizeBytes int
}

// 使用结构体记录更详细的搜索状态，避免冗余查询
type searchResult struct {
	actualCount int  // 实际获得的结果数量
	targetCount int  // 目标分配数量
	finished    bool // 是否已完成查询（避免冗余查询）
}

func NewRipGrepRetrieveEngine(workspaceUri string, options *RipGrepRetrieveOptions) *RipGrepRetrieveEngine {
	return &RipGrepRetrieveEngine{
		WorkspaceUri: workspaceUri,
		options:      options,
	}
}

func (r *RipGrepRetrieveEngine) Initialize() error {
	return nil
}

func (r *RipGrepRetrieveEngine) BatchIndex(filePaths []string, enableCheckChange bool) ([]string, error) {
	log.Error("RipGrepRetrieveEngine not implement BatchIndex")
	return nil, fmt.Errorf("RipGrepRetrieveEngine not implement BatchIndex")
}

func (r *RipGrepRetrieveEngine) BatchRemove(filePaths []string) error {
	log.Error("RipGrepRetrieveEngine not need do BatchRemove")
	return fmt.Errorf("RipGrepRetrieveEngine not need do BatchRemove")
}

func (r *RipGrepRetrieveEngine) Retrieve(ctx context.Context, textQuery TextQuery) (RetrieveResult, error) {
	log.Debugf("Retrieve use rg:%s", util.ToJsonStr(textQuery))
	// 1. 检查参数
	if r.WorkspaceUri == "" {
		return RetrieveResult{}, fmt.Errorf("WorkspaceUri is empty")
	}

	// 2. 获取rg的命令执行路径
	rgPath, err := getRipGrepExecutablePath(ctx)
	if err != nil {
		return RetrieveResult{}, err
	}

	// 3. 获取检索查询内容
	keyWords, err := r.getKeyWords(textQuery)
	if err != nil {
		return RetrieveResult{}, err
	}

	// 4. 构建rg命令参数
	args := r.buildExecuteArgs()

	// 5. 执行rg检索命令（限制读取前50行以节省内存）
	var output []string
	if textQuery.Operator == MatchAnd {
		// 如果是且操作符，则将keyWords进行聚合后执行一次rg查询
		pattern := matchAndPatternFunc(keyWords)
		args = append(args, pattern)
		args = append(args, r.getRgSearchPattern(textQuery))
		output, _, err = r.executeRgSearchCommand(rgPath, DefaultTotalMaxChunk, args...)
		if err != nil {
			return RetrieveResult{}, err
		}
	} else {
		// MatchOr操作符，使用比例分配策略确保各优先级关键词都有合理的检索机会
		output, err = r.executeProportionalSearch(rgPath, args, keyWords, textQuery)
		if err != nil {
			return RetrieveResult{}, err
		}
	}

	// 6. 解析rg输出
	result, err := r.parseRipGrepOutput(output)
	if err != nil {
		return RetrieveResult{}, fmt.Errorf("failed to parse rg output: %w", err)
	}
	return result, nil
}

// executeRgSearchCommand
// 执行rg命令检索
func (r *RipGrepRetrieveEngine) executeRgSearchCommand(rgPath string, maxSearchLine int, args ...string) ([]string, bool, error) {
	log.Debugf("rg args is %v", args)
	cmd := exec.Command(rgPath, args...)

	finishedSearch := false
	// 创建管道获取标准输出和标准错误
	stdout, err := cmd.StdoutPipe()
	if err != nil {
		return nil, finishedSearch, fmt.Errorf("failed to create stdout pipe: %w", err)
	}
	stderr, err := cmd.StderrPipe()
	if err != nil {
		return nil, finishedSearch, fmt.Errorf("failed to create stderr pipe: %w", err)
	}

	// 启动命令
	if err := cmd.Start(); err != nil {
		return nil, finishedSearch, fmt.Errorf("failed to start rg command: %w", err)
	}

	// 使用channel来处理并发读取
	outputChan := make(chan []string, 1)
	errorChan := make(chan error, 1)

	// 异步读取标准输出（限制maxSearchLine行）
	go func() {
		defer stdout.Close()
		scanner := bufio.NewScanner(stdout)
		var lines []string
		lineCount := 0

		isScannerBreak := false
		for scanner.Scan() {
			if lineCount >= maxSearchLine {
				isScannerBreak = true
				break
			}
			lineText := scanner.Text()
			if strings.Contains(lineText, OutOfLimitDesc) {
				log.Debugf("lineInfo %s out of limit or omitted", lineText)
				continue
			}
			lines = append(lines, lineText)
			lineCount++
		}

		if err := scanner.Err(); err != nil {
			errorChan <- fmt.Errorf("error reading stdout: %w", err)
			return
		}

		if !isScannerBreak {
			finishedSearch = true
		}
		outputChan <- lines
	}()

	// 异步读取标准错误
	go func() {
		defer stderr.Close()
		errorOutput, err := io.ReadAll(stderr)
		if err != nil {
			errorChan <- fmt.Errorf("error reading stderr: %w", err)
			return
		}

		if len(errorOutput) > 0 {
			errorChan <- fmt.Errorf("rg command error: %s", string(errorOutput))
			return
		}

		// 等待命令完成
		if err := cmd.Wait(); err != nil {
			var exitError *exec.ExitError
			if errors.As(err, &exitError) {
				return
			}
			errorChan <- fmt.Errorf("rg command failed: %w", err)
		}
	}()

	// 等待结果或错误
	var output []string
	select {
	case output = <-outputChan:
		// 成功读取到输出，终止命令进程
		if cmd.Process != nil {
			cmd.Process.Kill()
		}
	case err := <-errorChan:
		// 遇到错误，终止命令进程
		if cmd.Process != nil {
			cmd.Process.Kill()
		}
		return nil, finishedSearch, err
	}
	return output, finishedSearch, nil
}

// executeProportionalSearch
// 按比例分配策略执行关键词检索，确保各优先级关键词都能得到合理的检索机会
func (r *RipGrepRetrieveEngine) executeProportionalSearch(rgPath string, args []string, keyWords []string, query TextQuery) ([]string, error) {
	var allOutput []string

	// 根据优先级设定分配比例：第一优先级40%，第二优先级30%，其余平均分配
	allocationRatios := r.calculateAllocationRatios(len(keyWords))

	// 第一轮：按比例分配检索
	searchResults := make([]searchResult, len(keyWords))
	for i, keyWord := range keyWords {
		targetCount := int(float64(DefaultTotalMaxChunk) * allocationRatios[i])
		if targetCount < 1 {
			targetCount = 1 // 每个关键词至少分配1个位置
		}

		tmpArgs := append(args, fmt.Sprintf("(%s)", generateWordBoundaryPattern(keyWord)))
		tmpArgs = append(tmpArgs, r.getRgSearchPattern(query))

		outputLine, finishedSearch, err := r.executeRgSearchCommand(rgPath, targetCount, tmpArgs...)
		if err != nil {
			log.Warnf("Search failed for keyword %s: %v", keyWord, err)
			searchResults[i] = searchResult{
				actualCount: 0,
				targetCount: targetCount,
				finished:    false,
			}
			continue
		}

		actualCount := len(outputLine)
		searchResults[i] = searchResult{
			actualCount: actualCount,
			targetCount: targetCount,
			finished:    finishedSearch,
		}
		allOutput = append(allOutput, outputLine...)

		log.Debugf("Keyword %s (priority %d): allocated %d, got %d, hasResults: %v",
			keyWord, i+1, targetCount, actualCount, searchResults[i].finished)
	}

	// 第二轮：回填机制，将未使用的配额分配给结果不足的关键词
	remainingCapacity := DefaultTotalMaxChunk - len(allOutput)
	if remainingCapacity > 0 {
		allOutput = append(allOutput, r.executeBackfillSearch(rgPath, args, keyWords, searchResults, remainingCapacity, query)...)
	}

	// 去重和截断
	allOutput = r.deduplicateAndTruncate(allOutput, DefaultTotalMaxChunk)

	log.Debugf("Proportional search completed: total results %d", len(allOutput))
	return allOutput, nil
}

// calculateAllocationRatios
// 计算各优先级关键词的分配比例
func (r *RipGrepRetrieveEngine) calculateAllocationRatios(keyWordCount int) []float64 {
	if keyWordCount <= 0 {
		return []float64{}
	}

	ratios := make([]float64, keyWordCount)

	switch keyWordCount {
	case 1:
		ratios[0] = 1.0
	case 2:
		ratios[0] = 0.7 // 第一优先级70%
		ratios[1] = 0.3 // 第二优先级30%
	case 3:
		ratios[0] = 0.6 // 第一优先级60%
		ratios[1] = 0.3 // 第二优先级30%
		ratios[2] = 0.1 // 第三优先级10%
	default:
		// 多个关键词的情况：第一优先级50%，第二优先级20%，其余平均分配
		ratios[0] = 0.5
		ratios[1] = 0.2
		remaining := 0.3
		for i := 2; i < keyWordCount; i++ {
			ratios[i] = remaining / float64(keyWordCount-2)
		}
	}

	return ratios
}

// executeBackfillSearch
// 回填检索，将剩余配额分配给结果不足但有潜力的关键词（避免冗余查询）
func (r *RipGrepRetrieveEngine) executeBackfillSearch(rgPath string, args []string, keyWords []string, searchResults []searchResult, remainingCapacity int, query TextQuery) []string {
	var backfillOutput []string

	// 计算各关键词的缺口（需求与实际的差值）
	type keywordDeficit struct {
		index   int
		deficit int
		ratio   float64
	}

	allocationRatios := r.calculateAllocationRatios(len(keyWords))
	var deficits []keywordDeficit

	for i, result := range searchResults {
		// 只对未完整完成检索的关键词考虑回填，避免对无结果的关键词再进行冗余查询
		if result.finished {
			log.Debugf("Skipping backfill for keyword %s (index %d): no results in first round", keyWords[i], i)
			continue
		}

		deficit := result.targetCount - result.actualCount
		if deficit > 0 {
			deficits = append(deficits, keywordDeficit{
				index:   i,
				deficit: deficit,
				ratio:   allocationRatios[i],
			})
			log.Debugf("Keyword %s (index %d) eligible for backfill: deficit %d", keyWords[i], i, deficit)
		}
	}

	// 按优先级顺序进行回填
	for _, def := range deficits {
		if remainingCapacity <= 0 {
			break
		}

		backfillCount := def.deficit
		if backfillCount > remainingCapacity {
			backfillCount = remainingCapacity
		}

		keyWord := keyWords[def.index]
		tmpArgs := append(args, fmt.Sprintf("(%s)", generateWordBoundaryPattern(keyWord)))
		tmpArgs = append(tmpArgs, r.getRgSearchPattern(query))

		// 跳过已经获取的结果，只获取新的部分
		actualCount := searchResults[def.index].actualCount
		outputLine, _, err := r.executeRgSearchCommand(rgPath, actualCount+backfillCount, tmpArgs...)
		if err != nil {
			log.Warnf("Backfill search failed for keyword %s: %v", keyWord, err)
			continue
		}

		// 只取新增的部分
		if len(outputLine) > actualCount {
			newLines := outputLine[actualCount:]
			backfillOutput = append(backfillOutput, newLines...)
			remainingCapacity -= len(newLines)

			log.Debugf("Backfilled %d results for keyword %s", len(newLines), keyWord)
		}
	}

	return backfillOutput
}

// deduplicateAndTruncate
// 去重和截断结果
func (r *RipGrepRetrieveEngine) deduplicateAndTruncate(output []string, maxCount int) []string {
	seen := make(map[string]bool)
	var result []string

	for _, line := range output {
		if len(result) >= maxCount {
			break
		}
		if !seen[line] {
			seen[line] = true
			result = append(result, line)
		}
	}

	return result
}

// buildExecuteArgs
func (r *RipGrepRetrieveEngine) buildExecuteArgs() []string {
	// -i 忽略大小写
	// --line-number 需要展示行号
	// "--crlf" 识别/r/n为换行符
	// --no-binary 忽略二进制文件
	args := []string{"-i", "--line-number", "--crlf", "--no-binary"}

	// --max-count 单个文件最大返回的片段个数
	maxCount := r.options.TopN
	if maxCount == 0 {
		maxCount = DefaultTopN
	}
	args = append(args, "--max-count="+strconv.Itoa(maxCount))

	// --max-columns 匹配的单行最大的字节数
	maxColumns := r.options.MaxOneLineLength
	if maxColumns == 0 {
		maxColumns = DefaultMaxOneLineLength
	}
	args = append(args, "--max-columns="+strconv.Itoa(maxColumns))

	// --glob 过滤常见的源码dir
	ignoreGlobs := []string{
		"--glob", "!{.git,.svn,.hg}/",
		"--glob", "!{vendor,node_modules,bower_components,.history,target,build,dist,out,bin}/",
		"--glob", "!{.vscode,.idea,__pycache__,.pytest_cache}/",
		"--glob", "!*.{jpg,jpeg,png,gif,bmp,svg,ico,webp}",
		"--glob", "!*.{mp4,avi,mkv,mov,wmv,flv,webm,m4v}",
		"--glob", "!*.{mp3,wav,flac,aac,ogg,wma,m4a}",
		"--glob", "!*.{pdf,doc,docx,xls,xlsx,ppt,pptx}",
		"--glob", "!*.{zip,rar,7z,tar,gz,bz2,xz}",
		"--glob", "!*.{exe,dll,so,dylib,lib,a,o}",
		"--glob", "!*.{class,jar,war,ear,dex,apk}",
		"--glob", "!*.{pyc,pyo,pyd,cache,tmp,log}",
	}
	args = append(args, ignoreGlobs...)

	return args
}

// getKeyWords
func (r *RipGrepRetrieveEngine) getKeyWords(textQuery TextQuery) ([]string, error) {
	var queryTokens []string
	// 用于keyWords去重
	tokenSet := make(map[string]struct{})
	for _, keyWord := range textQuery.KeyWords {
		if keyWord == "" {
			continue
		}
		tmpKeyWord := strings.TrimSpace(keyWord)
		if _, exists := tokenSet[tmpKeyWord]; !exists {
			queryTokens = append(queryTokens, tmpKeyWord)
			tokenSet[tmpKeyWord] = struct{}{}
		}
	}
	if len(queryTokens) == 0 {
		return nil, fmt.Errorf("no valid query tokens")
	}
	return queryTokens, nil
}

// getRgSearchPattern
// 获取扫描的路径
// 默认扫描workspace，如果额外指定了工程内的扫描路径，则扫描工程内的路径的子路径
func (r *RipGrepRetrieveEngine) getRgSearchPattern(query TextQuery) string {
	if len(query.Must) == 0 {
		return r.WorkspaceUri
	}
	searchPattern := query.Must[0].Query
	if searchPattern == "" {
		return r.WorkspaceUri
	}
	if filepath.IsAbs(searchPattern) && strings.HasPrefix(searchPattern, r.WorkspaceUri) {
		return searchPattern
	}
	// 如果是相对路径，则组装成绝对理解
	return filepath.Join(r.WorkspaceUri, searchPattern)
}

// matchAndPatternFunc
func matchAndPatternFunc(queryTokens []string) string {
	pattern := ""
	for _, token := range queryTokens {
		pattern += fmt.Sprintf("(?=.*%s)", token)
	}
	pattern += ".*"
	return pattern
}

// getRipGrepExecutablePath
// 获取RipGrep的可执行路径，获取顺序如下
// 1.优先通过ideConfig的appRoot路径下获取
// 2.从环境变量中获取
// 3.无法获取的场景尝试从ide的默认安装目录下获取
func getRipGrepExecutablePath(ctx context.Context) (string, error) {
	// 从ideInfo中获取ide app的安装路径
	if ideConfig := ctx.Value(cosyDefinition.ContextKeyIdeConfig); ideConfig != nil {
		ide, ok := ideConfig.(*cosyDefinition.IdeConfig)
		if ok && ide.AppRoot != "" {
			if runtime.GOOS == "windows" {
				return filepath.Join(ide.AppRoot, "node_modules/@vscode/ripgrep/bin/rg.exe"), nil
			}
			return filepath.Join(ide.AppRoot, "node_modules/@vscode/ripgrep/bin/rg"), nil
		}
	}

	// 尝试从环境变量获取可执行路径
	ripGrepExecutablePath := os.Getenv("QODER_RG_PATH")
	if ripGrepExecutablePath != "" {
		log.Debugf("get ripGrepExecutablePath from QODER_RG_PATH env")
		return ripGrepExecutablePath, nil
	}

	// 根据操作系统从默认安装路径获取执行路径
	log.Debugf("get ripGrepExecutablePath from default ide install path")
	if runtime.GOOS == "windows" {
		return "C:\\Program Files\\Qoder\\resources\\app\\node_modules\\@vscode\\ripgrep\\bin\\rg.exe", nil
	}
	return "/Applications/Qoder.app/Contents/Resources/app/node_modules/@vscode/ripgrep/bin/rg", nil
}

// generateWordBoundaryPattern 生成单词边界匹配的正则表达式
func generateWordBoundaryPattern(searchTerm string) string {
	// 只匹配完整单词或以搜索词开头的单词
	return fmt.Sprintf(`\b%s\b|\b%s[_\-\.]\w*\b`, searchTerm, searchTerm)
}

func (r *RipGrepRetrieveEngine) GetFileChunks(filePath string, topN int) ([]indexer.CodeChunk, error) {
	return []indexer.CodeChunk{}, nil
}

func (r *RipGrepRetrieveEngine) BatchGetFileChunks(filePaths []string, topNPerFile int) ([]indexer.CodeChunk, error) {
	return []indexer.CodeChunk{}, nil
}

func (r *RipGrepRetrieveEngine) GetDocumentTotalCount() int64 {
	return 0
}

func (r *RipGrepRetrieveEngine) Close() {
}
