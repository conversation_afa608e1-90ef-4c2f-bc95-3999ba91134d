package rag

import (
	"context"
	"cosy/components"
	"cosy/definition"
	"cosy/global"
	"cosy/indexing/common"
	"cosy/indexing/index_setting"
	"cosy/lang/indexer"
	"cosy/log"
	"cosy/storage/vector"
	"cosy/tree"
	"cosy/util"
	"cosy/util/collection"
	"cosy/util/rag"
	"errors"
	"os"
	"path/filepath"
	"runtime"
	"runtime/debug"
	"sort"
	"strings"
	"sync"
	"sync/atomic"
	"time"
)

const (
	// DefaultMaxWait 任务最大等待时间
	DefaultMaxWait = 1 * time.Minute

	// DefaultMaxLineFeed 默认最多支持8000行
	DefaultMaxLineFeed = 8000

	// DefaultTolerantEmbeddingChunk 允许一次embedding浪费的块数
	// 假定，一次embedding最多20个块，如果一次请求只有14个块，则视作浪费6个块
	DefaultTolerantEmbeddingChunk = 3
)

type SqliteVecRetrieveEngine struct {
	WorkspacePath                    string
	Client                           vector.SqliteVecEngine
	AsyncAllRepoBuildIndexInProgress atomic.Bool
	IndexStatusTree                  *tree.MerkleTree
	IndexStatusTreeMutex             sync.RWMutex
}

// newChatSqliteVecRetrieveEngine
// 不希望外部随便new向量引擎
// 应该使用 GetClientChatVectorRetrieveEngine 来获取向量引擎
func newChatSqliteVecRetrieveEngine(workspacePath string) (*SqliteVecRetrieveEngine, error) {
	if workspacePath == "" {
		return nil, errors.New("workspace path is empty")
	}

	sqliteClient, err := vector.NewSqliteVecClient(workspacePath, definition.DatabaseVersion, vector.DatabaseModeChat, -1)
	if err != nil {
		log.Errorf("[codebase]-[vector engine] create sqlite client failed, err: %v", err)
		return nil, err
	}

	vecEngine := &SqliteVecRetrieveEngine{
		WorkspacePath:                    workspacePath,
		Client:                           sqliteClient,
		AsyncAllRepoBuildIndexInProgress: atomic.Bool{},
		IndexStatusTree:                  nil,
		IndexStatusTreeMutex:             sync.RWMutex{},
	}

	vecEngine.AsyncAllRepoBuildIndexInProgress.Store(false)

	// 初始化索引状态树，索引状态中节点数量为0
	vecEngine.BuildIndexStatusTree()
	vecEngine.PeriodRebuildIndexStatusTree()

	return vecEngine, nil
}

// newClientChatMemoSqliteVecRetrieveEngine
// 不希望外部随便new向量引擎
// 应该使用 GetClientChatMemoVectorRetrieveEngine 来获取向量引擎
func newClientChatMemoSqliteVecRetrieveEngine(workspacePath string) (*SqliteVecRetrieveEngine, error) {
	if workspacePath == "" {
		return nil, errors.New("workspace path is empty")
	}

	sqliteClient, err := vector.NewSqliteVecClient(workspacePath, definition.DatabaseVersion, vector.DatabaseModeWiki, -1)
	if err != nil {
		log.Errorf("[codebase]-[vector engine] create sqlite client failed, err: %v", err)
		return nil, err
	}

	vecEngine := &SqliteVecRetrieveEngine{
		WorkspacePath:                    workspacePath,
		Client:                           sqliteClient,
		AsyncAllRepoBuildIndexInProgress: atomic.Bool{},
		IndexStatusTree:                  nil,
		IndexStatusTreeMutex:             sync.RWMutex{},
	}

	return vecEngine, nil
}

// buildTokenBucket
// 如果传入参数小于等于0，代表希望按照cpu/2来划分桶
// 如果传入参数大于0，代表希望按照传入参数来划分桶
func buildTokenBucket(cnt int) chan struct{} {
	var retValue chan struct{}
	MaxTokenBucket := runtime.NumCPU() / 2
	if MaxTokenBucket <= 0 {
		MaxTokenBucket = 1
	}
	if cnt <= 0 || cnt > MaxTokenBucket {
		cnt = MaxTokenBucket
	}
	retValue = make(chan struct{}, cnt)
	for i := 0; i < cnt; i++ {
		retValue <- struct{}{}
	}
	return retValue
}

func (engine *SqliteVecRetrieveEngine) GetIndexProgress() string {
	return index_setting.IndexProgressFinish
}

// Retrieve 召回
// 目前不接受非embedding的文件召回
func (engine *SqliteVecRetrieveEngine) Retrieve(queryCondition definition.QueryCondition) (RetrieveResult, error) {
	if queryCondition.QueryEmbedding == nil {
		return RetrieveResult{}, errors.New("query embedding is nil")
	}

	sqliteVecRetrieveResult, err := engine.Client.Query(queryCondition)
	if err != nil {
		log.Errorf("[codebase]-[vector engine] sqlite client query failed, err: %v", err)
		return RetrieveResult{}, err
	}

	log.Debugf("[codebase]-[vector engine] [retrieve] sqlite client query success, result length: %v", len(sqliteVecRetrieveResult.Chunks))
	var result RetrieveResult
	result.Source = ClientVectorRetrieveSource
	for _, retrieveChunk := range sqliteVecRetrieveResult.Chunks {
		codeChunk := indexer.CodeChunk{
			Id:          retrieveChunk.ChunkId,
			Content:     retrieveChunk.Content,
			FilePath:    retrieveChunk.FilePath,
			FileName:    retrieveChunk.FileName,
			StartLine:   retrieveChunk.StartLine,
			EndLine:     retrieveChunk.EndLine,
			StartOffset: retrieveChunk.StartOffset,
			EndOffset:   retrieveChunk.EndOffset,
		}
		chunk := RetrieveChunk{
			CodeChunk: codeChunk,
			Score:     retrieveChunk.Score,
		}
		result.Chunks = append(result.Chunks, chunk)
	}

	return result, nil

}

// BatchDeleteIndex 批量删除索引
func (engine *SqliteVecRetrieveEngine) BatchDeleteIndex(virtualFiles []definition.VirtualFile, isDir bool) error {
	filePathPatterns := common.GetVirtualFilePaths(virtualFiles)
	if len(filePathPatterns) == 0 {
		return nil
	}
	log.Debugf("[codebase]-[vector engine] [delete] start to delete index, file path patterns: %v", filePathPatterns)

	err := engine.Client.BatchDelete(filePathPatterns)
	if err != nil {
		// sqlite中记录了日志，这里就不用记录了
		return err
	}

	// 检查IndexStatusTree是否为nil，wiki模式下IndexStatusTree为nil是正常的
	// 因为wiki文档不是文件系统中的真实文件，不需要通过MerkleTree管理
	engine.IndexStatusTreeMutex.RLock()
	defer engine.IndexStatusTreeMutex.RUnlock()
	if engine.IndexStatusTree != nil {
		err = engine.IndexStatusTree.DeleteFiles(filePathPatterns, isDir)
		if err != nil {
			log.Errorf("[codebase]-[vector engine] [delete] delete index status tree failed, err: %v", err)
			return err
		}
	} else {
		log.Debugf("[codebase]-[vector engine] [delete] IndexStatusTree is nil, skip tree update (wiki mode)")
	}

	return nil
}

// ExecuteBatchTask 建立索引的业务函数
// 执行任务系统下发的任务
// 返回当前存储是否已达到最大文件数量
func (engine *SqliteVecRetrieveEngine) ExecuteBatchTask(tasks definition.VectorBatchTask, enableCheckChange bool, limitRequire bool) bool {
	storageFileNum := engine.GetStorageFileNum()
	maxStorageFileNum := global.GetMaxClientStorageFileNum()
	if storageFileNum >= maxStorageFileNum {
		if tasks.Source == definition.VectorFileChangeIndexSource {
			// 如果文件达到限制，来源是文件修改，先触发淘汰策略
			log.Warnf("[codebase]-[vector engine] storage file num is %d, start to delete", storageFileNum)
			// 触发淘汰策略，然后继续正常建立索引
			go func() {
				err := engine.Client.AutoDelete()
				if err != nil {
					log.Errorf("[codebase]-[vector engine] auto delete failed, err: %v", err)
				}
			}()
		} else {
			log.Debugf("[codebase]-[vector engine] [skip] storage file num is %d, skip this batch tasks: %d", storageFileNum, len(tasks.Tasks))
			// 如果文件达到限制，来源是全量索引，直接返回
			return true
		}
	}

	totalTaskCnt := len(tasks.Tasks)
	log.Debugf("[codebase]-[vector engine] %d tasks, start to execute", totalTaskCnt)

	// 切分文件
	splitTaskWrappers := engine.BatchSplitFile(tasks, enableCheckChange)
	//log.Debugf("[codebase]-[vector engine] %d tasks, split file finish", len(splitTaskWrappers))

	//// merge算法执行，减少发出embedding请求的次数，节约资源
	//mergedTaskWrappers := engine.MergeWrappers(splitTaskWrappers)
	////log.Debugf("[codebase]-[vector engine] merge finish")

	// 分批执行embedding
	embeddingTaskWrappers := engine.BatchEmbedChunks(splitTaskWrappers, totalTaskCnt, limitRequire)
	//log.Debugf("[codebase]-[vector engine] %d tasks,embedding finish", len(embeddingTaskWrappers))

	// 将embedding结果入库
	successTaskWrappers := engine.BatchSaveIndex(embeddingTaskWrappers, totalTaskCnt)
	//log.Debugf("[codebase]-[vector engine] %d tasks,save index finish", len(successTaskWrappers))

	for _, task := range tasks.Tasks {
		if task.Status == definition.TaskStatusSkip ||
			task.Status == definition.TaskStatusDiscard {
			continue
		}

		finish := false
		for _, wrapper := range successTaskWrappers {
			if task.Id == wrapper.Task.Id {
				finish = true
				wrapper.Task.Status = definition.TaskStatusFinish
			}
		}

		if !finish {
			task.Status = definition.TaskStatusFailed
		}
	}

	engine.IndexStatusTreeMutex.RLock()
	defer engine.IndexStatusTreeMutex.RUnlock()
	successIndexFilePaths := make([]string, 0)
	for _, taskWrapper := range successTaskWrappers {
		successIndexFilePaths = append(successIndexFilePaths, taskWrapper.Task.VirtualFile.GetFilePath())
	}

	// 检查IndexStatusTree是否为nil，wiki模式下IndexStatusTree为nil是正常的
	// 因为wiki文档不是文件系统中的真实文件，不需要通过MerkleTree管理
	if engine.IndexStatusTree != nil {
		err := engine.IndexStatusTree.IndexFiles(successIndexFilePaths)
		if err != nil {
			log.Errorf("[codebase]-[vector engine] [build index] update index status tree failed, err: %v", err)
			return false
		}
	} else {
		log.Debugf("[codebase]-[vector engine] [build index] IndexStatusTree is nil, skip tree update (wiki mode)")
	}

	return false
}

// BatchSplitFile 切分文件
func (engine *SqliteVecRetrieveEngine) BatchSplitFile(tasks definition.VectorBatchTask, enableCheckChange bool) []*definition.TaskWrapper {
	totalTaskCnt := len(tasks.Tasks)

	// 切分文件
	splitResultChan := make(chan *definition.TaskWrapper, totalTaskCnt)
	wg := sync.WaitGroup{}
	bucket := buildTokenBucket(0)
	for _, task := range tasks.Tasks {
		tmpTask := task
		wg.Add(1)
		go func() {
			var oneToken struct{}
			defer func() {
				// ResultChan有可能已被关闭，因此这里需要处理panic
				// 捕获主逻辑中的panic
				if r := recover(); r != nil {
					log.Errorf("[codebase]-[vector engine] [panic recover] split file panic: %v, filePath: %s", r, tmpTask.GetFilePath())
				}

				// 捕获 bucket 和 wg 的 panic
				defer func() {
					if r := recover(); r != nil {
						log.Errorf("[codebase]-[vector engine] [panic recover] split file inner panic: %v", r)
					}
				}()
				wg.Done()
				bucket <- oneToken
			}()
			oneToken = <-bucket
			wrapper, err := engine.SplitFile(tmpTask, enableCheckChange)
			if err != nil {
				// 这个部分的日志直接跳过
				if strings.Contains(err.Error(), "is a directory") {
					// 目录也不重试任务了
					tmpTask.Status = definition.TaskStatusDiscard
					return
				}
				log.Errorf("[codebase]-[vector engine] [split file] error, filePath: %s, err: %v", tmpTask.GetFilePath(), err)
				return
			}
			if wrapper != nil && wrapper.Chunks != nil && len(wrapper.Chunks) > 0 {
				splitResultChan <- wrapper
			} else {
				// 这个日志太多了，不打了
				// log.Debugf("[codebase]-[vector engine] [split file] skip, filePath: %s", tmpTask.GetFilePath())
			}
		}()
	}

	done := make(chan struct{})
	go func() {
		wg.Wait() // 等待所有 goroutine 完成
		done <- struct{}{}
	}()

	select {
	case <-done:
	case <-time.After(DefaultMaxWait):
	}
	var splitWrappers []*definition.TaskWrapper
	close(splitResultChan)
	close(bucket)
	for result := range splitResultChan {
		if result == nil {
			continue
		}
		splitWrappers = append(splitWrappers, result)
	}

	return splitWrappers
}

func (engine *SqliteVecRetrieveEngine) SplitFile(task *definition.Task, enableCheckChange bool) (*definition.TaskWrapper, error) {
	var wrapper definition.TaskWrapper
	filePath := task.GetFilePath()
	fileContent, err := task.GetFileContent()
	if err != nil {
		//log.Errorf("[codebase]-[vector engine] [split file] error, filePath: %s, err: %v", filePath, err)
		return nil, err
	}
	if task.GetFileContentLineFeed() >= DefaultMaxLineFeed {
		// 文件过大，不再重试切块
		task.Status = definition.TaskStatusDiscard
		log.Warnf("[codebase]-[vector engine] [split file] file too large, filePath: %s", filePath)
		return nil, nil
	}

	if len(string(fileContent)) == 0 {
		// 空文件直接返回，不切块
		task.Status = definition.TaskStatusDiscard
		return nil, nil
	}

	// 先执行代码切块
	// 以便于查询ShareChunk
	splitChunks, err := SplitCodeChunksByIndexType(ChatIndexType, engine.WorkspacePath, filePath, fileContent)
	if err != nil {
		//log.Errorf("[codebase]-[vector engine] [split file] error, filePath: %s, err: %v", filePath, err)
		return nil, err
	}

	// 这里需要做切块的转换
	var chunks []*definition.StorageChunk
	for _, chunk := range splitChunks.Chunks {
		storageChunk := &definition.StorageChunk{
			ChunkId:     chunk.Id,
			Content:     chunk.Content,
			FileName:    chunk.FileName,
			FilePath:    chunk.FilePath,
			StartLine:   chunk.StartLine,
			EndLine:     chunk.EndLine,
			StartOffset: chunk.StartOffset,
			EndOffset:   chunk.EndOffset,
			Embedding:   nil,
		}

		contentID := definition.GetFileId([]byte(storageChunk.Content))
		shareChunk, err := vector.QueryShareChunk(contentID)
		if err == nil && shareChunk != nil {
			// 命中共享chunk
			storageChunk.Embedding = shareChunk.Embedding
		}

		chunks = append(chunks, storageChunk)
	}

	// 后做索引检查
	if enableCheckChange {
		fileExist, fileChanged := engine.CheckFileChanged(filePath, string(fileContent))
		if fileExist && !fileChanged {
			// 索引存在，且未发生改变，不需要重新建立索引
			// 索引已建立成功
			task.Status = definition.TaskStatusSkip
			// 这个日志太多了，就不打了
			//log.Debugf("[codebase]-[vector engine] [split file] index has been build correct, filePath: %s", filePath)
			return nil, nil
		}

		if fileChanged {
			// 删除索引后，可导致shareChunk无法命中，因此必须提前切块
			// 删除索引可能因为各种原因失败，不需要特殊处理
			err = engine.BatchDeleteIndex([]definition.VirtualFile{definition.NewVirtualFile(filePath)}, false)
			if err != nil {
				return nil, err
			}
		}
	}

	wrapper.Chunks = append(wrapper.Chunks, chunks...)
	wrapper.Task = task
	return &wrapper, nil
}

func (engine *SqliteVecRetrieveEngine) EmbedChunks(chunks []*definition.StorageChunk, limitRequire bool) error {
	if len(chunks) <= 0 {
		return nil
	}

	var texts []string
	for _, chunk := range chunks {
		texts = append(texts, chunk.Content)
	}

	embedder := components.NewLingmaEmbedder()
	var embeddings [][]float32
	var err error
	if limitRequire {
		embeddings, err = embedder.CreateEmbedding(context.Background(), texts, components.TextTypeDocument)
	} else {
		embeddings, err = embedder.CreateEmbedding(context.Background(), texts, components.TextTypeQuery)
	}
	if err != nil {
		log.Errorf("[codebase]-[vector engine] [embedding] error, err: %v", err)
		return err
	}

	if len(embeddings) != len(chunks) {
		log.Errorf("[codebase]-[vector engine] [embedding] length not equal to origin index list length")
		return errors.New("embedding length not equal to origin index list length")
	}

	for i := 0; i < len(embeddings); i++ {
		chunks[i].Embedding = embeddings[i]
	}

	return nil
}

// BatchEmbedChunks 批量嵌入向量
func (engine *SqliteVecRetrieveEngine) BatchEmbedChunks(taskWrappers []*definition.TaskWrapper, totalTaskCnt int, limitRequire bool) []*definition.TaskWrapper {
	if len(taskWrappers) <= 0 {
		return nil
	}
	wg := sync.WaitGroup{}
	embeddingResultChan := make(chan *definition.TaskWrapper, totalTaskCnt)
	for _, taskWrapper := range taskWrappers {
		tmpTaskWrapper := taskWrapper
		wg.Add(1)
		go func() {
			defer func() {
				// ResultChan有可能已被关闭
				// 捕获外部的panic
				if r := recover(); r != nil {
					log.Errorf("[codebase]-[vector engine] [panic recover] embed chunks panic, %v", r)
				}
				wg.Done()
			}()

			var todoEmbeddingChunks []*definition.StorageChunk
			for _, chunk := range tmpTaskWrapper.Chunks {
				if chunk.Embedding != nil {
					// 命中共享chunk
					// task共享chunk统计次数加1
					tmpTaskWrapper.Task.ShareChunkHitCnt += 1
					chunk.EmbeddingSource = definition.EmbeddingSourceShareChunk
				} else {
					// 未命中共享chunk
					todoEmbeddingChunks = append(todoEmbeddingChunks, chunk)
					// 实际执行Embedding的chunk统计次数加1
					tmpTaskWrapper.Task.DoEmbeddingChunkCnt += 1
					chunk.EmbeddingSource = definition.EmbeddingSourceEmbedding
				}
			}

			// 执行Embedding
			err := engine.EmbedChunks(todoEmbeddingChunks, limitRequire)
			if err == nil {
				allChunksEmbeddingSuccess := true
				// 检查当前Task的全部chunks
				for _, chunk := range tmpTaskWrapper.Chunks {
					if chunk.Embedding == nil {
						allChunksEmbeddingSuccess = false
						break
					}
				}

				// 可能有的chunk因为网络问题没有正确embedding
				// 由于索引记录是依据文件来建立的
				// 因此一个文件只要出现一个chunk未正确embedding，则这个wrapper就是失败的，丢弃即可
				if allChunksEmbeddingSuccess {
					embeddingResultChan <- tmpTaskWrapper
				} else {
					log.Errorf("[codebase]-[vector engine] [embed chunks] some chunks embedding failed, filePath: %s", tmpTaskWrapper.Task.GetFilePath())
				}
			}
		}()
	}

	wg.Wait() // 等待所有 goroutine 完成

	var embeddingWrappers []*definition.TaskWrapper
	close(embeddingResultChan)
	for result := range embeddingResultChan {
		if result == nil {
			continue
		}
		embeddingWrappers = append(embeddingWrappers, result)
	}

	return embeddingWrappers
}

func (engine *SqliteVecRetrieveEngine) CheckFileChanged(filePath string, fileContent string) (bool, bool) {
	return engine.Client.CheckFileChanged(filePath, fileContent)
}

// BatchSaveIndex 批量保存索引
// 先保存索引数据，然后保存索引记录
func (engine *SqliteVecRetrieveEngine) BatchSaveIndex(taskWrappers []*definition.TaskWrapper, totalTaskCnt int) []*definition.TaskWrapper {
	if len(taskWrappers) <= 0 {
		return nil
	}
	wg := sync.WaitGroup{}
	finalResultChan := make(chan *definition.TaskWrapper, totalTaskCnt)
	bucket := buildTokenBucket(0)
	for _, wrapper := range taskWrappers {
		tmpWrapper := wrapper
		wg.Add(1)
		go func(taskWrapper *definition.TaskWrapper) {
			var oneToken struct{}
			defer func() {
				// ResultChan有可能已被关闭
				// 捕获外部的panic
				if r := recover(); r != nil {
					log.Errorf("[codebase]-[vector engine] [panic recover] save index panic: %v", r)
				}

				// 捕获 bucket 和 wg 的 panic
				defer func() {
					if r := recover(); r != nil {
						log.Errorf("[codebase]-[vector engine] [panic recover] save index inner panic: %v", r)
					}
				}()

				wg.Done()
				bucket <- oneToken
			}()
			oneToken = <-bucket

			var chunks []definition.StorageChunk
			for _, chunk := range taskWrapper.Chunks {
				chunks = append(chunks, *chunk)
			}

			// 将结果存入数据库中
			err := engine.SaveChunks(chunks, taskWrapper.Task.VirtualFile)
			if err != nil {
				// sqlite中记录了日志，这里就不用记录了
				return
			}

			for _, chunk := range chunks {
				if chunk.EmbeddingSource == definition.EmbeddingSourceShareChunk {
					// 来自于shareChunk的chunk，不需要重复插入了
					continue
				}

				contentId := definition.GetFileId([]byte(chunk.Content))
				insertShareChunkErr := vector.InsertShareChunk(definition.ShareChunkEntity{
					ContentChunkId: contentId,
					SqliteChunkId:  chunk.ChunkId,
					HitCnt:         0,
					RepoPath:       engine.WorkspacePath,
				})
				if insertShareChunkErr != nil {
					log.Errorf("[codebase]-[vector engine] [insert share chunk] err: %v", insertShareChunkErr)
				}
			}

			// 保存索引记录并且入库都成功了
			if err == nil {
				finalResultChan <- taskWrapper
			}
		}(tmpWrapper)
	}

	done := make(chan struct{})
	go func() {
		wg.Wait() // 等待所有 goroutine 完成
		done <- struct{}{}
	}()

	select {
	case <-done:
	case <-time.After(DefaultMaxWait):
	}

	var successWrappers []*definition.TaskWrapper
	close(finalResultChan)
	close(bucket)
	for result := range finalResultChan {
		successWrappers = append(successWrappers, result)
	}

	return successWrappers
}

func (engine *SqliteVecRetrieveEngine) SaveChunks(chunks []definition.StorageChunk, file definition.VirtualFile) error {
	return engine.Client.BatchInsert(chunks, file)
}

func (engine *SqliteVecRetrieveEngine) GetStorageChunkNum() int {
	return engine.Client.GetStorageChunkNum()
}

func (engine *SqliteVecRetrieveEngine) GetStorageFileChunks(filePath string) ([]definition.StorageChunk, error) {
	return engine.Client.GetStorageFileChunks(filePath)
}

func (engine *SqliteVecRetrieveEngine) GetStorageFileNum() int {
	return engine.Client.GetStorageFileNum()
}

func (engine *SqliteVecRetrieveEngine) GetClonedIndexStatusTree() *tree.MerkleTree {
	engine.IndexStatusTreeMutex.RLock()
	defer engine.IndexStatusTreeMutex.RUnlock()
	if engine.IndexStatusTree != nil {
		return engine.IndexStatusTree.Clone()
	}
	// wiki模式下IndexStatusTree为nil，返回nil
	return nil
}

func (engine *SqliteVecRetrieveEngine) PeriodRebuildIndexStatusTree() {
	go func() {
		// 如果IndexStatusTree为nil（wiki模式），直接返回，不需要定期重建
		if engine.IndexStatusTree == nil {
			log.Debugf("[codebase]-[vector engine] IndexStatusTree is nil, skip periodic rebuild (wiki mode)")
			return
		}

		for {
			leafNodeCnt := engine.IndexStatusTree.GetLeafNodeCount()
			storageFileNum := engine.GetStorageFileNum()

			percent := float64(storageFileNum) / float64(leafNodeCnt)

			if percent <= definition.SkipScanStoragePercentage {
				// 如果实际存储的数量比索引树中的叶子结点的数量
				// 说明需要重建索引状态树，此时索引数据已经被清理
				engine.BuildIndexStatusTree()
			}

			// 索引状态没问题，直接跳过
			time.Sleep(definition.RebuildIndexStatusTreeInterval)
		}
	}()
}

func (engine *SqliteVecRetrieveEngine) BuildIndexStatusTree() {
	log.Info("[codebase]-[vector engine] [build mtree] start")
	engine.IndexStatusTreeMutex.Lock()

	// 捕获panic
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("[codebase]-[vector engine] [build mtree] panic: %v", r)
			log.Debugf("[codebase]-[vector engine] [build mtree] stack: %s", string(debug.Stack()))
		}
		engine.IndexStatusTreeMutex.Unlock()
	}()

	records, err := engine.Client.QueryAllStorageRecords()
	if err != nil {
		log.Errorf("[codebase]-[vector engine] [build mtree] failed, err: %v", err)
		return
	}

	filePaths := make([]string, 0)
	for _, record := range records {
		if record.FilePath == "" {
			continue
		}

		stat, err := os.Stat(record.FilePath)
		if err != nil || stat.IsDir() {
			log.Errorf("[codebase]-[vector engine] [build mtree] stat file failed, filePath: %s, err: %v", record.FilePath, err)
			continue
		}
		content, err := os.ReadFile(record.FilePath)
		if err != nil {
			log.Errorf("[codebase]-[vector engine] [build mtree] read file failed, filePath: %s, err: %v", record.FilePath, err)
			continue
		}

		newIdentifier := definition.GetIdentifier(string(content))
		if record.Identifier != newIdentifier {
			continue
		}
		filePaths = append(filePaths, record.FilePath)
	}

	// 初始化索引状态树
	indexStatusTree := tree.NewTmpMerkleTree(engine.WorkspacePath)
	indexStatusTree.IndexFiles(filePaths)
	engine.IndexStatusTree = indexStatusTree

	return
}

func (engine *SqliteVecRetrieveEngine) EngineType() string {
	return VectorEngineTypeClient
}

// AsyncBuildIndex
// 传入文件路径均为绝对路径
func (engine *SqliteVecRetrieveEngine) AsyncBuildIndex(virtualFiles []definition.VirtualFile, source string) {
	wg := sync.WaitGroup{}
	wg.Add(1)
	go func() {
		wg.Done()
		engine.executeAsyncBuildIndex(virtualFiles, source)
	}()
	wg.Wait()
}

func (engine *SqliteVecRetrieveEngine) executeAsyncBuildIndex(virtualFiles []definition.VirtualFile, source string) {
	if source == definition.VectorInitialFullIndexSource && !engine.AsyncAllRepoBuildIndexInProgress.CompareAndSwap(false, true) {
		// 异步全量索引正在建立，本次全量请求跳过
		log.Warnf("[codebase]-[vector client engine] [all repo] async building is in progress")
		return
	}

	log.Warnf("[codebase]-[vector client engine] [async build index] start")
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("[codebase]-[vector client engine] [async build index] panic: %v", r)
			log.Debugf("[codebase]-[vector client engine] [async build index] stack: %s", string(debug.Stack()))
		}
		if source == definition.VectorInitialFullIndexSource {
			engine.AsyncAllRepoBuildIndexInProgress.Store(false)
		}
	}()

	notIndexFiles := make([]definition.VirtualFile, 0)
	// 对已建索引进行check
	for _, virtualFile := range virtualFiles {
		content, err := virtualFile.GetFileContent()
		if err != nil {
			continue
		}
		fileExist, fileChanged := engine.CheckFileChanged(virtualFile.GetFilePath(), string(content))
		if fileExist && !fileChanged {
			// 索引存在，且未发生改变，不需要重新建立索引
			// 索引已建立成功
			continue
		}
		notIndexFiles = append(notIndexFiles, virtualFile)
	}

	// 构建文件优先级，按照优先级上传文件
	priorityQueue := collection.NewPriorityQueue[*definition.AsyncTask]()
	for _, virtualFile := range virtualFiles {
		task := definition.NewAsyncTaskWithVirtualFile(virtualFile)
		if task == nil {
			continue
		}

		priority := util.GetFilePriority(task.GetFilePath())
		priorityQueue.PushItem(task, priority)
	}

	//for _, node := range indexLeafNodes {
	//	filePath := filepath.Join(engine.WorkspacePath, node.RelativePath)
	//	stat, err := os.Stat(filePath)
	//	if err != nil {
	//		log.Errorf("[codebase]-[vector client engine] [async build index] stat file failed: %v", err)
	//		continue
	//	}
	//	if stat.IsDir() {
	//		log.Errorf("[codebase]-[vector client engine] [async build index] file is dir: %v", filePath)
	//		continue
	//	}
	//
	//	//// 文件大小过滤
	//	//if stat.Size() < definition.DefaultMinUploadFileSize {
	//	//	log.Errorf("[codebase]-[vector client engine] [async build index] file size is too small: %v", filePath)
	//	//	continue
	//	//}
	//
	//	if stat.Size() >= definition.DefaultPerRequestMaxUploadFileSize {
	//		log.Errorf("[codebase]-[vector client engine] [async build index] file size too large: %v", filePath)
	//		continue
	//	}
	//
	//	priority := util.GetFilePriority(filePath)
	//	virtualFile := definition.NewVirtualFile(filePath)
	//	asyncTask := definition.NewAsyncTask(virtualFile, node.Hash, int(stat.Size()))
	//	priorityQueue.PushItem(asyncTask, priority)
	//}

	// 批量检查服务端文件状态，以准备上传文件
	toUploadTasks, serverHandlingFileCnt := engine.GetUnUploadedFiles(priorityQueue)

	// 上传文件
	// 上传文件中有文件数量过滤
	engine.UploadFiles(toUploadTasks, serverHandlingFileCnt)

	// 轮训索引状态
	// 获取Embedding数据，存储本地项链数据库
	engine.PollingFileStatus(priorityQueue)
}

// GetUnUploadedFiles
// 返回需要上传文件的task 服务端正在处理的文件数量
func (engine *SqliteVecRetrieveEngine) GetUnUploadedFiles(priorityQueue *collection.PriorityQueue[*definition.AsyncTask]) ([]*definition.AsyncTask, int) {
	toCheckTasks := make([]*definition.AsyncTask, 0)
	for _, item := range priorityQueue.AllItems() {
		// 这么做是为了在上传文件和检查文件状态时，保证一定的优先级
		// 保证优先级高的代码文件最优先被上传
		toCheckTasks = append(toCheckTasks, item.Value())
	}

	toUploadTasks := make([]*definition.AsyncTask, 0)
	checkStatusBatchTasks := make([]*definition.AsyncTask, 0)
	// 服务端已正在处理或者已处理完毕的文件数量
	serverHandlingFileCnt := 0
	// 检查服务端文件状态，目的是拿到需要上传的文件
	for len(toCheckTasks) > 0 || len(checkStatusBatchTasks) > 0 {
		// 达到一次BatchSize，检查一次文件状态
		if len(checkStatusBatchTasks) > 0 {
			toCheckFileIdMap := make(map[string]struct{})
			for _, asyncTask := range checkStatusBatchTasks {
				toCheckFileIdMap[asyncTask.FileId] = struct{}{}
			}

			var toCheckFileIds []string
			for fileId, _ := range toCheckFileIdMap {
				toCheckFileIds = append(toCheckFileIds, fileId)
			}

			// 执行check文件状态
			response, err := components.CheckServerFileStatus(toCheckFileIds)
			if err != nil || response == nil {
				log.Errorf("[codebase]-[vector client engine] [check file status] check file status failed: %v", err)
				checkStatusBatchTasks = make([]*definition.AsyncTask, 0)
				continue
			}
			for _, task := range checkStatusBatchTasks {
				if fileStatus, found := response.FileStatuses[task.FileId]; found {
					if fileStatus == definition.ServerFileStatusNotSynced {
						// 说明此时文件未同步，需要上传文件
						toUploadTasks = append(toUploadTasks, task)
					} else if fileStatus == definition.ServerFileStatusPending {
						// 说明此时文件正在处理，等待即可
						serverHandlingFileCnt += 1
					} else if fileStatus == definition.ServerFileStatusSynced {
						// 说明此时文件已同步，直接获取Embedding即可
						serverHandlingFileCnt += 1
					}
				} else {
					// 服务端未找到该文件，需要上传
					// 这里是一个兜底，一般不会走到这里
					log.Errorf("[codebase]-[vector client engine] [check file status] error: file %s not found in response", task.GetFilePath())
					toUploadTasks = append(toUploadTasks, task)
				}
			}
			checkStatusBatchTasks = make([]*definition.AsyncTask, 0)
		}

		batchSize := definition.DefaultPerRequestMaxCheckFileStatusNum
		if len(toCheckTasks) < batchSize {
			batchSize = len(toCheckTasks)
		}

		checkStatusBatchTasks = append(checkStatusBatchTasks, toCheckTasks[:batchSize]...)
		toCheckTasks = toCheckTasks[batchSize:]
	}

	return toUploadTasks, serverHandlingFileCnt
}

func (engine *SqliteVecRetrieveEngine) UploadFiles(toUploadTasks []*definition.AsyncTask, serverHandlingFileCnt int) {
	// 根据本地存储情况和服务端处理情况，批量上传文件
	uploadFileBatchTasks := make([]*definition.AsyncTask, 0)
	for len(toUploadTasks) > 0 || len(uploadFileBatchTasks) > 0 {
		// 检查本地存储情况
		if serverHandlingFileCnt+engine.GetStorageFileNum() >=
			global.GetMaxClientStorageFileNum()+definition.DefaultAsyncRedundantUploadFileCount {
			// 达到最大的存储数量 + 部分冗余后，剩余文件不再上传
			break
		}

		fileSizeSum := 0
		for _, asyncTask := range uploadFileBatchTasks {
			fileSizeSum += asyncTask.FileSize
		}

		reachedMaxUploadFileSize := false
		if len(toUploadTasks) > 0 {
			task := toUploadTasks[0]
			if task.FileSize+fileSizeSum < definition.DefaultPerRequestMaxUploadFileSize {
				hasSameFileId := false
				for _, asyncTask := range uploadFileBatchTasks {
					if asyncTask.FileId == task.FileId {
						hasSameFileId = true
						break
					}
				}
				if !hasSameFileId {
					// 待上传文件中没有相同fileId的文件
					uploadFileBatchTasks = append(uploadFileBatchTasks, task)
				}
				toUploadTasks = toUploadTasks[1:]
			} else {
				reachedMaxUploadFileSize = true
			}
		}

		if reachedMaxUploadFileSize ||
			len(uploadFileBatchTasks) >= definition.DefaultPerRequestMaxUploadFileNum ||
			len(toUploadTasks) == 0 {
			// reachedMaxUploadFileSize 代表到达了这一批上传文件的大小上限
			// len(uploadFileBatchTasks) > definition.DefaultPerRequestMaxUploadFileNum 代表这一批上传文件的数量已达到上限
			// len(toUploadTasks) == 0 代表此时已经是最后一批上传的文件了
			var toUploadFiles []string
			for _, asyncTask := range uploadFileBatchTasks {
				toUploadFiles = append(toUploadFiles, asyncTask.GetFilePath())
			}
			uploadFileBatchTasks = make([]*definition.AsyncTask, 0)

			response, err := components.UploadFileToEmbedding(engine.WorkspacePath, toUploadFiles, len(toUploadFiles))
			if err != nil || response == nil {
				// 失败则丢弃这一批文件
				log.Errorf("[codebase]-[vector client engine] [upload file] error: %v", err)
				continue
			}

			for _, result := range response.Results {
				if result.Success {
					// 文件上传成功，最终需要获取该文件的Embedding数据
					serverHandlingFileCnt += 1
				} else {
					// 文件上传失败
					// 或者未从response找到该文件
					// 直接丢弃，等待下次索引
					filePath := filepath.Join(engine.WorkspacePath, result.FilePath)
					log.Errorf("[codebase]-[server vector engine] [upload file] filePath: %s, error: %v", filePath, result.Error)
				}
			}

			// 上传一次文件睡10s，不要太频繁大量文件上传
			// TODO 如果不需要可以删掉
			//time.Sleep(10 * time.Second)

		}
	}
}

// PollingFileStatus
// 达到本地存储文件阈值上限后结束
// 达到一个时间后结束
func (engine *SqliteVecRetrieveEngine) PollingFileStatus(priorityQueue *collection.PriorityQueue[*definition.AsyncTask]) {
	pollingQueue := make([]*definition.AsyncTask, 0)

	// 按照优先级取出所有队列数据
	for priorityQueue.Len() > 0 {
		item := priorityQueue.PopItem()
		if item == nil {
			continue
		}
		pollingQueue = append(pollingQueue, item.Value())
	}

	startTime := time.Now()
	for len(pollingQueue) > 0 {
		if len(pollingQueue) == 0 {
			break
		}

		// 达到最大时间，停止同步，等待下一次触发异步索引
		if time.Now().Sub(startTime) >= definition.DefaultAsyncMaxWaiting {
			log.Errorf("[codebase]-[vector client engine] [fetch embed chunks] waiting too long, stop")
			break
		}

		// 存储达到上限
		storageFileNum := engine.GetStorageFileNum()
		if storageFileNum >= global.GetMaxClientStorageFileNum() {
			log.Warnf("[codebase]-[vector client engine] [fetch embed chunks] storage file num is %d, stop", storageFileNum)
			break
		}

		batchSize := definition.DefaultPerRequestMaxCheckFileStatusNum
		if len(pollingQueue) < batchSize {
			batchSize = len(pollingQueue)
		}

		checkStatusBatchTasks := pollingQueue[:batchSize]
		pollingQueue = pollingQueue[batchSize:]

		toCheckFileIdMap := make(map[string]struct{})
		for _, asyncTask := range checkStatusBatchTasks {
			toCheckFileIdMap[asyncTask.FileId] = struct{}{}
		}

		var toCheckFileIds []string
		for fileId, _ := range toCheckFileIdMap {
			toCheckFileIds = append(toCheckFileIds, fileId)
		}

		response, err := components.CheckServerFileStatus(toCheckFileIds)
		if err != nil || response == nil {
			log.Errorf("[codebase]-[vector client engine] [fetch embed chunks] check file status failed: %v", err)
			continue
		}

		toGetEmbedChunksTasks := make([]*definition.AsyncTask, 0)
		for _, task := range checkStatusBatchTasks {
			if fileStatus, found := response.FileStatuses[task.FileId]; found {
				if fileStatus == definition.ServerFileStatusSynced {
					// 文件已完成同步，可以获取Embedding
					toGetEmbedChunksTasks = append(toGetEmbedChunksTasks, task)
				} else if fileStatus == definition.ServerFileStatusPending {
					// 文件未完成同步，需要继续等待
					pollingQueue = append(pollingQueue, task)
				} else {
					// 文件未上传，丢弃
				}
			} else {
				// 服务端未返回该文件状态
				// 丢弃该任务即可
			}
		}

		if len(toGetEmbedChunksTasks) > 0 {
			// 获取Embedding
			for idx := 0; idx < len(toGetEmbedChunksTasks); idx += definition.DefaultClientAsyncPerRequestMaxFetchEmbedFileNum {
				lastIdx := idx + definition.DefaultClientAsyncPerRequestMaxFetchEmbedFileNum
				if lastIdx > len(toGetEmbedChunksTasks) {
					lastIdx = len(toGetEmbedChunksTasks)
				}
				tasks := toGetEmbedChunksTasks[idx:lastIdx]
				go func() {
					if fetchErr := engine.FetchEmbedChunks(tasks); fetchErr != nil {
						log.Errorf("[codebase]-[vector client engine] fetch embed chunks failed, err: %v", fetchErr)
					}
				}()
			}
		} else {
			// 代表仍然有大量文件在队列中，因此适当做等待时间
			time.Sleep(definition.DefaultAsyncFetchEmbedChunksTimeInterval)
		}

	}

	return
}

func (engine *SqliteVecRetrieveEngine) FetchEmbedChunks(tasks []*definition.AsyncTask) error {
	fileIds := make([]string, 0)
	for _, task := range tasks {
		fileIds = append(fileIds, task.FileId)
	}

	if engine.GetStorageFileNum() >= global.GetMaxClientStorageFileNum() {
		log.Warnf("[codebase]-[vector client engine] [fetch embed chunks] storage file num is %d, skip", engine.GetStorageFileNum())
		return nil
	}

	response, err := components.FetchServerEmbedChunks(fileIds)
	if err != nil {
		log.Errorf("[codebase]-[vector client engine] [fetch embed chunks] get embed chunks failed: %v", err)
		return err
	}
	if response == nil {
		log.Errorf("[codebase]-[vector client engine] [fetch embed chunks] get embed chunks response is nil")
		return errors.New("get embed chunks response is nil")
	}

	for _, task := range tasks {
		allChunkEmbeddingSuccess := true
		storageChunks := make([]definition.StorageChunk, 0)

		for _, chunk := range response.FileChunks[task.FileId] {
			if chunk.Embedding == nil {
				log.Errorf("[codebase]-[vector client engine] [fetch embed chunks] get embed chunks embedding is nil")
				allChunkEmbeddingSuccess = false
				break
			}

			filePath := task.GetFilePath()
			fileContent, err := rag.GetFileContentByLine(filePath, chunk.StartLine, chunk.EndLine)
			if err != nil {
				allChunkEmbeddingSuccess = false
				break
			}
			chunkId := rag.GetChunkId(filePath, fileContent)
			storageChunks = append(storageChunks, definition.StorageChunk{
				ChunkId:     chunkId,
				FilePath:    filePath,
				FileName:    filepath.Base(filePath),
				Embedding:   chunk.Embedding,
				StartLine:   chunk.StartLine,
				EndLine:     chunk.EndLine,
				StartOffset: chunk.StartOffset,
				EndOffset:   chunk.EndOffset,
				Content:     fileContent,
			})
		}

		if allChunkEmbeddingSuccess {
			err = engine.Client.BatchUpdate(storageChunks, task.VirtualFile)
			if err != nil {
				log.Errorf("[codebase]-[vector client engine] [fetch embed chunks] save chunks err: %v", err)
				continue
			}

			// 更新共享chunk
			for _, chunk := range storageChunks {
				shareChunk := definition.NewShareChunkFromStorageChunk(chunk)
				if shareChunk == nil {
					continue
				}
				err = vector.InsertShareChunk(*shareChunk)
				if err != nil {
					log.Errorf("[codebase]-[vector client engine] [fetch embed chunks] insert share chunk err: %v", err)
				}
			}
		}
	}

	return nil
}

func (engine *SqliteVecRetrieveEngine) Close() {
	if engine.Client != nil {
		engine.Client.Close()
	}
}

// ============================= 本文件以下部分都是废代码，等待后续的架构变动再启用 =============================================================

// MergeWrappers 合并算法
// 这是一串废代码，因为batchSize不会改为1以上了
func (engine *SqliteVecRetrieveEngine) MergeWrappers(wrappers []*definition.TaskWrapper) []definition.MergedTaskWrapper {
	sort.Slice(wrappers, func(i, j int) bool {
		// 升序排序
		return len(wrappers[i].Chunks) <= len(wrappers[j].Chunks)
	})

	var mergedTasks []definition.MergedTaskWrapper

	tmpMergedTask := definition.MergedTaskWrapper{}
	tmpChunkSize := 0
	for _, wrapper := range wrappers {
		// 合并进去，计算是否达到可容忍的chunk数量
		tmpMergedTask.Wrappers = append(tmpMergedTask.Wrappers, wrapper)
		tmpChunkSize += len(wrapper.Chunks)

		// 每次请求最多使用 10 个chunk，这个参数代表最后一个请求使用多少个chunk
		lastRequestChunkNum := tmpChunkSize % definition.DefaultEmbeddingBatchSize

		// 不满 10 个的chunk数量则是每次请求浪费的chunk数量
		wastedChunkNum := definition.DefaultEmbeddingBatchSize - lastRequestChunkNum

		// 如果最后一个请求使用的chunk数量小于等于可容忍的chunk数量，则继续合并，否则重新初始化
		if lastRequestChunkNum == 0 || wastedChunkNum <= DefaultTolerantEmbeddingChunk {
			// 达到了可容忍的程度，合并进去，重新初始化
			mergedTasks = append(mergedTasks, tmpMergedTask)
			tmpChunkSize = 0
			tmpMergedTask = definition.MergedTaskWrapper{}
		}
	}

	if tmpChunkSize > 0 {
		mergedTasks = append(mergedTasks, tmpMergedTask)
	}

	return mergedTasks
}

type innerEmbeddingResult struct {
	originIndex int
	embedding   []float32
}

func (engine *SqliteVecRetrieveEngine) doEmbedding(originIndexList []int, chunks []*definition.StorageChunk) ([][]float32, error) {
	var texts []string
	for _, chunk := range chunks {
		texts = append(texts, chunk.Content)
	}

	embedder := components.NewLingmaEmbedder()
	embeddings, err := embedder.CreateEmbedding(context.Background(), texts, components.TextTypeDocument)
	if err != nil {
		log.Errorf("[codebase]-[vector engine] [embedding] error, err: %v", err)
		return nil, err
	}

	if len(embeddings) != len(originIndexList) {
		log.Errorf("[codebase]-[vector engine] [embedding] length not equal to origin index list length")
		return nil, errors.New("embedding length not equal to origin index list length")
	}

	return embeddings, nil
}
