package rag

import (
	"os"
	"path/filepath"
	"strings"
	"testing"
)

func TestDynamicContextLines(t *testing.T) {
	// Create a temporary test file
	tempDir := t.TempDir()
	testFile := filepath.Join(tempDir, "test.go")

	testContent := `line 1
line 2
line 3
line 4
line 5
line 6
line 7
line 8
line 9
line 10`

	err := os.WriteFile(testFile, []byte(testContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}

	testCases := []struct {
		name          string
		contextLines  int
		matchLine     uint32
		expectedStart uint32
		expectedEnd   uint32
	}{
		{
			name:          "Context 1 line",
			contextLines:  1,
			matchLine:     5,
			expectedStart: 4, // 5-1
			expectedEnd:   6, // 5+1
		},
		{
			name:          "Context 2 lines",
			contextLines:  2,
			matchLine:     5,
			expectedStart: 3, // 5-2
			expectedEnd:   7, // 5+2
		},
		{
			name:          "Context 3 lines",
			contextLines:  3,
			matchLine:     5,
			expectedStart: 2, // 5-3
			expectedEnd:   8, // 5+3
		},
		{
			name:          "Context at file beginning",
			contextLines:  2,
			matchLine:     1,
			expectedStart: 1, // Can't go below 1
			expectedEnd:   3, // 1+2
		},
		{
			name:          "Context at file end",
			contextLines:  2,
			matchLine:     10,
			expectedStart: 8,  // 10-2
			expectedEnd:   10, // Limited by file length
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			engine := &RipGrepRetrieveEngine{
				WorkspaceUri: "/test/workspace",
				options: &RipGrepRetrieveOptions{
					MaxOneLineLength:        512,
					TopN:                    20,
					ContextExpandLineLength: tc.contextLines,
				},
			}

			matchedLines := []MatchedLine{
				{FilePath: testFile, LineNum: tc.matchLine, Content: "matched content"},
			}

			expandedChunks, err := engine.expandAndMergeChunks(matchedLines, tc.contextLines)
			if err != nil {
				t.Fatalf("expandAndMergeChunks failed: %v", err)
			}

			if len(expandedChunks) != 1 {
				t.Fatalf("Expected 1 chunk, got %d", len(expandedChunks))
			}

			chunk := expandedChunks[0]
			if chunk.StartLine != tc.expectedStart {
				t.Errorf("Expected start line %d, got %d", tc.expectedStart, chunk.StartLine)
			}
			if chunk.EndLine > tc.expectedEnd {
				t.Errorf("Expected end line <= %d, got %d", tc.expectedEnd, chunk.EndLine)
			}

			t.Logf("Context %d: Line %d expanded to %d-%d", tc.contextLines, tc.matchLine, chunk.StartLine, chunk.EndLine)
		})
	}
}

func TestParseRipGrepOutputWithDynamicContext(t *testing.T) {
	// Create a temporary test file
	tempDir := t.TempDir()
	testFile := filepath.Join(tempDir, "test.go")

	testContent := `line 1
line 2
line 3
line 4
line 5
line 6
line 7
line 8
line 9
line 10`

	err := os.WriteFile(testFile, []byte(testContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}

	testCases := []struct {
		name                    string
		contextExpandLineLength int
		expectedContextLines    int
	}{
		{
			name:                    "Default context (2 lines)",
			contextExpandLineLength: 0, // Will use default 2
			expectedContextLines:    2,
		},
		{
			name:                    "Custom context (1 line)",
			contextExpandLineLength: 1,
			expectedContextLines:    1,
		},
		{
			name:                    "Custom context (4 lines)",
			contextExpandLineLength: 4,
			expectedContextLines:    4,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			engine := &RipGrepRetrieveEngine{
				WorkspaceUri: "/test/workspace",
				options: &RipGrepRetrieveOptions{
					MaxOneLineLength:        512,
					TopN:                    20,
					ContextExpandLineLength: tc.contextExpandLineLength,
				},
			}

			// Sample rg output with match on line 5
			sampleOutput := testFile + ":5:line 5\n"

			result, err := engine.parseRipGrepOutput(strings.Split(sampleOutput, "\n"))
			if err != nil {
				t.Fatalf("parseRipGrepOutput failed: %v", err)
			}

			if len(result.Chunks) != 1 {
				t.Fatalf("Expected 1 chunk, got %d", len(result.Chunks))
			}

			chunk := result.Chunks[0]
			expectedStart := uint32(5 - tc.expectedContextLines)
			if expectedStart < 1 {
				expectedStart = 1
			}
			expectedEnd := uint32(5 + tc.expectedContextLines)

			if chunk.StartLine != expectedStart {
				t.Errorf("Expected start line %d, got %d", expectedStart, chunk.StartLine)
			}
			if chunk.EndLine != expectedEnd {
				t.Errorf("Expected end line %d, got %d", expectedEnd, chunk.EndLine)
			}

			t.Logf("Context %d: Match line 5 expanded to %d-%d", tc.expectedContextLines, chunk.StartLine, chunk.EndLine)
		})
	}
}
