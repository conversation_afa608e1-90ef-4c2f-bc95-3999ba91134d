package rag

import (
	"os"
	"path/filepath"
	"strings"
	"testing"
)

func TestTruncateChunkBySize(t *testing.T) {
	engine := &RipGrepRetrieveEngine{
		WorkspaceUri: "/test/workspace",
		options: &RipGrepRetrieveOptions{
			MaxOneLineLength:        512,
			TopN:                    20,
			ContextExpandLineLength: 2,
			MaxChunkSizeBytes:       100, // Small limit for testing
		},
	}

	// Create a temporary test file with varying line lengths
	tempDir := t.TempDir()
	testFile := filepath.Join(tempDir, "test.go")

	testContent := `short line 1
this is a much longer line that contains more content than the others - line 2
short line 3
MATCHED LINE - this is the line we want to keep at all costs - line 4
short line 5
another longer line with substantial content that we might need to truncate - line 6
short line 7`

	err := os.WriteFile(testFile, []byte(testContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}

	testCases := []struct {
		name            string
		startLine       uint32
		endLine         uint32
		matchedLines    []uint32
		maxSizeBytes    int
		expectMatched   bool
		expectTruncated bool
	}{
		{
			name:            "Small limit with matched line",
			startLine:       3,
			endLine:         6,
			matchedLines:    []uint32{4}, // The matched line
			maxSizeBytes:    100,
			expectMatched:   true,
			expectTruncated: true,
		},
		{
			name:            "Large limit allows all content",
			startLine:       1,
			endLine:         7,
			matchedLines:    []uint32{4},
			maxSizeBytes:    1000,
			expectMatched:   true,
			expectTruncated: false,
		},
		{
			name:            "Very small limit only matched line",
			startLine:       2,
			endLine:         6,
			matchedLines:    []uint32{4},
			maxSizeBytes:    80, // Increase limit to accommodate the matched line
			expectMatched:   true,
			expectTruncated: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			content, actualStart, actualEnd, err := engine.truncateChunkBySize(
				testFile, tc.startLine, tc.endLine, tc.matchedLines, tc.maxSizeBytes)

			if err != nil {
				t.Fatalf("truncateChunkBySize failed: %v", err)
			}

			// Check that content is within size limit
			if len(content) > tc.maxSizeBytes {
				t.Errorf("Content size %d exceeds limit %d", len(content), tc.maxSizeBytes)
			}

			// Check that matched line is included
			if tc.expectMatched {
				if !strings.Contains(content, "MATCHED LINE") {
					t.Errorf("Expected matched line to be preserved in content. Got: '%s'", content)
				}
			}

			// Check truncation behavior
			lines := strings.Split(content, "\n")
			if tc.expectTruncated {
				expectedLines := int(tc.endLine - tc.startLine + 1)
				if len(lines) >= expectedLines {
					t.Errorf("Expected truncation but got %d lines (expected < %d)", len(lines), expectedLines)
				}
			}

			t.Logf("Test case '%s': Content size=%d, Lines=%d, Range=%d-%d",
				tc.name, len(content), len(lines), actualStart, actualEnd)
		})
	}
}

func TestParseRipGrepOutputWithSizeLimit(t *testing.T) {
	engine := &RipGrepRetrieveEngine{
		WorkspaceUri: "/test/workspace",
		options: &RipGrepRetrieveOptions{
			MaxOneLineLength:        512,
			TopN:                    20,
			ContextExpandLineLength: 3,   // Expand 3 lines each direction
			MaxChunkSizeBytes:       150, // Moderate limit
		},
	}

	// Create a temporary test file
	tempDir := t.TempDir()
	testFile := filepath.Join(tempDir, "test.go")

	testContent := `line 1 - short
line 2 - this is a longer line that takes up more space
line 3 - short  
line 4 - MATCHED CONTENT that we want to preserve
line 5 - short
line 6 - another longer line that might get truncated due to size limits
line 7 - short`

	err := os.WriteFile(testFile, []byte(testContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}

	// Sample rg output with match on line 4
	sampleOutput := testFile + ":4:line 4 - MATCHED CONTENT that we want to preserve\n"

	result, err := engine.parseRipGrepOutput(strings.Split(sampleOutput, "\n"))
	if err != nil {
		t.Fatalf("parseRipGrepOutput failed: %v", err)
	}

	if len(result.Chunks) == 0 {
		t.Fatal("Expected at least 1 chunk")
	}

	chunk := result.Chunks[0]

	// Verify size limit is respected
	if len(chunk.Content) > 150 {
		t.Errorf("Chunk content size %d exceeds limit 150", len(chunk.Content))
	}

	// Verify matched line is preserved
	if !strings.Contains(chunk.Content, "MATCHED CONTENT") {
		t.Error("Expected matched content to be preserved")
	}

	// Verify chunk range is reasonable
	if chunk.StartLine < 1 || chunk.EndLine > 7 {
		t.Errorf("Unexpected chunk range %d-%d", chunk.StartLine, chunk.EndLine)
	}

	t.Logf("Chunk size: %d bytes, Range: %d-%d, Lines: %d",
		len(chunk.Content), chunk.StartLine, chunk.EndLine,
		len(strings.Split(chunk.Content, "\n")))
}

func TestChunkSizeLimitWithMultipleMatches(t *testing.T) {
	engine := &RipGrepRetrieveEngine{
		WorkspaceUri: "/test/workspace",
		options: &RipGrepRetrieveOptions{
			MaxOneLineLength:        512,
			TopN:                    20,
			ContextExpandLineLength: 2,
			MaxChunkSizeBytes:       120,
		},
	}

	// Create a temporary test file
	tempDir := t.TempDir()
	testFile := filepath.Join(tempDir, "test.go")

	testContent := `line 1
FIRST MATCH - this is important content that must be preserved
line 3 - context line that might be truncated
SECOND MATCH - another important line that must be kept
line 5 - more context that could be truncated
line 6`

	err := os.WriteFile(testFile, []byte(testContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}

	// Sample rg output with matches on lines 2 and 4
	sampleOutput := testFile + ":2:FIRST MATCH - this is important content that must be preserved\n" +
		testFile + ":4:SECOND MATCH - another important line that must be kept\n"

	result, err := engine.parseRipGrepOutput(strings.Split(sampleOutput, "\n"))
	if err != nil {
		t.Fatalf("parseRipGrepOutput failed: %v", err)
	}

	if len(result.Chunks) == 0 {
		t.Fatal("Expected at least 1 chunk")
	}

	chunk := result.Chunks[0]

	// Verify size limit is respected
	if len(chunk.Content) > 120 {
		t.Errorf("Chunk content size %d exceeds limit 120", len(chunk.Content))
	}

	// Verify both matched lines are preserved
	if !strings.Contains(chunk.Content, "FIRST MATCH") {
		t.Error("Expected first matched content to be preserved")
	}
	if !strings.Contains(chunk.Content, "SECOND MATCH") {
		t.Error("Expected second matched content to be preserved")
	}

	t.Logf("Multi-match chunk size: %d bytes, Content: %s",
		len(chunk.Content), strings.ReplaceAll(chunk.Content, "\n", "\\n"))
}
