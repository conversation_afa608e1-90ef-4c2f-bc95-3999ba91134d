package rag

import (
	"context"
	"cosy/client"
	"cosy/components"
	"cosy/config"
	"cosy/definition"
	"cosy/indexing/common"
	"cosy/tree"
	"cosy/util"
	"cosy/util/rag"
	"fmt"
	"math"
	"os"
	"path/filepath"
	"runtime"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func GetRepoWorkspacePath() string {
	repoPath, _ := util.GetTestRepoLocalPath("https://github.com/macrozheng/mall.git")
	return repoPath
}

// 这个只用于测试，所以写死了
func GetFilePaths() []string {
	workspacePath := GetRepoWorkspacePath()
	// 目标路径为：mall-admin/src/main/java/com/macro/mall/controller
	targetDir := filepath.Join(workspacePath, "mall-admin/src/main/java/com/macro/mall/controller")
	// 读取目录内容
	entries, err := os.ReadDir(targetDir)
	if err != nil {
		return nil
	}

	// 存储文件名
	var files []string

	// 遍历目录内容
	for _, entry := range entries {
		// 判断是否为文件
		if !entry.IsDir() {
			filePath := filepath.Join(targetDir, entry.Name())
			files = append(files, filePath)
		}
	}

	return files
}

func GetVectorBatchTask() definition.VectorBatchTask {
	workspacePath := GetRepoWorkspacePath()

	filepathList := GetFilePaths()

	var tasks []*definition.Task
	for _, filePath := range filepathList {
		virtualFile := definition.NewVirtualFile(filePath)
		task := definition.NewTask(virtualFile, definition.VectorFullIndexSource, workspacePath, 0)
		tasks = append(tasks, task)
	}

	return definition.NewVectorBatchTask(workspacePath, tasks)
}

func TestGetFilePaths(t *testing.T) {
	filepathList := GetFilePaths()
	for _, path := range filepathList {
		fmt.Println(path)
	}
}

func TestNewChatSqliteVecRetrieveEngine(t *testing.T) {
	client.InitClients()
	config.InitLocalConfig()

	workspacePath := GetRepoWorkspacePath()
	fmt.Println(workspacePath)
	engine, err := newChatSqliteVecRetrieveEngine(workspacePath)
	assert.NotNil(t, engine)
	assert.Nil(t, err)
	assert.NotNil(t, engine.Client)
}

func TestSqliteVecRetrieveEngine_BatchSplitFiles(t *testing.T) {
	client.InitClients()
	config.InitLocalConfig()

	workspacePath := GetRepoWorkspacePath()
	engine, err := newChatSqliteVecRetrieveEngine(workspacePath)
	assert.NotNil(t, engine)
	assert.Nil(t, err)

	vectorBatchTask := GetVectorBatchTask()
	startTime := time.Now()
	wrappers := engine.BatchSplitFile(vectorBatchTask, false)
	endTime := time.Now()
	fmt.Printf("batch split file cost: %f s\n", endTime.Sub(startTime).Seconds())
	for _, wrapper := range wrappers {
		fmt.Println(wrapper.Task.VirtualFile.GetFilePath())
		for _, chunk := range wrapper.Chunks {
			//fmt.Println(chunk.Content)
			fmt.Printf("startLine: %d, endLine: %d\n", chunk.StartLine, chunk.EndLine)
			fmt.Println("*****************************************")
		}
		fmt.Printf("\n\n===================================\n\n")
	}

	assert.Equal(t, len(wrappers), len(vectorBatchTask.Tasks))
}

func TestSqliteVecRetrieveEngine_MergeWrappers(t *testing.T) {
	client.InitClients()
	config.InitLocalConfig()

	workspacePath := GetRepoWorkspacePath()
	engine, err := newChatSqliteVecRetrieveEngine(workspacePath)
	assert.NotNil(t, engine)
	assert.Nil(t, err)

	vectorBatchTask := GetVectorBatchTask()
	wrappers := engine.BatchSplitFile(vectorBatchTask, false)
	startTime := time.Now()
	mergedTasks := engine.MergeWrappers(wrappers)
	endTime := time.Now()
	fmt.Printf("merge task cost: %f s\n", endTime.Sub(startTime).Seconds())
	for idx, mergedTask := range mergedTasks {
		total := 0
		for _, inWrappers := range mergedTask.Wrappers {
			fmt.Printf("%d, ", len(inWrappers.Chunks))
			total += len(inWrappers.Chunks)
		}
		lastRequestNum := total % definition.DefaultEmbeddingBatchSize
		wastedNum := definition.DefaultEmbeddingBatchSize - lastRequestNum
		if idx != len(mergedTasks)-1 {
			assert.Equal(t, true, wastedNum <= DefaultTolerantEmbeddingChunk)
		}
		fmt.Println("\n===================")
	}
}

// 这个测试用来验证，embedding结果和对应的chunk是正确对应的
func TestSqliteVecRetrieveEngine_EmbedChunks(t *testing.T) {
	client.InitClients()
	config.InitLocalConfig()

	workspacePath := GetRepoWorkspacePath()
	engine, err := newChatSqliteVecRetrieveEngine(workspacePath)
	assert.NotNil(t, engine)
	assert.Nil(t, err)

	// 使用engine来进行embedding
	vectorBatchTask := GetVectorBatchTask()
	taskWrappers := engine.BatchSplitFile(vectorBatchTask, false)
	var engineChunks []*definition.StorageChunk
	var embedderChunks []*definition.StorageChunk
	for _, taskWrapper := range taskWrappers {
		for _, chunk := range taskWrapper.Chunks {
			engineChunks = append(engineChunks, &definition.StorageChunk{
				ChunkId:     chunk.ChunkId,
				Content:     chunk.Content,
				FilePath:    chunk.FilePath,
				FileName:    chunk.FileName,
				StartLine:   chunk.StartLine,
				EndLine:     chunk.EndLine,
				StartOffset: chunk.StartOffset,
				EndOffset:   chunk.EndOffset,
				Embedding:   nil,
			})

			embedderChunks = append(embedderChunks, &definition.StorageChunk{
				ChunkId:     chunk.ChunkId,
				Content:     chunk.Content,
				FilePath:    chunk.FilePath,
				FileName:    chunk.FileName,
				StartLine:   chunk.StartLine,
				EndLine:     chunk.EndLine,
				StartOffset: chunk.StartOffset,
				EndOffset:   chunk.EndOffset,
				Embedding:   nil,
			})
		}
	}

	err = engine.EmbedChunks(engineChunks, true)
	assert.Nil(t, err)

	embedder := components.NewLingmaEmbedder()
	var texts []string
	for _, chunk := range embedderChunks {
		texts = append(texts, chunk.Content)
	}
	embeddings, err := embedder.CreateEmbedding(context.Background(), texts, components.TextTypeDocument)
	assert.Nil(t, err)
	for idx, embedding := range embeddings {
		embedderChunks[idx].Embedding = embedding
	}

	compareCnt := 0
	for _, engineChunk := range engineChunks {
		for _, embedderChunk := range embedderChunks {
			if engineChunk.ChunkId == embedderChunk.ChunkId {
				compareCnt++
				engineEmbedding := engineChunk.Embedding
				embedderEmbedding := embedderChunk.Embedding
				assert.Equal(t, len(engineEmbedding), len(embedderEmbedding))
				for i := 0; i < len(engineEmbedding); i++ {
					assert.Equal(t, engineEmbedding[i], embedderEmbedding[i])
				}
			}
		}
	}

	assert.Equal(t, compareCnt, len(embedderChunks))

}

func TestSqliteVecRetrieveEngine_BatchEmbedChunks(t *testing.T) {
	client.InitClients()
	config.InitLocalConfig()

	workspacePath := GetRepoWorkspacePath()
	engine, err := newChatSqliteVecRetrieveEngine(workspacePath)
	assert.NotNil(t, engine)
	assert.Nil(t, err)

	vectorBatchTask := GetVectorBatchTask()
	wrappers := engine.BatchSplitFile(vectorBatchTask, false)
	//mergedTasks := engine.MergeWrappers(wrappers)
	startTime := time.Now()
	embedWrappers := engine.BatchEmbedChunks(wrappers, len(vectorBatchTask.Tasks), true)
	endTime := time.Now()
	fmt.Printf("batch embed cost: %f s\n", endTime.Sub(startTime).Seconds())

	assert.Equal(t, len(embedWrappers), len(vectorBatchTask.Tasks))
	for _, wrapper := range embedWrappers {
		for _, chunk := range wrapper.Chunks {
			assert.NotNil(t, chunk.Embedding)
			fmt.Println("*****************************************", chunk.Embedding[0])
		}
		fmt.Printf("\n\n===================================\n\n")
	}
}

func TestSqliteVecRetrieveEngine_BatchSaveIndex(t *testing.T) {
	client.InitClients()
	config.InitLocalConfig()

	workspacePath := GetRepoWorkspacePath()
	engine, err := newChatSqliteVecRetrieveEngine(workspacePath)
	assert.NotNil(t, engine)
	assert.Nil(t, err)

	vectorBatchTask := GetVectorBatchTask()
	wrappers := engine.BatchSplitFile(vectorBatchTask, false)
	//mergedTasks := engine.MergeWrappers(wrappers)
	embedWrappers := engine.BatchEmbedChunks(wrappers, len(vectorBatchTask.Tasks), true)
	startTime := time.Now()
	successWrapper := engine.BatchSaveIndex(embedWrappers, len(vectorBatchTask.Tasks))
	endTime := time.Now()
	fmt.Printf("batch save cost: %f s\n", endTime.Sub(startTime).Seconds())

	assert.Equal(t, len(successWrapper), len(vectorBatchTask.Tasks))
}

func TestSqliteVecRetrieveEngine_ExecuteBatchTask_ForOneFile(t *testing.T) {
	client.InitClients()
	config.InitLocalConfig()

	workspacePath := GetRepoWorkspacePath()
	engine, err := newChatSqliteVecRetrieveEngine(workspacePath)
	assert.NotNil(t, engine)
	assert.Nil(t, err)

	filePath := "/Users/<USER>/workspace/source-code/mall/mall-mbg/src/main/java/com/macro/mall/model/PmsProduct.java"
	virtualFile := definition.NewVirtualFile(filePath)
	task := definition.NewTask(virtualFile, definition.VectorFullIndexSource, workspacePath, 0)
	vectorBatchTask := definition.NewVectorBatchTask(workspacePath, []*definition.Task{task})
	startTime := time.Now()
	engine.ExecuteBatchTask(vectorBatchTask, false, true)
	endTime := time.Now()

	fmt.Println("存储块：", engine.GetStorageChunkNum())

	assert.Equal(t, true, engine.GetStorageChunkNum() > 0)

	fmt.Printf("batch save cost: %f s\n", endTime.Sub(startTime).Seconds())

}

func TestSqliteVecRetrieveEngine_ExecuteBatchTask(t *testing.T) {
	client.InitClients()
	config.InitLocalConfig()

	workspacePath := GetRepoWorkspacePath()
	engine, err := newChatSqliteVecRetrieveEngine(workspacePath)
	assert.NotNil(t, engine)
	assert.Nil(t, err)

	vectorBatchTask := GetVectorBatchTask()
	startTime := time.Now()
	engine.ExecuteBatchTask(vectorBatchTask, false, true)
	endTime := time.Now()

	assert.Equal(t, true, engine.GetStorageChunkNum() > 0)

	fmt.Printf("batch save cost: %f s\n", endTime.Sub(startTime).Seconds())

}

func TestSqliteVecRetrieveEngine_BatchDeleteIndex(t *testing.T) {
	client.InitClients()
	config.InitLocalConfig()

	workspacePath := GetRepoWorkspacePath()
	engine, err := newChatSqliteVecRetrieveEngine(workspacePath)
	assert.NotNil(t, engine)
	assert.Nil(t, err)

	filepathList := GetFilePaths()
	startTime := time.Now()
	virtualFiles := definition.BuildBatchVirtualFile(filepathList)
	err = engine.BatchDeleteIndex(virtualFiles, false)
	endTime := time.Now()
	assert.Nil(t, err)
	fmt.Printf("batch delete cost: %f s\n", endTime.Sub(startTime).Seconds())
}

func TestSqliteVecRetrieveEngine_Retrieve(t *testing.T) {
	client.InitClients()
	config.InitLocalConfig()

	workspacePath := GetRepoWorkspacePath()
	engine, err := newChatSqliteVecRetrieveEngine(workspacePath)
	assert.NotNil(t, engine)
	assert.Nil(t, err)

	vectorBatchTask := GetVectorBatchTask()
	startTime := time.Now()
	engine.ExecuteBatchTask(vectorBatchTask, true, true)
	endTime := time.Now()
	fmt.Printf("batch save cost: %f s\n", endTime.Sub(startTime).Seconds())

	embedder := components.NewLingmaEmbedder()
	queryEmbedding, err := embedder.CreateEmbedding(context.Background(), []string{"添加后台资源分类的controller在哪部分"}, components.TextTypeQuery)
	assert.Nil(t, err)

	queryCondition := definition.QueryCondition{
		QueryEmbedding: queryEmbedding[0],
		ScoreThreshold: 0.0,
		TopK:           10,
	}
	result, err := engine.Retrieve(queryCondition)
	assert.Nil(t, err)
	assert.Equal(t, len(result.Chunks), 10)
	for _, chunk := range result.Chunks {
		fmt.Println(chunk.Content)
		fmt.Println("=============================================")
	}

	// 这部分是为了测试ScoreThreshold的生效性
	queryCondition = definition.QueryCondition{
		QueryEmbedding: queryEmbedding[0],
		ScoreThreshold: 0.2,
		TopK:           10,
	}
	result, err = engine.Retrieve(queryCondition)
	assert.Nil(t, err)
	assert.Equal(t, true, len(result.Chunks) <= 10)
}

func TestSqliteVecRetrieveEngine_RebuildIndexStatusTree(t *testing.T) {
	client.InitClients()
	config.InitLocalConfig()

	workspacePath := "/Users/<USER>/workspace/lingma/cosy"
	engine, err := newChatSqliteVecRetrieveEngine(workspacePath)
	assert.NotNil(t, engine)
	assert.Nil(t, err)
}

func TestSqliteVecRetrieveEngine_executeAsyncBuildIndex(t *testing.T) {
	client.InitClients()
	config.InitLocalConfig()

	workspacePath := "/Users/<USER>/workspace/lingma/cosy"
	engine, err := newChatSqliteVecRetrieveEngine(workspacePath)
	assert.NotNil(t, engine)
	assert.Nil(t, err)
	realTimeMTree := tree.NewWorkspaceMerkleTree(workspacePath).Clone()
	followTree := engine.GetClonedIndexStatusTree()

	ignoreParser := common.NewProjectIgnore(workspacePath)
	//err = ignoreParser.LoadWorkspace(workspacePath)
	//assert.Nil(t, err)

	todoIndexTreeNode := tree.DiffMTreeNodes(realTimeMTree.Tree, followTree.Tree, true, true)

	dirIgnoreRule := &common.IgnoreRule{
		GlobalIgnoreEnable: true,
		IsDir:              true,
	}
	fileIgnoreRule := &common.IgnoreRule{
		GlobalIgnoreEnable: true,
		IsDir:              false,
	}
	virtualFiles := make([]definition.VirtualFile, 0)
	for _, node := range todoIndexTreeNode {
		absPath := filepath.Join(workspacePath, node.RelativePath)
		fileDir := filepath.Dir(absPath)
		if !util.FileExists(absPath) {
			continue
		}

		if ignoreParser != nil &&
			(ignoreParser.IsIgnored(workspacePath, fileDir, dirIgnoreRule) ||
				ignoreParser.IsIgnored(workspacePath, absPath, fileIgnoreRule)) {
			continue
		}

		virtualFiles = append(virtualFiles, definition.NewVirtualFile(absPath))
	}
	engine.executeAsyncBuildIndex(virtualFiles, definition.VectorFullIndexSource)

}

func TestAsyncAndSyncResultEqual(t *testing.T) {
	client.InitClients()
	config.InitLocalConfig()

	workspacePath := GetRepoWorkspacePath()
	engine, err := newChatSqliteVecRetrieveEngine(workspacePath)
	assert.NotNil(t, engine)
	assert.Nil(t, err)

	relFilePaths := []string{
		"mall-search/src/main/java/com/macro/mall/search/MallSearchApplication.java",
		"mall-search/src/main/java/com/macro/mall/search/domain/EsProduct.java",
		"mall-search/src/main/java/com/macro/mall/search/domain/EsProductAttributeValue.java",
		"mall-search/src/main/java/com/macro/mall/search/controller/EsProductController.java",
		"mall-portal/src/main/java/com/macro/mall/portal/service/MemberReadHistoryService.java",
		"mall-mbg/src/main/java/com/macro/mall/model/UmsResource.java",
		"mall-mbg/src/main/java/com/macro/mall/model/UmsMemberStatisticsInfoExample.java",
	}

	for _, relFilePath := range relFilePaths {
		// 1. 获取同步 embedding结果
		// 以测试库的README.md为例
		filePath := filepath.Join(workspacePath, relFilePath)
		virtualFile := definition.NewVirtualFile(filePath)

		task := definition.NewTask(virtualFile, definition.VectorFullIndexSource, workspacePath, 0)
		tasks := definition.NewVectorBatchTask(workspacePath, []*definition.Task{task})
		// 切分文件
		splitTaskWrappers := engine.BatchSplitFile(tasks, false)
		assert.Equal(t, 1, len(splitTaskWrappers))

		// 执行embedding
		embeddingTaskWrappers := engine.BatchEmbedChunks(splitTaskWrappers, len(tasks.Tasks), true)
		assert.Equal(t, 1, len(embeddingTaskWrappers))
		syncChunks := embeddingTaskWrappers[0].Chunks

		// 2. 获取异步 embedding结果
		uploadResponse, err := components.UploadFileToEmbedding(engine.WorkspacePath, []string{filePath}, 1)
		assert.Nil(t, err)
		assert.NotNil(t, uploadResponse)

		for _, result := range uploadResponse.Results {
			assert.Equal(t, result.Success, true)
		}

		fileContent, err := virtualFile.GetFileContent()
		assert.Nil(t, err)
		fileId := definition.GetFileId(fileContent)
		for {
			checkStatusResponse, err := components.CheckServerFileStatus([]string{fileId})
			assert.Nil(t, err)
			assert.Equal(t, 1, len(checkStatusResponse.FileStatuses))

			if checkStatusResponse.FileStatuses[fileId] == definition.ServerFileStatusSynced {
				// 服务端处理完成
				break
			}
			time.Sleep(3 * time.Second)
		}
		chunksResponse, err := components.FetchServerEmbedChunks([]string{fileId})
		assert.Nil(t, err)
		assert.Equal(t, 1, len(chunksResponse.FileChunks))
		asyncChunks := make([]definition.StorageChunk, 0)
		allChunkEmbeddingSuccess := true
		for _, chunk := range chunksResponse.FileChunks[fileId] {
			if chunk.Embedding == nil {
				allChunkEmbeddingSuccess = false
				break
			}

			//chunkContent, err := rag.GetFileContentByLine(filePath, chunk.StartLine, chunk.EndLine)
			chunkContent, err := rag.GetFileContentByOffset(filePath, chunk.StartOffset, chunk.EndOffset)
			assert.Nil(t, err)
			if err != nil {
				allChunkEmbeddingSuccess = false
				break
			}
			chunkId := rag.GetChunkId(filePath, chunkContent)
			asyncChunks = append(asyncChunks, definition.StorageChunk{
				ChunkId:     chunkId,
				FilePath:    filePath,
				FileName:    filepath.Base(filePath),
				Embedding:   chunk.Embedding,
				StartLine:   chunk.StartLine,
				EndLine:     chunk.EndLine,
				StartOffset: chunk.StartOffset,
				EndOffset:   chunk.EndOffset,
				Content:     chunkContent,
			})
		}
		assert.Equal(t, true, allChunkEmbeddingSuccess)
		assert.Equal(t, len(syncChunks), len(asyncChunks))
		for _, syncChunk := range syncChunks {
			found := false
			for _, asyncChunk := range asyncChunks {
				if syncChunk.ChunkId == asyncChunk.ChunkId {
					found = true
				}

				if found {
					for i := 0; i < len(syncChunk.Embedding); i++ {
						assert.Equal(t, true, math.Abs(float64(syncChunk.Embedding[i]-asyncChunk.Embedding[i])) <= 1e-6)
					}
					break
				}
			}

			assert.Equal(t, true, found)
		}
	}

}

func TestGetNumCpus(t *testing.T) {
	numCpus := runtime.NumCPU()
	fmt.Println("numCpus:", numCpus)
}
