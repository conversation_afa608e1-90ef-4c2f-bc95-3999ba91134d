package parser

import (
	"errors"
	"golang.org/x/mod/modfile"
	"os"
	"path/filepath"
)

// HACK: used to store the base name of the go mod file.
// It may be modified during unit testing to mock different module name scenarios.
var goModFileBaseName = GO_MODE_FILE

func getNearestDirPath(path string) (string, error) {
	fi, err := os.Stat(path)
	if err != nil {
		return "", err
	}

	if fi.IsDir() {
		return path, nil
	} else {
		return filepath.Dir(path), nil
	}
}

func locateVendorDir(path string) string {
	prevPath := path

	for {
		dir := filepath.Dir(prevPath)
		if filepath.Base(dir) == "vendor" {
			return dir
		}

		if dir == prevPath {
			break
		}

		prevPath = dir
	}

	return ""
}

func GetModulePathForVendored(path string) (string, error) {
	vendorDir := locateVendorDir(path)
	if vendorDir == "" {
		return "", nil
	}

	fi, err := os.Stat(path)
	if err != nil {
		return "", err
	}

	if fi.IsDir() {
		return filepath.Rel(vendorDir, path)
	} else {
		return filepath.Rel(vendorDir, filepath.Dir(path))
	}
}

func FindGoModPath(path string) (string, error) {
	fi, err := os.Stat(path)
	if err != nil {
		return "", err
	}

	if fi.IsDir() {
		goModPath := filepath.Join(path, goModFileBaseName)

		if _, err := os.Stat(goModPath); err != nil {
			if errors.Is(err, os.ErrNotExist) {
				basePath := filepath.Dir(path)
				if path == basePath {
					return "", errors.New("go.mod file not found")
				}
				return FindGoModPath(basePath)
			}

			return "", err
		} else {
			return goModPath, nil
		}
	} else {
		return FindGoModPath(filepath.Dir(path))
	}
}

func LocateModFile(path string) (*modfile.File, error) {
	goModPath, err := FindGoModPath(path)
	if err != nil {
		return nil, err
	}

	bytes, err := os.ReadFile(goModPath)
	if err != nil {
		return nil, err
	}

	return modfile.Parse(GO_MODE_FILE, bytes, nil)
}

func LocateWorkspacePath(path string) (string, error) {
	vendorPath := locateVendorDir(path)
	if vendorPath != "" {
		return getNearestDirPath(path)
	}

	goModPath, err := FindGoModPath(path)
	if err != nil {
		return "", err
	}

	return filepath.Dir(goModPath), nil
}

func GetModulePathForFile(path string) (string, error) {
	// NB: Test vendored first
	vendoredModulePath, err := GetModulePathForVendored(path)
	if err != nil {
		return "", err
	}
	if vendoredModulePath != "" {
		return vendoredModulePath, nil
	}

	file, err := LocateModFile(path)
	if err != nil {
		return "", err
	}
	if file == nil {
		return "", errors.New("go.mod file not found")
	}
	if file.Module == nil {
		return "", errors.New("module not found in go.mod file")
	}

	return file.Module.Mod.Path, nil
}

func GetGoFilesInDir(dirPath string) ([]string, error) {
	entries, err := os.ReadDir(dirPath)
	if err != nil {
		return nil, err
	}

	res := make([]string, 0, len(entries))
	for _, entry := range entries {
		if entry.IsDir() {
			continue
		}

		filename := entry.Name()
		if filepath.Ext(filename) != ".go" {
			continue
		}

		res = append(res, filepath.Join(dirPath, filename))
	}

	return res, nil
}
