package lang

import (
	"context"
	"cosy/definition"
	"cosy/indexing/file_change"
	"cosy/log"
	"cosy/sls"
	"cosy/tokenizer"
	"cosy/util"
	"cosy/util/distance"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"
	"unicode/utf8"

	"github.com/go-git/go-git/v5/utils/diff"
	"github.com/samber/lo"
	"github.com/sergi/go-diff/diffmatchpatch"
)

const (
	// 重写相似度阈值
	rewriteCodeSimilarityThreshold = 0.4
)

var (
	clearSpaceRegex = regexp.MustCompile(`[\s\r\n\t]+`)

	allowedImportRewriteCodeLanguageExts = []string{
		"js", "jsx", "ts", "tsx", "vue", "py",
	}
)

func IsNextEditActionInRewriteArea(nextEditLineNumber int, params definition.InlineEditParams, rewriteCodeArea definition.CodeToRewriteData) bool {
	if nextEditLineNumber >= rewriteCodeArea.StartLine && nextEditLineNumber <= rewriteCodeArea.EndLine {
		return true
	}
	return false
}

func IsNextEditActionInRewriteAreaHistorys(nextEditLineNumber int, params definition.InlineEditParams, inlineEditTriggerHistorys []InlineEditTriggerHistoryItem) bool {
	if len(inlineEditTriggerHistorys) <= 0 {
		return false
	}
	for _, triggerHistory := range inlineEditTriggerHistorys {
		if int(triggerHistory.RewriteCodeResult.Data.EditRange.Start.Line) <= nextEditLineNumber && int(triggerHistory.RewriteCodeResult.Data.EditRange.End.Line) >= nextEditLineNumber {
			return true
		}
	}
	return false
}

type ValidateContext struct {
	WorkspacePath     string
	TextDocumentUri   string
	FileContent       string
	RewriteStartLine  int
	RewriteEndLine    int
	Params            definition.InlineEditParams
	RewriteCodeResult definition.RewriteCodeActionMessage
	NextEditResult    definition.NextEditLocationActionMessage
	OriginalCode      string
	SessionId         string
	RequestId         string
	ContextData       map[string]interface{}
}

type ValidateResult struct {
	IsValid bool
	Message string
}

type ValidateRule interface {
	Validate(ctx context.Context, validContext ValidateContext) ValidateResult
}

func newRewriteResultValidators() []ValidateRule {
	return []ValidateRule{
		&RewriteLinesTooLongRule{},
		&EndWithDuplicatedLinesRule{},
		&IsRewriteResultLineSimilarityBelowThresholdRule{},
		&RewriteImportCodeFilterRule{},
		&InvalidEmptyCharRewriteRule{},
		&BracketMatchCheckRewriteRule{},
		NewChangeDiffVerifyRule(),
		&IsRewriteContentRollbackRule{},
	}
}

func nextPredictResultValidators() []ValidateRule {
	return []ValidateRule{
		&NextPredictLinesTooLongRule{},
		&EndWithDuplicatedLinesRule{},
		//&IsRewriteResultLineSimilarityBelowThresholdRule{}, 此校验规则存在误判，先去掉这个校验规则，观察一下线上的bad case
		&RewriteImportCodeFilterRule{},
		&InvalidEmptyCharRewriteRule{},
		&BracketMatchCheckRewriteRule{},
		NewChangeDiffVerifyRule(),
		&IsRewriteContentRollbackRuleV2{},
	}
}

func newNextEditLocationValidators() []ValidateRule {
	return []ValidateRule{
		&JumpToImportCodeFilterRule{},
	}
}

// 规则：改写内容出现大量重复，最后2行完全一致
type EndWithDuplicatedLinesRule struct {
}

func (e *EndWithDuplicatedLinesRule) Validate(ctx context.Context, validContext ValidateContext) ValidateResult {
	rewriteCode := validContext.RewriteCodeResult.Data.Content
	lines := make([]string, 0)
	for _, line := range splitLines(rewriteCode) {
		if len(line) > 0 {
			lines = append(lines, line)
		}
	}
	if len(lines) < 2 {
		return ValidateResult{
			IsValid: true,
		}
	}
	isDuplicated := lines[len(lines)-1] == lines[len(lines)-2]
	return ValidateResult{
		IsValid: !isDuplicated,
	}
}

// 规则：改写内容回退了用户修改
// a -> b，predict c，if c==a
type IsRewriteContentRollbackRule struct {
}

func (e *IsRewriteContentRollbackRule) Validate(ctx context.Context, validContext ValidateContext) ValidateResult {
	return e.validateWithFileChanges(ctx, validContext)
	//if !rollbackCheckWithFileChanges.IsValid {
	//	return rollbackCheckWithFileChanges
	//}
	//return e.validateWithTriggerHistory(ctx, validContext)
}

func (e *IsRewriteContentRollbackRule) validateWithTriggerHistory(ctx context.Context, validContext ValidateContext) ValidateResult {
	editTriggerHistory := inlineEditTriggerHistory.GetHistory(string(validContext.Params.TextDocument.URI), time.Now().Add(time.Minute*5), 1)

	// 如果没有历史记录,说明不是回退
	if len(editTriggerHistory) == 0 {
		return ValidateResult{
			IsValid: true,
		}
	}

	// 获取最近一次的改写记录
	lastHistory := editTriggerHistory[0]
	lastRewriteContent := lastHistory.RewriteCodeResult.Data.Content
	currentRewriteContent := validContext.RewriteCodeResult.Data.Content

	// 如果当前改写内容和上一次改写内容完全一致,说明是回退
	if lastRewriteContent == currentRewriteContent {
		return ValidateResult{
			IsValid: false,
			Message: "llm rollback user edit",
		}
	}

	// 如果当前改写内容是上一次改写内容的子集,说明是回退
	if strings.Contains(lastRewriteContent, currentRewriteContent) {
		return ValidateResult{
			IsValid: false,
			Message: "llm rollback user edit",
		}
	}

	return ValidateResult{
		IsValid: true,
	}
}

func (e *IsRewriteContentRollbackRule) validateWithFileChanges(ctx context.Context, validContext ValidateContext) ValidateResult {
	changes := file_change.GlobalFileService.GetTracedChangeQueue(validContext.WorkspacePath, string(validContext.Params.TextDocument.URI))
	if changes == nil || changes.IsEmpty() {
		return ValidateResult{
			IsValid: true,
		}
	}

	lastChange, exist := changes.PeekLast()
	if !exist {
		return ValidateResult{
			IsValid: true,
		}
	}
	diffA, err := util.GetDiffWithLineOffset(lastChange.OldText, lastChange.NewText, lastChange.StartLine)
	if err != nil {
		log.Debugf("Failed to generate diff patch: %v", err)
		return ValidateResult{
			IsValid: true,
		}
	}
	if len(diffA) == 0 {
		return ValidateResult{
			IsValid: true,
		}
	}
	diffB, err := util.GetDiffWithLineOffset(validContext.OriginalCode, validContext.RewriteCodeResult.Data.Content, validContext.RewriteStartLine)
	if err != nil {
		log.Debugf("Failed to generate diff patch: %v", err)
		return ValidateResult{
			IsValid: true,
		}
	}
	if len(diffB) == 0 {
		return ValidateResult{
			IsValid: true,
		}
	}
	hunksA := parseDiff(diffA)
	hunksB := parseDiff(diffB)
	if len(hunksA) <= 0 || len(hunksB) <= 0 {
		return ValidateResult{
			IsValid: true,
		}
	}

	//只取第一个diff校验

	if isRollbackHunk(hunksA[0], hunksB[0]) {
		//最后编辑前的内容与当前改写内容一致，说明模型发生了rollback
		return ValidateResult{
			IsValid: false,
			Message: "llm rollback user edit.",
		}
	}

	return ValidateResult{
		IsValid: true,
	}
}

// 校验文件是否发生回退，1. 与上一次操作记录比较 2. 与30秒内的操作记录比较，最多只比较5次记录
type IsRewriteContentRollbackRuleV2 struct {
}

func (e *IsRewriteContentRollbackRuleV2) Validate(ctx context.Context, validContext ValidateContext) ValidateResult {
	return e.validateWithFileChangesV2(ctx, validContext)
}

// 校验文件是否发生回退，1. 与上一次操作记录比较 2. 与30秒内的操作记录比较，最多只比较5次记录
func (e *IsRewriteContentRollbackRuleV2) validateWithFileChangesV2(ctx context.Context,
	validContext ValidateContext) ValidateResult {
	currentTime := time.Now().UnixMilli()
	changes := file_change.GlobalFileService.GetTracedChangeQueue(validContext.WorkspacePath, string(validContext.Params.TextDocument.URI))
	if changes == nil || changes.IsEmpty() {
		return ValidateResult{
			IsValid: true,
		}
	}

	lastChange, exist := changes.PeekLast()
	if !exist {
		return ValidateResult{
			IsValid: true,
		}
	}
	diffA, err := util.GetDiffWithLineOffset(lastChange.OldText, lastChange.NewText, lastChange.StartLine)
	if err != nil {
		log.Debugf("Failed to generate diff patch: %v", err)
		return ValidateResult{
			IsValid: true,
		}
	}
	if len(diffA) == 0 {
		return ValidateResult{
			IsValid: true,
		}
	}
	diffB, err := util.GetDiffWithLineOffset(validContext.OriginalCode, validContext.RewriteCodeResult.Data.Content, validContext.RewriteStartLine)
	if err != nil {
		log.Debugf("Failed to generate diff patch: %v", err)
		return ValidateResult{
			IsValid: true,
		}
	}
	if len(diffB) == 0 {
		return ValidateResult{
			IsValid: true,
		}
	}
	hunksA := parseDiff(diffA)
	hunksB := parseDiff(diffB)
	if len(hunksA) <= 0 || len(hunksB) <= 0 {
		return ValidateResult{
			IsValid: true,
		}
	}

	//只取第一个diff校验

	if isRollbackHunk(hunksA[0], hunksB[0]) {
		//最后编辑前的内容与当前改写内容一致，说明模型发生了rollback
		return ValidateResult{
			IsValid: false,
			Message: "llm rollback user edit.",
		}
	}

	// 30秒内的变更记录，最多取5个
	historyChangeSize := changes.Size()
	if historyChangeSize >= 5 {
		historyChangeSize = 5
	}
	tailChanges, _ := changes.Tail(historyChangeSize)
	if len(tailChanges) < 2 {
		return ValidateResult{
			IsValid: true,
		}
	}
	// 最后一次记录前面已经处理过了
	for i := len(tailChanges) - 2; i >= 0; i-- {
		tailChange := tailChanges[i]
		// 超过30秒不做处理
		if currentTime-tailChange.UpdateTime.Load() > 30*1000 {
			break
		}
		historyDiffA, err := util.GetDiffWithLineOffset(tailChange.OldText, tailChange.NewText, tailChange.StartLine)
		if err != nil {
			log.Debugf("Failed to generate diff patch: %v", err)
			break
		}
		if len(historyDiffA) == 0 {
			continue
		}
		historyHunksA := parseDiff(historyDiffA)
		if len(historyHunksA) <= 0 {
			continue
		}
		if isRollbackDeletedHunk(historyHunksA[0], hunksB[0]) {
			// 添加的内容是用户历史删除的内容，说明模型发生了rollback
			return ValidateResult{
				IsValid: false,
				Message: "llm rollback user history edit.",
			}
		}
	}
	return ValidateResult{
		IsValid: true,
	}
}

// 规则：判断改写结果以字符为单位的相似度，是否低于阈值
type IsRewriteResultLineSimilarityBelowThresholdRule struct {
}

func (e *IsRewriteResultLineSimilarityBelowThresholdRule) Validate(ctx context.Context, validContext ValidateContext) ValidateResult {
	rewriteCode := validContext.RewriteCodeResult.Data.Content

	// 如果改写内容为空,返回false
	if len(rewriteCode) == 0 || len(validContext.OriginalCode) == 0 {
		return ValidateResult{
			IsValid: false,
			Message: "no rewrite result",
		}
	}

	// 计算相似度
	similarity := CalculateSimilarity(validContext.OriginalCode, rewriteCode)

	isValid := similarity > rewriteCodeSimilarityThreshold
	if isValid {
		return ValidateResult{
			IsValid: true,
		}
	}
	return ValidateResult{
		IsValid: false,
		Message: fmt.Sprintf("similarity: %f is too low [threshold: %f]", similarity, rewriteCodeSimilarityThreshold),
	}
}

func ValidateRewriteResult(ctx context.Context, params definition.InlineEditParams, codeToRewrite string,
	rewriteCodeResult definition.RewriteCodeActionMessage, contextData map[string]interface{},
	isNextPredict bool) bool {
	rules := newRewriteResultValidators()
	if isNextPredict {
		rules = nextPredictResultValidators()
	}

	validStart := time.Now().UnixMilli()

	validResult := ValidateResult{
		IsValid: true,
	}

	validateContext := buildValidateContext(ctx, params, codeToRewrite, rewriteCodeResult, definition.NextEditLocationActionMessage{}, contextData)

	for _, rule := range rules {
		validResult = rule.Validate(ctx, validateContext)

		if !validResult.IsValid {
			log.Debugf("rewrite result is invalid, rule: %s, message: %s", util.GetTypeName(rule), validResult.Message)
			break
		}
	}
	logInfo := map[string]string{
		"request_id":  params.RequestId,
		"session_id":  params.SessionId,
		"valid_time":  strconv.FormatInt(time.Now().UnixMilli()-validStart, 10),
		"is_valid":    strconv.FormatBool(validResult.IsValid),
		"message":     validResult.Message,
		"action_type": "rewrite_code",
	}
	sls.Report(sls.EventTypeInlineEditValidate, params.RequestId, logInfo)

	return validResult.IsValid
}

func ValidateNextEditResult(ctx context.Context, params definition.InlineEditParams, codeToRewrite string, nextEditResult definition.NextEditLocationActionMessage, contextData map[string]interface{}) bool {
	rules := newNextEditLocationValidators()

	validStart := time.Now().UnixMilli()

	validResult := ValidateResult{
		IsValid: true,
	}

	validateContext := buildValidateContext(ctx, params, codeToRewrite, definition.RewriteCodeActionMessage{}, nextEditResult, contextData)

	for _, rule := range rules {
		validResult = rule.Validate(ctx, validateContext)

		if !validResult.IsValid {
			log.Debugf("rewrite result is invalid, rule: %s, message: %s", util.GetTypeName(rule), validResult.Message)
			break
		}
	}
	logInfo := map[string]string{
		"request_id":  params.RequestId,
		"session_id":  params.SessionId,
		"valid_time":  strconv.FormatInt(time.Now().UnixMilli()-validStart, 10),
		"is_valid":    strconv.FormatBool(validResult.IsValid),
		"message":     validResult.Message,
		"action_type": "next_edit_location",
	}
	sls.Report(sls.EventTypeInlineEditValidate, params.RequestId, logInfo)

	return validResult.IsValid
}

func buildValidateContext(ctx context.Context, params definition.InlineEditParams, codeToRewrite string, rewriteCodeResult definition.RewriteCodeActionMessage, nextEditResult definition.NextEditLocationActionMessage, contextData map[string]interface{}) ValidateContext {
	workspacePath := ""
	if workspaceInfo, ok := ctx.Value(definition.ContextKeyWorkspace).(definition.WorkspaceInfo); ok {
		workspacePath, _ = workspaceInfo.GetWorkspaceFolder()
	}
	codeToRewriteData, _ := contextData[definition.InlineEditContextKeyCodeToRewrite].(definition.CodeToRewriteData)

	validateContext := ValidateContext{
		WorkspacePath:     workspacePath,
		TextDocumentUri:   string(params.TextDocument.URI),
		FileContent:       params.FileContent,
		RewriteStartLine:  codeToRewriteData.StartLine,
		RewriteEndLine:    codeToRewriteData.EndLine,
		Params:            params,
		RewriteCodeResult: rewriteCodeResult,
		NextEditResult:    nextEditResult,
		OriginalCode:      codeToRewrite,
		SessionId:         params.SessionId,
		RequestId:         params.RequestId,
		ContextData:       contextData,
	}

	return validateContext
}

// 改写行数超过限制规则：2*inputLines
type RewriteLinesTooLongRule struct {
}

func (r *RewriteLinesTooLongRule) Validate(ctx context.Context, validContext ValidateContext) ValidateResult {
	inputLines := util.GetFileLines(validContext.OriginalCode)
	outputLines := util.GetFileLines(validContext.RewriteCodeResult.Data.Content)

	if inputLines < 5 {
		if outputLines > 3*inputLines {
			return ValidateResult{
				IsValid: false,
				Message: fmt.Sprintf("rewrite lines too long, input lines: %d, output lines: %d", inputLines, outputLines),
			}
		}
	} else {
		if outputLines > 2*inputLines {
			return ValidateResult{
				IsValid: false,
				Message: fmt.Sprintf("rewrite lines too long, input lines: %d, output lines: %d", inputLines, outputLines),
			}
		}
	}

	return ValidateResult{
		IsValid: true,
	}
}

type NextPredictLinesTooLongRule struct {
}

func (r *NextPredictLinesTooLongRule) Validate(ctx context.Context, validContext ValidateContext) ValidateResult {
	inputLines := util.GetFileLines(validContext.OriginalCode)
	outputLines := util.GetFileLines(validContext.RewriteCodeResult.Data.Content)

	if outputLines > 10 {
		return ValidateResult{
			IsValid: false,
			Message: fmt.Sprintf("rewrite lines too long, input lines: %d, output lines: %d", inputLines, outputLines),
		}
	}

	return ValidateResult{
		IsValid: true,
	}
}

type InvalidEmptyCharRewriteRule struct {
}

// 如果变更行只有删除空格、空行、tab改空格、空格改tab、增加删除无效字符，如}{，则忽略变更
func (e *InvalidEmptyCharRewriteRule) Validate(ctx context.Context, validContext ValidateContext) ValidateResult {
	rewriteCode := validContext.RewriteCodeResult.Data.Content
	language := util.GetLanguageByFilePath(validContext.TextDocumentUri)
	if definition.Python == language {
		return ValidateResult{
			IsValid: true,
		}
	}
	rewriteCode = strings.Trim(rewriteCode, "\n \t")
	originalCode := strings.Trim(validContext.OriginalCode, "\n \t")
	if rewriteCode == originalCode {
		return ValidateResult{
			IsValid: false,
			Message: "change is empty",
		}
	}
	//校验是否只是有反括号场景
	additionCode := strings.Trim(strings.TrimPrefix(rewriteCode, originalCode), "\n \t")
	if additionCode == "}" || additionCode == "{" {
		return ValidateResult{
			IsValid: false,
			Message: "no valid rewrite content",
		}
	}

	additionCode = strings.Trim(strings.TrimSuffix(rewriteCode, originalCode), "\n \t")
	if additionCode == "{" {
		return ValidateResult{
			IsValid: false,
			Message: "no valid rewrite content",
		}
	}

	//计算rewriteCode与originalCode的差异
	//diffText, err := util.GetDiff(originalCode, rewriteCode)
	return ValidateResult{
		IsValid: true,
	}
}

// 括号匹配校验
type BracketMatchCheckRewriteRule struct {
}

func (e *BracketMatchCheckRewriteRule) Validate(ctx context.Context, validContext ValidateContext) ValidateResult {
	rewriteCode := validContext.RewriteCodeResult.Data.Content
	startLine := validContext.RewriteStartLine
	endLine := validContext.RewriteEndLine

	language := util.GetLanguageByFilePath(validContext.TextDocumentUri)
	toApplyContent := util.ReplaceFileWithSpecificLines(validContext.FileContent, startLine, endLine, rewriteCode)

	// 创建一个带超时的context
	ctx, cancel := context.WithTimeout(context.Background(), 50*time.Millisecond)
	defer cancel()

	// 创建一个channel用于接收检查结果
	resultChan := make(chan bool)

	// 在goroutine中执行括号匹配检查
	go func() {
		resultChan <- util.IsBracketsBalanced(toApplyContent, language)
	}()

	// 等待检查结果或超时
	select {
	case isBalanced := <-resultChan:
		if !isBalanced {
			return ValidateResult{
				IsValid: false,
				Message: "bracket not match",
			}
		}
	case <-ctx.Done():
		log.Debugf("bracket match check timeout, ignore")
	}
	return ValidateResult{
		IsValid: true,
	}
}

// ChangeDiffVerifyRule diff变更有效性校验
type ChangeDiffVerifyRule struct {
	tokenizer *tokenizer.JiebaTokenizer
}

func NewChangeDiffVerifyRule() *ChangeDiffVerifyRule {
	return &ChangeDiffVerifyRule{
		tokenizer: tokenizer.NewJiebaTokenizer(false),
	}
}

func (e *ChangeDiffVerifyRule) Validate(ctx context.Context, validContext ValidateContext) ValidateResult {
	dmp := diffmatchpatch.New()
	lineDiffs := diff.Do(validContext.OriginalCode, validContext.RewriteCodeResult.Data.Content)
	if len(lineDiffs) == 0 {
		return ValidateResult{
			IsValid: false,
			Message: "no valid rewrite content",
		}
	}
	if verifyResult, pass := e.verifyOverRewriteAreaByDiff(ctx, lineDiffs, validContext); !pass {
		return verifyResult
	}
	if verifyResult, pass := e.verifyGenerateDuplicateCode(ctx, lineDiffs, validContext); !pass {
		return verifyResult
	}
	// 检测是否有有效的字符变更
	charsDiffs := dmp.DiffMain(validContext.OriginalCode, validContext.RewriteCodeResult.Data.Content, false)
	validChange := false
	for _, diffItem := range charsDiffs {
		if diffItem.Type == diffmatchpatch.DiffDelete || diffItem.Type == diffmatchpatch.DiffInsert {
			if strings.Trim(diffItem.Text, " \t\r\n{}();") != "" {
				validChange = true
				break
			}
		}
	}

	message := ""
	if !validChange {
		message = "no valid rewrite content by chars"
	}
	return ValidateResult{
		IsValid: validChange,
		Message: message,
	}
}

// verifyOverRewriteAreaByDiff 通过行级diff判断最后的新增代码，是否在重写区外的重复度比较高，从而判断是否修改了重写区外的代码
func (e *ChangeDiffVerifyRule) verifyOverRewriteAreaByDiff(ctx context.Context, lineDiffs []diffmatchpatch.Diff, validContext ValidateContext) (ValidateResult, bool) {
	codeAreaCode, _ := validContext.ContextData[definition.InlineEditContextKeyAreaAroundCode].(definition.AreaAroundCodeData)
	var diffs []diffmatchpatch.Diff
	for i := 0; i < len(lineDiffs); i++ {
		if lineDiffs[i].Type == diffmatchpatch.DiffEqual {
			continue
		}
		diffs = append(diffs, lineDiffs[i])
	}
	if len(diffs) == 0 {
		return ValidateResult{
			IsValid: false,
			Message: "no valid rewrite content by lines",
		}, false
	}
	lastDiff := diffs[len(diffs)-1]
	if lastDiff.Type == diffmatchpatch.DiffInsert {
		lastDiffContent := lastDiff.Text
		if len(diffs) > 1 && diffs[len(diffs)-2].Type == diffmatchpatch.DiffDelete {
			deleteDiff := diffs[len(diffs)-2]
			clearDeleteDiff := strings.Trim(deleteDiff.Text, "\n \t")
			idx := strings.Index(lastDiffContent, clearDeleteDiff)
			if !strings.HasPrefix(strings.Trim(lastDiff.Text, "\n \t"), clearDeleteDiff) || idx+len(clearDeleteDiff) >= len(lastDiffContent) {
				// 不是纯增加场景，是修改场景，跳过校验
				return ValidateResult{}, true
			} else {
				// 把重复的部分去掉
				lastDiffContent = lastDiffContent[idx+len(clearDeleteDiff):]
				if strings.HasPrefix(lastDiffContent, "\n") {
					lastDiffContent = lastDiffContent[1:]
				}
			}
		}
		lines := strings.Split(validContext.FileContent, "\n")
		startLine := validContext.RewriteEndLine + 1
		endLine := codeAreaCode.EndLine
		if startLine < endLine && endLine < len(lines) {
			// 存在有效的可视区代码
			diffLines := strings.Split(lastDiffContent, "\n")
			endLine = util.IntMin(endLine, startLine+len(diffLines)-1)
			visibleSuffix := util.GetCodeRangeFromLines(lines, startLine, endLine)
			if strings.Trim(visibleSuffix, "\n \t{}();:?") == "" {
				// 如果剩余的可视区没有内容了，则跳过校验
				return ValidateResult{}, true
			}
			visibleSuffix = clearSpaceRegex.ReplaceAllString(visibleSuffix, " ")
			lastDiffContent = clearSpaceRegex.ReplaceAllString(lastDiffContent, " ")
			dist, err := e.wordEditDistance(visibleSuffix, lastDiffContent)
			log.Debugf("ChangeDiffVerifyRule visibleSuffix: %s, lastDiff: %s, dist: %d", visibleSuffix, lastDiffContent, dist)
			if err == nil && dist < 4 {
				return ValidateResult{
					IsValid: false,
					Message: "over rewrite by change diff",
				}, false
			}
		}
	}
	return ValidateResult{}, true
}

// verifyGenerateDuplicateCode 检测是否生成了重复代码
func (e *ChangeDiffVerifyRule) verifyGenerateDuplicateCode(ctx context.Context, lineDiffs []diffmatchpatch.Diff, validContext ValidateContext) (ValidateResult, bool) {
	var insertDiffs []diffmatchpatch.Diff
	for _, diffItem := range lineDiffs {
		if diffItem.Type == diffmatchpatch.DiffInsert {
			insertDiffs = append(insertDiffs, diffItem)
		} else if diffItem.Type == diffmatchpatch.DiffDelete {
			// 只处理纯新增场景
			return ValidateResult{}, true
		}
	}
	if len(insertDiffs) == 0 || len(insertDiffs) > 1 {
		return ValidateResult{}, true
	}
	codeAreaCode, _ := validContext.ContextData[definition.InlineEditContextKeyAreaAroundCode].(definition.AreaAroundCodeData)
	insertDiff := insertDiffs[0]
	insertDiffTrim := strings.Trim(insertDiff.Text, " \t\r\n{}();")
	if insertDiffTrim == "" || utf8.RuneCountInString(insertDiffTrim) < 10 {
		// 如何新增代码不超过10个字符，则跳过校验逻辑
		return ValidateResult{}, true
	}
	sourceTokens, err := e.tokenizer.TokenizeToString(insertDiffTrim)
	if err == nil && len(sourceTokens) < 5 {
		// 如果新增代码token数过少，则跳过校验逻辑
		return ValidateResult{}, true
	}
	newText := clearSpaceRegex.ReplaceAllString(insertDiff.Text, "")
	originText := clearSpaceRegex.ReplaceAllString(codeAreaCode.Content, "")
	count := strings.Count(originText, newText)
	if count > 0 {
		// 如果出现了两次以上，则认为是重复代码
		return ValidateResult{
			IsValid: false,
			Message: "repeat code by change diff",
		}, false
	}
	return ValidateResult{}, true
}

func (e *ChangeDiffVerifyRule) wordEditDistance(source, target string) (int, error) {
	sourceTokens, err := e.tokenizer.TokenizeToString(source)
	if err != nil {
		return 0, err
	}

	targetTokens, err := e.tokenizer.TokenizeToString(target)
	if err != nil {
		return 0, err
	}

	dist, _, _ := distance.CalculateWordLevenshtein(sourceTokens, targetTokens)
	return dist, nil
}

// RewriteImportCodeFilterRule 检测是否是import语句，部分语言有ide的import忽略生成
// 只允许js/ts/jsx/tsx/vue/python语言修改import
type RewriteImportCodeFilterRule struct {
}

func (e *RewriteImportCodeFilterRule) Validate(ctx context.Context, validContext ValidateContext) ValidateResult {
	// 获取文件扩展名
	ext := strings.ToLower(util.GetFileLanguageSuffix(validContext.TextDocumentUri, false))
	if ext == strings.ToLower(definition.Others) {
		return ValidateResult{
			IsValid: true,
		}
	}

	isImportCodeForLanguage := isImportCode(validContext.TextDocumentUri, validContext.RewriteCodeResult.Data.Content)
	if !isImportCodeForLanguage {
		return ValidateResult{
			IsValid: true,
		}
	}
	if lo.Contains(allowedImportRewriteCodeLanguageExts, ext) {
		return ValidateResult{
			IsValid: true,
		}
	}
	return ValidateResult{
		IsValid: false,
		Message: fmt.Sprintf("rewrite import code not allowed. languageExt: %s", ext),
	}
}

// 判断是否是常见语言的import语句
func isImportCode(documentUri string, rewriteCode string) bool {
	// 获取文件扩展名
	ext := strings.ToLower(util.GetFileLanguageSuffix(documentUri, false))

	// 清理空格
	rewriteCode = strings.TrimSpace(rewriteCode)

	// 根据不同语言判断是否是import语句
	switch ext {
	case "js", "jsx", "ts", "tsx":
		// JS/TS的import语句
		return strings.HasPrefix(rewriteCode, "import ") || strings.HasPrefix(rewriteCode, "require(")
	case "py":
		// Python的import语句
		return strings.HasPrefix(rewriteCode, "import ") || strings.HasPrefix(rewriteCode, "from ")
	case "vue":
		// Vue文件的import语句
		return strings.HasPrefix(rewriteCode, "import ")
	case "go":
		// Go的import语句，暂时不支持多行import
		return strings.HasPrefix(rewriteCode, "import ")
	case "java":
		return strings.HasPrefix(rewriteCode, "import ")
	default:
		return false
	}
}

// JumpToImportCodeFilterRule 检测是否是import语句，部分语言有ide的import忽略生成
// 只允许js/ts/jsx/tsx/vue/python语言修改import
type JumpToImportCodeFilterRule struct {
}

func (e *JumpToImportCodeFilterRule) Validate(ctx context.Context, validContext ValidateContext) ValidateResult {

	// 获取文件扩展名
	ext := strings.ToLower(util.GetFileLanguageSuffix(validContext.TextDocumentUri, false))
	if ext == strings.ToLower(definition.Others) {
		return ValidateResult{
			IsValid: true,
		}
	}

	fileContent := validContext.FileContent
	if validContext.NextEditResult.Data.HasRewriteCode {
		fileContent = util.ReplaceFileWithSpecificLines(fileContent, validContext.RewriteStartLine, validContext.RewriteEndLine, validContext.RewriteCodeResult.Data.Content)
	}

	jumpToLineNumber := validContext.NextEditResult.Data.NextLineNumber

	fileLines := strings.Split(fileContent, "\n")
	if jumpToLineNumber >= len(fileLines) {
		return ValidateResult{
			IsValid: false,
			Message: fmt.Sprintf("jump to line number %d is out of range", jumpToLineNumber),
		}
	}

	jumpToCodeLine := fileLines[jumpToLineNumber]

	isImportCodeForLanguage := isImportCode(validContext.TextDocumentUri, jumpToCodeLine)
	if isImportCodeForLanguage {
		return ValidateResult{
			IsValid: false,
			Message: fmt.Sprintf("jump to line number %d is %s file's import code", jumpToLineNumber, ext),
		}
	}
	return ValidateResult{
		IsValid: true,
	}
}
