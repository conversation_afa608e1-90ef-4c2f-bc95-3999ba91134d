triggers:
  push:
    branches: 'qoder-0.0.1'

params:
  go:
    type: string
    default: 1.23.8
jobs:
#  unittest:
#    name: 单元测试
#    steps:
#      - uses: checkout
#      - uses: setup-env
#        inputs:
#          go-version: '1.23.8'
#          go-cache: true
#          go-mod-cache: true
#      - uses: golang-unittest
  build:
    name: 构建

    steps:
      - uses: checkout
      - uses: setup-env
        inputs:
          go-version: '1.23.8'
          go-cache: true
          go-mod-cache: true
      - image: hub.docker.alibaba-inc.com/aone-base/qbase_golang-cgo:20250717164510_containerd_accelerated
        run: |
          echo "machine code.alibaba-inc.com login yunxiaoBuilder password KWSOUuHPsC7ma4FbizAr" >> ~/.netrc
          echo "machine gitlab.alibaba-inc.com login yunxiaoBuilder password KWSOUuHPsC7ma4FbizAr" >> ~/.netrc

          export GOPRIVATE=*.alibaba-inc.com
          sh build_ci_only_linux_qoder.sh
      - uses: upload-artifact
        inputs:
          path: out/*/x86_64_linux/Cosy
          storage-path: 'qoder-remote/${{git.branch ?: ""}}/Cosy'
          oss-bucket: qoder-ide
          oss-endpoint: https://oss-ap-southeast-1.aliyuncs.com
#          bucket-url: https://oss-ap-southeast-1.aliyuncs.com # 绑定域名，默认为 bucket external endpoint
        envs:
          OSS_ACCESS_KEY_ID: ${{secrets.access_key_id}}
          OSS_ACCESS_KEY_SECRET: ${{secrets.access_key_secret}}
