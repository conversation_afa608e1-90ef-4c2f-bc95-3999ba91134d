package user

import (
	"encoding/json"
	"errors"
	"io/fs"
	"os"
	"path/filepath"
	"runtime"
	"strconv"
	"strings"
	"sync"

	"cosy/config"
	"cosy/definition"
	"cosy/log"
	"cosy/rsa"
	"cosy/util"
	"cosy/util/encrypt"

	"github.com/google/uuid"
)

type CosyUser struct {
	Name               string `json:"name"`                 // 登录显示名（主账号固定为root）
	Aid                string `json:"aid"`                  // 主账号ID
	Uid                string `json:"uid"`                  // 登录账号ID（主账号或子账号ID）
	YxUid              string `json:"yx_uid"`               // 云效uid
	OrgId              string `json:"organization_id"`      // 所属企业ID
	OrgName            string `json:"organization_name"`    // 所属企业名称
	StaffId            string `json:"staffId"`              //工号id，企业版才有
	AvatarUrl          string `json:"avatar_url"`           //企业头像url
	LoginTimestamp     int64  `json:"login_timestamp"`      // 最后登录时间
	LoginDeviceId      string `json:"login_device_id"`      // 最后登录设备ID
	UserSourceChannel  string `json:"user_source_channel"`  // 用户来源，公有云/fc/pai平台等等
	SecurityOauthToken string `json:"security_oauth_token"` // 个人凭证
	RefreshToken       string `json:"refresh_token"`        // 刷新token
	TokenExpireTime    int64  `json:"expire_time"`          // token过期时间
	// 用户类型 由用户在登陆界面选择
	// 个人标准版  personal_standard
	// （未实现）个人专业版  personal_professional
	// 企业标准版  enterprise_standard
	// 企业专业版  enterprise_professional
	UserType         string `json:"user_type"`
	DataPolicyAgreed bool   `json:"data_policy_agreed"` // 数据回流协议签署状态， 详见definition/datapolicy.go
	Email            string `json:"email"`              // 用户邮箱
}

type CachedUserInfo struct {
	Aid                string `json:"aid"`                  // 登录用户所属账号ID
	Uid                string `json:"uid"`                  // 登录用户ID
	Name               string `json:"name"`                 // 登录用户名
	YxUid              string `json:"yxUid"`                // 云效uid
	OrgId              string `json:"orgId"`                // 所属企业ID
	OrgName            string `json:"orgName"`              // 所属企业名称
	StaffId            string `json:"staffId"`              //工号id，企业版才有
	AvatarUrl          string `json:"avatarUrl"`            //企业头像url
	Info               string `json:"info"`                 // AES加密过的鉴权信息
	Key                string `json:"key"`                  // RSA加密过的鉴权信息秘钥
	Token              string `json:"token"`                // OAuth令牌
	SecurityOauthToken string `json:"security_oauth_token"` // 个人凭证
	RefreshToken       string `json:"refresh_token"`        // 刷新token
	TokenExpireTime    int64  `json:"expire_time"`          // token过期时间
	UserSourceChannel  string `json:"user_source_channel"`  // 用户来源，公有云/fc/pai平台等等
	// 用户类型 由用户在登陆界面选择
	// 个人标准版  personal_standard
	// （未实现）个人专业版  personal_professional
	// 企业标准版  enterprise_standard
	// 企业专业版  enterprise_professional
	UserType         string `json:"user_type"`
	DataPolicyAgreed bool   `json:"data_policy_agreed"` // 数据回流协议签署状态， 详见definition/datapolicy.go
	Email            string `json:"email"`
}

type SaveUserInfoData struct {
	Name               string `json:"name"`              // 登录显示名（主账号固定为root）
	Aid                string `json:"aid"`               // 主账号ID
	Uid                string `json:"uid"`               // 登录账号ID（主账号或子账号ID）
	YxUid              string `json:"yx_uid"`            // 云效uid
	OrgId              string `json:"organization_id"`   // 所属企业ID
	OrgName            string `json:"organization_name"` // 所属企业名称
	StaffId            string `json:"staffId"`           //工号id，企业版才有
	AvatarUrl          string `json:"avatar_url"`        //企业头像url
	LoginTimestamp     int64  `json:"login_timestamp"`   // 最后登录时间
	LoginDeviceId      string `json:"login_device_id"`   // 最后登录设备ID
	AccessToken        string `json:"access_token"`
	SecurityOauthToken string `json:"security_oauth_token"` // 个人凭证
	RefreshToken       string `json:"refresh_token"`        // 刷新token
	TokenExpireTime    int64  `json:"expire_time"`          // token过期时间
	Key                string `json:"key"`
	EncryptUserInfo    string `json:"encrypt_user_info"`
	OnPremise          string `json:"on_premise"`          //是否是专有云企业版，true/false
	UserSourceChannel  string `json:"user_source_channel"` // 用户来源，公有云/fc/pai平台等等
	// 用户类型 由用户在登陆界面选择
	// 个人标准版  personal_standard
	// （未实现）个人专业版  personal_professional
	// 企业标准版  enterprise_standard
	// 企业专业版  enterprise_professional
	UserType         string `json:"user_type"`
	DataPolicyAgreed bool   `json:"data_policy_agreed"` // 数据回流协议签署状态， 详见definition/datapolicy.go
	Email            string `json:"email"`
}

var cachedUserInfo *CachedUserInfo = nil

var cachedAuthStatus *definition.AuthStatusResult = nil

var mutex sync.Mutex

func SaveUserInfo(cosyUser *CosyUser, accessToken string) error {
	userFilePath := filepath.Join(util.GetCosyHomePath(), "cache", definition.UserFile)
	userInfo, err := json.Marshal(cosyUser)
	if err != nil {
		log.Warnf("Failed to marshal user info: %s", err)
		return errors.New("failed to marshal user info")
	}
	machineKey := util.GetMachineId(true)[:16]
	temporaryKey := strings.ReplaceAll(uuid.NewString(), "-", "")[:16]
	encryptTemporaryKey, err := encrypt.RsaEncrypt([]byte(temporaryKey), []byte(rsa.GetPublicKey()))
	if err != nil {
		log.Warnf("Failed to encrypt temporary key: %s", err)
		return errors.New("failed to encrypt temporary key")
	}
	encryptUserInfo, err := encrypt.AesEncryptWithBase64(string(userInfo), temporaryKey)
	if err != nil {
		log.Warnf("Failed to encrypt user info. err: %+v", err)
		return errors.New("failed to encrypt user info")
	}
	savedUserInfo := util.ToJsonStr(SaveUserInfoData{
		Name:               cosyUser.Name,
		Aid:                cosyUser.Aid,
		Uid:                cosyUser.Uid,
		YxUid:              cosyUser.YxUid,
		OrgId:              cosyUser.OrgId,
		OrgName:            cosyUser.OrgName,
		StaffId:            cosyUser.StaffId,
		AvatarUrl:          cosyUser.AvatarUrl,
		LoginTimestamp:     cosyUser.LoginTimestamp,
		LoginDeviceId:      cosyUser.LoginDeviceId,
		AccessToken:        accessToken,
		Key:                encrypt.Base64Encode(encryptTemporaryKey),
		EncryptUserInfo:    encryptUserInfo,
		OnPremise:          strconv.FormatBool(config.OnPremiseMode),
		UserSourceChannel:  cosyUser.UserSourceChannel,
		SecurityOauthToken: cosyUser.SecurityOauthToken,
		RefreshToken:       cosyUser.RefreshToken,
		TokenExpireTime:    cosyUser.TokenExpireTime,
		UserType:           cosyUser.UserType,
		DataPolicyAgreed:   cosyUser.DataPolicyAgreed,
		Email:              cosyUser.Email,
	})
	encryptContent, err := encrypt.AesEncryptWithBase64(savedUserInfo, machineKey)
	if err != nil {
		log.Warnf("Failed to encrypt user content. Using machineKey: %s, err: %+v", machineKey, err)
		return errors.New("failed to encrypt user content")
	}
	mutex.Lock()
	err = os.WriteFile(userFilePath, []byte(encryptContent), 0644)
	defer mutex.Unlock()
	if err != nil {
		log.Warnf("Failed to write user info file: %s", err)
		return errors.New("failed to write user info file")
	}
	cachedUserInfo = &CachedUserInfo{
		Aid:                cosyUser.Aid,
		Uid:                cosyUser.Uid,
		Name:               cosyUser.Name,
		OrgId:              cosyUser.OrgId,
		OrgName:            cosyUser.OrgName,
		StaffId:            cosyUser.StaffId,
		AvatarUrl:          cosyUser.AvatarUrl,
		YxUid:              cosyUser.YxUid,
		Token:              accessToken,
		Key:                encrypt.Base64Encode(encryptTemporaryKey),
		Info:               encryptUserInfo,
		UserSourceChannel:  cosyUser.UserSourceChannel,
		SecurityOauthToken: cosyUser.SecurityOauthToken,
		RefreshToken:       cosyUser.RefreshToken,
		TokenExpireTime:    cosyUser.TokenExpireTime,
		UserType:           cosyUser.UserType,
		DataPolicyAgreed:   cosyUser.DataPolicyAgreed,
		Email:              cosyUser.Email,
	}
	return nil
}

func UpdateCachedAuthIfLoginSuccess(authStatus definition.AuthStatusResult) {
	if authStatus.Status == definition.AuthStatusSuccess {
		//仅当授权成功时才更新，避免切换用户时未授权状态影响其他账号登录
		cachedAuthStatus = &authStatus
	}
}

// UpdateLocalDataPolicySignStatus 更新本地用户签署状态
func UpdateLocalDataPolicySignStatus(dataPolicySignStatus string) error {
	if config.OnPremiseMode || config.IsDedicatedEndpointConfigured() {
		return nil
	}

	config.SignDataPolicyStatus = dataPolicySignStatus
	userInfo := GetCachedUserInfo()
	if userInfo == nil {
		return errors.New("user not login")
	}

	if dataPolicySignStatus == definition.SignStatusAgree {
		userInfo.DataPolicyAgreed = true
	} else {
		userInfo.DataPolicyAgreed = false
	}

	return refreshSavedUserInfo(userInfo)
}

// RefreshSavedUserInfo 执行使用CacheUserInfo更新SavedUserInfo
func refreshSavedUserInfo(userInfo *CachedUserInfo) error {
	defer func() {
		if r := recover(); r != nil {
			// 获取当前堆栈信息
			stack := make([]byte, 4096)
			stack = stack[:runtime.Stack(stack, false)]
			log.Warnf("Recover from crash. err: %+v, stack: %s", r, stack)
		}
	}()

	cosyUser, err := GetCosyUserViaCachedFile()
	if err != nil {
		return err
	}
	if cosyUser == nil {
		return nil
	}

	cosyUser.DataPolicyAgreed = userInfo.DataPolicyAgreed
	err = SaveUserInfo(cosyUser, userInfo.Token)
	if err != nil {
		return err
	}
	return nil
}

func GetCachedAuthStatus() *definition.AuthStatusResult {
	return cachedAuthStatus
}

func Logout() bool {

	//清理内存缓存
	cachedAuthStatus = nil

	// 先删配额缓存
	CleanQuotaCache()

	log.Debugf("logout Clean QuotaCache")

	// 再删用户信息缓存
	cleanUserInfoCache()

	log.Debugf("logout clean UserInfoCache")

	return true
}

func cleanUserInfoCache() {
	// 内存缓存
	cachedUserInfo = nil
	// 文件缓存
	userFilePath := filepath.Join(util.GetCosyHomePath(), "cache", definition.UserFile)
	if !util.PathExists(userFilePath) {
		// 文件不存在，已经登出
		return
	}
	mutex.Lock()
	defer mutex.Unlock()
	if err := os.Remove(userFilePath); err != nil {
		log.Warnf("Unable to delete user file: %s", err)
		return
	} else {
		log.Info("Deleted user info file.")
	}
}

func GetUserIdAndName() (string, string, string) {
	if userInfo := GetCachedUserInfo(); userInfo != nil {
		return userInfo.Aid, userInfo.Uid, userInfo.Name
	}
	return "", "", ""
}

func GetUserType() string {
	if userInfo := GetCachedUserInfo(); userInfo != nil && userInfo.UserType != "" {
		return userInfo.UserType
	} else {
		// 对于已登陆用户，默认是个人标准版
		retUserType := definition.UserTypePersonalStandard
		if userInfo != nil && userInfo.OrgId != "" {
			// 如果已有企业信息，则改为企业标准版
			retUserType = definition.UserTypeEnterpriseStandard
		}

		// 如已配置Url，修改为专属版
		if config.IsDedicatedEndpointConfigured() {
			retUserType = definition.UserTypeEnterpriseDedicated
		}
		// 将userType信息写回CacheUserInfo
		if userInfo != nil && userInfo.UserType == "" {
			userInfo.UserType = retUserType
		}
		return retUserType
	}
}

func GetCosyUserViaCachedFile() (*CosyUser, error) {
	userFilePath := filepath.Join(util.GetCosyHomePath(), "cache", definition.UserFile)
	encryptedContent, err := os.ReadFile(userFilePath)
	if err != nil {
		// 检查错误是否为文件不存在
		if errors.Is(err, fs.ErrNotExist) {
			// 通常是因为用户未登录，没有生成user文件
			log.Infof("user info not exist, not login.")
			return nil, errors.New("user info not exist, not login")
		}
		log.Warnf("Failed to read user info: %s", err)
		return nil, errors.New("failed to read user info")
	}
	if string(encryptedContent) == "" {
		return nil, nil
	}
	machineKey := util.GetMachineId(true)[:16]
	content, err := encrypt.AesDecryptWithBase64(string(encryptedContent), machineKey)
	if err != nil {
		log.Warnf("Unable to decrypt user info. Using machineKey: %s, err: %+v", machineKey, err)
		_ = os.Remove(userFilePath)
		return nil, errors.New("empty user info")
	}
	var saveUserInfoData = &SaveUserInfoData{}
	if err = json.Unmarshal([]byte(content), saveUserInfoData); err != nil {
		return nil, errors.New("unmarshal savedUserInfo error")
	}
	return &CosyUser{
		Name:               saveUserInfoData.Name,
		Aid:                saveUserInfoData.Aid,
		Uid:                saveUserInfoData.Uid,
		YxUid:              saveUserInfoData.YxUid,
		OrgId:              saveUserInfoData.OrgId,
		OrgName:            saveUserInfoData.OrgName,
		StaffId:            saveUserInfoData.StaffId,
		AvatarUrl:          saveUserInfoData.AvatarUrl,
		LoginTimestamp:     saveUserInfoData.LoginTimestamp,
		LoginDeviceId:      saveUserInfoData.LoginDeviceId,
		UserSourceChannel:  saveUserInfoData.UserSourceChannel,
		SecurityOauthToken: saveUserInfoData.SecurityOauthToken,
		RefreshToken:       saveUserInfoData.RefreshToken,
		TokenExpireTime:    saveUserInfoData.TokenExpireTime,
		UserType:           saveUserInfoData.UserType,
		DataPolicyAgreed:   saveUserInfoData.DataPolicyAgreed,
		Email:              saveUserInfoData.Email,
	}, nil
}

func GetCachedUserInfo() *CachedUserInfo {
	if cachedUserInfo != nil {
		return cachedUserInfo
	}
	userFilePath := filepath.Join(util.GetCosyHomePath(), "cache", definition.UserFile)
	encryptedContent, err := os.ReadFile(userFilePath)
	if err != nil {
		// 检查错误是否为文件不存在
		if errors.Is(err, fs.ErrNotExist) {
			// 通常是因为用户未登录，没有生成user文件
			log.Infof("user info not exist, not login.")
			if log.IsDebugEnabled() {
				log.Debugf("user info not exist at path. path: %s", userFilePath)
			}
			return nil
		}
		log.Warnf("Failed to read user info: %s", err)
		return nil
	}
	if string(encryptedContent) == "" {
		return nil
	}
	machineKey := util.GetMachineId(true)[:16]

	defer func() {
		if r := recover(); r != nil {
			log.Warnf("Recovered from user info decrypt panic. err: %+v", r)
		}
	}()

	content, err := encrypt.AesDecryptWithBase64(string(encryptedContent), machineKey)
	if err != nil {
		log.Warnf("Unable to decrypt user info. Using machineKey: %s, err: %+v", machineKey, err)
		_ = os.Remove(userFilePath)
		return nil
	}
	userInfo := parseCachedUserContent(content, userFilePath)
	if cachedUserInfo == nil && userInfo != nil {
		cachedUserInfo = userInfo
	}
	return userInfo
}

func parseCachedUserContent(content string, userFilePath string) *CachedUserInfo {
	if content == "" {
		return nil
	}
	var saveUserInfoData = &SaveUserInfoData{}
	if err := json.Unmarshal([]byte(content), saveUserInfoData); err != nil {
		//老版存储内容
		parts := strings.Split(content, "\n")
		if len(parts) != 6 {
			log.Warnf("Incorrect user info file with %d lines", len(parts))
			_ = os.Remove(userFilePath)
			return nil
		}
		if len(parts[1]) <= 0 {
			log.Warnf("User info file content invalid id: %s", len(parts[1]))
			_ = os.Remove(userFilePath)
			return nil
		}
		return &CachedUserInfo{
			Aid:   parts[0],
			Uid:   parts[1],
			Name:  parts[2],
			Token: parts[3],
			Key:   parts[4],
			Info:  parts[5],
		}
	}
	if onPremiseValue, err := strconv.ParseBool(saveUserInfoData.OnPremise); err != nil {
		if onPremiseValue != config.OnPremiseMode {
			return nil
		}
	}

	//新版json格式
	return &CachedUserInfo{
		Aid:                saveUserInfoData.Aid,
		Uid:                saveUserInfoData.Uid,
		Name:               saveUserInfoData.Name,
		Token:              saveUserInfoData.AccessToken,
		Key:                saveUserInfoData.Key,
		Info:               saveUserInfoData.EncryptUserInfo,
		OrgId:              saveUserInfoData.OrgId,
		OrgName:            saveUserInfoData.OrgName,
		StaffId:            saveUserInfoData.StaffId,
		YxUid:              saveUserInfoData.YxUid,
		AvatarUrl:          saveUserInfoData.AvatarUrl,
		SecurityOauthToken: saveUserInfoData.SecurityOauthToken,
		RefreshToken:       saveUserInfoData.RefreshToken,
		TokenExpireTime:    saveUserInfoData.TokenExpireTime,
		UserType:           saveUserInfoData.UserType,
		DataPolicyAgreed:   saveUserInfoData.DataPolicyAgreed,
		Email:              saveUserInfoData.Email,
	}
}

// LoadAndSaveUserInfoForRemoteAgent remoteAgent模式下，从filePath路径加载明文用户数据，加密写入cache
func LoadAndSaveUserInfoForRemoteAgent(filePath string) error {
	userInfoBytes, err := os.ReadFile(filePath)
	if err != nil {
		log.Warnf("Failed to read user info from file: %s", err)
		return err
	}
	userInfo := &RemoteAgentUserInfo{}
	if err := json.Unmarshal(userInfoBytes, userInfo); err != nil {
		log.Warnf("Failed to unmarshal remote agent user info: %s, err: %s", string(userInfoBytes), err)
		return err
	}
	if err := SaveUserInfo(userInfo.ToCosyUser(), ""); err != nil {
		log.Warnf("Failed to save user info: %s", err)
		return err
	}
	log.Info("LoadAndSaveUserInfoForRemoteAgent success")
	return nil
}

type RemoteAgentUserInfo struct {
	Id                 string `json:"id"`
	Name               string `json:"name"`
	Quota              int    `json:"quota"`
	WhitelistStatus    string `json:"whitelistStatus"`
	SecurityOauthToken string `json:"securityOauthToken"`
	RefreshToken       string `json:"refreshToken"`
	ExpireTime         int64  `json:"expireTime"`
	IsSubAccount       bool   `json:"isSubAccount"`
}

func (u *RemoteAgentUserInfo) ToCosyUser() *CosyUser {
	return &CosyUser{
		Uid:                u.Id,
		Name:               u.Name,
		SecurityOauthToken: u.SecurityOauthToken,
		RefreshToken:       u.RefreshToken,
		TokenExpireTime:    u.ExpireTime,
	}
}
