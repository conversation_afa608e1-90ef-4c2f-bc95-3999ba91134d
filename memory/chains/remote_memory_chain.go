package chains

import (
	"context"
	chainUtil "cosy/chat/chains/chain"
	"cosy/chat/chains/common"
	"cosy/definition"
	"cosy/log"
	"cosy/memory/ltm"
	"github.com/tmc/langchaingo/chains"
	"runtime/debug"
)

// InvokeExtractMemoryFromRemoteChains 从远程获取长记忆
func InvokeExtractMemoryFromRemoteChains(ctx context.Context, taskId, sessionId string, inputs map[string]any, messages []definition.ChatMessage) {
	if len(messages) == 0 {
		return
	}
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("extract memory from remote panic: %v panic stack: %s", r, string(debug.Stack()))
		}
	}()
	memoryExtractChain := ltm.ExtractLongTermMemoryChain{}
	createMemoryExtractChain := ExtractRemoteCreateMemoryChain{}
	//workflowChain := experience.ExtractTaskWorkflowMemoryChain{}
	memoryConsolidateChain := ltm.ConsolidateLongTermMemoryChain{}
	chatChains := []chains.Chain{
		memoryExtractChain,
		createMemoryExtractChain,
		//workflowChain,
		memoryConsolidateChain,
	}
	chatChains = chainUtil.NewWrapperChains(chatChains)
	pipeline, err := chains.NewSequentialChain(chatChains, []string{common.KeyRequestId, common.KeySessionId}, []string{})
	if err != nil {
		log.Errorf("init memory accpet pipeline error. reason: %v", err)
		return
	}

	outputs, err := chains.Call(ctx, pipeline, inputs)
	if err != nil {
		log.Debugf("extract history task memory error. reason: %v output:%d", err, len(outputs))
	}
}
