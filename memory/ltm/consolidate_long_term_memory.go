package ltm

import (
	"context"
	"cosy/chat/chains/common"
	"cosy/definition"
	"cosy/experiment"
	"cosy/log"
	"cosy/memory/storage"
	"cosy/memory/util"
	"cosy/prompt"
	"cosy/sls"
	"strconv"
	"strings"
	"time"

	agentDefinition "code.alibaba-inc.com/cosy/lingma-agent-graph/definition"

	"github.com/tmc/langchaingo/callbacks"
	"github.com/tmc/langchaingo/chains"
	"github.com/tmc/langchaingo/memory"
	"github.com/tmc/langchaingo/schema"
)

const (
	similarMemoryThreshold = 0.7
)

// ConsolidateLongTermMemoryChain 记忆巩固链
type ConsolidateLongTermMemoryChain struct {
}

func (c ConsolidateLongTermMemoryChain) Call(ctx context.Context, inputs map[string]any, options ...chains.ChainCallOption) (map[string]any, error) {
	if !IsEnableAutoMemory(inputs) {
		return inputs, nil
	}
	if !experiment.ConfigService.GetBoolConfigWithEnv(definition.ExperimentMemoryAutoExtractEnable, experiment.ConfigScopeClient, true) {
		return inputs, nil
	}
	requestId := inputs[common.KeyRequestId].(string)
	if _, ok := StopRequestCache.Get(requestId); ok {
		log.Debugf("ignore consolidate long term memory chain caused of stop requestId=%s", requestId)
		return inputs, nil
	}
	newestMemoryRecords, _ := inputs[common.KeyNewLongTermMemories].([]storage.MemoryRecord)
	retrieveMemoryRecords, _ := inputs[common.KeyLongTermMemories].([]storage.MemoryRecord)
	retrieveMemoryRecords = c.appendSimilarMemory(ctx, inputs, retrieveMemoryRecords, newestMemoryRecords)
	retrieveMemoryRecords = c.filterRetrieveMemoryRecords(retrieveMemoryRecords)
	sessionMemoryRecords := c.getCurrentSessionMemory(inputs)
	dataPack := map[string]string{
		"newest":   strconv.Itoa(len(newestMemoryRecords)),
		"retrieve": strconv.Itoa(len(retrieveMemoryRecords)),
		"session":  strconv.Itoa(len(sessionMemoryRecords)),
	}
	log.Debugf("consolidate long term memory chain called. newest=%d retrieve=%d session:%d requestId=%s", len(newestMemoryRecords), len(retrieveMemoryRecords), len(sessionMemoryRecords), requestId)
	if !c.canConsolidateMemory(newestMemoryRecords, retrieveMemoryRecords, sessionMemoryRecords) {
		log.Debugf("consolidate long term memory chain called. no need to consolidate, requestId=%s", requestId)
		telemetry(requestId, dataPack, "no_need_to_consolidate")
		return inputs, nil
	}
	startTime := time.Now()
	log.Debugf("consolidate long term memory chain called. requestId=%s", requestId)
	if len(sessionMemoryRecords) > 0 {
		newestMemoryRecords = append(newestMemoryRecords, sessionMemoryRecords...)
	}
	promptInput, memoryIndexMap := c.buildMemoryPromptInput(ctx, inputs, newestMemoryRecords, retrieveMemoryRecords)
	messages, err := c.buildMemoryRequest(inputs, promptInput)
	if err != nil {
		log.Debugf("consolidate long term memory build request error, reason=%v, requestId=%s", err, requestId)
		telemetry(requestId, dataPack, "build_request_error")
		return inputs, nil
	}
	outputResp, err := util.InvokeMemoryModel(ctx, inputs, messages, 120*time.Second)
	if err != nil {
		log.Debugf("consolidate long term memory error, reason=%v, requestId=%s", err, requestId)
		telemetry(requestId, dataPack, "invoke_model_error")
		return inputs, err
	}
	outputText := outputResp.Text
	items := ParseMemoryOutputText(outputText)
	log.Debugf("memory consolidate model requestId:%s outputText:%s cost: %v", outputResp.RequestId, outputText, time.Since(startTime))
	c.updateCurrentSessionMemory(inputs, sessionMemoryRecords)
	c.updateStore(ctx, inputs, memoryIndexMap, items, dataPack)
	inputs[common.KeyLongTermMemories] = items
	if len(items) == 0 {
		return inputs, nil
	}
	return inputs, nil
}

// canConsolidateMemory 判断是否需要进行记忆的合并
// 如果同时存在新增记忆和检索记忆，则进行合并；如果同时存在会话记忆和检索记忆，则合并；如果同时存在会话记忆和新增记忆，则合并；如果仅存在新增记忆，则直接返回；
func (c ConsolidateLongTermMemoryChain) canConsolidateMemory(newestMemoryRecords, retrieveMemoryRecords, sessionMemoryRecords []storage.MemoryRecord) bool {
	matchCount := 0
	if len(newestMemoryRecords) > 0 {
		matchCount++
	}
	if len(retrieveMemoryRecords) > 0 {
		matchCount++
	}
	if len(sessionMemoryRecords) > 0 {
		matchCount++
	}
	return matchCount >= 2
}

// filterRetrieveMemoryRecords 过滤检索记忆，去除初始化记忆
func (c ConsolidateLongTermMemoryChain) filterRetrieveMemoryRecords(retrieveMemoryRecords []storage.MemoryRecord) []storage.MemoryRecord {
	result := make([]storage.MemoryRecord, 0, len(retrieveMemoryRecords))
	for _, r := range retrieveMemoryRecords {
		if r.Source != definition.MemoryInitSource {
			result = append(result, r)
		}
	}
	return result
}

// appendSimilarMemory 追加相似记忆
func (c ConsolidateLongTermMemoryChain) appendSimilarMemory(ctx context.Context, inputs map[string]any, retrieveMemoryRecords []storage.MemoryRecord, newestMemoryRecords []storage.MemoryRecord) []storage.MemoryRecord {
	workspacePath := util.GetWorkspacePath(ctx, inputs)
	uniqueIds := make(map[string]bool)
	for _, r := range retrieveMemoryRecords {
		uniqueIds[r.ID] = true
	}
	for _, r := range newestMemoryRecords {
		uniqueIds[r.ID] = true
	}
	for _, r := range newestMemoryRecords {
		similarItems, err := storage.QuerySimilar(r.Content, workspacePath, "")
		if err != nil {
			log.Debugf("[memory]-[ltm] query similar memory error: %v content: %s", err, r.Content)
			continue
		}
		for _, similarItem := range similarItems {
			if similarItem.ID == r.ID {
				continue
			}
			score := 1 - similarItem.Distance
			if score >= similarMemoryThreshold {
				// 去重
				if uniqueIds[similarItem.ID] {
					continue
				}
				uniqueIds[similarItem.ID] = true
				log.Debugf("[memory]-[ltm] append similar memory: %S title: %s score: %f", similarItem.ID, similarItem.Title, score)
				retrieveMemoryRecords = append(retrieveMemoryRecords, similarItem)
			}
		}
	}
	return retrieveMemoryRecords
}

// getCurrentSessionMemory 获取当前会话的长期记忆
func (c ConsolidateLongTermMemoryChain) getCurrentSessionMemory(inputs map[string]any) []storage.MemoryRecord {
	sessionId := inputs[common.KeySessionId].(string)
	condition := storage.MemoryQueryCondition{
		SessionID: sessionId,
		Type:      definition.LongTermMemoryType,
		TopK:      20,
	}
	queryResult, err := storage.Query(condition)
	if err != nil {
		log.Debugf("[memory]-[ltm] query current session memory error: %v sessionId: %s", err, sessionId)
		return nil
	}
	log.Debugf("[memory]-[ltm] query current session memory success: %d sessionId: %s", len(queryResult.Records), sessionId)
	return queryResult.Records
}

// updateCurrentSessionMemory 更新当前会话的长期记忆，去除会话管理
func (c ConsolidateLongTermMemoryChain) updateCurrentSessionMemory(inputs map[string]any, sessionMemoryRecords []storage.MemoryRecord) {
	sessionId := inputs[common.KeySessionId].(string)

	if len(sessionMemoryRecords) > 0 {
		// 解除与会话的关联
		updateItems := make([]storage.MemoryRecord, 0, len(sessionMemoryRecords))
		for _, r := range sessionMemoryRecords {
			updateItems = append(updateItems, storage.MemoryRecord{
				ID:        r.ID,
				SessionID: "",
			})
		}
		err := storage.UpdateRecordsPlus(updateItems, map[string]bool{
			"session_id": true,
		})
		if err != nil {
			log.Debugf("[memory]-[ltm] update current session memory error: %v sessionId: %s", err, sessionId)
		}
		log.Debugf("[memory]-[ltm] update current session memory success: %d sessionId: %s", len(sessionMemoryRecords), sessionId)
	}
}

// buildMemoryPromptInput 构建prompt输入
func (c ConsolidateLongTermMemoryChain) buildMemoryPromptInput(ctx context.Context, inputs map[string]any, newMemoryRecords []storage.MemoryRecord,
	retrieveMemoryRecords []storage.MemoryRecord) (prompt.LongTermMemoryConsolidatePromptInput, map[string]storage.MemoryRecord) {
	// prompt只放入序号，不放入实际id，避免模型幻觉，所以需要做个映射
	memoryIndexMap := make(map[string]storage.MemoryRecord, len(newMemoryRecords)+len(retrieveMemoryRecords))
	promptInput := prompt.LongTermMemoryConsolidatePromptInput{
		BaseInput: prompt.BaseInput{
			RequestId: inputs[common.KeyRequestId].(string),
			SessionId: inputs[common.KeySessionId].(string),
		},
		ExistMemories: util.ConvertPromptItems(retrieveMemoryRecords, memoryIndexMap),
		NewMemories:   util.ConvertPromptItems(newMemoryRecords, memoryIndexMap),
	}
	return promptInput, memoryIndexMap
}

// buildMemoryRequest 构建请求
func (c ConsolidateLongTermMemoryChain) buildMemoryRequest(inputs map[string]any, promptInput prompt.LongTermMemoryConsolidatePromptInput) ([]*agentDefinition.Message, error) {
	systemPrompt, err := prompt.Engine.RenderConsolidateMemorySystemPrompt(promptInput)
	if err != nil {
		return nil, err
	}
	userPrompt, err := prompt.Engine.RenderConsolidateMemoryUserPrompt(promptInput)
	if err != nil {
		return nil, err
	}
	return util.BuildSystemUserMessage(systemPrompt, userPrompt), nil
}

// updateStore 更新长期记忆存储。
// 该函数负责处理长期记忆的合并和存储，通过接收一系列新的长期记忆项，
// 并根据这些项中的合并ID删除旧的记录，然后将新的记忆项保存到数据库中。
// 参数:
//
//	inputs - 包含会话ID等信息的映射。
//	index2Ids - 将标识符映射到其对应的记录ID的映射。
//	newItems - 新的长期记忆项的列表。
func (c ConsolidateLongTermMemoryChain) updateStore(ctx context.Context, inputs map[string]any, memoryIndexMap map[string]storage.MemoryRecord,
	newItems []definition.LongTermMemory, dataPack map[string]string) {
	startTime := time.Now()
	// 初始化将要删除的记录ID列表和将要创建的记忆项列表。
	deleteIds := make([]string, 0, len(memoryIndexMap))
	createMemories := make([]definition.LongTermMemory, 0, len(newItems))

	// 遍历新的长期记忆项，收集需要删除的记录ID。
	for _, item := range newItems {
		if len(item.MergeIds) <= 1 {
			continue
		}
		maxFreq := 0
		for _, id := range item.MergeIds {
			if originMemory, ok := memoryIndexMap[strings.TrimSpace(id)]; ok {
				deleteIds = append(deleteIds, originMemory.ID)
				if originMemory.Freq > maxFreq {
					maxFreq = originMemory.Freq
				}
			}
		}
		item.Freq = maxFreq + 1
		createMemories = append(createMemories, item)
	}

	if len(deleteIds) > 0 {
		log.Debugf("delete memory ids: %s", deleteIds)
		// 调用存储层接口删除旧的记录。
		err := storage.DeleteRecords(deleteIds)
		if err != nil {
			log.Debugf("delete memory error, reason=%v, deleteIds=%s", err, deleteIds)
		}
	}

	if len(createMemories) > 0 {
		// 将新的长期记忆项转换为记忆记录。
		newRecords := ConvertMemoryRecord(ctx, inputs, createMemories)
		log.Debugf("save memory records: %+v", newRecords)
		// 将新的记忆记录保存到数据库中。
		err := SaveMemoryRecordsToDB(newRecords)
		if err != nil {
			// 如果保存失败，记录错误信息。
			log.Debugf("save memory failed: %v newRecords: %v", err, newRecords)
		}
	}
	log.Debugf("consolidate long term memory update store cost: %v", time.Since(startTime))
	requestId := inputs[common.KeyRequestId].(string)
	dataPack["create_count_after_consolidate"] = strconv.Itoa(len(createMemories))
	dataPack["delete_count_after_consolidate"] = strconv.Itoa(len(deleteIds))
	telemetry(requestId, dataPack, "consolidate")
}

func telemetry(requestId string, dataPack map[string]string, status string) {
	dataPack["status"] = status
	sls.Report(sls.EventTypeConsolidateMemory, requestId, dataPack)
}

func (c ConsolidateLongTermMemoryChain) GetMemory() schema.Memory {
	return memory.NewSimple()
}

func (c ConsolidateLongTermMemoryChain) GetInputKeys() []string {
	return []string{}
}

func (c ConsolidateLongTermMemoryChain) GetOutputKeys() []string {
	return []string{}
}

func (c ConsolidateLongTermMemoryChain) GetCallbackHandler() callbacks.Handler { //nolint:ireturn
	return nil
}
