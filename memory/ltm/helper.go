package ltm

import (
	"context"
	"cosy/chat/chains/common"
	"cosy/definition"
	"cosy/experiment"
	"cosy/global"
	"cosy/log"
	"cosy/memory/storage"
	"cosy/memory/util"
	"cosy/tokenizer"
	"cosy/user"
	"fmt"
	"regexp"
	"strings"
	"time"

	"github.com/patrickmn/go-cache"

	"github.com/google/uuid"
)

// 定义正则表达式为全局变量
var memoryPattern = regexp.MustCompile(`<content>([\s\S]*?)</content>`)

var StopRequestCache = cache.New(5*time.Minute, 10*time.Minute)

// ParseMemoryOutputText 将记忆输出解析为长期记忆
// 格式：<think>思考：输入的记忆信息XXX</think>
//
//	<content>
//	title: 项目整体架构
//	scope: workspace
//	keywords: 架构,技术栈,前端,后端
//	content: 项目技术架构：XXX
//	</content>
//	<content>
//	title: 交流风格偏好
//	scope: global
//	keywords: 俏皮,风格
//	content: xxx
//	</content>
func ParseMemoryOutputText(outputText string) []definition.LongTermMemory {
	matches := memoryPattern.FindAllStringSubmatch(outputText, -1)

	var memories []definition.LongTermMemory

	for _, match := range matches {
		memoryBlock := match[1]
		lines := strings.Split(memoryBlock, "\n")

		var memory definition.LongTermMemory
		var contentLines []string
		var inContent bool = false

		for _, line := range lines {
			line = strings.TrimSpace(line)
			if strings.HasPrefix(line, "mergeIds:") {
				memory.MergeIds = strings.Split(strings.TrimSpace(line[9:]), ",")
			} else if strings.HasPrefix(line, "title:") {
				memory.Title = strings.TrimSpace(line[6:])
			} else if strings.HasPrefix(line, "source:") {
				memory.Source = strings.TrimSpace(line[7:])
			} else if strings.HasPrefix(line, "scope:") {
				memory.Scope = strings.TrimSpace(line[6:])
			} else if strings.HasPrefix(line, "category:") {
				memory.Category = strings.TrimSpace(line[9:])
			} else if strings.HasPrefix(line, "keywords:") {
				keywordsStr := strings.TrimSpace(line[9:])
				if keywordsStr != "" {
					memory.Keywords = strings.Split(keywordsStr, ",")
					// 去除每个关键词的空格
					for i, kw := range memory.Keywords {
						memory.Keywords[i] = strings.TrimSpace(kw)
					}
				}
			} else if strings.HasPrefix(line, "content:") {
				inContent = true
				contentLines = append(contentLines, strings.TrimSpace(line[8:]))
			} else if inContent {
				contentLines = append(contentLines, line)
			}
		}

		memory.Content = strings.TrimSpace(strings.Join(contentLines, "\n"))
		if memory.Content != "" && memory.Title != "" && memory.Scope != "" && memory.Content != "无" && memory.Title != "无" {
			memories = append(memories, memory)
		}
	}

	return memories
}

// ConvertMemoryRecord 将记忆记录转换为存储格式
func ConvertMemoryRecord(ctx context.Context, inputs map[string]any, memoryItems []definition.LongTermMemory) []storage.MemoryRecord {
	result := make([]storage.MemoryRecord, 0, len(memoryItems))
	workspacePath := util.GetWorkspacePath(ctx, inputs)
	userId := ""
	cachedUser := user.GetCachedUserInfo()
	if cachedUser != nil && cachedUser.Uid != "" {
		userId = cachedUser.Uid
	}
	for _, item := range memoryItems {
		// 设置 ScopeId
		var scopeId string
		if item.Scope == definition.MemoryWorkspaceScope {
			scopeId = workspacePath
		} else {
			scopeId = definition.MemoryGlobalScopeId
		}
		tokenCount, err := tokenizer.CalQwenTokenCount(item.Content)
		if err != nil {
			tokenCount = len([]byte(item.Content+item.Title)) / 3
		}

		category := item.Category
		if _, ok := definition.MemoryCategoryMap[category]; !ok {
			category = definition.MemoryCategoryUnknown
		}
		record := storage.MemoryRecord{
			ID:          uuid.NewString(),
			GmtCreate:   int(time.Now().Unix()),
			GmtModified: int(time.Now().Unix()),
			Title:       item.Title,
			Scope:       item.Scope,
			ScopeId:     scopeId,
			Keywords:    strings.Join(item.Keywords, ","),
			Content:     item.Content,
			SessionID:   "", // 长期记忆不需要关联会话
			IsMerged:    0,
			Freq:        item.Freq,
			Source:      item.Source,
			TokenCount:  tokenCount,
			Type:        definition.LongTermMemoryType,
			UserId:      userId,
			Status:      definition.MemoryActiveState,
			Category:    category,
		}
		InitMemoryRecordParam(&record)
		result = append(result, record)
	}
	return result
}

// SaveMemoryRecordsToDB 保存记忆记录到数据库
func SaveMemoryRecordsToDB(records []storage.MemoryRecord) error {
	// 检查记录是否为空
	if len(records) == 0 {
		return nil
	}
	excludeCategory := experiment.ConfigService.GetStringConfigWithEnv(definition.ExperimentMemoryExcludeCategory, experiment.ConfigScopeClient, "")
	if excludeCategory != "" {
		for _, record := range records {
			if !strings.Contains(excludeCategory, record.Category) {
				records = append(records, record)
			}
		}
	}
	if len(records) == 0 {
		return nil
	}

	// 使用便捷函数保存记录
	err := storage.InsertRecords(records)
	if err != nil {
		log.Errorf("[memory]-[ltm] batch insert memory failed: %v", err)
		return fmt.Errorf("batch insert failed: %w", err)
	}

	log.Debugf("[memory]-[ltm] successfully saved %d memory records", len(records))
	return nil
}

// DeleteMemoryRecordsBySession 删除指定会话的记忆记录
func DeleteMemoryRecordsBySession(category, sessionId string) error {
	return storage.DeleteRecordsByCategoryAndSessionId(category, sessionId)
}

// countGroupRecordsByCategory 统计每个分类的记忆数量
func countGroupRecordsByCategory(records []storage.MemoryRecord) map[string]int {
	categoryMap := make(map[string]int)
	for _, record := range records {
		categoryMap[record.Category]++
	}
	return categoryMap
}

// IsEnableAutoMemory 判断是否启用自动记忆功能
func IsEnableAutoMemory(inputs map[string]any) bool {
	if global.IsEvaluationMode() {
		return true
	}
	rawInputParams, ok := inputs[common.KeyChatAskParams].(*definition.AskParams)
	return ok && rawInputParams != nil && rawInputParams.PluginPayloadConfig.IsEnableAutoMemory
}
