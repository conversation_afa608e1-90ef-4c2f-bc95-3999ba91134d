package ltm

import (
	"context"
	"cosy/definition"
	"cosy/experiment"
	"cosy/log"
	"cosy/memory/storage"
	"cosy/sls"
	"cosy/user"
	"encoding/json"
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
)

// 艾宾浩斯遗忘曲线常量
const (

	// EbbinghausDecayRate 遗忘衰减率，艾宾浩斯曲线参数
	EbbinghausDecayRate = 0.12

	// DefaultForgetScoreThreshold 记忆遗忘阈值，低于此值的记忆遗忘值加1，达到MaxForgetCount数量后删除记忆
	DefaultForgetScoreThreshold = 0.3

	// ReviewScoreThreshold 复习阈值，低于此值的记忆将被标记为需要复习
	ReviewScoreThreshold = 0.5

	// DefaultMaxForgetCount 默认最大遗忘次数，超过此次数的记忆将被归档
	DefaultMaxForgetCount = 1

	// MaxReviewDetailItemCount 最大复习记录数量
	MaxReviewDetailItemCount = 60
)

var (
	reviewIntervals            = []int{24, 72, 168, 336, 720, 1440, 2160, 4320, 8640}
	defaultRetentionScoreParam = map[string]string{
		"frequency": "0.2",
		"review":    "0.1",
		"quality":   "0.2",
		"decay":     "0.12",
	}
)

// CalculateEbbinghausRetention 计算艾宾浩斯遗忘曲线记忆保留率
// t: 经过的时间（小时）
// k: 遗忘衰减率（艾宾浩斯建议0.12 - 0.17）
// strength: 记忆强度，多次复习会增加记忆强度，提高记忆保留率
func CalculateEbbinghausRetention(t float64, k float64, strength float64) float64 {
	// 艾宾浩斯遗忘曲线公式: R = e^(-k*t/s)
	// R: 记忆保留率
	// k: 遗忘衰减率
	// t: 时间（小时）
	// s: 记忆强度，默认为1，复习可增加强度
	return math.Exp(-k * t / strength)
}

// CalculateNextReviewInterval 计算下一次复习间隔（小时）
func CalculateNextReviewInterval(reviewCount int) int {
	intervalIndex := reviewCount
	if intervalIndex >= len(reviewIntervals) {
		intervalIndex = len(reviewIntervals) - 1
	}
	return reviewIntervals[intervalIndex]
}

// calculateMemoryStrength 计算记忆强度
// 基于记忆频率、复习次数和分类重要性
func calculateMemoryStrength(memory storage.MemoryRecord, params map[string]string) float64 {
	// 基础强度
	strength := 1.0

	// 根据访问频率增加强度
	frequencyRate, err := strconv.ParseFloat(params["frequency"], 64)
	if err != nil {
		frequencyRate = 0.2
	}
	frequencyFactor := math.Log(float64(memory.Freq+1)) * frequencyRate
	strength += frequencyFactor

	// 根据复习历史增加强度
	reviewHistory := memory.GetReviewHistory()
	reviewCount := reviewHistory.ReviewCount
	reviewRate, err := strconv.ParseFloat(params["review"], 64)
	if err != nil {
		reviewRate = 0.1
	}
	reviewFactor := float64(reviewCount) * reviewRate
	strength += reviewFactor

	// 根据记忆质量决定增加或者减小强度
	qualityRate, err := strconv.ParseFloat(params["quality"], 64)
	if err != nil {
		qualityRate = 0.2
	}
	// 归一记忆质量分数
	// 将质量从 [-50, 50] 归一到 [-40, 60]/5 * 0.2 = [-1.6, 2.4]
	qualityFactor := (memory.QualityScore + 10) / 5 * qualityRate
	strength += qualityFactor

	// 根据分类重要性调整强度
	categoryImportance := getCategoryImportance(memory.Category)
	strength *= categoryImportance

	//log.Debugf("[memory]-[forget] memory strength: %f, frequencyFactor: %f, reviewFactor: %f, qualityFactor: %f, categoryImportance: %f", strength, frequencyFactor, reviewFactor, qualityFactor, categoryImportance)

	return math.Max(strength, 1.0) // 确保最小强度为1.0
}

// calculateRetentionScore 计算记忆保留分数
func calculateRetentionScore(memory storage.MemoryRecord) float64 {
	now := time.Now()
	lastReviewTime := time.Unix(int64(memory.LastReviewTime), 0)

	// 计算经过的小时数
	hoursPassed := now.Sub(lastReviewTime).Hours() / 24

	params := experiment.ConfigService.GetStringMapConfigWithEnv(definition.ExperimentMemoryRetentionScoreParam, experiment.ConfigScopeClient, defaultRetentionScoreParam)

	// 计算记忆强度
	strength := calculateMemoryStrength(memory, params)

	// 计算记忆保留率
	decayRate, err := strconv.ParseFloat(params["decay"], 64)
	if err != nil {
		decayRate = EbbinghausDecayRate
	}
	retention := CalculateEbbinghausRetention(hoursPassed, decayRate, strength)
	log.Debugf("[memory]-[forget] retention: %f, strength: %f, decayRate: %f, hoursPassed: %f", retention, strength, decayRate, hoursPassed)

	return retention
}

// calculateMemoryScoreV2 计算记忆综合分数
func calculateMemoryScoreV2(memory storage.MemoryRecord) float64 {
	now := time.Now()

	// 基础遗忘曲线
	hoursPassed := now.Sub(time.Unix(int64(memory.LastReviewTime), 0)).Hours()
	retention := math.Exp(-hoursPassed / (float64(memory.Freq+1) * 1.2))

	// 访问频率权重
	frequencyWeight := math.Log(float64(memory.Freq + 1))

	// 时间衰减
	timeDecay := math.Exp(-now.Sub(time.Unix(int64(memory.GmtCreate), 0)).Hours() / (365 * 24))

	// 综合分数
	return retention*0.7 +
		frequencyWeight*0.2 +
		timeDecay*0.1
}

// getCategoryImportance 获取分类重要性系数
func getCategoryImportance(category string) float64 {
	if importance, ok := definition.MemoryCategoryMap[category]; ok {
		return importance
	}
	return 1.0
}

// RecordReview 记录记忆复习
// 复习将提高记忆保留分数，并更新下次复习时间
func RecordReview(records []storage.MemoryRecord) error {
	// 更新复习历史
	now := time.Now()
	reviewTime := now.Unix()
	updateRecords := make([]storage.MemoryRecord, 0, len(records))
	for _, record := range records {
		reviewHistory := record.GetReviewHistory()
		reviewHistory.ReviewCount++
		reviewHistory.Items = append(reviewHistory.Items, storage.MemoryReviewItem{
			ReviewTime:     reviewTime,
			Freq:           record.Freq,
			RetentionScore: record.RetentionScore,
		})
		if len(reviewHistory.Items) > MaxReviewDetailItemCount {
			reviewHistory.Items = reviewHistory.Items[1:]
		}
		historyJson, err := json.Marshal(reviewHistory)
		if err != nil {
			log.Errorf("[memory]-[forget] failed to marshal review history: %s", err)
		} else {
			record.ReviewHistory = string(historyJson)
		}
		if record.Category != definition.MemoryCategoryUserPrefer {
			// 如果不是用户偏好记忆，则强制为工程级记忆
			record.Scope = definition.MemoryWorkspaceScope
		}

		// 更新记忆保留分数（复习后重置为1.0）
		record.RetentionScore = 1.0

		// 计算下次复习时间
		nextInterval := CalculateNextReviewInterval(reviewHistory.ReviewCount)
		record.NextReviewTime = int(now.Add(time.Duration(nextInterval) * time.Hour).Unix())

		// 更新最近复习时间
		record.LastReviewTime = int(now.Unix())

		// 确保状态为活跃
		record.Status = definition.MemoryActiveState
		// 不更新向量
		record.MemoryEmbedding = nil
		updateRecords = append(updateRecords, record)
		// 将时间戳转换为可读的日期时间格式
		nextReviewTime := time.Unix(int64(record.NextReviewTime), 0).Format("2006-01-02 15:04:05")
		log.Debugf("[memory]-[forget] memory reviewed, id:%s title:%s nextReviewTime:%s (timestamp:%d)", record.ID, record.Title, nextReviewTime, record.NextReviewTime)
	}

	if len(updateRecords) > 0 {
		// 更新记录
		err := storage.UpdateRecords(updateRecords)
		if err != nil {
			return fmt.Errorf("failed to update memory after review: %w", err)
		}
	}
	return nil
}

// ProcessForgetting 处理记忆遗忘
// 计算所有记忆的保留分数，并处理可能需要遗忘或归档的记忆
func ProcessForgetting() error {
	log.Debugf("[memory]-[forget] start processing memory forgetting")

	// 获取存储服务
	memoryService := storage.GetMemoryService()
	if memoryService == nil {
		return fmt.Errorf("memory service not initialized")
	}
	userId := ""
	cachedUser := user.GetCachedUserInfo()
	if cachedUser != nil && cachedUser.Uid != "" {
		userId = cachedUser.Uid
	}

	// 查询所有活跃的长期记忆
	condition := storage.MemoryQueryCondition{
		Status: definition.MemoryActiveState,
		Source: []string{definition.MemoryAutoSource},
		Type:   definition.LongTermMemoryType,
		UserId: userId,
	}

	result, err := memoryService.Query(condition)
	if err != nil {
		return fmt.Errorf("failed to query memories: %w", err)
	}

	var updatedCount, inactiveCount, forgotCount int
	var forgotMemoryInfo []interface{}

	scores := make([]string, 0, len(result.Records))
	for _, record := range result.Records {
		if record.Status != definition.MemoryActiveState {
			continue // 只处理活跃状态的记忆
		}
		if record.Source == definition.MemoryUserSource {
			// 用户要求的记忆不参与遗忘处理
			continue
		}

		// 计算记忆保留分数
		retentionScore := calculateRetentionScore(record)
		record.RetentionScore = retentionScore
		scores = append(scores, fmt.Sprintf("%.3f", retentionScore))
		// 检查是否遗忘
		forgotThreshold := experiment.ConfigService.GetDoubleConfigWithEnv(definition.ExperimentKeyMemoryForgotThreshold, experiment.ConfigScopeClient, DefaultForgetScoreThreshold)
		if retentionScore < forgotThreshold {
			// 达到非活跃条件，但还未需要归档
			record.ForgetCount++
			inactiveCount++
		}
		maxForgotCount := experiment.ConfigService.GetIntConfigWithEnv(definition.ExperimentKeyMemoryMaxForgotCount, experiment.ConfigScopeClient, DefaultMaxForgetCount)
		if record.ForgetCount >= maxForgotCount {
			err := memoryService.Delete([]string{record.ID})
			if err != nil {
				log.Errorf("[memory]-[forget] failed to delete memory %s: %v", record.ID, err)
			} else {
				log.Infof("[memory]-[forget] memory %s forgot after %d forgets", record.ID, record.ForgetCount)
				log.Debugf("[memory]-[forget] memory delete detail: %+v", record)

				// 记录被删除记忆的详细信息，转成JSON字符串
				memoryInfo := map[string]interface{}{
					"id":               record.ID,
					"title":            record.Title,
					"freq":             record.Freq,
					"distance":         record.Distance,
					"source":           record.Source,
					"scope":            record.Scope,
					"token_count":      record.TokenCount,
					"type":             record.Type,
					"category":         record.Category,
					"retention_score":  record.RetentionScore,
					"next_review_time": record.NextReviewTime,
					"last_review_time": record.LastReviewTime,
					"forget_count":     record.ForgetCount,
					"status":           record.Status,
					"quality_score":    record.QualityScore,
					"keyword":          record.Keywords,
					"review_passed":    strconv.FormatFloat(time.Now().Sub(time.Unix(int64(record.LastReviewTime), 0)).Hours(), 'f', -1, 64),
					"survival_time":    strconv.FormatFloat(time.Now().Sub(time.Unix(int64(record.GmtCreate), 0)).Hours(), 'f', -1, 64),
				}
				forgotMemoryInfo = append(forgotMemoryInfo, memoryInfo)
			}
			forgotCount++
			continue
		}

		// 更新记录
		err = memoryService.Update(record)
		if err != nil {
			log.Errorf("[memory]-[forget] failed to update memory %s: %v", record.ID, err)
			continue
		}

		updatedCount++
	}

	log.Debugf("[memory]-[forget] processed forgetting for %d memories: %d inactive, %d forgot",
		updatedCount, inactiveCount, forgotCount)
	eventData := map[string]string{
		"memory_count":   strconv.Itoa(len(result.Records)),
		"inactive_count": strconv.Itoa(inactiveCount),
		"forgot_count":   strconv.Itoa(forgotCount),
		"scores":         strings.Join(scores, ","),
	}

	// 将被删除的记忆信息添加到埋点事件中
	if len(forgotMemoryInfo) > 0 {
		// 将记忆信息数组转换为JSON字符串
		jsonForgotMemoryInfo, err := json.Marshal(forgotMemoryInfo)
		if err != nil {
			log.Warnf("[memory]-[forget] failed to marshal forgot memory info: %v", err)
		} else {
			eventData["forgot_memory_info"] = string(jsonForgotMemoryInfo)
		}
	}

	go sls.Report(sls.EventTypeChatAgentMemoryForget, uuid.NewString(), eventData)
	return nil
}

// GetDueReviewMemories 获取需要复习的记忆
// 返回下次复习时间已到的记忆记录，用于提醒用户复习
func GetDueReviewMemories(ctx context.Context) ([]storage.MemoryRecord, error) {
	// 获取存储服务
	memoryService := storage.GetMemoryService()
	if memoryService == nil {
		return nil, fmt.Errorf("memory service not initialized")
	}

	now := time.Now()
	userId := ""
	cachedUser := user.GetCachedUserInfo()
	if cachedUser != nil && cachedUser.Uid != "" {
		userId = cachedUser.Uid
	}

	// 查询所有活跃的且复习时间已到的长期记忆
	condition := storage.MemoryQueryCondition{
		Status: definition.MemoryActiveState,
		Source: []string{definition.MemoryAutoSource},
		Type:   definition.LongTermMemoryType,
		UserId: userId,
	}

	result, err := memoryService.Query(condition)
	if err != nil {
		return nil, fmt.Errorf("failed to query memories: %w", err)
	}

	var dueMemories []storage.MemoryRecord
	for _, record := range result.Records {
		retentionScore := calculateRetentionScore(record)
		// 检查记录状态和复习时间是否已到
		if retentionScore < ReviewScoreThreshold && record.NextReviewTime <= int(now.Unix()) {
			dueMemories = append(dueMemories, record)
		}
	}

	return dueMemories, nil
}

// InitMemoryRecordParam 初始化记忆基本信息
func InitMemoryRecordParam(record *storage.MemoryRecord) {
	now := time.Now()

	// 检查并设置保留分数
	record.RetentionScore = 1.0

	// 如果没有复习时间，使用记录的创建时间或当前时间
	if record.GmtCreate > 0 {
		record.LastReviewTime = record.GmtCreate
	} else {
		record.LastReviewTime = int(now.Unix())
	}

	// 默认设置为一天后复习
	record.NextReviewTime = int(time.Unix(int64(record.LastReviewTime), 0).Add(24 * time.Hour).Unix())
	record.ForgetCount = 0
	record.ReviewHistory = ""
	// 检查并设置状态
	record.Status = definition.MemoryActiveState
}
