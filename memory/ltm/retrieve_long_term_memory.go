package ltm

import (
	"context"
	chainUtil "cosy/chat/chains/chain"
	"cosy/chat/chains/common"
	"cosy/config"
	"cosy/definition"
	"cosy/experiment"
	"cosy/global"
	"cosy/log"
	"cosy/memory/rerank"
	"cosy/memory/storage"
	"cosy/memory/util"
	"cosy/prompt"
	"cosy/sls"
	"cosy/tokenizer"
	cosyUtil "cosy/util"
	"encoding/json"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/tmc/langchaingo/chains"
	"github.com/tmc/langchaingo/memory"
	"github.com/tmc/langchaingo/schema"
)

const (
	// minRerankThreshold 最小重排记忆数量
	minRerankThreshold = 0
	// noneRerankStrategy 无需重排的策略
	noneRerankStrategy = "none"
	// modelBasedRerankStrategy 基于模型重排策略
	modelBasedRerankStrategy = "model_based"
	// apiBasedRerankStrategy 基于API的重排策略
	apiBasedRerankStrategy = "api_based"
	// fusionRerankStrategy 基于模型重排和API重排的融合策略
	fusionRerankStrategy = "fusion"
)

// RetrieveLongTermMemoryChain 记忆巩固链
type RetrieveLongTermMemoryChain struct {
}

func (c RetrieveLongTermMemoryChain) Call(ctx context.Context, inputs map[string]any, options ...chains.ChainCallOption) (map[string]any, error) {
	if !experiment.ConfigService.GetBoolConfigWithEnv(definition.ExperimentMemoryFetchEnable, experiment.ConfigScopeClient, true) {
		return inputs, nil
	}
	if !experiment.ConfigService.GetBoolConfigWithEnv(definition.ExperimentMemoryChatEditEnable, experiment.ConfigScopeClient, true) {
		if chainUtil.IsChatOrEditAgent(inputs) {
			log.Debugf("skip retrieve long term memory for chat or edit mode")
			return inputs, nil
		}
	}
	startTime := time.Now()
	stopWatch := cosyUtil.NewStopwatch()
	requestId := inputs[common.KeyRequestId].(string)
	sessionId := inputs[common.KeySessionId].(string)
	rawInputParams := inputs[common.KeyChatAskParams].(*definition.AskParams)
	userQuery := chainUtil.GetUserInputQueryWithoutCode(rawInputParams.ChatContext)
	if refinedQuery, ok := inputs[common.KeyRefinedGenernalQuery].(string); ok {
		log.Debugf("retrieve long term memory. userefined query: %s", refinedQuery)
		userQuery = refinedQuery
	}
	if userQuery == "" {
		userQuery = rawInputParams.QuestionText
	}
	userQuery = strings.TrimSpace(userQuery)
	if userQuery == "" {
		log.Debugf("retrieve long term memory no user query, requestId=%s", requestId)
		return inputs, nil
	}
	// 根据当前问题查询记忆
	workspacePath := util.GetWorkspacePath(ctx, inputs)
	isUseTool := chainUtil.IsUseTool(inputs)
	stopWatch.Start("query_memory_cost")
	records, err := storage.QueryByQuestion(sessionId, userQuery, workspacePath, isUseTool)
	if err != nil {
		log.Debugf("retrieve long term memory error, reason=%v, requestId=%s", err, requestId)
		return inputs, nil
	}
	if len(records) == 0 {
		log.Debugf("retrieve long term memory no records, requestId=%s", requestId)
		return inputs, nil
	}
	sort.Slice(records, func(i, j int) bool {
		return records[i].GmtModified > records[j].GmtModified
	})
	recallCount := len(records)
	rerankCount := 0
	truncateCount := 0
	rerankName := ""
	if len(records) > minRerankThreshold {
		stopWatch.Start("rerank_memory_cost")
		records, rerankName = c.rerank(ctx, inputs, records)
		rerankCount = len(records)
	}
	if len(records) > 0 {
		stopWatch.Start("truncate_memory_cost")
		records = c.truncateMemory(ctx, inputs, records)
		stopWatch.Start("post_memory_cost")
		truncateCount = len(records)
		// 构建精简的日志记录，只包含id、title和distance
		simplifiedRecords := make([]map[string]interface{}, 0, len(records))
		for _, record := range records {
			recordRecord := map[string]interface{}{
				"id":           record.ID,
				"title":        record.Title,
				"distance":     record.Distance,
				"qualityScore": record.QualityScore,
			}
			if global.IsEvaluationMode() {
				recordRecord["content"] = record.Content
			}
			simplifiedRecords = append(simplifiedRecords, recordRecord)
		}
		recordJson, _ := json.Marshal(simplifiedRecords)
		log.Debugf("[memory]-[retrieve] retrieve long term memory records count: %d, requestId=%s, records=%s", len(records), requestId, recordJson)
		go func() {
			err = c.reviewMemory(ctx, records)
			if err != nil {
				log.Debugf("review memory error, reason=%v, requestId=%s", err, requestId)
			}
		}()
		inputs[common.KeyLongTermMemories] = records
		memoryPrompt, err := c.renderPrompt(ctx, inputs, records)
		if err != nil {
			log.Debugf("render memory prompt error, reason=%v, requestId=%s", err, requestId)
			return inputs, nil
		}
		if memoryPrompt == "" {
			log.Debugf("retrieve long term memory no memory prompt, requestId=%s", requestId)
			return inputs, nil
		}
		inputs[common.KeyLongTermMemoryPrompt] = memoryPrompt
	}
	eventData := map[string]string{
		"request_id":        requestId,
		"sessionId":         sessionId,
		"memory_count":      strconv.Itoa(len(records)),
		"recall_count":      strconv.Itoa(recallCount),
		"rerank_count":      strconv.Itoa(rerankCount),
		"truncate_count":    strconv.Itoa(truncateCount),
		"quality_threshold": strconv.FormatFloat(experiment.ConfigService.GetDoubleConfigWithEnv(definition.ExperimentMemoryQualityScoreThreshold, experiment.ConfigScopeClient, 0.0), 'f', 2, 64),
		"cost":              strconv.Itoa(int(time.Since(startTime).Milliseconds())),
		"rerank_name":       rerankName,
	}
	stopWatch.Export(eventData)
	go sls.Report(sls.EventTypeChatAgentMemoryRetrieve, requestId, eventData)
	return inputs, nil
}

// renderPrompt 渲染记忆
// <memory id="1" title="用户交流偏好">
// 用户要求XXX
// </memory>
func (c RetrieveLongTermMemoryChain) renderPrompt(ctx context.Context, inputs map[string]any, records []storage.MemoryRecord) (string, error) {
	if len(records) == 0 {
		return "", nil
	}
	groupRecords := make(map[string][]storage.MemoryRecord)
	for _, record := range records {
		groupKey := record.Category
		if record.Source == definition.MemoryInitSource {
			groupKey = definition.MemoryInitSource
		}
		if items, ok := groupRecords[groupKey]; ok {
			items = append(items, record)
			groupRecords[groupKey] = items
		} else {
			groupRecords[groupKey] = []storage.MemoryRecord{record}
		}
	}
	rawInputParams := inputs[common.KeyChatAskParams].(*definition.AskParams)
	promptInput := prompt.ChatMemoryPromptInput{
		BaseInput: prompt.BaseInput{
			RequestId: rawInputParams.RequestId,
			SessionId: rawInputParams.SessionId,
		},
	}
	if userPreferRecords, ok := groupRecords[definition.MemoryCategoryUserPrefer]; ok {
		promptInput.UserPreferMemoryPrompt = c.buildMemoryGroupString(userPreferRecords)
	}
	if projectInfoRecords, ok := groupRecords[definition.MemoryCategoryProjectInfo]; ok {
		promptInput.ProjectInfoMemoryPrompt = c.buildMemoryGroupString(projectInfoRecords)
	}
	if experienceLessonsRecords, ok := groupRecords[definition.MemoryCategoryExperienceLessons]; ok {
		promptInput.ExperienceLessonsMemoryPrompt = c.buildMemoryGroupString(experienceLessonsRecords)
	}
	if projectIntroRecords, ok := groupRecords[definition.MemoryInitSource]; ok {
		promptInput.ProjectIntroMemoryPrompt = c.buildMemoryGroupString(projectIntroRecords)
	}
	if projectSpecificationRecords, ok := groupRecords[definition.MemoryCategoryProjectSpecification]; ok {
		promptInput.ProjectSpecificationMemoryPrompt = c.buildMemoryGroupString(projectSpecificationRecords)
	}
	if taskWorkflowRecords, ok := groupRecords[definition.MemoryCategoryTaskWorkflow]; ok {
		promptInput.TaskWorkflowMemoryPrompt = c.buildMemoryGroupString(taskWorkflowRecords)
	}
	if taskReferenceFilesRecords, ok := groupRecords[definition.MemoryCategoryTaskReferenceFiles]; ok {
		promptInput.TaskReferenceFilesMemoryPrompt = c.buildMemoryGroupString(taskReferenceFilesRecords)
	}
	if rawInputParams.SessionType == definition.SessionTypeCoder || rawInputParams.SessionType == definition.SessionTypeAssistant {
		return prompt.Engine.RenderChatAgentMemoryV2Prompt(promptInput)
	}
	return prompt.Engine.RenderChatMemoryPrompt(promptInput)
}

// buildMemoryGroupString 构建记忆组字符串
func (c RetrieveLongTermMemoryChain) buildMemoryGroupString(records []storage.MemoryRecord) string {
	sb := strings.Builder{}
	for _, record := range records {
		clearContent := strings.TrimSpace(record.Content)
		if strings.HasPrefix(record.Content, "```") {
			clearContent = strings.TrimPrefix(record.Content, "``")
			clearContent = strings.TrimSuffix(clearContent, "``")
		}
		sb.WriteString(fmt.Sprintf("<memory id=\"%s\" title=\"%s\">\n", record.ID, record.Title))
		sb.WriteString(clearContent)
		sb.WriteString("\n</memory>\n")
	}
	return sb.String()
}

// rerank 重排记忆
func (c RetrieveLongTermMemoryChain) rerank(ctx context.Context, inputs map[string]any, records []storage.MemoryRecord) ([]storage.MemoryRecord, string) {
	strategy := experiment.ConfigService.GetStringConfigWithEnv(definition.ExperimentKeyMemoryRerankStrategy, experiment.ConfigScopeClient, fusionRerankStrategy)
	if strategy == noneRerankStrategy {
		return records, ""
	}
	var reranker rerank.MemoryReranker
	if strategy == modelBasedRerankStrategy {
		reranker = rerank.NewModelBasedReranker()
	} else if strategy == apiBasedRerankStrategy {
		reranker = rerank.NewApiBasedReranker()
	} else if strategy == fusionRerankStrategy {
		reranker = rerank.NewFusionReranker()
	} else {
		log.Debugf("unknown memory rerank strategy: %s", strategy)
		return nil, ""
	}
	memoryRecordResult := reranker.Rerank(ctx, inputs, records)
	if memoryRecordResult.Error != nil {
		log.Debugf("[memory]-[rerank] rerank error: %v", memoryRecordResult.Error)
		return nil, ""
	}
	log.Debugf("[memory]-[rerank] rerank success, strategy: %s, name: %s records: %d", strategy, memoryRecordResult.Name, len(memoryRecordResult.Records))
	return memoryRecordResult.Records, memoryRecordResult.Name
}

// truncateMemory 截断记忆
func (c RetrieveLongTermMemoryChain) truncateMemory(ctx context.Context, inputs map[string]any, records []storage.MemoryRecord) []storage.MemoryRecord {
	// 增加基于QualityScore的过滤
	qualityScoreThreshold := experiment.ConfigService.GetDoubleConfigWithEnv(definition.ExperimentMemoryQualityScoreThreshold, experiment.ConfigScopeClient, 0.0)
	if qualityScoreThreshold > 0.0 {
		filteredRecords := make([]storage.MemoryRecord, 0, len(records))
		for _, record := range records {
			if record.QualityScore >= qualityScoreThreshold {
				filteredRecords = append(filteredRecords, record)
			}
		}
		records = filteredRecords
		// 如果过滤后没有记录，则返回空
		if len(records) == 0 {
			log.Debugf("[memory]-[ltm] no memory record after quality score filter, quality score threshold: %f", qualityScoreThreshold)
			return records
		}
	}

	chatTokenLimit := config.GlobalTruncateConfig.GetChunkLimit(config.TruncateKeyChatFetchMemory)
	totalCount := 0
	keepRecords := make([]storage.MemoryRecord, 0, len(records))
	for _, record := range records {
		if record.Source == definition.MemoryAutoSource || record.Source == definition.MemoryInitSource {
			if !IsEnableAutoMemory(inputs) {
				log.Debugf("[memory]-[ltm] auto memory disabled, source: %s", record.Source)
				continue
			}
			if record.Source == definition.MemoryAutoSource && !experiment.ConfigService.GetBoolConfigWithEnv(definition.ExperimentMemoryAutoExtractEnable, experiment.ConfigScopeClient, true) {
				log.Debugf("[memory]-[ltm] skip auto memory record, source: %s", record.Source)
				continue
			} else if record.Source == definition.MemoryInitSource && !experiment.ConfigService.GetBoolConfigWithEnv(definition.ExperimentMemoryInitExtractEnable, experiment.ConfigScopeClient, true) {
				log.Debugf("[memory]-[ltm] skip init memory record, source: %s", record.Source)
				continue
			}
		}
		tokenCount := record.TokenCount
		if record.TokenCount == 0 {
			tokenCount, _ = tokenizer.CalQwenTokenCount(record.Content)
		}
		if totalCount+tokenCount > chatTokenLimit {
			log.Debugf("truncate memory, record id=%s, token count=%d total=%d", record.ID, record.TokenCount, tokenCount)
			break
		}
		totalCount += tokenCount
		keepRecords = append(keepRecords, record)
	}
	return keepRecords
}

// reviewMemory 复习记忆
func (c RetrieveLongTermMemoryChain) reviewMemory(ctx context.Context, records []storage.MemoryRecord) error {
	//dueMemories, err := GetDueReviewMemories(ctx)
	//if err != nil {
	//	log.Debugf("get due review memories error, reason=%v", err)
	//}
	//log.Debugf("review due memories count=%d, retrieve count=%d", len(dueMemories), len(records))
	//waitingMemories := make([]storage.MemoryRecord, 0, len(records)+len(dueMemories))
	//duplicates := make(map[string]bool)
	//for _, record := range records {
	//	if _, ok := duplicates[record.ID]; !ok {
	//		// 只有检索出来的记忆才增加访问次数
	//		record.Freq++
	//		waitingMemories = append(waitingMemories, record)
	//		duplicates[record.ID] = true
	//	}
	//}
	//if len(dueMemories) > 0 {
	//	// 处理待复习记忆
	//	for _, record := range dueMemories {
	//		if _, ok := duplicates[record.ID]; !ok {
	//			waitingMemories = append(waitingMemories, record)
	//			duplicates[record.ID] = true
	//		}
	//	}
	//}
	err := RecordReview(records)
	if err != nil {
		return err
	}
	return nil
}

func (c RetrieveLongTermMemoryChain) GetMemory() schema.Memory {
	return memory.NewSimple()
}

func (c RetrieveLongTermMemoryChain) GetInputKeys() []string {
	return []string{}
}

func (c RetrieveLongTermMemoryChain) GetOutputKeys() []string {
	return []string{}
}
