package ltm

import (
	"context"
	"cosy/chat/chains/common"
	"cosy/chat/service"
	"cosy/config"
	"cosy/definition"
	"cosy/experiment"
	"cosy/log"
	"cosy/memory/storage"
	"cosy/memory/util"
	"cosy/prompt"
	"cosy/sls"
	"cosy/tokenizer"
	"encoding/json"
	"errors"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"

	agentDefinition "code.alibaba-inc.com/cosy/lingma-agent-graph/definition"

	"github.com/tmc/langchaingo/callbacks"
	"github.com/tmc/langchaingo/chains"
	langchainMemory "github.com/tmc/langchaingo/memory"
	"github.com/tmc/langchaingo/schema"
)

var (
	ignoreQuestions = map[string]bool{
		"/comment":  true,
		"/unittest": true,
		"/explain":  true,
		"/optimize": true,
	}
)

// ExtractLongTermMemoryChain 提取记忆链
type ExtractLongTermMemoryChain struct {
}

func (c ExtractLongTermMemoryChain) Call(ctx context.Context, inputs map[string]any, options ...chains.ChainCallOption) (map[string]any, error) {
	if !IsEnableAutoMemory(inputs) {
		return inputs, nil
	}
	if !experiment.ConfigService.GetBoolConfigWithEnv(definition.ExperimentMemoryAutoExtractEnable, experiment.ConfigScopeClient, true) {
		return inputs, nil
	}
	requestId := inputs[common.KeyRequestId].(string)
	sessionId := inputs[common.KeySessionId].(string)
	if _, ok := StopRequestCache.Get(requestId); ok {
		log.Debugf("ignore extract long term memory chain caused of stop requestId=%s", requestId)
		return inputs, nil
	}
	startTime := time.Now()
	log.Debugf("extract long term memory chain called. requestId=%s", requestId)
	messages, err := c.buildMemoryRequest(ctx, inputs)
	if err != nil {
		log.Debugf("extract long term memory build request error, reason=%v, requestId=%s", err, requestId)
		return inputs, nil
	}
	outputResp, err := util.InvokeMemoryModel(ctx, inputs, messages, 120*time.Second)
	if err != nil {
		log.Debugf("extract long term memory error, reason=%v, requestId=%s", err, requestId)
		return inputs, err
	}
	outputText := outputResp.Text
	items := ParseMemoryOutputText(outputText)
	log.Debugf("[memory]-[ltm] memory extract requestId:%s outputText:%s cost: %v", outputResp.RequestId, outputText, time.Since(startTime))
	if len(items) == 0 {
		return inputs, nil
	}

	// 将记忆转换为记录
	records := ConvertMemoryRecord(ctx, inputs, items)

	// 2. 保存到数据库
	if len(records) > 0 {
		go func(recs []storage.MemoryRecord) {
			// 直接调用数据库操作，避免循环依赖
			err := SaveMemoryRecordsToDB(recs)
			if err != nil {
				log.Errorf("[memory]-[ltm] save memory failed: %v requestId: %s", err, requestId)
			} else {
				log.Debugf("[memory]-[ltm] successfully saved %d memory records requestId: %s", len(recs), requestId)
			}
		}(records)
		// 统计每个分类的记忆数量
		categoryMap := countGroupRecordsByCategory(records)
		categoryJson := ""
		categoryJsonByte, err := json.Marshal(categoryMap)
		if err == nil {
			categoryJson = string(categoryJsonByte)
		}
		eventData := map[string]string{
			"request_id":   requestId,
			"sessionId":    sessionId,
			"memory_count": strconv.Itoa(len(records)),
			"category":     categoryJson,
			"cost":         strconv.Itoa(int(time.Since(startTime).Milliseconds())),
		}
		go sls.Report(sls.EventTypeChatAgentMemoryExtract, requestId, eventData)
	}
	inputs[common.KeyNewLongTermMemories] = records
	return inputs, nil
}

// buildMemoryRequest 构建请求
func (c ExtractLongTermMemoryChain) buildMemoryRequest(ctx context.Context, inputs map[string]any) ([]*agentDefinition.Message, error) {
	if askInfo, err := c.buildMemoryRequestByMessages(ctx, inputs); err == nil {
		return askInfo, nil
	}
	requestId := inputs[common.KeyRequestId].(string)
	sessionId := inputs[common.KeySessionId].(string)
	//会话历史
	validRecords, _ := service.SessionServiceManager.GetSessionRecordsForHistoryBuild(sessionId)
	if validRecords == nil || len(validRecords) <= 0 {
		log.Warnf("extract long term memory without history records. sessionId=%s", sessionId)
		return nil, errors.New("extract long term memory without history records")
	}
	historyContent := c.buildHistory(validRecords)
	// TODO buildExperience先失去作用，后面用于处理短期记忆升级为长期记忆
	exprContent := c.buildExperience(inputs)
	currentRecord := validRecords[len(validRecords)-1]
	if ignoreQuestions[strings.TrimSpace(currentRecord.Question)] {
		return nil, errors.New("ignore question")
	}

	promptInput := prompt.LongTermMemoryExtractPromptInput{
		BaseInput: prompt.BaseInput{
			RequestId: requestId,
			SessionId: sessionId,
		},
		CurrentRecord: definition.ChatRecord{
			Question: currentRecord.Question,
			Answer:   currentRecord.Answer,
		},
		HistoryItems:    historyContent,
		ExperienceItems: exprContent,
	}
	systemPrompt, err := prompt.Engine.RenderExtractMemorySystemPrompt(promptInput)
	if err != nil {
		return nil, err
	}
	userPrompt, err := prompt.Engine.RenderExtractMemoryUserPrompt(promptInput)
	if err != nil {
		return nil, err
	}
	return util.BuildSystemUserMessage(systemPrompt, userPrompt), nil
}

// buildMemoryRequestByMessages 根据消息构建请求
func (c ExtractLongTermMemoryChain) buildMemoryRequestByMessages(ctx context.Context, inputs map[string]any) ([]*agentDefinition.Message, error) {
	if !experiment.ConfigService.GetBoolConfigWithEnv(definition.ExperimentKeyMemoryExtractMessageEnable, experiment.ConfigScopeClient, true) {
		return nil, errors.New("memory extract message disabled")
	}
	requestId := inputs[common.KeyRequestId].(string)
	messages, ok := inputs[common.KeyAgentMessages].([]definition.ChatMessage)
	if !ok {
		var err error
		messages, err = service.SessionServiceManager.GetChatMessageByRequest(requestId)
		if err != nil {
			return nil, err
		}
		// 判断是否包含tool call
		existToolCall := false
		for _, message := range messages {
			if message.Role == "tool" {
				existToolCall = true
				break
			}
		}
		// 只有存在工具调用时，才基于消息历史提取信息
		if !existToolCall {
			return nil, errors.New("no tool call in messages")
		}
	}
	flatMessage := util.FlattenConversation(messages)
	chatTokenLimit := config.GlobalTruncateConfig.GetChunkLimit(config.TruncateKeyChat)
	tokenCount, _ := tokenizer.CalQwenTokenCount(flatMessage)
	if tokenCount > chatTokenLimit {
		return nil, errors.New("message too long")
	}
	historyItems := c.buildHistoryForMessage(inputs)
	promptInput := prompt.LongTermMemoryExtractPromptInput{
		BaseInput: prompt.BaseInput{
			RequestId: requestId,
		},
		CurrentMessages:  flatMessage,
		HistoryItems:     historyItems,
		ExistMemoryItems: c.buildExistMemory(inputs),
	}
	systemPrompt, err := prompt.Engine.RenderExtractMemoryByMessageSystemPrompt(promptInput)
	if err != nil {
		return nil, err
	}
	userPrompt, err := prompt.Engine.RenderExtractMemoryByMessageUserPrompt(promptInput)
	if err != nil {
		return nil, err
	}
	return util.BuildSystemUserMessage(systemPrompt, userPrompt), nil
}

// buildExperience 获取对话短期记忆
func (c ExtractLongTermMemoryChain) buildExperience(inputs map[string]any) string {
	exprResult := "无"
	//requestId := inputs[common.KeyRequestId].(string)
	//memories, found := stm.GlobalDialogMemoryService.GetMemory(requestId)
	//if !found || memories.Size() == 0 {
	//	return exprResult
	//}
	//dialogMemories := memories.GetAllMemory()
	//exprItems := make([]string, 0)
	//for _, m := range dialogMemories {
	//	sb := strings.Builder{}
	//	sb.WriteString(fmt.Sprintf("<experience>\ntitle: %s\nsource: %s\nscope: %s\nkeywords: %s\ncontent: %s\n</experience>",
	//		m.Title, m.Source, m.Scope, m.Keywords, m.Content))
	//	content := sb.String()
	//	exprItems = append(exprItems, content)
	//}
	return exprResult
}

// buildExistMemory 获取对话短期记忆
func (c ExtractLongTermMemoryChain) buildExistMemory(inputs map[string]any) string {
	exprResult := "无"
	retrieveMemoryRecords, found := inputs[common.KeyLongTermMemories].([]storage.MemoryRecord)
	if !found || len(retrieveMemoryRecords) == 0 {
		return exprResult
	}
	exprItems := make([]string, 0)
	for _, m := range retrieveMemoryRecords {
		if m.Source == definition.MemoryInitSource {
			continue
		}
		category := util.ConvertMemoryCategory(m.Category)
		sb := strings.Builder{}
		sb.WriteString(fmt.Sprintf("<content>\ntitle: %s\nsource: %s\nscope: %s\nkeywords: %s\ncategory: %s\ncontent: %s\n</content>",
			m.Title, m.Source, m.Scope, m.Keywords, category, m.Content))
		content := sb.String()
		exprItems = append(exprItems, content)
	}
	exprResult = "<memories>\n" + strings.Join(exprItems, "\n") + "\n</memories>"
	return exprResult
}

// buildHistory 构建会话历史
func (c ExtractLongTermMemoryChain) buildHistory(validRecords []definition.ChatRecord) string {
	historyItems := make([]string, 0)
	chatTokenLimit := config.GlobalTruncateConfig.GetChunkLimit(config.TruncateKeyChatMemory)
	totalCount := 0
	for i := len(validRecords) - 2; i >= 0; i-- {
		// 优先保留问题，如果答案过长，则尝试使用摘要，如果再过长，则忽略答案
		record := validRecords[i]
		userTokenCount, _ := tokenizer.CalQwenTokenCount(record.Question)
		if userTokenCount+totalCount > chatTokenLimit {
			break
		}
		totalCount += userTokenCount
		answer := record.Answer
		answerTokenCount, _ := tokenizer.CalQwenTokenCount(answer)
		if answerTokenCount+totalCount > int(float32(chatTokenLimit)*0.6) {
			if record.Summary != "" {
				answer = record.Summary
				answerTokenCount, _ = tokenizer.CalQwenTokenCount(answer)
				if answerTokenCount+totalCount > chatTokenLimit {
					answer = "..."
					answerTokenCount = 0
				}
			}
		}
		totalCount += answerTokenCount

		sb := strings.Builder{}
		sb.WriteString(fmt.Sprintf("<history>\n%d User: %s\n%d Assistant: %s\n</history>",
			i*2+1, strings.TrimLeft(record.Question, " "), i*2+2, strings.TrimLeft(record.Answer, " ")))
		content := sb.String()

		historyItems = append(historyItems, content)
	}
	sort.Slice(historyItems, func(i, j int) bool {
		return i > j
	})
	historyContent := "无"
	if len(historyItems) > 0 {
		historyContent = strings.Join(historyItems, "\n")
	}
	return historyContent
}

// buildHistoryForMessage 构建会话历史
func (c ExtractLongTermMemoryChain) buildHistoryForMessage(inputs map[string]any) string {
	historyContent := "无"
	if service.SessionServiceManager == nil {
		return historyContent
	}
	historyItems := make([]string, 0)
	sessionId := inputs[common.KeySessionId].(string)
	validRecords, _ := service.SessionServiceManager.GetSessionRecordsForHistoryBuild(sessionId)
	if len(validRecords) <= 1 {
		return historyContent
	}
	chatTokenLimit := config.GlobalTruncateConfig.GetChunkLimit(config.TruncateKeyChatMemory)
	totalCount := 0
	for i := len(validRecords) - 2; i >= 0; i-- {
		// 优先保留问题，如果答案过长，则尝试使用摘要，如果再过长，则忽略答案
		record := validRecords[i]
		userTokenCount, _ := tokenizer.CalQwenTokenCount(record.Question)
		if userTokenCount+totalCount > chatTokenLimit {
			break
		}
		totalCount += userTokenCount

		sb := strings.Builder{}
		sb.WriteString(fmt.Sprintf("[user] %s", strings.TrimLeft(record.Question, " ")))
		content := sb.String()

		historyItems = append(historyItems, content)
	}
	sort.Slice(historyItems, func(i, j int) bool {
		return i > j
	})
	if len(historyItems) > 0 {
		historyContent = strings.Join(historyItems, "\n")
	}
	return historyContent
}

func (c ExtractLongTermMemoryChain) GetMemory() schema.Memory {
	return langchainMemory.NewSimple()
}

func (c ExtractLongTermMemoryChain) GetInputKeys() []string {
	return []string{}
}

func (c ExtractLongTermMemoryChain) GetOutputKeys() []string {
	return []string{}
}

func (c ExtractLongTermMemoryChain) GetCallbackHandler() callbacks.Handler { //nolint:ireturn
	return nil
}
