package ltm

import (
	"context"
	chainUtil "cosy/chat/chains/chain"
	"cosy/chat/chains/common"
	"cosy/chat/service"
	"cosy/definition"
	"cosy/experiment"
	"cosy/log"
	"cosy/memory/storage"
	"cosy/memory/util"
	"cosy/prompt"
	"cosy/tokenizer"
	cosyUtil "cosy/util"
	"fmt"
	"regexp"
	"strings"
	"sync"
	"time"

	agentDefinition "code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"github.com/tmc/langchaingo/chains"
	"github.com/tmc/langchaingo/memory"
	"github.com/tmc/langchaingo/schema"
)

const (
	// MaxQueryTokenCount 最大查询token数
	MaxQueryTokenCount = 30
)

var (
	// codeBlockRegex 匹配代码块
	codeBlockRegex = regexp.MustCompile("```\\w*\\n([\\s\\S]*?)```")
)

// MemoryRefineQueryChain 重构查询
type MemoryRefineQueryChain struct {
}

func NewMemoryRefineQueryChain() *MemoryRefineQueryChain {
	return &MemoryRefineQueryChain{}
}

func (c MemoryRefineQueryChain) Call(ctx context.Context, inputs map[string]any, options ...chains.ChainCallOption) (map[string]any, error) {
	if !experiment.ConfigService.GetBoolConfigWithEnv(definition.ExperimentMemoryFetchEnable, experiment.ConfigScopeClient, true) {
		return inputs, nil
	}
	if !experiment.ConfigService.GetBoolConfigWithEnv(definition.ExperimentMemoryChatEditEnable, experiment.ConfigScopeClient, true) {
		if chainUtil.IsChatOrEditAgent(inputs) {
			return inputs, nil
		}
	}
	requestId := inputs[common.KeyRequestId].(string)
	sessionId := inputs[common.KeySessionId].(string)
	validRecords, _ := service.SessionServiceManager.GetSessionRecordsForHistoryBuild(sessionId)
	// 这个地方获取到的validRecords是没有当前问题的
	allowRefineQuery, err := c.checkAllowRefineQuery(ctx, inputs, validRecords)
	if err != nil {
		log.Infof("check allow refine query error, reason=%v, requestId=%s", err, requestId)
		return inputs, nil
	}
	if !allowRefineQuery {
		return inputs, nil
	}
	if newQuery, ok := c.buildRefineByRule(inputs, validRecords); ok {
		inputs[common.KeyRefinedGenernalQuery] = newQuery
		return inputs, nil
	}
	startTime := time.Now()
	messages, err := c.buildRefineQueryRequest(requestId, inputs, validRecords)
	if err != nil {
		log.Debugf("build refine query request error, reason=%v, requestId=%s", err, requestId)
		return inputs, nil
	}
	mutex := sync.Mutex{}
	var newQuery string
	totalCost, err := cosyUtil.GoWithTimeout(func() {
		outputResp, err := util.InvokeMemoryModelWithTask(ctx, inputs, messages, definition.AgentTaskQuestionRefine, 2000*time.Millisecond)
		if err != nil {
			log.Debugf("refine memory error, reason=%v, requestId=%s", err, requestId)
			return
		}
		outputText := outputResp.Text
		mutex.Lock()
		defer mutex.Unlock()
		newQuery = c.parseOutput(outputText)
		log.Debugf("refine query result: %s, model requestId: %s time cost: %s", newQuery, outputResp.RequestId, time.Since(startTime))
	}, 1000*time.Millisecond)
	if err != nil {
		log.Debugf("refine query timeout, requestId=%s cost=%v", requestId, totalCost)
	}
	mutex.Lock()
	defer mutex.Unlock()
	inputs[common.KeyRefinedGenernalQuery] = newQuery
	return inputs, nil
}

// buildRefineByRule 根据规则重构查询
func (c MemoryRefineQueryChain) buildRefineByRule(inputs map[string]any, records []definition.ChatRecord) (string, bool) {
	if len(records) == 0 {
		return "", false
	}
	rawInputParams := inputs[common.KeyChatAskParams].(*definition.AskParams)
	userQuery := chainUtil.GetUserInputQueryWithoutCode(rawInputParams.ChatContext)
	userQuery = strings.ToLower(userQuery)
	if userQuery == "continue" || userQuery == "继续" {
		lastRecord := records[len(records)-1]
		return lastRecord.Question, true
	}
	return "", false
}

// checkAllowRefineQuery 检查是否允许重构查询
func (c MemoryRefineQueryChain) checkAllowRefineQuery(ctx context.Context, inputs map[string]any, validRecords []definition.ChatRecord) (bool, error) {
	if !experiment.ConfigService.GetBoolConfigWithEnv(definition.ExperimentKeyMemoryRefineQueryEnable, experiment.ConfigScopeClient, true) {
		// 是否启用重构查询的配置项
		return false, nil
	}
	requestId := inputs[common.KeyRequestId].(string)
	// 这个地方获取到的validRecords是没有当前问题的
	if len(validRecords) == 0 {
		// 如果会话历史为空，则不进行重构查询
		return false, fmt.Errorf("history records is %d, too short, ignore refine query", len(validRecords))
	}
	rawInputParams := inputs[common.KeyChatAskParams].(*definition.AskParams)
	userQuery := chainUtil.GetUserInputQueryWithoutCode(rawInputParams.ChatContext)
	// 去除代码块
	userQuery = codeBlockRegex.ReplaceAllString(userQuery, "")
	userQuery = strings.Trim(userQuery, " \r\n\t")
	if userQuery == "" {
		// 如果用户输入为空，则不进行重构查询
		return false, fmt.Errorf("user query is empty, ignore refine query")
	}
	queryLines := strings.Split(userQuery, "\n")
	if len(queryLines) > 1 {
		// 如果用户输入不是单行，则不进行重构查询
		return false, fmt.Errorf("user query is not a single line, ignore refine query")
	}
	queryTokenCount, _ := tokenizer.CalQwenTokenCount(userQuery)
	if queryTokenCount > MaxQueryTokenCount {
		// 如果用户输入的token数超过最大限制，则不进行重构查询
		return false, fmt.Errorf("user query is too long, ignore refine query")
	}
	workspacePath := util.GetWorkspacePath(ctx, inputs)
	validRecordCount, err := storage.CountRecord(workspacePath)
	if err != nil {
		log.Debugf("count valid record error, reason=%v, requestId=%s", err, requestId)
		return false, fmt.Errorf("count valid record error, ignore refine query")
	}
	if validRecordCount == 0 {
		return false, fmt.Errorf("valid record count is 0, ignore refine query")
	}
	return true, nil
}

// buildRefineQueryRequest 构建请求
func (r MemoryRefineQueryChain) buildRefineQueryRequest(requestId string, inputs map[string]any, validRecords []definition.ChatRecord) ([]*agentDefinition.Message, error) {
	systemPrompt, err := prompt.Engine.RenderRefineQuerySystemPrompt(prompt.BaseInput{RequestId: requestId})
	if err != nil {
		return nil, err
	}
	userPrompt, err := r.renderUserPrompt(inputs, validRecords)
	if err != nil {
		return nil, err
	}
	return util.BuildSystemUserMessage(systemPrompt, userPrompt), nil
}

// renderUserPrompt 渲染用户输入
// 历史对话记录:
// <history>
// 增加商品品牌目录功能
// </history>
// <history>
// 缺少mapper代码的实现
// </history>
// 当前问题: 增加单元测试
func (r MemoryRefineQueryChain) renderUserPrompt(inputs map[string]any, validRecords []definition.ChatRecord) (string, error) {
	rawInputParams := inputs[common.KeyChatAskParams].(*definition.AskParams)
	userQuery := chainUtil.GetUserInputQueryWithoutCode(rawInputParams.ChatContext)
	userQuery = codeBlockRegex.ReplaceAllString(userQuery, "")
	//会话历史
	historyQuestions := make([]string, 0)
	for _, record := range validRecords {
		historyQuestions = append(historyQuestions, fmt.Sprintf("<history>\n%s\n</history>", record.Question))
	}
	sb := strings.Builder{}
	if len(historyQuestions) > 0 {
		sb.WriteString("历史对话记录:\n")
		sb.WriteString(strings.Join(historyQuestions, "\n"))
	} else {
		sb.WriteString("历史对话记录: 无\n")
	}
	sb.WriteString(fmt.Sprintf("当前提问: %s\n", userQuery))
	return sb.String(), nil
}

// parseOutput 解析输出
func (r MemoryRefineQueryChain) parseOutput(outputText string) string {
	return outputText
}

func (c MemoryRefineQueryChain) GetMemory() schema.Memory {
	return memory.NewSimple()
}

func (c MemoryRefineQueryChain) GetInputKeys() []string {
	return []string{}
}

func (c MemoryRefineQueryChain) GetOutputKeys() []string {
	return []string{}
}
