package rerank

import (
	"context"
	chainUtil "cosy/chat/chains/chain"
	"cosy/chat/chains/common"
	"cosy/components"
	"cosy/definition"
	"cosy/experiment"
	"cosy/memory/storage"
	"errors"
	"fmt"
	"sort"
	"strings"
)

// ApiBasedReranker 基于重排API的重排
type ApiBasedReranker struct {
	reranker *components.LingmaReranker
}

func NewApiBasedReranker() *ApiBasedReranker {
	return &ApiBasedReranker{
		reranker: components.NewLingmaReranker(),
	}
}

func (r *ApiBasedReranker) Rerank(ctx context.Context, inputs map[string]any, records []storage.MemoryRecord) MemoryRerankResult {
	rawInputParams := inputs[common.KeyChatAskParams].(*definition.AskParams)
	userQuery := chainUtil.GetUserInputQuery(rawInputParams.ChatContext)
	documents := make([]string, 0, len(records))
	// 优先使用用户偏好的记录
	userPreferredRecords := make([]storage.MemoryRecord, 0)
	for _, record := range records {
		if record.Category == definition.MemoryCategoryUserPrefer && record.Scope == definition.MemoryGlobalScope {
			userPreferredRecords = append(userPreferredRecords, record)
			continue
		}
		sb := strings.Builder{}
		sb.WriteString(fmt.Sprintf("title: %s\n", record.Title))
		sb.WriteString(fmt.Sprintf("keywords: %s\n", record.Keywords))
		sb.WriteString(fmt.Sprintf("content: %s", record.Content))
		content := sb.String()
		documents = append(documents, content)
	}
	if len(documents) == 0 {
		if len(userPreferredRecords) > 0 {
			return MemoryRerankResult{
				Name:     ApiBasedRerankerName,
				Records:  userPreferredRecords,
				Finished: true,
			}
		} else {
			return MemoryRerankResult{
				Name:  ApiBasedRerankerName,
				Error: errors.New("no memory to rerank"),
			}
		}
	}
	rerankResponse, err := r.reranker.RerankDocuments(context.Background(), userQuery, documents, 10)
	if err != nil {
		return MemoryRerankResult{
			Name:  ApiBasedRerankerName,
			Error: err,
		}
	}
	threshold := experiment.ConfigService.GetDoubleConfigWithEnv(definition.ExperimentKeyRerankApiThreshold, experiment.ConfigScopeClient, 0.1)
	results := rerankResponse.Output.Results
	sort.Slice(results, func(i, j int) bool {
		return results[i].RelevanceScore > results[j].RelevanceScore
	})
	newRecords := make([]storage.MemoryRecord, 0, len(results))
	newRecords = append(newRecords, userPreferredRecords...)
	for _, result := range results {
		if result.Index >= len(records) || result.Index < 0 {
			continue
		}
		if result.RelevanceScore < threshold {
			continue
		}
		newRecords = append(newRecords, records[result.Index])
	}
	return MemoryRerankResult{Name: ApiBasedRerankerName, Records: newRecords, Finished: true}
}
