package util

import (
	"cosy/definition"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"testing"
)

func TestProcessMessage(t *testing.T) {
	tests := []struct {
		name         string
		input        definition.ChatMessage
		lastToolName string
		expected     string
	}{
		{
			name: "User message with content",
			input: definition.ChatMessage{
				Role:    "user",
				Content: `{"role": "user", "content": "Hello, this is a test message."}`,
			},
			lastToolName: "",
			expected:     "[user]: Hello, this is a test message.",
		},
		{
			name: "System message should be ignored",
			input: definition.ChatMessage{
				Role:    "system",
				Content: `{"role": "system", "content": "This is a system message."}`,
			},
			lastToolName: "",
			expected:     "",
		},
		{
			name: "Tool message with JSON content",
			input: definition.ChatMessage{
				Role:    "tool",
				Content: `{"content": "This is a tool message."}`,
			},
			lastToolName: "read_file",
			expected:     "[tool]: This is a tool message.",
		},
		{
			name: "Assistant message with tool calls",
			input: definition.ChatMessage{
				Role:    "assistant",
				Content: `{"role":"assistant","content":"当然可以！以后的回答都会更加活泼俏皮，让我们的交流充满乐趣和活力！","tool_calls":[{"id":"tool","type":"function","function":{"name":"create_memory","arguments":"{\"action\":\"create\",\"title\":\"用户沟通风格偏好-俏皮\",\"source\":\"user\",\"scope\":\"global\",\"content\":\"用户希望回答风格俏皮有趣，应当使用更加活泼、幽默的语言，可以适当使用表情符号和轻松的表达方式，让交流更加生动有趣。\",\"keywords\":\"沟通风格,俏皮,幽默,活泼,表情符号,用户偏好\"}"}}],"response_meta":{"id":"","finish_reason":"tool_calls","usage":{"prompt_tokens":6774,"completion_tokens":327,"total_tokens":7101}}}`,
			},
			lastToolName: "",
			expected:     "[assistant]: 当然可以！以后的回答都会更加活泼俏皮，让我们的交流充满乐趣和活力！\ntool call name: create_memory",
		},
		{
			name: "Assistant message with tool calls",
			input: definition.ChatMessage{
				Role:    "assistant",
				Content: `{"role":"assistant","content":"","tool_calls":[{"id":"toolu_vrtx_016KPWBRguUYJvQHMajxkVmH","type":"function","function":{"name":"edit_file","arguments":"{\"explanation\": \"创建tree-sitter解析器包装类，负责初始化和管理不同语言的解析器\", \"file_path\": \"/Users/<USER>/Documents/codes/lab/python/data_process/tree_sitter_parser.py\", \"language\": \"python\", \"code_edit\": \"import tree_sitter\\nfrom tree_sitter import Language, Parser\\nfrom typing import Dict, Optional\\nimport os\\n\\n\\nclass TreeSitterParser:\\n    \\\"\\\"\\\"Tree-sitter解析器管理器\\\"\\\"\\\"\\n    \\n    def __init__(self):\\n        self.parsers: Dict[str, Parser] = {}\\n        self.languages: Dict[str, Language] = {}\\n        self._init_languages()\\n    \\n    def _init_languages(self):\\n        \\\"\\\"\\\"初始化支持的编程语言\\\"\\\"\\\"\\n        try:\\n            # 尝试加载预编译的语言库\\n            # 实际使用时需要先编译tree-sitter语言库\\n            self._load_language(\\\"python\\\")\\n            self._load_language(\\\"java\\\")\\n        except Exception as e:\\n            print(f\\\"Warning: Failed to load tree-sitter languages: {e}\\\")\\n            print(\\\"Please ensure tree-sitter language libraries are properly installed and compiled.\\\")\\n    \\n    def _load_language(self, language: str):\\n        \\\"\\\"\\\"加载指定语言的tree-sitter库\\\"\\\"\\\"\\n        try:\\n            # 这里需要根据实际的tree-sitter库路径进行调整\\n            # 通常需要先编译对应语言的.so文件\\n            \\n            if language == \\\"python\\\":\\n                # 假设已经编译了Python语言库\\n                # Language.build_library('build/my-languages.so', ['tree-sitter-python'])\\n                # self.languages[language] = Language('build/my-languages.so', 'python')\\n                pass\\n            elif language == \\\"java\\\":\\n                # 假设已经编译了Java语言库\\n                # Language.build_library('build/my-languages.so', ['tree-sitter-java'])\\n                # self.languages[language] = Language('build/my-languages.so', 'java')\\n                pass\\n            \\n            # 为演示目的，我们使用模拟的方式\\n            # 实际使用时需要替换为真实的Language对象\\n            self.languages[language] = None\\n            \\n            parser = Parser()\\n            if self.languages[language]:\\n                parser.set_language(self.languages[language])\\n            self.parsers[language] = parser\\n            \\n        except Exception as e:\\n            print(f\\\"Failed to load {language} language: {e}\\\")\\n    \\n    def parse(self, source_code: str, language: str) -\u003e Optional[tree_sitter.Node]:\\n        \\\"\\\"\\\"解析源代码为tree-sitter AST\\\"\\\"\\\"\\n        language = language.lower()\\n        \\n        if language not in self.parsers:\\n            raise ValueError(f\\\"Unsupported language: {language}\\\")\\n        \\n        parser = self.parsers[language]\\n        \\n        # 将源代码转换为字节码\\n        source_bytes = source_code.encode('utf-8')\\n        \\n        try:\\n            tree = parser.parse(source_bytes)\\n            return tree.root_node\\n        except Exception as e:\\n            print(f\\\"Failed to parse {language} code: {e}\\\")\\n            return None\\n    \\n    def is_language_supported(self, language: str) -\u003e bool:\\n        \\\"\\\"\\\"检查是否支持指定语言\\\"\\\"\\\"\\n        return language.lower() in self.parsers\\n    \\n    def get_supported_languages(self) -\u003e list:\\n        \\\"\\\"\\\"获取支持的语言列表\\\"\\\"\\\"\\n        return list(self.parsers.keys())\"}"},"extra":{"edit_file_file_id":"52cfeeb9-116a-4ba6-859a-70d06497b4ce"}}],"response_meta":{"id":"","finish_reason":"tool_calls","usage":{"prompt_tokens":13077,"completion_tokens":1008,"total_tokens":1008,"completion_tokens_details":{"reasoning_tokens":0},"prompt_tokens_details":{"cached_tokens":0}}},"extra":{"callServerRequestId":"8be06779-534b-4f59-b3ba-2185764e3902"}}`,
			},
			lastToolName: "",
			expected:     "[assistant]:\n创建tree-sitter解析器包装类，负责初始化和管理不同语言的解析器",
		},
		{
			name: "Assistant message with tool calls",
			input: definition.ChatMessage{
				Role:    "tool",
				Content: `{"role":"tool","content":"create memory success","name":"create_memory","tool_call_id":"toolu_vrtx_01NpeP5mwHQpz6iBGaFYEoJQ","response_meta":{"id":"","usage":{"prompt_tokens":0,"completion_tokens":0,"total_tokens":0}}}`,
			},
			lastToolName: "create_memory",
			expected:     "[tool]: create memory success",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := &MessageParseContext{
				LastToolName: tt.lastToolName,
			}
			result := processMessage(ctx, tt.input)
			if result != tt.expected {
				t.Errorf("expected %q, got %q", tt.expected, result)
			}
		})
	}
}

func TestFlattenConversation(t *testing.T) {
	messages := []definition.ChatMessage{
		{
			Role:    "user",
			Content: `{"role": "user", "content": "Hello, this is a test message."}`,
		},
		{
			Role:    "assistant",
			Content: `{"role": "assistant", "content": "Sure, let's process it!"}`,
		},
		{
			Role:    "tool",
			Content: `{"role": "tool", "content": "Tool execution successful."}`,
		},
	}

	expected := `[user]: Hello, this is a test message.

[assistant]: Sure, let's process it!

[tool]: Tool execution successful.`

	result := flattenConversation(messages)
	if strings.TrimSpace(result) != strings.TrimSpace(expected) {
		t.Errorf("expected %q, got %q", expected, result)
	}
}

func TestFlattenConversationFromSessions(t *testing.T) {
	files, err := os.ReadDir("./testdata")
	if err != nil {
		t.Fatalf("can't read testdata directory: %v", err)
	}
	for _, file := range files {
		if file.IsDir() {
			continue
		}
		fmt.Println("======parse:", file.Name())
		bytes, err := os.ReadFile(filepath.Join("./testdata", file.Name()))
		if err != nil {
			t.Fatalf("can't read testdata file: %v", err)
		}
		var obj map[string]any
		if err := json.Unmarshal(bytes, &obj); err != nil {
			t.Fatalf("can't unmarshal testdata file: %v", err)
		}
		messages := []definition.ChatMessage{}
		historyItems := obj["history"].([]any)
		for _, historyItem := range historyItems {
			historyItemObj := historyItem.(map[string]interface{})
			input := definition.ChatMessage{
				Role:    strings.ToLower(historyItemObj["actorType"].(string)),
				Content: historyItemObj["content"].(string),
			}
			messages = append(messages, input)
		}
		result := flattenConversation(messages)
		fmt.Println(result)
	}
}
