package storage

import (
	"context"
	"cosy/client"
	"cosy/components"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestQueryMostSimilarVectorRecords 测试向量相似度查询
// 该测试将"俏皮"向量化后，查询向量最相似的记录并打印距离信息
func TestQueryMostSimilarVectorRecords(t *testing.T) {
	// 跳过测试，因为需要真实数据库环境
	// 如果需要在真实环境中运行，可以注释掉下面这行
	// t.Ski<PERSON>("需要真实数据库环境，默认跳过")

	// 1. 初始化内存客户端
	memoryClient, err := GetMemoryClient()
	if err != nil {
		t.Fatalf("获取内存客户端失败: %v", err)
	}

	// 2. 远程调用可能会出现nil指针错误，先跳过embedder步骤
	// embedder := components.NewLingmaEmbedder()
	// queryEmbeddings, err := embedder.CreateEmbedding(context.Background(), []string{queryText})

	// 3. 使用预设向量代替API调用，避免nil指针错误
	queryText := "注释生成"

	client.InitClients()
	embedder := components.NewLingmaEmbedder()
	embedding, err := embedder.CreateEmbedding(context.Background(), []string{queryText}, components.TextTypeQuery)

	if err != nil {
		t.Fatalf("获取embedder失败: %v", err)
	}
	// 4. 构建查询条件，进行向量相似度查询
	queryCondition := MemoryQueryCondition{
		QueryEmbedding: embedding[0], // 使用预设向量作为查询条件
		TopK:           10,           // 获取相似度最高的10条记录
		// 不指定Scope和ScopeId，查询所有范围
	}

	// 5. 执行查询
	result, err := memoryClient.Query(queryCondition)
	if err != nil {
		t.Fatalf("查询失败: %v", err)
	}

	// 6. 打印查询结果
	fmt.Printf("\n===== 与'%s'最相似的记录 =====\n", queryText)
	if len(result.Records) == 0 {
		fmt.Println("没有找到相似记录")
	} else {
		fmt.Printf("找到 %d 条相似记录:\n", len(result.Records))
		for i, record := range result.Records {
			fmt.Printf("%d. [距离: %.6f] [ID: %s] [标题: %s]\n", i+1, record.Distance, record.ID, record.Title)
			fmt.Printf("   内容: %s\n", truncateString(record.Content, 1000))
			fmt.Println("   -----------------------------")
		}
	}

	// 7. 进行断言，确保测试正常运行
	if len(result.Records) > 0 {
		// 检查查询结果是否包含Distance字段
		for _, record := range result.Records {
			assert.GreaterOrEqual(t, record.Distance, 0.0, "向量距离应该大于等于0")
		}

		// 检查结果排序 - 在SQLite-Vec中，距离值越大表示越不相似
		// 因此排序应该是升序而非降序（值越来越大）
		for i := 1; i < len(result.Records); i++ {
			assert.GreaterOrEqual(t,
				result.Records[i].Distance,
				result.Records[i-1].Distance,
				"记录应该按距离升序排列（值越大越不相似）")
		}
	}
}

// 辅助函数：截断字符串，避免过长内容打印
func truncateString(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}
	return s[:maxLen] + "..."
}

// TestVectorSimilarityWithDatabaseJoin 测试向量相似度查询，同时展示表连接查询
func TestVectorSimilarityWithDatabaseJoin(t *testing.T) {
	// 跳过测试，因为需要真实数据库环境
	// 如果需要在真实环境中运行，可以注释掉下面这行
	// t.Skip("需要真实数据库环境，默认跳过")

	// 1. 初始化内存客户端
	client, err := GetMemoryClient()
	if err != nil {
		t.Fatalf("获取内存客户端失败: %v", err)
	}

	sqliteClient, ok := client.(*MemorySqliteVecClient)
	if !ok {
		t.Fatalf("客户端类型不是MemorySqliteVecClient")
	}

	// 2. 跳过embedder，直接使用预设向量
	queryText := "俏皮"

	// 3. 预设向量（这里使用512维的随机向量，实际应根据你的模型调整）
	queryEmbedding := make([]float32, 512)
	for i := 0; i < 512; i++ {
		queryEmbedding[i] = float32(i%100) * 0.01
	}

	// 4. 直接使用SQL查询，演示表连接
	// 将向量序列化为二进制数据
	vectorBytes, err := serializeFloat32(queryEmbedding)
	if err != nil {
		t.Fatalf("向量序列化失败: %v", err)
	}

	// 5. 执行连接查询：连接agent_memory_embedding和agent_memory表
	// 从agent_memory_embedding表获取向量相似度，从agent_memory表获取元数据
	// 使用实际的表结构和列名
	query := `
		SELECT 
			a.id, 
			a.title, 
			a.content, 
			a.keywords,
			a.scope,
			a.scope_id,
			a.session_id,
			a.gmt_create,
			a.gmt_modified,
			distance as distance
		FROM agent_memory a
		JOIN agent_memory_embedding b ON a.id = b.memory_id
		WHERE 1=1 AND k = 10 
		AND (b.memory_embedding MATCH ?)
		ORDER BY distance 
		LIMIT 10
	`

	// 执行SQL查询
	rows, err := sqliteClient.db.Query(query, vectorBytes)
	if err != nil {
		t.Fatalf("SQL查询失败: %v", err)
	}
	defer rows.Close()

	fmt.Printf("\n===== 使用JOIN查询与'%s'最相似的记录 =====\n", queryText)

	count := 0
	// 处理查询结果
	for rows.Next() {
		count++
		var (
			id, title, content, keywords, scope, scopeId, sessionId string
			gmtCreate, gmtModified                                  int
			distance                                                float64
		)

		err := rows.Scan(&id, &title, &content, &keywords, &scope, &scopeId, &sessionId, &gmtCreate, &gmtModified, &distance)
		if err != nil {
			t.Fatalf("读取查询结果失败: %v", err)
		}

		fmt.Printf("%d. [距离: %.6f] [ID: %s] [标题: %s]\n", count, distance, id, title)
		fmt.Printf("   内容: %s\n", truncateString(content, 100))
		fmt.Printf("   范围: %s, 范围ID: %s\n", scope, scopeId)
		fmt.Printf("   关键词: %s\n", keywords)
		fmt.Printf("   会话ID: %s\n", sessionId)
		fmt.Printf("   创建时间: %d, 修改时间: %d\n", gmtCreate, gmtModified)
		fmt.Println("   -----------------------------")
	}

	if count == 0 {
		fmt.Println("没有找到相似记录")
	}

	// 检查是否有错误
	if err := rows.Err(); err != nil {
		t.Fatalf("迭代查询结果时发生错误: %v", err)
	}
}
