package storage

import (
	"bytes"
	"context"
	"cosy/definition"
	"cosy/util"
	"database/sql"
	"encoding/binary"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"cosy/components"
	"cosy/log"
	"cosy/tokenizer"

	sqlitevec "github.com/asg017/sqlite-vec-go-bindings/cgo"
	_ "github.com/mattn/go-sqlite3"
)

const (
	// 数据库文件名
	MemoryDatabaseName = "agent_memory.db"

	// 数据库模式
	DatabaseModeMemory = "memory"
	DatabaseModeDisk   = "disk"

	// 删除策略
	DeleteStrategyByID                   = "id"
	DeleteStrategyBySessionID            = "session_id"
	DeleteStrategyByCategoryAndSessionID = "category_and_session_id"
	DeleteStrategyByScope                = "scope"

	// 默认查询限制
	DefaultQueryLimit = 10
)

var (
	VectorMatchError = errors.New("vector match failed")
)

// MemoryReviewItem 记忆复习记录
type MemoryReviewItem struct {
	ReviewTime     int64   `json:"review_time"`
	Freq           int     `json:"freq"`
	RetentionScore float64 `json:"retention_score""`
}

// MemoryReviewHistory 记忆复习历史记录
type MemoryReviewHistory struct {
	ReviewCount int `json:"review_count"`
	// Items: 记忆复习历史记录，只保留固定数量
	Items []MemoryReviewItem `json:"items"`
}

// MemoryRecord 记忆记录
type MemoryRecord struct {
	ID              string    `json:"id"`
	GmtCreate       int       `json:"gmt_create,omitempty"`
	GmtModified     int       `json:"gmt_modified,omitempty"`
	Scope           string    `json:"scope"`
	ScopeId         string    `json:"scope_id,omitempty"`
	Keywords        string    `json:"keywords,omitempty"`
	Title           string    `json:"title,omitempty"` // 记忆标题
	Content         string    `json:"content"`
	MemoryEmbedding []float32 `json:"memory_embedding"`
	SessionID       string    `json:"session_id,omitempty"`
	IsMerged        int       `json:"is_merged"`
	Freq            int       `json:"freq"`
	Distance        float64   `json:"distance,omitempty"`    // 查询结果中的距离
	Source          string    `json:"source,omitempty"`      // 记忆来源：user-用户要求，auto-系统自动总结
	TokenCount      int       `json:"token_count,omitempty"` // 内容的token数量
	Type            string    `json:"type,omitempty"`        // 记忆类型
	UserId          string    `json:"user_id"`
	Category        string    `json:"category,omitempty"`         // 记忆分类
	RetentionScore  float64   `json:"retention_score,omitempty"`  // 记忆保持分数
	NextReviewTime  int       `json:"next_review_time,omitempty"` // 下次复习时间
	LastReviewTime  int       `json:"last_review_time,omitempty"` // 上次复习时间
	ForgetCount     int       `json:"forget_count,omitempty"`     // 遗忘次数
	Status          string    `json:"status,omitempty"`           // 记忆状态
	ReviewHistory   string    `json:"review_history,omitempty"`   // 复习历史
	QualityScore    float64   `json:"quality_score,omitempty"`    // 记忆质量分数
}

func (r *MemoryRecord) GetReviewHistory() MemoryReviewHistory {
	var reviewHistory MemoryReviewHistory
	if r.ReviewHistory != "" {
		err := json.Unmarshal([]byte(r.ReviewHistory), &reviewHistory)
		if err != nil {
			log.Errorf("[memory]-[forget] failed to unmarshal review history: %s", err)
			reviewHistory = MemoryReviewHistory{
				ReviewCount: 0,
			}
		}
	}
	return reviewHistory
}

// MemoryQueryCondition 查询条件
type MemoryQueryCondition struct {
	QueryEmbedding            []float32 // 查询向量
	TopK                      int       // 最多召回数量限制
	ScoreThreshold            float64   // 召回的相似度阈值
	Scopes                    []string  // 记忆范围
	Query                     string    // 查询文本，用于匹配keywords字段
	SessionID                 string    // 会话ID
	IsMerged                  *int      // 是否已合并
	ScopeId                   string    // 记忆范围ID
	Page                      int       // 页码，从1开始
	PageSize                  int       // 每页大小
	UseGlobalOrWorkspaceScope bool      // 是否使用全局或工作区范围的查询逻辑，即 scope='global' OR (scope='workspace' AND scope_id='<workspaceId>')
	Type                      string    // 记忆类型
	UserId                    string    // 用户ID
	Source                    []string  // 记忆来源
	Status                    string    // 记忆状态
	Categories                []string  // 记忆分类
}

// MemoryQueryResult 查询结果
type MemoryQueryResult struct {
	Records []MemoryRecord
}

// MemorySqliteVecEngine 接口定义
type MemorySqliteVecEngine interface {
	// 基础操作
	Available() (string, bool)
	Close() error

	// 查询操作
	Query(condition MemoryQueryCondition) (MemoryQueryResult, error)

	// 分页查询操作
	QueryWithPagination(condition MemoryQueryCondition) (MemoryQueryResult, error)

	// 统计记录数量
	CountMemoryRecords(condition MemoryQueryCondition) (int, error)

	// 更新操作
	Update(record MemoryRecord) error

	// 更新操作
	// forceUpdateFields 为true时，强制更新指定字段，否则只更新非空字段
	UpdatePlus(record MemoryRecord, forceUpdateFields map[string]bool) error

	// 插入/更新操作
	Save(record MemoryRecord) error

	// 批量操作
	BatchInsert(records []MemoryRecord) error

	// 删除操作
	Delete(record MemoryRecord, strategy string) error
	BatchDelete(records []MemoryRecord, strategy string) error

	// 统计操作
	GetRecordCount() int

	// 向量操作
	DoEmbed(contents []string) ([][]float32, error)

	// GetValidRecordCountForScopeId 获取有效记忆记录数量，查询指定scopeId或global的记忆记录
	GetValidRecordCountForScopeId(scopeId string) int
}

// MemorySqliteVecClient SQLite向量客户端实现
type MemorySqliteVecClient struct {
	dbPath    string
	db        *sql.DB
	mtx       sync.Mutex
	closed    bool
	dbMode    string
	dbVersion string
	mutex     sync.RWMutex
}

// NewMemorySqliteVecClient 创建新的SQLite向量客户端
func NewMemorySqliteVecClient(dbVersion, dbMode, dbPath string) (*MemorySqliteVecClient, error) {
	// 先检查目录是否存在
	dbFileDir := filepath.Join(util.GetCosyHomePath(), "cache", "db")
	if !util.DirExists(dbFileDir) {
		err := os.MkdirAll(dbFileDir, 0755)
		if err != nil {
			log.Errorf("Failed to Create local db dir: %s", err.Error())
			return nil, fmt.Errorf("failed to Create local db dir: %s", err)
		}
	}
	// 连接数据库
	db, err := sql.Open("sqlite3", dbPath)
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %v", err)
	}

	// 设置连接池
	db.SetMaxOpenConns(1)
	db.SetMaxIdleConns(1)
	db.SetConnMaxLifetime(time.Hour)

	client := &MemorySqliteVecClient{
		dbPath:    dbPath,
		db:        db,
		mtx:       sync.Mutex{},
		closed:    false,
		dbMode:    dbMode,
		dbVersion: dbVersion,
	}
	client.db = db

	// 初始化数据库
	if err := client.CreateLingmaMemoryTable(); err != nil {
		db.Close()
		return nil, fmt.Errorf("failed to initialize database: %v", err)
	}

	return client, nil
}

// checkTableExists
// 检查表是否存在
func (c *MemorySqliteVecClient) checkTableExists(tableName string) (bool, error) {
	query := fmt.Sprintf("SELECT name FROM sqlite_master WHERE type='table' AND name=?")
	var name string
	err := c.db.QueryRow(query, tableName).Scan(&name)
	if err != nil {
		if err == sql.ErrNoRows {
			return false, nil // 表不存在
		}
		log.Errorf("[codebase]-[storage] check %s table exists error: %v", tableName, err)
		return false, err // 其他错误
	}
	return true, nil // 表存在
}

// CreateLingmaMemoryTable 创建记忆表
func (c *MemorySqliteVecClient) CreateLingmaMemoryTable() error {
	if exist, err := c.checkTableExists("agent_memory_embedding"); exist {
		// 表已存在
		//log.Debugf("[codebase]-[storage] data table already exists")
		return nil
	} else if err != nil {
		return err
	}
	createTableSql := `
	CREATE VIRTUAL TABLE IF NOT EXISTS agent_memory_embedding USING vec0(
		memory_id TEXT PRIMARY KEY,
		gmt_create INTEGER,
		gmt_modified INTEGER,
		memory_embedding FLOAT[512] distance_metric=cosine
	);
	`

	_, err := c.db.Exec(createTableSql)
	if err != nil {
		log.Errorf("[codebase]-[storage] create data table error: %v", err)
		return err
	}

	return nil
}

// Available 检查客户端是否可用
func (c *MemorySqliteVecClient) Available() (string, bool) {
	c.mtx.Lock()
	defer c.mtx.Unlock()

	if c.closed {
		return c.dbPath, true
	}

	// 检查数据库连接
	if err := c.db.Ping(); err != nil {
		log.Errorf("[memory]-[sqlite] database connection error: %v", err)
		return c.dbPath, true
	}

	return c.dbPath, false
}

// Close 关闭客户端
func (c *MemorySqliteVecClient) Close() error {
	c.mtx.Lock()
	defer c.mtx.Unlock()

	if c.closed {
		return nil
	}

	c.closed = true
	return c.db.Close()
}

// Query 查询记忆
func (c *MemorySqliteVecClient) Query(condition MemoryQueryCondition) (MemoryQueryResult, error) {
	c.mtx.Lock()
	defer c.mtx.Unlock()

	if c.closed {
		return MemoryQueryResult{}, errors.New("client is closed")
	}

	// 尝试使用向量查询
	// 将查询向量转换为二进制格式
	embeddingBytes, err := serializeFloat32(condition.QueryEmbedding)
	if err != nil {
		return MemoryQueryResult{}, fmt.Errorf("failed to serialize query embedding: %v", err)
	}

	// 构建SQL
	var whereClause []string
	var queryClause []string
	var args []interface{}

	// 基础条件
	whereClause = append(whereClause, "1=1")

	// 范围条件
	if len(condition.Scopes) > 0 {
		placeholders := make([]string, len(condition.Scopes))
		for i := range placeholders {
			placeholders[i] = "?"
			args = append(args, condition.Scopes[i])
		}
		whereClause = append(whereClause, fmt.Sprintf("scope IN (%s)", strings.Join(placeholders, ", ")))
	}

	// 范围ID条件
	if condition.ScopeId != "" {
		whereClause = append(whereClause, "scope_id = ?")
		args = append(args, condition.ScopeId)
	}

	// 会话ID条件
	if condition.SessionID != "" {
		whereClause = append(whereClause, "session_id = ?")
		args = append(args, condition.SessionID)
	}

	// 合并状态条件
	if condition.IsMerged != nil {
		whereClause = append(whereClause, "is_merged = ?")
		args = append(args, *condition.IsMerged)
	}

	if condition.Type != "" {
		whereClause = append(whereClause, "type = ?")
		args = append(args, condition.Type)
	}

	if len(condition.Categories) > 0 {
		placeholders := make([]string, len(condition.Categories))
		for i := range placeholders {
			placeholders[i] = "?"
			args = append(args, condition.Categories[i])
		}
		whereClause = append(whereClause, fmt.Sprintf("category IN (%s)", strings.Join(placeholders, ", ")))
	}

	if condition.UserId != "" {
		whereClause = append(whereClause, "user_id = ?")
		args = append(args, condition.UserId)
	}
	if len(condition.Source) > 0 {
		placeholders := make([]string, len(condition.Source))
		for i := range placeholders {
			placeholders[i] = "?"
			args = append(args, condition.Source[i])
		}
		whereClause = append(whereClause, fmt.Sprintf("source IN (%s)", strings.Join(placeholders, ", ")))
	}
	if condition.Status != "" {
		whereClause = append(whereClause, "status = ?")
		args = append(args, condition.Status)
	}

	// 关键词条件
	if condition.Query != "" {
		queryClause = append(queryClause, `
			EXISTS (
				SELECT 1 FROM (
					SELECT value FROM json_each('["' || REPLACE(keywords, ',', '","') || '"]')
				) AS kw
				WHERE INSTR(LOWER(?), LOWER(kw.value)) > 0
			)
		`)
		args = append(args, condition.Query)
	}

	// 向量匹配条件
	if len(condition.QueryEmbedding) > 0 {
		queryClause = append(queryClause, "memory_embedding MATCH ? ")
		args = append(args, embeddingBytes)

		// TopK参数
		if condition.TopK > 0 {
			whereClause = append(whereClause, fmt.Sprintf("k = %d ", condition.TopK))
		}
	}

	// 构建完整的SQL
	var sql string
	if len(condition.QueryEmbedding) > 0 {
		sql = fmt.Sprintf(`
			SELECT 
				a.id, a.gmt_create, a.gmt_modified, a.scope, a.scope_id, a.keywords, a.title, a.content, b.memory_embedding, a.session_id, a.is_merged, a.freq, 
				COALESCE(distance, 0) as distance, a.source, a.token_count, a.type, a.user_id, a.category, a.retention_score, a.next_review_time, a.last_review_time, a.forget_count, a.status, a.review_history, a.quality_score
			FROM agent_memory a
			JOIN agent_memory_embedding b ON a.id = b.memory_id
			WHERE %s
				AND (%s)
			ORDER BY distance 
			`, strings.Join(whereClause, " AND "), strings.Join(queryClause, " OR "))
	} else if len(queryClause) > 0 {
		sql = fmt.Sprintf(`
			SELECT 
				id, gmt_create, gmt_modified, scope, scope_id, keywords, title, content, '' as memory_embedding, session_id, is_merged, freq, 
				0 as distance, source, token_count, type, user_id, category, retention_score, next_review_time, last_review_time, forget_count, status, review_history, quality_score
			FROM agent_memory
			WHERE %s AND (%s)
			ORDER BY gmt_modified DESC
			`, strings.Join(whereClause, " AND "), strings.Join(queryClause, " OR "))
	} else {
		sql = fmt.Sprintf(`
			SELECT 
				id, gmt_create, gmt_modified, scope, scope_id, keywords, title, content, '' as memory_embedding, session_id, is_merged, freq, 
				0 as distance, source, token_count, type, user_id, category, retention_score, next_review_time, last_review_time, forget_count, status, review_history, quality_score
			FROM agent_memory
			WHERE %s
			ORDER BY gmt_modified DESC
			`, strings.Join(whereClause, " AND "))
	}
	if condition.TopK > 0 {
		sql += fmt.Sprintf(" LIMIT %d", condition.TopK)
	}

	//log.Debugf("[memory]-[sqlite] query sql: %s", sql)

	// 执行查询
	rows, err := c.db.Query(sql, args...)
	if err != nil {
		// 如果向量查询失败，尝试使用普通查询
		if strings.Contains(err.Error(), "no such function") ||
			strings.Contains(err.Error(), "no such module") ||
			strings.Contains(err.Error(), "MATCH") {
			log.Warnf("[memory]-[sqlite] vector search failed, fallback to normal query: %v", err)
			return MemoryQueryResult{}, VectorMatchError
		}
		return MemoryQueryResult{}, fmt.Errorf("query failed: %v", err)
	}
	defer rows.Close()

	// 处理结果
	var result MemoryQueryResult
	for rows.Next() {
		var record MemoryRecord
		var embeddingBytes []byte
		err := rows.Scan(
			&record.ID,
			&record.GmtCreate,
			&record.GmtModified,
			&record.Scope,
			&record.ScopeId,
			&record.Keywords,
			&record.Title,
			&record.Content,
			&embeddingBytes,
			&record.SessionID,
			&record.IsMerged,
			&record.Freq,
			&record.Distance,
			&record.Source,
			&record.TokenCount,
			&record.Type,
			&record.UserId,
			&record.Category,
			&record.RetentionScore,
			&record.NextReviewTime,
			&record.LastReviewTime,
			&record.ForgetCount,
			&record.Status,
			&record.ReviewHistory,
			&record.QualityScore,
		)
		if err != nil {
			return MemoryQueryResult{}, fmt.Errorf("scan row failed: %v", err)
		}

		// 解析向量
		record.MemoryEmbedding, err = deserializeFloat32(embeddingBytes)
		if err != nil {
			return MemoryQueryResult{}, fmt.Errorf("failed to deserialize embedding: %v", err)
		}

		result.Records = append(result.Records, record)
	}

	if err := rows.Err(); err != nil {
		return MemoryQueryResult{}, fmt.Errorf("rows iteration failed: %v", err)
	}

	return result, nil
}

// serializeFloat32 将float32切片序列化为二进制格式
func serializeFloat32(values []float32) ([]byte, error) {
	buf := new(bytes.Buffer)
	for _, v := range values {
		err := binary.Write(buf, binary.LittleEndian, v)
		if err != nil {
			return nil, err
		}
	}
	return buf.Bytes(), nil
}

// deserializeFloat32 将二进制数据反序列化为float32切片
func deserializeFloat32(data []byte) ([]float32, error) {
	if len(data) == 0 {
		return []float32{}, nil
	}

	// 确保数据长度是4的倍数（float32的字节数）
	if len(data)%4 != 0 {
		return nil, fmt.Errorf("invalid data length: %d", len(data))
	}

	count := len(data) / 4
	result := make([]float32, count)

	buf := bytes.NewReader(data)
	for i := 0; i < count; i++ {
		err := binary.Read(buf, binary.LittleEndian, &result[i])
		if err != nil {
			return nil, err
		}
	}

	return result, nil
}

func (c *MemorySqliteVecClient) Update(record MemoryRecord) error {
	return c.UpdatePlus(record, make(map[string]bool))
}

// Update 更新记忆
func (c *MemorySqliteVecClient) UpdatePlus(record MemoryRecord, forceUpdateFields map[string]bool) error {
	c.mtx.Lock()
	defer c.mtx.Unlock()

	if c.closed {
		return errors.New("client is closed")
	}

	if record.ID == "" {
		return errors.New("invalid record ID")
	}

	// 准备更新字段
	var setFields []string
	var args []interface{}

	// 更新修改时间
	now := int(time.Now().Unix())
	setFields = append(setFields, "gmt_modified = ?")
	args = append(args, now)

	// 更新范围
	if record.Scope != "" || forceUpdateFields["scope"] {
		setFields = append(setFields, "scope = ?")
		args = append(args, record.Scope)
	}

	// 更新关键词
	if record.Keywords != "" || forceUpdateFields["keywords"] {
		setFields = append(setFields, "keywords = ?")
		args = append(args, record.Keywords)
	}

	// 更新标题
	if record.Title != "" || forceUpdateFields["title"] {
		setFields = append(setFields, "title = ?")
		args = append(args, record.Title)
	}

	// 更新内容
	if record.Content != "" || forceUpdateFields["content"] {
		setFields = append(setFields, "content = ?")
		args = append(args, record.Content)

		// 如果内容更新了，重新计算token数量
		tokenCount, err := tokenizer.CalQwenTokenCount(record.Content)
		if err != nil {
			log.Warnf("[memory]-[sqlite] calculate token count error: %v", err)
			// 计算失败不影响更新
		} else {
			setFields = append(setFields, "token_count = ?")
			args = append(args, tokenCount)
		}
	}

	// 更新会话ID
	if record.SessionID != "" || forceUpdateFields["session_id"] {
		setFields = append(setFields, "session_id = ?")
		args = append(args, record.SessionID)
	}

	if record.IsMerged >= 0 || forceUpdateFields["is_merged"] {
		// 更新合并状态
		setFields = append(setFields, "is_merged = ?")
		args = append(args, record.IsMerged)
	}

	if record.Freq >= 0 || forceUpdateFields["freq"] {
		// 更新使用热度
		setFields = append(setFields, "freq = ?")
		args = append(args, record.Freq)
	}

	// 更新来源
	if record.Source != "" || forceUpdateFields["source"] {
		setFields = append(setFields, "source = ?")
		args = append(args, record.Source)
	}

	if record.Type != "" || forceUpdateFields["type"] {
		setFields = append(setFields, "type = ?")
		args = append(args, record.Type)
	}

	if record.UserId != "" || forceUpdateFields["user_id"] {
		setFields = append(setFields, "user_id = ?")
		args = append(args, record.UserId)
	}

	// 添加新字段的更新逻辑
	if record.Category != "" || forceUpdateFields["category"] {
		setFields = append(setFields, "category = ?")
		args = append(args, record.Category)
	}

	if record.RetentionScore > 0 || forceUpdateFields["retention_score"] {
		setFields = append(setFields, "retention_score = ?")
		args = append(args, record.RetentionScore)
	}

	if record.NextReviewTime > 0 || forceUpdateFields["next_review_time"] {
		setFields = append(setFields, "next_review_time = ?")
		args = append(args, record.NextReviewTime)
	}

	if record.LastReviewTime > 0 || forceUpdateFields["last_review_time"] {
		setFields = append(setFields, "last_review_time = ?")
		args = append(args, record.LastReviewTime)
	}

	if record.ForgetCount >= 0 || forceUpdateFields["forget_count"] {
		setFields = append(setFields, "forget_count = ?")
		args = append(args, record.ForgetCount)
	}

	if record.Status != "" || forceUpdateFields["status"] {
		setFields = append(setFields, "status = ?")
		args = append(args, record.Status)
	}

	if record.ReviewHistory != "" || forceUpdateFields["review_history"] {
		setFields = append(setFields, "review_history = ?")
		args = append(args, record.ReviewHistory)
	}

	if record.QualityScore > 0 || forceUpdateFields["quality_score"] {
		setFields = append(setFields, "quality_score = ?")
		args = append(args, record.QualityScore)
	}

	// 构建SQL
	sql := fmt.Sprintf("UPDATE agent_memory SET %s WHERE id = ?", strings.Join(setFields, ", "))
	args = append(args, record.ID)

	// 执行更新
	_, err := c.db.Exec(sql, args...)
	if err != nil {
		return fmt.Errorf("update failed: %v", err)
	}

	// 更新向量
	if len(record.MemoryEmbedding) > 0 {
		var setFieldsVector []string
		var argsVector []interface{}

		// 更新修改时间
		now := int(time.Now().Unix())
		setFieldsVector = append(setFieldsVector, "gmt_modified = ?")
		argsVector = append(argsVector, now)

		embeddingBytes, err := serializeFloat32(record.MemoryEmbedding)
		if err != nil {
			return fmt.Errorf("failed to serialize embedding: %v", err)
		}
		setFieldsVector = append(setFieldsVector, "memory_embedding = ?")
		argsVector = append(argsVector, embeddingBytes)

		sqlVector := fmt.Sprintf("UPDATE agent_memory_embedding SET %s WHERE memory_id = ?", strings.Join(setFieldsVector, ", "))
		argsVector = append(argsVector, record.ID)

		// 执行更新
		_, err = c.db.Exec(sqlVector, argsVector...)
		if err != nil {
			return fmt.Errorf("update embedding failed: %v", err)
		}
	}

	return nil
}

// Save 保存记忆（插入或更新）
func (c *MemorySqliteVecClient) Save(record MemoryRecord) error {
	c.mtx.Lock()
	defer c.mtx.Unlock()

	if c.closed {
		return errors.New("client is closed")
	}

	// 验证必填字段
	if record.Scope == "" {
		return errors.New("scope is required")
	}
	if record.Content == "" {
		return errors.New("content is required")
	}
	if len(record.MemoryEmbedding) == 0 {
		return errors.New("memory embedding is required")
	}

	// 如果ID为空，生成一个新的UUID
	if record.ID == "" {
		// 使用随机字符串作为ID
		record.ID = fmt.Sprintf("%d", time.Now().UnixNano())
	}

	// 设置创建和修改时间
	now := int(time.Now().Unix())
	if record.GmtCreate == 0 {
		record.GmtCreate = now
	}
	if record.GmtModified == 0 {
		record.GmtModified = now
	}

	// 设置默认来源
	if record.Source == "" {
		record.Source = definition.MemoryAutoSource
	}

	// 计算token数量
	if record.TokenCount == 0 && record.Content != "" {
		tokenCount, err := tokenizer.CalQwenTokenCount(record.Content)
		if err != nil {
			log.Warnf("[memory]-[sqlite] calculate token count error: %v", err)
			// 计算失败不影响保存
		} else {
			record.TokenCount = tokenCount
		}
	}

	// 检查记录是否已存在
	var exists bool
	err := c.db.QueryRow("SELECT EXISTS(SELECT 1 FROM agent_memory WHERE id = ?)", record.ID).Scan(&exists)
	if err != nil {
		return fmt.Errorf("check record existence failed: %v", err)
	}

	if exists {
		// 更新记录
		var setFields []string
		var args []interface{}

		// 更新修改时间
		setFields = append(setFields, "gmt_modified = ?")
		args = append(args, record.GmtModified)

		// 更新范围
		setFields = append(setFields, "scope = ?")
		args = append(args, record.Scope)

		// 更新关键词
		setFields = append(setFields, "keywords = ?")
		args = append(args, record.Keywords)

		// 更新标题
		setFields = append(setFields, "title = ?")
		args = append(args, record.Title)

		// 更新内容
		setFields = append(setFields, "content = ?")
		args = append(args, record.Content)

		// 更新会话ID
		setFields = append(setFields, "session_id = ?")
		args = append(args, record.SessionID)

		// 更新合并状态
		setFields = append(setFields, "is_merged = ?")
		args = append(args, record.IsMerged)

		// 更新使用热度
		setFields = append(setFields, "freq = ?")
		args = append(args, record.Freq)

		// 更新来源
		setFields = append(setFields, "source = ?")
		args = append(args, record.Source)

		// 更新token数量
		setFields = append(setFields, "token_count = ?")
		args = append(args, record.TokenCount)

		if record.Type != "" {
			// 更新类型
			setFields = append(setFields, "type = ?")
			args = append(args, record.Type)
		}

		if record.UserId != "" {
			// 更新用户ID
			setFields = append(setFields, "user_id = ?")
			args = append(args, record.UserId)
		}

		// 添加新字段的更新逻辑
		if record.Category != "" {
			setFields = append(setFields, "category = ?")
			args = append(args, record.Category)
		}

		if record.RetentionScore > 0 {
			setFields = append(setFields, "retention_score = ?")
			args = append(args, record.RetentionScore)
		}

		if record.NextReviewTime > 0 {
			setFields = append(setFields, "next_review_time = ?")
			args = append(args, record.NextReviewTime)
		}

		if record.LastReviewTime > 0 {
			setFields = append(setFields, "last_review_time = ?")
			args = append(args, record.LastReviewTime)
		}

		if record.ForgetCount >= 0 {
			setFields = append(setFields, "forget_count = ?")
			args = append(args, record.ForgetCount)
		}

		if record.Status != "" {
			setFields = append(setFields, "status = ?")
			args = append(args, record.Status)
		}

		if record.ReviewHistory != "" {
			setFields = append(setFields, "review_history = ?")
			args = append(args, record.ReviewHistory)
		}

		if record.QualityScore > 0 {
			setFields = append(setFields, "quality_score = ?")
			args = append(args, record.QualityScore)
		}

		// 构建SQL
		sql := fmt.Sprintf("UPDATE agent_memory SET %s WHERE id = ?", strings.Join(setFields, ", "))
		args = append(args, record.ID)

		// 执行更新
		_, err = c.db.Exec(sql, args...)
		if err != nil {
			return fmt.Errorf("update failed: %v", err)
		}
	} else {
		// 插入新记录
		sql := `
			INSERT INTO agent_memory (
				id, gmt_create, gmt_modified, scope, scope_id, keywords, title, content, session_id, is_merged, freq,
				source, token_count, type, user_id, category, retention_score, next_review_time, last_review_time, 
				forget_count, status, review_history, quality_score
			) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
			`

		// 执行插入
		_, err = c.db.Exec(
			sql,
			record.ID,
			record.GmtCreate,
			record.GmtModified,
			record.Scope,
			record.ScopeId,
			record.Keywords,
			record.Title,
			record.Content,
			record.SessionID,
			record.IsMerged,
			record.Freq,
			record.Source,
			record.TokenCount,
			record.Type,
			record.UserId,
			record.Category,
			record.RetentionScore,
			record.NextReviewTime,
			record.LastReviewTime,
			record.ForgetCount,
			record.Status,
			record.ReviewHistory,
			record.QualityScore,
		)
		if err != nil {
			return fmt.Errorf("insert failed: %v", err)
		}
	}
	err = c.saveEmbeddingTable(record)
	if err != nil {
		return fmt.Errorf("save embedding table failed: %v", err)
	}

	return nil
}

// saveEmbeddingTable 保存记忆向量
func (c *MemorySqliteVecClient) saveEmbeddingTable(record MemoryRecord) error {
	// 将向量转换为二进制格式
	embeddingBytes, err := serializeFloat32(record.MemoryEmbedding)
	if err != nil {
		return fmt.Errorf("failed to serialize embedding: %v", err)
	}

	// 检查记录是否已存在
	var exists bool
	err = c.db.QueryRow("SELECT EXISTS(SELECT 1 FROM agent_memory_embedding WHERE memory_id = ?)", record.ID).Scan(&exists)
	if err != nil {
		return fmt.Errorf("check record existence failed: %v", err)
	}

	if exists {
		// 更新记录
		var setFields []string
		var args []interface{}

		// 更新修改时间
		setFields = append(setFields, "gmt_modified = ?")
		args = append(args, record.GmtModified)

		// 更新向量
		setFields = append(setFields, "memory_embedding = ?")
		args = append(args, embeddingBytes)

		// 构建SQL
		sql := fmt.Sprintf("UPDATE agent_memory_embedding SET %s WHERE memory_id = ?", strings.Join(setFields, ", "))
		args = append(args, record.ID)

		// 执行更新
		_, err = c.db.Exec(sql, args...)
		if err != nil {
			return fmt.Errorf("update failed: %v", err)
		}
	} else {
		// 插入新记录
		sql := `
		INSERT INTO agent_memory_embedding (
			memory_id, gmt_create, gmt_modified, memory_embedding
		) VALUES (?, ?, ?, ?)
		`

		// 执行插入
		_, err = c.db.Exec(
			sql,
			record.ID,
			record.GmtCreate,
			record.GmtModified,
			embeddingBytes,
		)
		if err != nil {
			return fmt.Errorf("insert failed: %v", err)
		}
	}

	return nil
}

// BatchInsert 批量插入记忆
func (c *MemorySqliteVecClient) BatchInsert(records []MemoryRecord) error {
	if len(records) == 0 {
		return nil
	}

	c.mtx.Lock()
	defer c.mtx.Unlock()

	if c.closed {
		return errors.New("client is closed")
	}
	// 开始事务
	tx, err := c.db.Begin()
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %v", err)
	}

	// 准备插入语句
	stmt, err := tx.Prepare(`
		INSERT INTO agent_memory (
			id, gmt_create, gmt_modified, scope, scope_id, keywords, title, content, session_id, is_merged, freq,
			source, token_count, type, user_id, category, retention_score, next_review_time, last_review_time, 
			forget_count, status, review_history, quality_score
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`)
	if err != nil {
		rollbackErr := tx.Rollback()
		if rollbackErr != nil {
			log.Errorf("[memory]-[sqlite] rollback failed: %v", rollbackErr)
		}
		return fmt.Errorf("failed to prepare statement: %v", err)
	}
	defer func(stmt *sql.Stmt) {
		err := stmt.Close()
		if err != nil {
			log.Errorf("[memory]-[sqlite] close statement failed: %v", err)
		}
	}(stmt)

	// 准备插入语句
	stmtVec, err := tx.Prepare(`
	INSERT INTO agent_memory_embedding (
			memory_id, gmt_create, gmt_modified, memory_embedding
		) VALUES (?, ?, ?, ?)
	`)
	if err != nil {
		rollbackErr := tx.Rollback()
		if rollbackErr != nil {
			log.Errorf("[memory]-[sqlite] vector rollback failed: %v", rollbackErr)
		}
		return fmt.Errorf("failed to prepare vector statement: %v", err)
	}
	defer func(stmt *sql.Stmt) {
		err := stmt.Close()
		if err != nil {
			log.Errorf("[memory]-[sqlite] close vector statement failed: %v", err)
		}
	}(stmtVec)

	// 批量插入
	for _, record := range records {
		// 验证必填字段
		if record.Scope == "" || record.Content == "" || len(record.MemoryEmbedding) == 0 {
			rollbackErr := tx.Rollback()
			if rollbackErr != nil {
				log.Errorf("[memory]-[sqlite] rollback failed: %v", rollbackErr)
			}
			return errors.New("invalid record: scope, content and memory_embedding are required")
		}

		// 将向量转换为二进制格式
		embeddingBytes, err := serializeFloat32(record.MemoryEmbedding)
		if err != nil {
			rollbackErr := tx.Rollback()
			if rollbackErr != nil {
				log.Errorf("[memory]-[sqlite] rollback failed: %v", rollbackErr)
			}
			return fmt.Errorf("failed to serialize embedding: %v", err)
		}

		// 设置默认来源
		if record.Source == "" {
			record.Source = definition.MemoryAutoSource
		}

		// 计算token数量
		if record.TokenCount == 0 && record.Content != "" {
			tokenCount, err := tokenizer.CalQwenTokenCount(record.Content)
			if err != nil {
				log.Warnf("[memory]-[sqlite] calculate token count error: %v", err)
				// 计算失败不影响插入
			} else {
				record.TokenCount = tokenCount
			}
		}
		// 执行插入
		_, err = stmt.Exec(
			record.ID,
			record.GmtCreate,
			record.GmtModified,
			record.Scope,
			record.ScopeId,
			record.Keywords,
			record.Title,
			record.Content,
			record.SessionID,
			record.IsMerged,
			record.Freq,
			record.Source,
			record.TokenCount,
			record.Type,
			record.UserId,
			record.Category,
			record.RetentionScore,
			record.NextReviewTime,
			record.LastReviewTime,
			record.ForgetCount,
			record.Status,
			record.ReviewHistory,
			record.QualityScore,
		)
		if err != nil {
			rollbackErr := tx.Rollback()
			if rollbackErr != nil {
				log.Errorf("[memory]-[sqlite] rollback failed: %v", rollbackErr)
			}
			return fmt.Errorf("batch insert failed: %v", err)
		}

		// 插入向量
		_, err = stmtVec.Exec(
			record.ID,
			record.GmtCreate,
			record.GmtModified,
			embeddingBytes,
		)
		if err != nil {
			rollbackErr := tx.Rollback()
			if rollbackErr != nil {
				log.Errorf("[memory]-[sqlite] vector rollback failed: %v", rollbackErr)
			}
			return fmt.Errorf("batch insert vector failed: %v", err)
		}
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %v", err)
	}

	return nil
}

// Delete 删除记忆
func (c *MemorySqliteVecClient) Delete(record MemoryRecord, strategy string) error {
	c.mtx.Lock()
	defer c.mtx.Unlock()

	if c.closed {
		return errors.New("client is closed")
	}

	err := c.deleteEmbedding(record, strategy)
	if err != nil {
		return fmt.Errorf("delete embedding failed: %v", err)
	}

	var memorySql string
	var memoryArgs []interface{}
	var embeddingSql string
	var embeddingArgs []interface{}

	switch strategy {
	case DeleteStrategyByID:
		if record.ID == "" {
			return errors.New("invalid record ID")
		}
		memorySql = "DELETE FROM agent_memory WHERE id = ?"
		memoryArgs = []interface{}{record.ID}
		embeddingSql = "DELETE FROM agent_memory_embedding WHERE memory_id = ?"
		embeddingArgs = []interface{}{record.ID}
	case DeleteStrategyBySessionID:
		if record.SessionID == "" {
			return errors.New("session ID is required")
		}
		memorySql = "DELETE FROM agent_memory WHERE session_id = ?"
		memoryArgs = []interface{}{record.SessionID}
		embeddingSql = "DELETE FROM agent_memory_embedding WHERE memory_id in (select id from agent_memory where session_id = ?)"
		embeddingArgs = []interface{}{record.SessionID}
	case DeleteStrategyByCategoryAndSessionID:
		if record.SessionID == "" || record.Category == "" {
			return errors.New("session ID and category is required")
		}
		memorySql = "DELETE FROM agent_memory WHERE session_id = ? and category = ?"
		memoryArgs = []interface{}{record.SessionID, record.Category}
		embeddingSql = "DELETE FROM agent_memory_embedding WHERE memory_id in (select id from agent_memory where session_id = ? and category = ?)"
		embeddingArgs = []interface{}{record.SessionID, record.Category}
	case DeleteStrategyByScope:
		if record.Scope == "" {
			return errors.New("scope is required")
		}
		memorySql = "DELETE FROM agent_memory WHERE scope = ?"
		memoryArgs = []interface{}{record.Scope}
		embeddingSql = "DELETE FROM agent_memory_embedding WHERE memory_id in (select id from agent_memory where scope = ?)"
		embeddingArgs = []interface{}{record.Scope}
	default:
		return fmt.Errorf("unsupported delete strategy: %s", strategy)
	}

	// 执行删除
	_, err = c.db.Exec(memorySql, memoryArgs...)
	if err != nil {
		return fmt.Errorf("delete failed: %v", err)
	}
	_, err = c.db.Exec(embeddingSql, embeddingArgs...)
	if err != nil {
		return fmt.Errorf("delete failed: %v", err)
	}

	return nil
}

// deleteEmbedding 删除记忆向量
func (c *MemorySqliteVecClient) deleteEmbedding(record MemoryRecord, strategy string) error {
	var sql string
	var args []interface{}

	switch strategy {
	case DeleteStrategyByID:
		if record.ID == "" {
			return errors.New("invalid record ID")
		}
		sql = "DELETE FROM agent_memory_embedding WHERE memory_id = ?"
		args = []interface{}{record.ID}
	case DeleteStrategyBySessionID:
		if record.SessionID == "" {
			return errors.New("session ID is required")
		}
		sql = "DELETE FROM agent_memory_embedding WHERE memory_id in (select id from agent_memory where session_id = ?)"
		args = []interface{}{record.SessionID}
	case DeleteStrategyByScope:
		if record.Scope == "" {
			return errors.New("scope is required")
		}
		sql = "DELETE FROM agent_memory_embedding WHERE memory_id in (select id from agent_memory where scope = ?)"
		args = []interface{}{record.Scope}
	default:
		return fmt.Errorf("unsupported delete strategy: %s", strategy)
	}

	// 执行删除
	_, err := c.db.Exec(sql, args...)
	if err != nil {
		return fmt.Errorf("delete failed: %v", err)
	}

	return nil
}

// BatchDelete 批量删除记忆
func (c *MemorySqliteVecClient) BatchDelete(records []MemoryRecord, strategy string) error {
	if len(records) == 0 {
		return nil
	}

	c.mtx.Lock()
	defer c.mtx.Unlock()

	if c.closed {
		return errors.New("client is closed")
	}

	// 开始事务
	tx, err := c.db.Begin()
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %v", err)
	}

	var sql string

	switch strategy {
	case DeleteStrategyByID:
		// 收集所有ID
		var ids []interface{}
		var placeholders []string

		for _, record := range records {
			if record.ID == "" {
				tx.Rollback()
				return errors.New("invalid record ID")
			}
			ids = append(ids, record.ID)
			placeholders = append(placeholders, "?")
		}

		sql = "DELETE FROM agent_memory_embedding WHERE memory_id in (" + strings.Join(placeholders, ",") + ")"

		// 执行删除
		_, err = tx.Exec(sql, ids...)
		if err != nil {
			tx.Rollback()
			return fmt.Errorf("batch delete failed: %v", err)
		}

		sql = fmt.Sprintf("DELETE FROM agent_memory WHERE id IN (%s)", strings.Join(placeholders, ", "))

		// 执行删除
		_, err = tx.Exec(sql, ids...)
		log.Debugf("[memory]-[sqlite] deleting memory ids: %v", ids)

	case DeleteStrategyBySessionID:
		// 收集所有会话ID
		sessionIDs := make(map[string]bool)

		for _, record := range records {
			if record.SessionID != "" {
				sessionIDs[record.SessionID] = true
			}
		}

		if len(sessionIDs) == 0 {
			tx.Rollback()
			return errors.New("no valid session IDs")
		}

		var ids []interface{}
		var placeholders []string

		for sessionID := range sessionIDs {
			ids = append(ids, sessionID)
			placeholders = append(placeholders, "?")
		}

		sql = "DELETE FROM agent_memory_embedding WHERE memory_id in (select id from agent_memory where session_id in (" + strings.Join(placeholders, ",") + "))"

		// 执行删除
		_, err = tx.Exec(sql, ids...)
		if err != nil {
			tx.Rollback()
			return fmt.Errorf("batch delete failed: %v", err)
		}

		sql = fmt.Sprintf("DELETE FROM agent_memory WHERE session_id IN (%s)", strings.Join(placeholders, ", "))

		// 执行删除
		_, err = tx.Exec(sql, ids...)

	case DeleteStrategyByScope:
		// 收集所有范围
		scopes := make(map[string]bool)

		for _, record := range records {
			if record.Scope != "" {
				scopes[record.Scope] = true
			}
		}

		if len(scopes) == 0 {
			tx.Rollback()
			return errors.New("no valid scopes")
		}

		var scopeList []interface{}
		var placeholders []string

		for scope := range scopes {
			scopeList = append(scopeList, scope)
			placeholders = append(placeholders, "?")
		}

		sql = "DELETE FROM agent_memory_embedding WHERE memory_id in (select id from agent_memory where scope in (" + strings.Join(placeholders, ",") + "))"

		// 执行删除
		_, err = tx.Exec(sql, scopeList...)
		if err != nil {
			tx.Rollback()
			return fmt.Errorf("batch delete failed: %v", err)
		}

		sql = fmt.Sprintf("DELETE FROM agent_memory WHERE scope IN (%s)", strings.Join(placeholders, ", "))

		// 执行删除
		_, err = tx.Exec(sql, scopeList...)

	default:
		tx.Rollback()
		return fmt.Errorf("unsupported delete strategy: %s", strategy)
	}

	if err != nil {
		tx.Rollback()
		return fmt.Errorf("batch delete failed: %v", err)
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %v", err)
	}
	log.Debugf("[memory]-[sqlite] batch delete success")

	return nil
}

// GetRecordCount 获取记录数量
func (c *MemorySqliteVecClient) GetRecordCount() int {
	c.mtx.Lock()
	defer c.mtx.Unlock()

	if c.closed {
		return 0
	}

	var count int
	err := c.db.QueryRow("SELECT COUNT(*) FROM agent_memory").Scan(&count)
	if err != nil {
		log.Errorf("[memory]-[sqlite] failed to get record count: %v", err)
		return 0
	}

	return count
}

func (c *MemorySqliteVecClient) GetValidRecordCountForScopeId(scopeId string) int {
	c.mtx.Lock()
	defer c.mtx.Unlock()

	if c.closed {
		return 0
	}

	var count int
	err := c.db.QueryRow("SELECT COUNT(*) FROM agent_memory WHERE scope_id = ? or scope = ?", scopeId, "global").Scan(&count)
	if err != nil {
		log.Errorf("[memory]-[sqlite] failed to get record count by scope id: %v", err)
		return 0
	}

	return count
}

// DoEmbed 为内容生成向量嵌入
func (c *MemorySqliteVecClient) DoEmbed(contents []string) ([][]float32, error) {
	if len(contents) == 0 {
		return [][]float32{}, nil
	}

	// 使用LingmaEmbedder生成向量嵌入
	embedder := components.NewLingmaEmbedder()
	embeddings, err := embedder.CreateEmbedding(context.Background(), contents, components.TextTypeDocument)
	if err != nil {
		log.Errorf("[memory]-[sqlite] embedding error: %v", err)
		return nil, fmt.Errorf("embedding error: %w", err)
	}

	if len(embeddings) != len(contents) {
		log.Errorf("[memory]-[sqlite] embedding length mismatch: %d != %d", len(embeddings), len(contents))
		return nil, fmt.Errorf("embedding length mismatch: %d != %d", len(embeddings), len(contents))
	}

	return embeddings, nil
}

// buildWhereClause 构建查询条件的WHERE子句
// 返回WHERE子句字符串和对应的参数列表
func (c *MemorySqliteVecClient) buildWhereClause(condition MemoryQueryCondition) (string, []interface{}) {
	var whereClause []string
	var args []interface{}

	// 基础条件
	whereClause = append(whereClause, "1=1")

	// 当UseGlobalOrWorkspaceScope为true时，查询全局记忆和当前scope记忆
	if !condition.UseGlobalOrWorkspaceScope {
		// 范围条件
		if len(condition.Scopes) > 0 {
			placeholders := make([]string, len(condition.Scopes))
			for i := range placeholders {
				placeholders[i] = "?"
				args = append(args, condition.Scopes[i])
			}
			whereClause = append(whereClause, fmt.Sprintf("scope IN (%s)", strings.Join(placeholders, ", ")))
		}

		// 范围ID条件
		if condition.ScopeId != "" {
			whereClause = append(whereClause, "scope_id = ?")
			args = append(args, condition.ScopeId)
		}
	} else {
		// 范围ID条件
		if condition.ScopeId != "" {
			whereClause = append(whereClause, "(scope='global' or scope_id = ?)")
			args = append(args, condition.ScopeId)
		}
	}

	// 会话ID条件
	if condition.SessionID != "" {
		whereClause = append(whereClause, "session_id = ?")
		args = append(args, condition.SessionID)
	}

	// 合并状态条件
	if condition.IsMerged != nil {
		whereClause = append(whereClause, "is_merged = ?")
		args = append(args, *condition.IsMerged)
	}

	// 类型条件
	if condition.Type != "" {
		whereClause = append(whereClause, "type = ?")
		args = append(args, condition.Type)
	}

	// 用户ID条件
	if condition.UserId != "" {
		whereClause = append(whereClause, "user_id = ?")
		args = append(args, condition.UserId)
	}

	if len(condition.Source) > 0 {
		placeholders := make([]string, len(condition.Source))
		for i := range placeholders {
			placeholders[i] = "?"
			args = append(args, condition.Source[i])
		}
		whereClause = append(whereClause, fmt.Sprintf("source IN (%s)", strings.Join(placeholders, ", ")))
	}

	if condition.Status != "" {
		whereClause = append(whereClause, "status = ?")
		args = append(args, condition.Status)
	}

	// 关键词条件
	if condition.Query != "" {
		whereClause = append(whereClause, `
			EXISTS (
				SELECT 1 FROM (
					SELECT value FROM json_each('["' || REPLACE(keywords, ',', '","') || '"]')
				) AS kw
				WHERE INSTR(LOWER(?), LOWER(kw.value)) > 0
			)
		`)
		args = append(args, condition.Query)
	}

	return strings.Join(whereClause, " AND "), args
}

// CountMemoryRecords 统计符合条件的记录数量
func (c *MemorySqliteVecClient) CountMemoryRecords(condition MemoryQueryCondition) (int, error) {
	c.mtx.Lock()
	defer c.mtx.Unlock()

	if c.closed {
		return 0, errors.New("client is closed")
	}

	// 构建WHERE子句，使用与查询相同的逻辑
	whereClause, args := c.buildWhereClause(condition)

	// 构建完整的SQL
	sql := fmt.Sprintf(`
	SELECT COUNT(*) 
	FROM agent_memory
	WHERE %s
	`, whereClause)

	//log.Debugf("[memory]-[sqlite] count sql: %s, whereClause: %s", sql, whereClause)

	// 执行查询
	var count int
	err := c.db.QueryRow(sql, args...).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("count query failed: %v", err)
	}

	return count, nil
}

// QueryWithPagination 分页查询记录
func (c *MemorySqliteVecClient) QueryWithPagination(condition MemoryQueryCondition) (MemoryQueryResult, error) {
	c.mtx.Lock()
	defer c.mtx.Unlock()

	if c.closed {
		return MemoryQueryResult{}, errors.New("client is closed")
	}

	// 参数校验
	if condition.Page <= 0 {
		condition.Page = 1
	}
	if condition.PageSize <= 0 {
		condition.PageSize = 10
	}

	// 计算分页参数
	offset := (condition.Page - 1) * condition.PageSize
	limit := condition.PageSize

	// 构建WHERE子句
	whereClause, args := c.buildWhereClause(condition)

	// 构建自定义排序顺序
	// 1. 全局记忆 (scope='global')
	// 2. 本工程记忆 (scope='workspace' AND scope_id='当前工程ID')
	// 3. 其他工程记忆
	// 每种类型内部按照gmt_modified DESC倒序排序
	orderByClause := `
	ORDER BY 
		CASE 
			WHEN scope = 'global' THEN 1
			WHEN scope = 'workspace' AND scope_id = ? THEN 2
			ELSE 3
		END,
		gmt_modified DESC
	`

	// 将当前工程ID添加到参数列表
	args = append(args, condition.ScopeId)

	// 构建完整的SQL
	sql := fmt.Sprintf(`
	SELECT 
		id, gmt_create, gmt_modified, scope, scope_id, keywords, title, content, '' as memory_embedding, session_id, is_merged, freq, 
		0 as distance, source, token_count, type, user_id, category, retention_score, next_review_time, last_review_time, forget_count, status, review_history, quality_score
	FROM agent_memory
	WHERE %s
	%s
	LIMIT %d OFFSET %d
	`, whereClause, orderByClause, limit, offset)

	//log.Debugf("[memory]-[sqlite] pagination query sql: %s, whereClause: %s, limit: %d, offset: %d", sql, whereClause, limit, offset)

	// 执行查询
	rows, err := c.db.Query(sql, args...)
	if err != nil {
		return MemoryQueryResult{}, fmt.Errorf("pagination query failed: %v", err)
	}
	defer rows.Close()

	// 处理结果
	var result MemoryQueryResult
	for rows.Next() {
		var record MemoryRecord
		var embeddingBytes []byte
		err := rows.Scan(
			&record.ID,
			&record.GmtCreate,
			&record.GmtModified,
			&record.Scope,
			&record.ScopeId,
			&record.Keywords,
			&record.Title,
			&record.Content,
			&embeddingBytes,
			&record.SessionID,
			&record.IsMerged,
			&record.Freq,
			&record.Distance,
			&record.Source,
			&record.TokenCount,
			&record.Type,
			&record.UserId,
			&record.Category,
			&record.RetentionScore,
			&record.NextReviewTime,
			&record.LastReviewTime,
			&record.ForgetCount,
			&record.Status,
			&record.ReviewHistory,
			&record.QualityScore,
		)
		if err != nil {
			return MemoryQueryResult{}, fmt.Errorf("scan row failed: %v", err)
		}

		result.Records = append(result.Records, record)
	}

	if err := rows.Err(); err != nil {
		return MemoryQueryResult{}, fmt.Errorf("rows iteration failed: %v", err)
	}

	return result, nil
}

func init() {
	sqlitevec.Auto()
}
