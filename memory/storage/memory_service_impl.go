package storage

import (
	"context"
	"cosy/components"
	"cosy/definition"
	"cosy/experiment"
	"cosy/log"
	"cosy/storage/database"
	"cosy/user"
	"cosy/util"
	"cosy/util/collection"
	"errors"
	"fmt"
	"sort"
	"strings"
	"sync"
	"time"
)

// 全局变量
var (
	// 单例实例
	instance      MemoryService
	serviceOnce   sync.Once
	serviceClient MemorySqliteVecEngine
	clientOnce    sync.Once
	clientInitErr error
)

const (
	globalTopK         = 20
	workspaceQueryTopK = 20
	shortTermQueryTopK = 10
)

// MemoryServiceImpl 记忆服务实现
type MemoryServiceImpl struct {
	client MemorySqliteVecEngine
}

// GetMemoryClient 获取全局内存客户端对象
func GetMemoryClient() (MemorySqliteVecEngine, error) {
	var err error
	clientOnce.Do(func() {
		// 创建数据库客户端
		dbPath := database.GetDatabasePath()
		sqliteClient, e := NewMemorySqliteVecClient("v1", DatabaseModeMemory, dbPath)
		if e != nil {
			log.Errorf("[memory]-[service] create memory client error: %v", e)
			err = e
			return
		}

		// 创建表
		e = sqliteClient.CreateLingmaMemoryTable()
		if e != nil {
			log.Errorf("[memory]-[service] create memory table error: %v", e)
			err = e
			return
		}

		// 初始化全局客户端
		serviceClient = sqliteClient
		log.Debugf("[memory]-[service] global client initialized in ltm package")
	})

	if err != nil {
		return nil, err
	}

	// 额外检查 serviceClient 是否为 nil，防止出现 nil, nil 的返回情况
	if serviceClient == nil {
		err = fmt.Errorf("memory service client is nil after initialization with no error")
		log.Errorf("[memory]-[service] %v", err)
		return nil, err
	}

	return serviceClient, nil
}

// InitMemoryService 初始化并注册内存服务单例实例
func InitMemoryService() (MemoryService, error) {
	var err error
	serviceOnce.Do(func() {
		client, e := GetMemoryClient()
		if e != nil {
			err = e
			return
		}

		// 检查客户端是否为 nil
		if client == nil {
			err = fmt.Errorf("memory client is nil after initialization")
			log.Errorf("[memory]-[service] %v", err)
			return
		}

		instance = &MemoryServiceImpl{
			client: client,
		}

		// 注册为全局服务实例
		RegisterMemoryService(instance)
	})

	if err != nil {
		return nil, err
	}

	// 最后再次检查 instance 是否为 nil，避免初始化失败但没有设置 err 的情况
	if instance == nil {
		err = fmt.Errorf("memory service instance is nil after initialization")
		log.Errorf("[memory]-[service] %v", err)
		return nil, err
	}

	return instance, nil
}

// NewMemoryService 创建新的内存服务实例
func NewMemoryService() (MemoryService, error) {
	client, err := GetMemoryClient()
	if err != nil {
		return nil, err
	}

	service := &MemoryServiceImpl{
		client: client,
	}

	return service, nil
}

// SaveRecords 实现MemoryService接口方法，批量保存记忆记录
func (s *MemoryServiceImpl) SaveRecords(records []MemoryRecord) error {
	return s.BatchInsert(records)
}

// Query 实现MemoryService接口，根据查询条件查询记忆
func (s *MemoryServiceImpl) Query(condition MemoryQueryCondition) (MemoryQueryResult, error) {
	return s.client.Query(condition)
}

func (s *MemoryServiceImpl) QuerySimilar(content string, workspacePath string, optionalCategory string) ([]MemoryRecord, error) {
	if content == "" {
		return nil, errors.New("content is empty")
	}
	userId := ""
	cachedUser := user.GetCachedUserInfo()
	if cachedUser != nil && cachedUser.Uid != "" {
		userId = cachedUser.Uid
	}
	// 先查询全局记忆
	embedder := components.NewLingmaEmbedder()
	queryEmbedding, err := embedder.CreateEmbedding(context.Background(), []string{content}, components.TextTypeQuery)
	if err != nil {
		return nil, err
	}
	if len(queryEmbedding) == 0 {
		return nil, fmt.Errorf("queryEmbedding is empty")
	}
	// 向量查询
	queryVecCondition := MemoryQueryCondition{
		TopK:           3,                                         // 多查询一条以判断是否超过阈值
		Scopes:         []string{definition.MemoryWorkspaceScope}, // 查询 workspace 类型的记忆
		ScopeId:        workspacePath,                             // 使用当前项目的 scopeId 进行精确匹配
		QueryEmbedding: queryEmbedding[0],                         // 使用问题的向量表示
		Type:           definition.LongTermMemoryType,
		UserId:         userId,
		Source:         []string{definition.MemoryAutoSource, definition.MemoryUserSource},
	}
	if optionalCategory != "" {
		queryVecCondition.Categories = append(queryVecCondition.Categories, optionalCategory)
	}
	data, err := s.client.Query(queryVecCondition)
	if err != nil {
		return nil, err
	}
	return data.Records, nil
}

// QueryByQuestion 根据问题查询相关记忆
// 生成question的向量，根据query和向量检索，如果没有搜到则尝试用向量检索相关记忆
// 设定合理阈值尽量召回相关记忆，仅召回scope=global或者是本工程的记忆
func (s *MemoryServiceImpl) QueryByQuestion(sessionId string, question string, workspacePath string, enableWorkspace bool) ([]MemoryRecord, error) {
	if question == "" {
		return nil, errors.New("question is empty")
	}
	userId := ""
	cachedUser := user.GetCachedUserInfo()
	if cachedUser != nil && cachedUser.Uid != "" {
		userId = cachedUser.Uid
	}
	memoryMap := collection.NewSynchronizedMap[string, MemoryRecord]()
	// 先查询全局记忆
	scopes := []string{definition.MemoryGlobalScope}
	scopeOnlyCondition := MemoryQueryCondition{
		Scopes: scopes, // 只查询全局记忆
		UserId: userId,
		TopK:   globalTopK,
	}
	// 查询全局记忆
	s.queryMemory("global-scope", memoryMap, scopeOnlyCondition, 0, false, nil)
	if enableWorkspace {
		s.queryWorkspaceMemory(sessionId, question, workspacePath, userId, memoryMap)
	}

	queryResults := util.MapToArray(memoryMap.Data, false)
	sort.Slice(queryResults, func(i, j int) bool {
		return queryResults[i].Distance < queryResults[j].Distance
	})

	return queryResults, nil
}

// queryWorkspaceMemory 查询工作区记忆
func (s *MemoryServiceImpl) queryWorkspaceMemory(sessionId string, question string, workspacePath string, userId string, memoryMap *collection.SynchronizedMap[string, MemoryRecord]) {
	// 查询初始化记忆
	initSourceCondition := MemoryQueryCondition{
		Scopes:  []string{definition.MemoryWorkspaceScope},
		ScopeId: workspacePath,
		UserId:  userId,
		Source:  []string{definition.MemoryInitSource},
		Type:    definition.LongTermMemoryType,
		TopK:    1,
	}
	// 查询全局记忆
	s.queryMemory("init-source", memoryMap, initSourceCondition, 0, false, nil)

	// 获取当前项目的 scopeId，用于查询当前工作区的记忆
	currentScopeId := workspacePath
	s.queryByEmbedding(sessionId, question, workspacePath, userId, memoryMap)
	// 关键词查询
	queryCondition := MemoryQueryCondition{
		TopK:    workspaceQueryTopK,                        // 多查询一条以判断是否超过阈值
		Scopes:  []string{definition.MemoryWorkspaceScope}, // 查询 workspace 类型的记忆
		ScopeId: currentScopeId,                            // 使用当前项目的 scopeId 进行精确匹配
		Query:   question,
		Type:    definition.LongTermMemoryType,
		UserId:  userId,
	}
	s.queryMemory("keyword-query", memoryMap, queryCondition, 0, false, nil)

	// 固定查询
	queryFixCondition := MemoryQueryCondition{
		TopK:    workspaceQueryTopK,                        // 多查询一条以判断是否超过阈值
		Scopes:  []string{definition.MemoryWorkspaceScope}, // 查询 workspace 类型的记忆
		ScopeId: currentScopeId,                            // 使用当前项目的 scopeId 进行精确匹配
		Type:    definition.LongTermMemoryType,
		UserId:  userId,
		Categories: []string{
			definition.MemoryCategoryProjectSpecification,
			definition.MemoryCategoryProjectInfo,
		},
	}
	s.queryMemory("fix-query", memoryMap, queryFixCondition, 0, false, nil)
}

// queryMemory 通过embedding查询记忆
func (s *MemoryServiceImpl) queryByEmbedding(sessionId string, question string, workspacePath string, userId string, memoryMap *collection.SynchronizedMap[string, MemoryRecord]) {
	embeddingFilter := func(record *MemoryRecord) bool {
		similarScore := 1 - record.Distance
		if record.Category == definition.MemoryCategoryTaskWorkflow ||
			record.Category == definition.MemoryCategoryTaskReferenceFiles {
			return similarScore >= 0.8
		}
		return true
	}
	_, err := util.GoWithTimeout(func() {
		embedder := components.NewLingmaEmbedder()
		queryEmbedding, err := embedder.CreateEmbedding(context.Background(), []string{question}, components.TextTypeQuery)
		if err != nil || len(queryEmbedding) == 0 {
			log.Errorf("create question embedding error, err: %v", err)
		} else {
			// 向量查询
			queryVecCondition := MemoryQueryCondition{
				TopK:           workspaceQueryTopK,                        // 多查询一条以判断是否超过阈值
				Scopes:         []string{definition.MemoryWorkspaceScope}, // 查询 workspace 类型的记忆
				ScopeId:        workspacePath,                             // 使用当前项目的 scopeId 进行精确匹配
				QueryEmbedding: queryEmbedding[0],                         // 使用问题的向量表示
				Type:           definition.LongTermMemoryType,
				UserId:         userId,
			}
			vecThreshold := experiment.ConfigService.GetDoubleConfigWithEnv(definition.ExperimentKeyMemoryVectorRetrieveThreshold, experiment.ConfigScopeClient, 0.01)
			s.queryMemory("vector-query", memoryMap, queryVecCondition, vecThreshold, true, embeddingFilter)
		}
		if len(queryEmbedding) > 0 {
			// 查询当前会话的短期记忆
			stmQueryVecCondition := MemoryQueryCondition{
				TopK:           shortTermQueryTopK,                        // 多查询一条以判断是否超过阈值
				Scopes:         []string{definition.MemoryWorkspaceScope}, // 查询 workspace 类型的记忆
				ScopeId:        workspacePath,                             // 使用当前项目的 scopeId 进行精确匹配
				QueryEmbedding: queryEmbedding[0],                         // 使用问题的向量表示
				Type:           definition.ShortTermMemoryType,
				SessionID:      sessionId,
				UserId:         userId,
			}
			s.queryMemory("stm-vector-query", memoryMap, stmQueryVecCondition, 0, false, embeddingFilter)
		}
	}, time.Millisecond*700)
	if err != nil {
		log.Debugf("[memory]-[service] query memory by embedding timed out.")
	}
}

func (s *MemoryServiceImpl) queryMemory(logPrefix string, memoryMap *collection.SynchronizedMap[string, MemoryRecord],
	condition MemoryQueryCondition, minDistance float64, logDetail bool, filter func(record *MemoryRecord) bool) {
	// 查询全局记忆
	data, err := s.client.Query(condition)
	if err != nil {
		log.Errorf("[memory]-[service] %s query memory error: %v", logPrefix, err)
		return
	}
	sb := strings.Builder{}
	for _, record := range data.Records {
		// 相似度越高的，距离越小，cosine值需要取反
		similarScore := 1 - record.Distance
		if logDetail {
			sb.WriteString(fmt.Sprintf("[%s] %s score:%.4f\n", record.ID, record.Title, similarScore))
		}
		if filter != nil && !filter(&record) {
			continue
		}
		if minDistance > 0.00001 && similarScore < minDistance {
			log.Debugf("[memory]-[service] %s query memory result distance too small, skip: %f title: %s", logPrefix, similarScore, record.Title)
			continue
		}
		if oldRecord, ok := memoryMap.Get(record.ID); !ok || oldRecord.Distance == 0 {
			// 如果不存在或者距离为0，则插入/替换
			memoryMap.Set(record.ID, record)
		}
	}
	log.Debugf("[memory]-[service] %s query memory results: %d", logPrefix, len(data.Records))
	if logDetail {
		log.Debugf("[memory]-[service] %s query memory results detail: %s", logPrefix, sb.String())
	}
}

// Insert 实现MemoryService接口，插入单条记忆
func (s *MemoryServiceImpl) Insert(record MemoryRecord) error {
	// 插入前对MemoryRecord的Content生成MemoryEmbedding
	if len(record.Content) > 0 && len(record.MemoryEmbedding) == 0 {
		embeddings, err := s.client.DoEmbed([]string{record.Content})
		if err != nil {
			log.Errorf("[memory]-[service] generate embedding for memory content error: %v", err)
			return fmt.Errorf("generate embedding error: %w", err)
		}

		if len(embeddings) > 0 {
			record.MemoryEmbedding = embeddings[0]
		}
	}

	return s.client.Save(record)
}

// Update 实现MemoryService接口，更新单条记忆
func (s *MemoryServiceImpl) Update(record MemoryRecord) error {
	return s.client.Update(record)
}

// UpdatePlus 实现MemoryService接口，更新单条记忆
func (s *MemoryServiceImpl) UpdatePlus(record MemoryRecord, forceUpdateFields map[string]bool) error {
	return s.client.UpdatePlus(record, forceUpdateFields)
}

// BatchInsert 实现MemoryService接口，批量插入记忆
func (s *MemoryServiceImpl) BatchInsert(records []MemoryRecord) error {
	if len(records) == 0 {
		return errors.New("records is empty")
	}

	// 插入前对MemoryRecord的Content生成MemoryEmbedding
	var contentsToEmbed []string
	var recordsNeedEmbedding []int

	// 收集需要生成嵌入的内容
	for i, record := range records {
		if len(record.Content) > 0 && len(record.MemoryEmbedding) == 0 {
			contentsToEmbed = append(contentsToEmbed, record.Content)
			recordsNeedEmbedding = append(recordsNeedEmbedding, i)
		}
	}

	// 如果有需要生成嵌入的内容，则生成嵌入
	if len(contentsToEmbed) > 0 {
		embedder := components.NewLingmaEmbedder()
		embeddings, err := embedder.CreateEmbedding(context.Background(), contentsToEmbed, components.TextTypeDocument)
		//embeddings, err := s.client.DoEmbed(contentsToEmbed)
		if err != nil {
			log.Errorf("[memory]-[service] batch generate embedding for memory content error: %v", err)
			return fmt.Errorf("batch generate embedding error: %w", err)
		}

		// 将生成的嵌入分配给对应的记录
		for i, idx := range recordsNeedEmbedding {
			if i < len(embeddings) {
				records[idx].MemoryEmbedding = embeddings[i]
			}
		}
	}

	// 执行批量插入
	err := s.client.BatchInsert(records)
	if err != nil {
		log.Errorf("[memory]-[service] batch insert memory error: %v", err)
		return err
	}

	return nil
}

// Delete 实现MemoryService接口，删除记忆
func (s *MemoryServiceImpl) Delete(ids []string) error {
	if len(ids) == 0 {
		return errors.New("ids is empty")
	}

	// 构建要删除的记录列表
	records := make([]MemoryRecord, 0, len(ids))
	for _, id := range ids {
		records = append(records, MemoryRecord{
			ID: id,
		})
	}

	// 执行批量删除
	err := s.client.BatchDelete(records, DeleteStrategyByID) // 使用ID策略删除
	if err != nil {
		log.Errorf("[memory]-[service] batch delete memory error: %v", err)
		return err
	}

	return nil
}

// DeleteByCategoryAndSessionID 实现MemoryService接口，删除记忆
func (s *MemoryServiceImpl) DeleteByCategoryAndSessionID(category, sessionId string) error {
	// 执行删除
	err := s.client.Delete(MemoryRecord{Category: category, SessionID: sessionId}, DeleteStrategyByCategoryAndSessionID)
	if err != nil {
		log.Errorf("[memory]-[service] delete memory error: %v", err)
		return err
	}

	return nil
}

// CountRecord 实现MemoryService接口，统计记忆记录数量
func (s *MemoryServiceImpl) CountRecord(workspacePath string) (int, error) {
	validRecordCount := s.client.GetValidRecordCountForScopeId(workspacePath)
	return validRecordCount, nil
}

// QueryWithPagination 实现MemoryService接口，分页查询记忆
func (s *MemoryServiceImpl) QueryWithPagination(condition MemoryQueryCondition) (MemoryQueryResult, error) {
	return s.client.QueryWithPagination(condition)
}

// CountMemoryRecords 实现MemoryService接口，统计符合条件的记录数量
func (s *MemoryServiceImpl) CountMemoryRecords(condition MemoryQueryCondition) (int, error) {
	// 检查 client 是否为 nil
	if s.client == nil {
		log.Errorf("[memory]-[service] client is nil")
		return 0, fmt.Errorf("memory client is nil")
	}
	return s.client.CountMemoryRecords(condition)
}
