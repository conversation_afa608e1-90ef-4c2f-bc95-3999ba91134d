package experience

import (
	"context"
	"cosy/chat/chains/common"
	"cosy/chat/service"
	"cosy/definition"
	"cosy/log"
	"cosy/memory/ltm"
	"cosy/memory/storage"
	"cosy/memory/util"
	"cosy/prompt"
	"cosy/util/collection"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tmc/langchaingo/callbacks"
	"github.com/tmc/langchaingo/chains"
	"github.com/tmc/langchaingo/memory"
	"github.com/tmc/langchaingo/schema"
	"math"
	"regexp"
	"strings"
	"time"

	agentDefinition "code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
)

const (
	// 阈值，低于该值的文件不进行存储
	minValidScore = 3
)

type relatedFilesOutputData struct {
	Score            float64
	Title            string
	Keywords         []string
	ScenarioText     string
	Scenario         []string
	RelatedFilesText string
	RelatedFiles     []string
}

// ExtractRelatedFileMemoryChain 将对话中引用的文件存储到记忆
type ExtractRelatedFileMemoryChain struct {
}

func (c ExtractRelatedFileMemoryChain) Call(ctx context.Context, inputs map[string]any, options ...chains.ChainCallOption) (map[string]any, error) {
	requestId := inputs[common.KeyRequestId].(string)
	sessionId := inputs[common.KeySessionId].(string)
	title, keywords, content, err := extractRelateFilesFromMessage(ctx, inputs, sessionId, requestId)
	if err != nil {
		return inputs, err
	}
	memoryItem := definition.LongTermMemory{
		MergeIds: []string{},
		Title:    title,
		Source:   definition.MemoryAutoSource,
		Scope:    definition.MemoryWorkspaceScope,
		Content:  content,
		Keywords: keywords,
		Freq:     0,
		Category: definition.MemoryCategoryTaskReferenceFiles,
	}
	records := ltm.ConvertMemoryRecord(ctx, map[string]any{}, []definition.LongTermMemory{memoryItem})
	if len(records) > 0 {
		go func() {
			defer func() {
				if err := recover(); err != nil {
					log.Errorf("[memory]-[ltm] memory extract panic: %v", err)
				}
			}()
			_ = ltm.DeleteMemoryRecordsBySession(definition.MemoryCategoryTaskReferenceFiles, sessionId)
			_ = ltm.SaveMemoryRecordsToDB(records)
		}()
		if existRecords, ok := inputs[common.KeyNewLongTermMemories].([]storage.MemoryRecord); ok {
			existRecords = append(existRecords, records...)
			inputs[common.KeyNewLongTermMemories] = existRecords
		} else {
			inputs[common.KeyNewLongTermMemories] = records
		}
	}
	return inputs, nil
}

// extractRelateFilesFromMessage 从对话中抽取引用的文件
func extractRelateFilesFromMessage(ctx context.Context, inputs map[string]any, sessionId, requestId string) (string, []string, string, error) {
	messages, ok := inputs[common.KeyAgentMessages].([]definition.ChatMessage)
	var err error
	if !ok {
		messages, err = service.SessionServiceManager.GetChatMessageBySession(sessionId)
		if err != nil {
			return "", []string{}, "", err
		}
	}
	messageSummery := extractToolUsedFiles(messages, err)

	// 本轮会话新增的文件
	newFiles := getNewFiles(ctx, sessionId)
	result, err := summaryRelatedFiles(ctx, messageSummery, sessionId, requestId)
	if err != nil {
		return "", []string{}, "", err
	}

	relatedFiles := collection.Difference(result.RelatedFiles, newFiles)
	log.Debugf("[memory]-[ltm] memory extract %d relate files", len(relatedFiles))
	if len(relatedFiles) == 0 {
		return "", []string{}, "", errors.New("no related files with new file excluded")
	}

	content := fmt.Sprintf("%s:\n%s\n\n%s:\n%s\n", result.ScenarioText, strings.Join(result.Scenario, "\n"),
		result.RelatedFilesText, strings.Join(relatedFiles, "\n"))

	return result.Title, result.Keywords, content, nil
}

// extractToolUsedFiles 从对话中抽取关键信息和工具调用的文件列表
func extractToolUsedFiles(messages []definition.ChatMessage, err error) string {
	messageSummery := ""
	for _, message := range messages {
		var messageBody map[string]interface{}
		err = json.Unmarshal([]byte(message.Content), &messageBody)
		if err != nil {
			continue
		}
		if message.Role == "tool" {
			rawData, success := fetchToolCallRawData(messageBody)
			if !success {
				continue
			}
			messageSummery += fmt.Sprintf("\n[tool]\nTool type: %s\n", messageBody["name"])
			switch messageBody["name"] {
			case "edit_file", "read_file", "create_file", "search_replace":
				filePath, success := getWithAlternativeField(rawData, "filePath", "FilePath", "path").(string)
				if success {
					content, success := messageBody["content"].(string)
					if success {
						messageSummery += fmt.Sprintf("File path: %s\nContent: %s", filePath, content)
					}
				}
			case "get_problems":
				problems, success := rawData["problems"].([]interface{})
				if !success {
					continue
				}
				messageSummery += "Problems stack:\n"
				for _, problem := range problems {
					problemMap, success := problem.(map[string]interface{})
					if !success {
						continue
					}
					filePath, success := problemMap["filePath"].(string)
					if success {
						messageSummery += fmt.Sprintf("- %s\n", filePath)
					}
				}
			case "list_dir":
				files, success := rawData["files"].([]interface{})
				if !success {
					continue
				}
				messageSummery += "File list:\n"
				for _, file := range files {
					fileMap, success := file.(map[string]interface{})
					if !success {
						continue
					}
					isDirectory, success := getWithAlternativeField(fileMap, "isDirectory", "is_directory").(bool)
					if !isDirectory && success {
						path, success := fileMap["path"].(string)
						if success {
							messageSummery += fmt.Sprintf("- %s\n", path)
						}
					}
				}
			case "search_symbol":
				result, success := rawData["result"].(map[string]interface{})
				if !success {
					continue
				}
				symbols, success := result["symbols"].([]interface{})
				if !success {
					continue
				}
				messageSummery += "Checked files:\n"
				for _, symbol := range symbols {
					symbolMap, success := symbol.(map[string]interface{})
					if !success {
						continue
					}
					filePath, success := getWithAlternativeField(symbolMap, "filePath", "FilePath").(string)
					if success {
						messageSummery += fmt.Sprintf("- %s\n", filePath)
					}
				}
			case "search_file":
				files, success := rawData["files"].([]interface{})
				if !success {
					continue
				}
				messageSummery += "Found files:\n"
				for _, file := range files {
					fileMap, success := file.(map[string]interface{})
					if !success {
						continue
					}
					path, success := fileMap["path"].(string)
					if success {
						messageSummery += fmt.Sprintf("- %s\n", path)
					}
				}
			}
		} else {
			content, success := messageBody["content"].(string)
			if success && len(content) > 0 {
				messageSummery += fmt.Sprintf("\n[%s]\nContent: %s\n", message.Role, content)
			}
		}
	}
	return messageSummery
}

// getWithAlternativeField 从map中获取字段，如果字段不存在，则尝试获取另一个字段
func getWithAlternativeField(data map[string]any, fields ...string) any {
	for _, field := range fields {
		if value, ok := data[field]; ok {
			return value
		}
	}
	return nil
}

// getNewFiles 查询当次对话中新增加的文件
func getNewFiles(ctx context.Context, sessionId string) []string {
	files, err := service.WorkingSpaceServiceManager.GetCurrentWorkingSpaceFiles(ctx, sessionId)
	if err != nil {
		return []string{}
	}
	return collection.Unique(
		collection.Map(
			collection.Filter(files, func(file definition.WorkingSpaceFile) bool { return file.Mode == service.ADD }),
			func(file definition.WorkingSpaceFile) string { return file.FileId },
		),
	)
}

// fetchToolCallRawData 从toolCallBody中提取rawData字段
func fetchToolCallRawData(body map[string]interface{}) (map[string]interface{}, bool) {
	extra, success := body["extra"].(map[string]interface{})
	if !success {
		return nil, false
	}
	toolCallResult, success := extra["toolCallResult"].(map[string]interface{})
	if !success {
		return nil, false
	}
	rawData, success := toolCallResult["rawData"].(map[string]interface{})
	if !success {
		return nil, false
	}
	return rawData, true
}

// summaryRelatedFiles 根据查看、编辑和检索过的文件，生成相关文件列表
func summaryRelatedFiles(ctx context.Context, messageSummery, sessionId, requestId string) (relatedFilesOutputData, error) {
	messages, err := buildRelatedFilesRequest(prompt.BaseInput{
		SessionId: sessionId,
		RequestId: requestId,
	}, messageSummery)
	if err != nil {
		log.Debugf("build Related files request error, reason=%v", err)
		return relatedFilesOutputData{}, err
	}
	inputs := map[string]any{common.KeySessionId: sessionId, common.KeyRequestId: requestId}
	outputResp, err := util.InvokeMemoryModel(ctx, inputs, messages, 120*time.Second)
	if err != nil {
		log.Debugf("extract Related files memory error, reason=%v, requestId=%s", err, requestId)
		return relatedFilesOutputData{}, err
	}
	log.Debugf("extract Related files memory requestId: %s, modelRequestId: %s, output: %s", requestId, outputResp.RequestId, outputResp.Text)
	result, err := parseRelatedFilesModelOutput(outputResp.Text)
	if err != nil {
		log.Debugf("parse Related files memory error, reason=%v, requestId=%s", err, requestId)
		return relatedFilesOutputData{}, err
	}
	log.Debugf("related file experience quality score=%f, requestId=%s", result.Score, requestId)
	if result.Score < minValidScore {
		return relatedFilesOutputData{}, fmt.Errorf("related files quality score %f is too low", result.Score)
	}

	return result, nil
}

func buildRelatedFilesRequest(input prompt.BaseInput, messageSummery string) (
	[]*agentDefinition.Message, error) {
	promptInput := prompt.LongTermMemoryWorkflowPromptInput{
		BaseInput: prompt.BaseInput{
			RequestId:         input.RequestId,
			PreferredLanguage: input.PreferredLanguage,
		},
		CurrentMessages: messageSummery,
	}
	systemPrompt, err := prompt.Engine.RenderRelatedFilesSystemPrompt(promptInput)
	if err != nil {
		return nil, err
	}
	userPrompt, err := prompt.Engine.RenderRelatedFilesUserPrompt(promptInput)
	if err != nil {
		return nil, err
	}
	return util.BuildSystemUserMessage(systemPrompt, userPrompt), nil
}

// parseRelatedFilesModelOutput 解析任务执行过程模型输出
func parseRelatedFilesModelOutput(text string) (relatedFilesOutputData, error) {
	var result relatedFilesOutputData

	// 提取场景内容
	scenarioRegex := regexp.MustCompile(`(?s)<scenario>(.*?)</scenario>`)
	scenarioMatch := scenarioRegex.FindStringSubmatch(text)
	if len(scenarioMatch) <= 1 {
		return result, errors.New("scenario content not found")
	}
	scenarioContent := scenarioMatch[1]

	// 提取标题
	titleRegex := regexp.MustCompile(`Title:\s*(.*?)(?:$|\n)`)
	titleMatch := titleRegex.FindStringSubmatch(scenarioContent)
	if len(titleMatch) > 1 {
		result.Title = strings.TrimSpace(titleMatch[1])
	}

	// 提取关键词
	keywordsRegex := regexp.MustCompile(`Keywords:\s*(.*?)(?:$|\n)`)
	keywordsMatch := keywordsRegex.FindStringSubmatch(scenarioContent)
	if len(keywordsMatch) > 1 {
		keywordsStr := strings.TrimSpace(keywordsMatch[1])
		keywords := strings.Split(keywordsStr, ",")
		result.Keywords = make([]string, 0, len(keywords))
		for _, k := range keywords {
			trimmed := strings.TrimSpace(k)
			if trimmed != "" {
				result.Keywords = append(result.Keywords, trimmed)
			}
		}
	}

	// 提取场景列表
	contentRegex := regexp.MustCompile(`Scenario:(?:\s*\n|\s*)([^\n]*)\n([\s\S]*)`)
	contentMatch := contentRegex.FindStringSubmatch(scenarioContent)
	if len(contentMatch) > 1 {
		result.ScenarioText = strings.TrimSpace(contentMatch[1])
		if len(contentMatch) > 2 {
			content := strings.TrimSpace(contentMatch[2])
			result.Scenario = collection.Filter(strings.Split(content, "\n"),
				func(path string) bool { return path != "" })
		}
	}

	// 提取分数
	scenarioScore, err := parseScore(scenarioContent)
	if err != nil {
		return result, err
	}

	// 提取场景内容
	relatedFilesRegex := regexp.MustCompile(`(?s)<related_files>(.*?)</related_files>`)
	relatedFilesMatch := relatedFilesRegex.FindStringSubmatch(text)
	if len(relatedFilesMatch) <= 1 {
		return result, errors.New("related files content not found")
	}
	relatedFilesContent := relatedFilesMatch[1]

	// 提取文件列表
	contentRegex = regexp.MustCompile(`RelatedFiles:(?:\s*\n|\s*)([^\n]*)\n([\s\S]*)`)
	contentMatch = contentRegex.FindStringSubmatch(relatedFilesContent)
	if len(contentMatch) > 1 {
		result.RelatedFilesText = strings.TrimSpace(contentMatch[1])
		if len(contentMatch) > 2 {
			content := strings.TrimSpace(contentMatch[2])
			result.RelatedFiles = collection.Filter(strings.Split(content, "\n"),
				func(path string) bool { return path != "" })
		}
	}

	// 提取分数
	relatedFilesScore, err := parseScore(relatedFilesContent)
	if err != nil {
		return result, err
	}
	result.Score = math.Min(scenarioScore, relatedFilesScore)

	// 验证结果
	if result.Title == "" || len(result.Keywords) == 0 || len(result.RelatedFiles) == 0 || len(result.Scenario) == 0 {
		return result, errors.New("incomplete related-file data")
	}

	return result, nil
}

func parseScore(text string) (float64, error) {
	// 提取分数
	scoreRegex := regexp.MustCompile(`Score:\s*(\d+)分?`)
	scoreMatch := scoreRegex.FindStringSubmatch(text)
	if len(scoreMatch) > 1 {
		score := 0
		var err error
		if scoreMatch[1] != "" {
			_, err = fmt.Sscanf(scoreMatch[1], "%d", &score)
		} else if len(scoreMatch) > 2 && scoreMatch[2] != "" {
			_, err = fmt.Sscanf(scoreMatch[2], "%d", &score)
		} else {
			return 0, errors.New("invalid score")
		}
		if err == nil {
			return float64(score), nil
		}
	}
	return 0, errors.New("invalid content")
}

func (c ExtractRelatedFileMemoryChain) GetMemory() schema.Memory {
	return memory.NewSimple()
}

func (c ExtractRelatedFileMemoryChain) GetInputKeys() []string {
	return []string{}
}

func (c ExtractRelatedFileMemoryChain) GetOutputKeys() []string {
	return []string{}
}

func (c ExtractRelatedFileMemoryChain) GetCallbackHandler() callbacks.Handler { //nolint:ireturn
	return nil
}
