package eval

import (
	"strconv"
	"strings"
)

// MemoryAnalysis 表示单条记忆的分析结果
type MemoryAnalysis struct {
	Analysis      string // 分析内容
	MemoryID      int    // 记忆ID
	UsageLevel    int    // 使用程度
	QualityImpact int    // 质量影响
}

// OverallEvaluation 表示总体评估结果
type OverallEvaluation struct {
	TotalScore  int    // 总分
	Suggestions string // 建议
}

// EvaluationResult 包含完整的评估结果
type EvaluationResult struct {
	Analyses []MemoryAnalysis  // 记忆分析列表
	Overall  OverallEvaluation // 总体评估
}

func ParseMemoryEvalOutput(output string) *EvaluationResult {
	// 首先移除开头的记忆分析标记
	output = strings.TrimPrefix(output, "===记忆分析===")
	output = strings.TrimSpace(output)

	// 分割记忆分析和总体评估部分
	parts := strings.Split(output, "===总体评估===")
	if len(parts) == 0 {
		return nil
	}

	// 处理记忆分析部分
	analysisText := strings.TrimSpace(parts[0])
	var analyses []string

	// 使用空行分割每条记忆分析
	currentAnalysis := ""
	for _, line := range strings.Split(analysisText, "\n") {
		if strings.TrimSpace(line) == "" {
			if currentAnalysis != "" {
				analyses = append(analyses, strings.TrimSpace(currentAnalysis))
				currentAnalysis = ""
			}
		} else {
			if currentAnalysis != "" {
				currentAnalysis += "\n"
			}
			currentAnalysis += line
		}
	}
	// 添加最后一条分析（如果有）
	if currentAnalysis != "" {
		analyses = append(analyses, strings.TrimSpace(currentAnalysis))
	}

	// 处理总体评估部分
	overallEval := ""
	if len(parts) > 1 {
		overallEval = "===总体评估===\n" + strings.TrimSpace(parts[1])
	}

	// 调用ParseMemoryEvaluation进行结构化
	return ParseMemoryEvaluation(analyses, overallEval)
}

// ParseMemoryEvaluation 解析并结构化记忆评估信息
func ParseMemoryEvaluation(analyses []string, overallEval string) *EvaluationResult {
	result := &EvaluationResult{
		Analyses: make([]MemoryAnalysis, 0),
	}

	// 解析每条记忆分析
	for _, analysis := range analyses {
		memAnalysis := parseMemoryAnalysis(analysis)
		if memAnalysis != nil {
			result.Analyses = append(result.Analyses, *memAnalysis)
		}
	}
	if len(result.Analyses) == 0 {
		return nil
	}

	// 解析总体评估
	if overallEval != "" {
		result.Overall = parseOverallEvaluation(overallEval)
	}

	return result
}

// parseMemoryAnalysis 解析单条记忆分析
func parseMemoryAnalysis(content string) *MemoryAnalysis {
	var analysis MemoryAnalysis

	// 使用正则表达式或字符串处理来提取信息
	lines := strings.Split(content, "\n")
	validCount := 0
	for _, line := range lines {
		line = strings.TrimSpace(line)

		// 提取分析内容
		if strings.HasPrefix(line, "分析:") {
			analysis.Analysis = strings.TrimPrefix(line, "分析:")
			analysis.Analysis = strings.TrimSpace(analysis.Analysis)
			validCount++
			continue
		}

		// 提取记忆ID
		if strings.HasPrefix(line, "记忆ID:") {
			idStr := strings.TrimPrefix(line, "记忆ID:")
			idStr = strings.TrimSpace(idStr)
			id, err := strconv.Atoi(idStr)
			if err == nil {
				analysis.MemoryID = id
				validCount++
			}
			continue
		}

		// 提取使用程度
		if strings.HasPrefix(line, "使用程度:") {
			levelStr := strings.TrimPrefix(line, "使用程度:")
			levelStr = strings.TrimSpace(levelStr)
			level, err := strconv.Atoi(levelStr)
			if err == nil {
				analysis.UsageLevel = level
				validCount++
			}
			continue
		}

		// 提取质量影响
		if strings.HasPrefix(line, "质量影响:") {
			impactStr := strings.TrimPrefix(line, "质量影响:")
			impactStr = strings.TrimSpace(impactStr)
			impact, err := strconv.Atoi(impactStr)
			if err == nil {
				analysis.QualityImpact = impact
				validCount++
			}
			continue
		}
	}
	if validCount < 4 {
		return nil
	}

	// 只有当至少有分析内容和记忆ID时才返回结果
	if analysis.Analysis != "" && analysis.MemoryID > 0 {
		return &analysis
	}
	return nil
}

// parseOverallEvaluation 解析总体评估
func parseOverallEvaluation(content string) OverallEvaluation {
	var eval OverallEvaluation

	lines := strings.Split(content, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)

		// 提取总分
		if strings.HasPrefix(line, "总分:") {
			scoreStr := strings.TrimPrefix(line, "总分:")
			scoreStr = strings.TrimSpace(scoreStr)
			scoreStr = strings.TrimSuffix(scoreStr, "分")
			score, err := strconv.Atoi(scoreStr)
			if err == nil {
				eval.TotalScore = score
			}
			continue
		}

		// 提取建议
		if strings.HasPrefix(line, "建议:") {
			eval.Suggestions = strings.TrimPrefix(line, "建议:")
			eval.Suggestions = strings.TrimSpace(eval.Suggestions)
			continue
		}
	}

	return eval
}
