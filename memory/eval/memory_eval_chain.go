package eval

import (
	"context"
	chainUtil "cosy/chat/chains/chain"
	"cosy/chat/chains/common"
	"cosy/chat/service"
	"cosy/definition"
	"cosy/experiment"
	"cosy/log"
	"cosy/memory/storage"
	"cosy/memory/util"
	"cosy/prompt"
	"cosy/sls"
	cosyUtil "cosy/util"
	"encoding/json"
	"fmt"
	"math"
	"regexp"
	"strconv"
	"strings"
	"time"

	agentDefinition "code.alibaba-inc.com/cosy/lingma-agent-graph/definition"

	"github.com/tmc/langchaingo/callbacks"
	"github.com/tmc/langchaingo/chains"
	"github.com/tmc/langchaingo/memory"
	"github.com/tmc/langchaingo/schema"
)

var (
	// codeBlockRegex 匹配代码块
	codeBlockRegex = regexp.MustCompile("```\\w*\\n([\\s\\S]*?)```")
)

// MemoryEvalChain 记忆效果的评估
type MemoryEval<PERSON>hain struct {
}

func NewMemoryEvalChain() *MemoryEvalChain {
	return &MemoryEvalChain{}
}

func (c MemoryEvalChain) Call(ctx context.Context, inputs map[string]any, options ...chains.ChainCallOption) (map[string]any, error) {
	if !experiment.ConfigService.GetBoolConfigWithEnv(definition.ExperimentMemoryEvalEnable, experiment.ConfigScopeClient, true) {
		return inputs, nil
	}
	requestId := inputs[common.KeyRequestId].(string)
	allowEval, err := c.checkAllowEval(ctx, inputs)
	if err != nil {
		log.Debugf("check allow eval error, reason=%v, requestId=%s", err, requestId)
		return inputs, nil
	}
	if !allowEval {
		return inputs, nil
	}
	startTime := time.Now()
	messages, index2RecordMap, err := c.buildEvalRequest(requestId, inputs)
	if err != nil {
		log.Debugf("build eval request error, reason=%v, requestId=%s", err, requestId)
		return inputs, nil
	}
	go func() {
		outputResp, err := util.InvokeMemoryModel(ctx, inputs, messages, 30*time.Second)
		if err != nil {
			log.Debugf("eval memory error, reason=%v, requestId=%s", err, requestId)
			return
		}
		outputText := outputResp.Text
		evalResult := c.parseOutput(inputs, outputText, index2RecordMap, outputResp.RequestId)
		if evalResult == nil {
			log.Debugf("[memory]-[eval] eval memory result is nil, model requestId: %s", outputResp.RequestId)
			return
		}
		log.Debugf("[memory]-[eval] eval memory result: %d, model requestId: %s time cost: %s", evalResult.Overall.TotalScore, outputResp.RequestId, time.Since(startTime))
		log.Debugf("[memory]-[eval] eval memory output: %v", outputText)
	}()
	return inputs, nil
}

// checkAllowEval 检查是否允许评估
func (c MemoryEvalChain) checkAllowEval(ctx context.Context, inputs map[string]any) (bool, error) {
	if !experiment.ConfigService.GetBoolConfigWithEnv(definition.ExperimentMemoryFetchEnable, experiment.ConfigScopeClient, true) {
		// 是否启用重构查询的配置项
		return false, nil
	}
	if !experiment.ConfigService.GetBoolConfigWithEnv(definition.ExperimentMemoryChatEditEnable, experiment.ConfigScopeClient, true) {
		if chainUtil.IsChatOrEditAgent(inputs) {
			return false, nil
		}
	}
	retrieveMemoryRecords, _ := inputs[common.KeyLongTermMemories].([]storage.MemoryRecord)
	return len(retrieveMemoryRecords) > 0, nil
}

// buildEvalRequest 构建请求
func (r MemoryEvalChain) buildEvalRequest(requestId string, inputs map[string]any) ([]*agentDefinition.Message, map[string]storage.MemoryRecord, error) {
	systemPrompt, err := prompt.Engine.RenderMemoryEvalSystemPrompt(prompt.BaseInput{RequestId: requestId})
	if err != nil {
		return nil, nil, err
	}
	userPrompt, index2RecordMap, err := r.renderUserPrompt(inputs)
	if err != nil {
		return nil, nil, err
	}
	return util.BuildSystemUserMessage(systemPrompt, userPrompt), index2RecordMap, nil
}

// renderUserPrompt 渲染用户输入
// 问题: XXXX
// 记忆:
// <content id="1">用户在GitHub仓库中使用Java Spring框架</content>
// <content id="2">产品需求文档应包含用户故事、验收标准等要素</content>
// <content id="3">团队采用敏捷开发方法论</content>
//
// 回答: XXX
func (r MemoryEvalChain) renderUserPrompt(inputs map[string]any) (string, map[string]storage.MemoryRecord, error) {
	var conversation string
	var err error
	if !chainUtil.IsChatOrEditAgent(inputs) {
		conversation, err = r.buildMessagePrompt(inputs)
	}
	if conversation == "" || err != nil {
		conversation, err = r.buildChatPrompt(inputs)
	}
	sb := strings.Builder{}
	retrieveMemoryRecords, _ := inputs[common.KeyLongTermMemories].([]storage.MemoryRecord)
	index2RecordMap := make(map[string]storage.MemoryRecord)
	index := 0
	for _, item := range retrieveMemoryRecords {
		index++
		id := fmt.Sprintf("%d", index)
		index2RecordMap[id] = item
		sb.WriteString(fmt.Sprintf("<content id=\"%s\">\n%s\n</content>\n", id, item.Content))
	}
	userPrompt := fmt.Sprintf(`对话消息：
<conversation>
%s
</conversation>

记忆:
<memories>
%s
</memories>

**注意：**
- 如果对话中没有明确遵循和使用记忆信息，禁止编造虚假评分
- 如果对话没有遵循记忆规则，只影响使用程度，即0分，不影响质量分`, conversation, sb.String())
	return userPrompt, index2RecordMap, nil
}

// buildChatPrompt 构建聊天提示
func (r MemoryEvalChain) buildChatPrompt(inputs map[string]any) (string, error) {
	sessionId := inputs[common.KeySessionId].(string)
	rawInputParams := inputs[common.KeyChatAskParams].(*definition.AskParams)
	userQuery := chainUtil.GetUserInputQuery(rawInputParams.ChatContext)
	userQuery = codeBlockRegex.ReplaceAllString(userQuery, "")
	if newQuery, ok := inputs[common.KeyRefinedGenernalQuery]; ok {
		userQuery = newQuery.(string)
	}
	validRecords, _ := service.SessionServiceManager.GetSessionRecordsForHistoryBuild(sessionId)
	if validRecords == nil || len(validRecords) <= 0 {
		return "", fmt.Errorf("sessionId=%s, no valid records", sessionId)
	}
	currentRecord := validRecords[len(validRecords)-1]
	answer := currentRecord.Answer
	sb := strings.Builder{}
	sb.WriteString(fmt.Sprintf("[user]: %s\n", userQuery))
	sb.WriteString(fmt.Sprintf("[assistant]: %s\n", answer))
	return sb.String(), nil
}

// buildMessagePrompt 构建消息提示
func (r MemoryEvalChain) buildMessagePrompt(inputs map[string]any) (string, error) {
	requestId := inputs[common.KeyRequestId].(string)
	messages, err := service.SessionServiceManager.GetChatMessageByRequest(requestId)
	if err != nil {
		return "", err
	}
	if len(messages) <= 0 {
		return "", fmt.Errorf("sessionId=%s, no messages", requestId)
	}
	flatMessage := util.FlattenConversation(messages)
	return flatMessage, nil
}

// parseOutput 解析输出
func (r MemoryEvalChain) parseOutput(inputs map[string]any, outputText string, index2RecordMap map[string]storage.MemoryRecord, modelRequestId string) *EvaluationResult {
	requestId := inputs[common.KeyRequestId].(string)
	sessionId := inputs[common.KeySessionId].(string)
	// 获取存储服务
	memoryService := storage.GetMemoryService()
	if memoryService == nil {
		log.Warnf("memory service not initialized, requestId=%s", requestId)
		return nil
	}
	parseResult := ParseMemoryEvalOutput(outputText)
	if parseResult == nil {
		log.Warnf("parse memory eval output error, requestId=%s", requestId)
		return nil
	}
	evalData := map[string]any{}
	evalData["totalScore"] = strconv.Itoa(parseResult.Overall.TotalScore)
	evalData["suggestions"] = parseResult.Overall.Suggestions
	evalItems := []map[string]any{}
	for _, analysis := range parseResult.Analyses {
		evalItem := map[string]any{}
		record, ok := index2RecordMap[strconv.Itoa(analysis.MemoryID)]
		if !ok {
			continue
		}
		evalItem["analysis"] = analysis.Analysis
		evalItem["memoryId"] = record.ID
		evalItem["memoryCategory"] = record.Category
		evalItem["memoryScope"] = record.Scope
		evalItem["memorySource"] = record.Source
		evalItem["memoryFreq"] = record.Freq
		evalItem["memoryUsageLevel"] = strconv.Itoa(analysis.UsageLevel)
		evalItem["memoryQualityImpact"] = strconv.Itoa(analysis.QualityImpact)
		// 记忆内容回流需要内容安全评估
		//evalItem["memoryContent"] = record.Content
		evalItem["memoryRetrieveDistance"] = strconv.FormatFloat(record.Distance, 'f', 5, 64)
		evalItem["memoryQualityScore"] = strconv.FormatFloat(record.QualityScore, 'f', 5, 64)
		evalItem["memoryRetentionScore"] = strconv.FormatFloat(record.RetentionScore, 'f', 5, 64)
		evalItem["memoryForgetCount"] = strconv.Itoa(record.ForgetCount)
		evalItem["memoryTitle"] = record.Title
		evalItem["memoryKeyword"] = record.Keywords

		evalItems = append(evalItems, evalItem)

		scoreBefore := record.QualityScore
		record.QualityScore = UpdateQualityScoreByEvalAnalysis(analysis, scoreBefore)
		if scoreBefore != record.QualityScore {
			// 更新记录
			log.Debugf("[memory]-[eval] update memory %s quality score: %f -> %f", record.ID, scoreBefore, record.QualityScore)
			err := memoryService.Update(record)
			if err != nil {
				log.Errorf("[memory]-[eval] failed to update memory %s after updating quality score: %v", record.ID, err)
			}
		}
	}
	evalData["evalItems"] = evalItems
	evalDataJson, _ := json.Marshal(evalData)
	eventData := map[string]string{
		"requestId":      requestId,
		"sessionId":      sessionId,
		"totalScore":     strconv.Itoa(parseResult.Overall.TotalScore),
		"modelRequestId": modelRequestId,
		"evalData":       string(evalDataJson),
	}
	go sls.Report(sls.EventTypeChatAgentMemoryEval, requestId, eventData)

	return parseResult
}

// UpdateQualityScoreByEvalAnalysis 根据评估分析更新记忆质量分
// $Delta = $Use * $Quality \in [-4,4]
func UpdateQualityScoreByEvalAnalysis(analysis MemoryAnalysis, score float64) float64 {
	impactScore := float64(analysis.UsageLevel * analysis.QualityImpact)
	realQualityScore := score + impactScore
	newScore := 0.0
	if realQualityScore > 0 {
		newScore = transformedSin(realQualityScore)
	} else if realQualityScore < 0 {
		newScore = transformedSinLessZero(realQualityScore)
	}
	return cosyUtil.NormalizeFloat(newScore, -50.0, 50.0)
}

// 计算变换后的正弦函数值（带镜像处理）
func transformedSinLessZero(x float64) float64 {
	// 基本函数：f(x) = -(25 * sin( (π/50) * (abs(x) - 25) ) + 25)
	phase := (math.Pi / 50) * (math.Abs(x) - 25)
	return -(25*math.Sin(phase) + 25)
}

// 计算变换后的正弦函数值
// [-50,50]区间
func transformedSin(x float64) float64 {
	// 函数公式：f(x) = 25 * sin( (π/50) * (x - 25) ) + 25
	phase := (math.Pi / 50) * (x - 25)
	return 25*math.Sin(phase) + 25
}

func (c MemoryEvalChain) GetMemory() schema.Memory {
	return memory.NewSimple()
}

func (c MemoryEvalChain) GetInputKeys() []string {
	return []string{}
}

func (c MemoryEvalChain) GetOutputKeys() []string {
	return []string{}
}

func (c MemoryEvalChain) GetCallbackHandler() callbacks.Handler { //nolint:ireturn
	return nil
}
