package sls

import (
	"cosy/global"
	"cosy/util"
	"fmt"
	"runtime"
	"time"
)

type CosyReportData struct {
	// 事件触发时间
	EventTime int64 `json:"event_time"`
	// 事件类型
	EventType string `json:"event_type"`
	// Cosy用户识别ID
	MachineId string `json:"mid"`
	// 系统架构
	OsArch string `json:"os_arch,omitempty"`
	// 系统版本
	OsVersion string `json:"os_version,omitempty"`
	// ide类型
	IdeType string `json:"ide_type,omitempty"`
	// ide版本
	IdeVersion string `json:"ide_version,omitempty"`
	// 阿里云用户ID（仅登录用户有值）
	AliyunAid string `json:"aid"`
	// 阿里云用户ID（仅登录用户有值）
	AliyunUid string `json:"uid"`
	// 请求ID（用于串联调用上下文）
	RequestId string `json:"rid"`
	// 云效用户id
	YxUid string `json:"yid"`
	//企业id
	OrganizationId string `json:"oid"`
	//用户数据region
	UserRegion string `json:"user_region,omitempty"`
	// 事件参数
	EventData map[string]string `json:"event_data"`
}

type CosyUserReportData struct {
	// 阿里云Id
	AliyunAid string `json:"aid"`
	// 阿里云用户ID（仅登录用户有值）
	AliyunUid string `json:"uid"`
	// 云效用户id
	YxUid string `json:"yid,omitempty"`
	//企业id
	OrganizationId string `json:"oid,omitempty"`
}

type DevStudioReportBody struct {
	Content []string `json:"contents"`
}

func NewReportData(eventType, machineId, requestId string, userReportData CosyUserReportData, eventData map[string]string) (data CosyReportData) {
	// 从Cosy端发出的埋点统一加上版本标识
	eventData["cosy_version"] = global.CosyVersion
	userRegion, _ := eventData["user_region"]

	data = CosyReportData{
		EventTime:      time.Now().UnixMilli(),
		EventType:      eventType,
		MachineId:      machineId,
		OsArch:         fmt.Sprintf("%s_%s", runtime.GOOS, runtime.GOARCH),
		OsVersion:      util.GetOsVersion(),
		IdeType:        eventData["ide_type"],
		IdeVersion:     eventData["ide_version"],
		AliyunAid:      userReportData.AliyunAid,
		AliyunUid:      userReportData.AliyunUid,
		YxUid:          userReportData.YxUid,
		OrganizationId: userReportData.OrganizationId,
		RequestId:      requestId,
		UserRegion:     userRegion,
		EventData:      eventData,
	}
	return
}
