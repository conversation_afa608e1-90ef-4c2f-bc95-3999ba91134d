package wiki_executor

import (
	executor_import "cosy/dataImport/executor"
	"cosy/definition"
	"cosy/log"
	"cosy/storage/vector"
	"cosy/util"
	"os"
	"path/filepath"
)

type WikiImportExecutor struct {
	*executor_import.BaseImportExecutor
}

func NewWikiImportExecutor(workspaceDir string) *WikiImportExecutor {
	return &WikiImportExecutor{
		&executor_import.BaseImportExecutor{
			WorkspaceDir: workspaceDir,
			BizType:      definition.Wiki,
		},
	}
}

// Execute
// 导入wiki.db
func (e *WikiImportExecutor) Execute(sourceFileDir string) error {
	sourceFilePath := filepath.Join(sourceFileDir, e.BizType, vector.WikiDatabaseFileName)
	exists, err := util.Exists(sourceFilePath)
	if !exists || err != nil {
		// 此种情况打印警告日志
		log.Info("wikidb file not exist:" + sourceFilePath)
		return nil
	}
	wikiDbPath := vector.GetSqliteVecDbPath(e.WorkspaceDir, definition.DatabaseVersion, vector.DatabaseModeWiki)
	wikiDbDir := filepath.Dir(wikiDbPath)
	err = os.MkdirAll(wikiDbDir, os.ModePerm)
	if err != nil {
		return err
	}

	err = util.CopyFile(sourceFilePath, wikiDbPath)
	if err != nil {
		return err
	}
	return nil
}
