create table if not exists agent_memory
(
id TEXT PRIMARY KEY,
gmt_create INTEGER,
gmt_modified INTEGER,
scope TEXT NOT NULL,
scope_id TEXT,
keywords TEXT,
title TEXT,
content TEXT NOT NULL,
session_id TEXT,
is_merged INTEGER DEFAULT 0,
freq INTEGER DEFAULT 0,
source TEXT DEFAULT 'auto',
token_count INTEGER DEFAULT 0,
type TEXT NOT NULL,
user_id TEXT,
category TEXT,
retention_score REAL DEFAULT 1.0,
next_review_time INTEGER DEFAULT 0,
last_review_time INTEGER DEFAULT 0,
forget_count INTEGER DEFAULT 0,
status TEXT DEFAULT 'active',
review_history TEXT,
quality_score REAL DEFAULT 0.0
);

CREATE VIRTUAL TABLE IF NOT EXISTS agent_memory_embedding USING vec0(
		memory_id TEXT PRIMARY KEY,
		gmt_create INTEGER,
		gmt_modified INTEGER,
		memory_embedding FLOAT[512] distance_metric=cosine
	);

CREATE TABLE IF NOT EXISTS agent_wiki_item (
    id TEXT PRIMARY KEY,
    catalog_id TEXT NOT NULL,
    repo_id TEXT,
    content TEXT NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    extend TEXT,
    progress_status TEXT,
    workspace_path TEXT,
    gmt_create DATETIME NOT NULL,
    gmt_modified DATETIME NOT NULL,
    FOREIGN KEY (catalog_id) REFERENCES agent_wiki_catalog(id)
);
