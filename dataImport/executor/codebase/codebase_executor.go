package codebase_executor

import (
	merkletree "code.alibaba-inc.com/cosy/mtree"
	executor_import "cosy/dataImport/executor"
	"cosy/definition"
	"cosy/log"
	"cosy/storage"
	"cosy/tree"
	"cosy/util"
	"os"
	"path/filepath"
)

type CodebaseImportExecutor struct {
	*executor_import.BaseImportExecutor
}

func NewCodebaseImportExecutor(workspaceDir string) *CodebaseImportExecutor {
	return &CodebaseImportExecutor{
		&executor_import.BaseImportExecutor{
			WorkspaceDir: workspaceDir,
			BizType:      definition.Codebase,
		},
	}
}

// Execute
// 导入codebase的Graphdb，将graph.db复制到lingmaHome/index/graph/v1路径下
// 导入codebase的merkle_tree，解析merkle_tree.json文件并还原到对应工作目录下
func (e *CodebaseImportExecutor) Execute(sourceFileDir string) error {
	// 导入graph.db
	err := e.importGraphDb(sourceFileDir)
	if err != nil {
		return err
	}

	// 导入merkle_tree.json文件
	err = e.importMerkleTree(sourceFileDir)
	if err != nil {
		return err
	}

	return nil
}

func (e *CodebaseImportExecutor) importGraphDb(sourceFileDir string) error {
	sourceFilePath := filepath.Join(sourceFileDir, e.BizType, storage.GraphDbName)
	exists, err := util.Exists(sourceFilePath)
	if !exists || err != nil {
		// 此种情况打印警告日志
		log.Warn("graphdb file not exist:" + sourceFilePath)
		return nil
	}
	graphStorePath := storage.GetGraphDbStoragePath(e.WorkspaceDir)
	graphStoreDir := filepath.Dir(graphStorePath)
	err = os.MkdirAll(graphStoreDir, os.ModePerm)
	if err != nil {
		return err
	}

	err = util.CopyFile(sourceFilePath, graphStorePath)
	if err != nil {
		return err
	}
	return nil
}

func (e *CodebaseImportExecutor) importMerkleTree(sourceFileDir string) error {
	sourceFilePath := filepath.Join(sourceFileDir, e.BizType, definition.MerkleTreeExportFileName)
	exists, err := util.Exists(sourceFilePath)
	if !exists || err != nil {
		// 此种情况打印警告日志
		log.Warn("merkleTree file not exist:" + sourceFilePath)
		return nil
	}
	treeStorageFilePath := tree.GetTreeStorageFilePath(e.WorkspaceDir)
	treeStorageDir := filepath.Dir(treeStorageFilePath)
	err = os.MkdirAll(treeStorageDir, os.ModePerm)
	if err != nil {
		return err
	}

	importMerkleTree, err := util.ReadJsonFile[merkletree.MerkleTree](sourceFilePath)
	if err != nil {
		return err
	}
	importMerkleTree.RootPath = e.WorkspaceDir
	err = tree.WriteTree(e.WorkspaceDir, importMerkleTree)
	if err != nil {
		return err
	}
	return nil
}
