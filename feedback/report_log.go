package feedback

import (
	"bytes"
	"context"
	"cosy/client"
	"cosy/components"
	"cosy/definition"
	"cosy/log"
	"cosy/remote"
	"cosy/stable"
	"cosy/user"
	"cosy/util"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
)

const (
	fileSizeLimit = 1024 * 1024 * 6

	//目前看到diagnosis.bin做zip压缩后会减小到原来的1/5
	diagnosisFileSizeLimit = 1024 * 1024 * 6 * 2
)

type UploadResponse struct {
	Result    UploadResultBody
	Message   string `json:"message"`
	RequestId string `json:"requestId"`
	Success   bool   `json:"success"`
}

type UploadResultBody struct {
	Url string `json:"url"`
}

func ReportDiagnosisLog(ctx context.Context, params definition.LogFeedbackParams) definition.LogFeedbackResult {
	if params.Feedback == "" || !util.FileExists(params.PluginLogFile) {
		return definition.LogFeedbackResult{
			BaseResult: definition.BaseResult{
				ErrorCode:    definition.ParamErrorCode,
				ErrorMessage: "plugin log file does not exist",
			},
		}
	}
	feedbackId := uuid.NewString()

	var ideInfo definition.IdeConfig
	if ideConfig := ctx.Value(definition.ContextKeyIdeConfig); ideConfig != nil {
		ide, _ := ideConfig.(*definition.IdeConfig)
		ideInfo = *ide
	}

	stable.GoSafe(ctx, func() {

		var imageUrls []string
		if params.ImageUris != nil {
			for _, uri := range params.ImageUris {
				uploadRes, err := components.UploadImage(uri)
				if err != nil {
					log.Warn("Failed to upload feedback image: ", err)
					continue
				}
				log.Debugf("uploaded image: %+v", uploadRes)
				imageUrls = append(imageUrls, uploadRes.Result.Url)
			}
		}

		zipFile, err := collectLogs(params, imageUrls)
		if err != nil {
			log.Errorf("collect diagnosis log error. err: %+v", err)
			return
		}
		defer os.Remove(zipFile)

		fileName := fmt.Sprintf("%s.zip", feedbackId)
		resp, err := uploadFile(ideInfo, params, feedbackId, zipFile, fileName)
		if err != nil {
			log.Errorf("upload diagnosis log error. err: %+v", err)
			return
		}
		if !resp.Success {
			log.Errorf("failed to upload diagnosis log. err: %s", resp.Message)
			return
		}
		log.Infof("upload diagnosis log success, feedbackId: %s", feedbackId)
		log.Debugf("> zipFile: %s, Url: %s", zipFile, resp.Result.Url)
	}, stable.SceneMonitor)

	return definition.LogFeedbackResult{
		Result: definition.LogFeedbackBody{
			FeedbackId: feedbackId,
		},
	}
}

func buildDiagnosisEvent(feedbackId string, params definition.LogFeedbackParams, response UploadResponse,
	ideInfo definition.IdeConfig, success bool) map[string]string {
	eventData := map[string]string{}

	eventData["feedback"] = params.Feedback
	eventData["logUrl"] = response.Result.Url
	eventData["feedbackId"] = feedbackId
	eventData["ideInfo"] = util.ToJsonStr(ideInfo)
	eventData["platformInfo"] = util.GetOsVersion()
	eventData["success"] = strconv.FormatBool(success)

	return eventData
}

func uploadFile(ideInfo definition.IdeConfig, params definition.LogFeedbackParams, feedbackId, zipFile string, fileName string) (UploadResponse, error) {
	httpPayload := &bytes.Buffer{}
	writer := multipart.NewWriter(httpPayload)
	defer writer.Close()

	userInfo := user.GetCachedUserInfo()
	if userInfo == nil || userInfo.Uid == "" {
		return UploadResponse{}, errors.New("user not login")
	}

	formFile, err := writer.CreateFormFile("file", fileName)
	if err != nil {
		log.Errorf("when uploading, create form file error: %+v", err)
		return UploadResponse{}, err
	}

	file, fileOpenErr := os.Open(zipFile)
	if fileOpenErr != nil {
		log.Errorf("open zip file error. err: %+v", fileOpenErr)
		return UploadResponse{}, fileOpenErr
	}
	defer file.Close()

	fileBytes, err := io.ReadAll(file)
	if err != nil {
		log.Errorf("read zip file error. err: %+v", err)
		return UploadResponse{}, err
	}
	buf := bytes.Buffer{}
	buf.Write(fileBytes)

	_, err = io.Copy(formFile, &buf)
	if err != nil {
		log.Errorf("when uploading, copy image error: %+v", err)
		return UploadResponse{}, err
	}

	//设置form表单
	writer.WriteField("user_id", userInfo.Uid)
	writer.WriteField("user_name", userInfo.Name)
	writer.WriteField("ide_type", ideInfo.IdePlatform+" —— "+ideInfo.IdeVersion)
	writer.WriteField("plugin_version", ideInfo.PluginVersion)
	writer.WriteField("description", params.Feedback)

	err = writer.Close()

	requestUrl := definition.UrlPathUploadFileEndpoint + "?request_id=" + feedbackId
	req, err := remote.BuildBigModelAuthUploadFileRequest(http.MethodPut, requestUrl, httpPayload.Bytes())

	if err != nil {
		log.Errorf("upload image build big model auth request error: %v", err)
		return UploadResponse{}, err
	}
	req.Header.Set("AI-CLIENT-TIMESTAMP", strconv.FormatInt(time.Now().Unix(), 10))
	req.Header.Set("Content-Type", writer.FormDataContentType())

	resp, err := client.GetUploadFileClient().Do(req)
	if err != nil {
		return UploadResponse{}, err
	}
	defer resp.Body.Close()

	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Errorf("read response body error when uploading file, the err: %v", err)
		return UploadResponse{}, err
	}

	var result UploadResponse
	err = json.Unmarshal(bodyBytes, &result)
	if err != nil {
		log.Errorf("unmarshal response body error when uploading file, the err: %v", err)
		return UploadResponse{}, err
	}
	return result, nil
}

func collectLogs(params definition.LogFeedbackParams, imageUrls []string) (string, error) {
	timestampSec := time.Now().Unix()
	tmpDir, tmpPathErr := getTmpPath()
	filesToZip := []string{}
	if tmpPathErr != nil {
		return "", tmpPathErr
	}

	pluginLogFile := params.PluginLogFile
	truncatedPluginLogFile, truncErr := duplicateFile(tmpDir, pluginLogFile, fileSizeLimit)
	defer os.Remove(truncatedPluginLogFile)
	if truncErr != nil {
		return "", truncErr
	}
	filesToZip = append(filesToZip, truncatedPluginLogFile)

	cacheLogFile := filepath.Join(util.GetCosyHomePath(), "cache", "diagnosis.bin")
	truncatedCacheLogFile, truncErr := duplicateFile(tmpDir, cacheLogFile, diagnosisFileSizeLimit)
	defer os.Remove(truncatedCacheLogFile)
	if truncErr != nil {
		return "", truncErr
	}
	filesToZip = append(filesToZip, truncatedCacheLogFile)

	feedbackFileFile := strings.Join([]string{tmpDir, "feedback.txt"}, string(os.PathSeparator))
	defer os.Remove(feedbackFileFile)
	if err := util.NewFile(feedbackFileFile, params.Feedback); err != nil {
		return "", err
	}
	filesToZip = append(filesToZip, feedbackFileFile)

	imageListFile := strings.Join([]string{tmpDir, "imageList.txt"}, string(os.PathSeparator))
	defer os.Remove(imageListFile)
	if len(imageUrls) > 0 {
		if err := util.NewFile(imageListFile, strings.Join(imageUrls, "\n")); err != nil {
			return "", err
		}
		filesToZip = append(filesToZip, imageListFile)
	}

	zipFile := strings.Join([]string{tmpDir, strconv.FormatInt(timestampSec, 10) + ".zip"}, string(os.PathSeparator))
	err := util.ZipFiles(zipFile, filesToZip)
	if err != nil {
		return "", err
	}
	return zipFile, nil
}

func duplicateFile(tmpDir, file string, sizeLimit int64) (string, error) {
	newTmpFileName := strings.Join([]string{tmpDir, filepath.Base(file)}, string(os.PathSeparator))
	if util.FileExists(newTmpFileName) {
		os.Remove(newTmpFileName)
	}

	tmpFile, createErr := os.Create(newTmpFileName)
	if createErr != nil {
		return "", createErr
	}

	cpErr := util.CopyFile(file, tmpFile.Name())
	if cpErr != nil {
		return "", cpErr
	}

	truncErr := util.TailTruncateFileByLines(tmpFile.Name(), sizeLimit)
	if truncErr != nil {
		return "", truncErr
	}
	return tmpFile.Name(), nil
}

func getTmpPath() (string, error) {
	tmpDir := os.TempDir()
	if !util.DirExists(tmpDir) {
		tmpDir = strings.Join([]string{util.GetCosyHomePath(), "tmp"}, string(os.PathSeparator))
		if !util.DirExists(tmpDir) {
			err := os.MkdirAll(tmpDir, 0755)
			if err != nil {
				return "", err
			}
		}
	}
	tmpDir = strings.TrimSuffix(tmpDir, string(os.PathSeparator))
	return tmpDir, nil
}
