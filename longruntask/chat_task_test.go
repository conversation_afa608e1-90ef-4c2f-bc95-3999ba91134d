package longruntask

import (
	"context"
	"cosy/client"
	"cosy/config"
	"cosy/definition"
	"cosy/global"
	"cosy/log"
	"cosy/prompt"
	"cosy/sls"
	"cosy/util"
	"fmt"
	"github.com/google/uuid"
	"testing"
	"time"
)

func createChatTask(ctx context.Context) string {
	createParams := &definition.CreateChatTaskParams{
		Name:    "mockName",
		Project: "/Users/<USER>/GithubWorksapce/mall",
	}
	result := CreateChatTask(ctx, createParams)
	if !result.Success {
		log.Warnf("CreateChatTask result=%+v", result)
		return ""
	}
	log.Info("CreateChatTask result=%+v", result)
	task, ok := result.Data.(definition.ChatTask)
	if !ok {
		return ""
	}
	log.Info("CreateChatTask taskId=%s", task.Id)
	return task.Id
}

func TestCreateChatTask(t *testing.T) {
	config.InitLocalConfig()
	client.InitClients()

	createParams := &definition.CreateChatTaskParams{
		Name:    "mockName",
		Project: "/Users/<USER>/GithubWorksapce/mall",
	}
	result := CreateChatTask(context.Background(), createParams)
	if !result.Success {
		t.Errorf("CreateChatTask failed: %v", result)
		return
	}
	log.Info("CreateChatTask result=%+v", result)
	task, ok := result.Data.(definition.ChatTask)
	if !ok {
		t.Errorf("expected ChatTask type in result.Data, got %T", result.Data)
		return
	}
	log.Info("CreateChatTask taskId=%s", task.Id)
	listTaskParams := &definition.ListChatTaskParams{
		Project:    "/Users/<USER>/GithubWorksapce/mall",
		InProgress: boolPtr(true), // 使用辅助函数创建 *bool
		Page:       1,
		PageSize:   10,
	}

	result = ListProjectTasks(listTaskParams)
	if !result.Success {
		t.Errorf("ListProjectTasks failed: %v", result)
		return
	}
	log.Info("ListProjectTasks result=%v", result)
}

func TestUpdateChatTask(t *testing.T) {
	config.InitLocalConfig()
	client.InitClients()
	config.Remote.BigModelEndpoint = "http://test-api-ap-southeast-1.qoder.ai/algo"
	config.Remote.BigModelHost = "http://test-api-ap-southeast-1.qoder.ai"

	updateParams := &definition.UpdateChatTaskParams{
		Name: "test111",
	}
	result := UpdateChatTask("task-d1k9s4nemnjomaip3r4g", updateParams)
	if !result.Success {
		t.Errorf("UpdateChatTask failed: %v", result)
		return
	}
	log.Info("UpdateChatTask result=%v", result)
}

func TestGetChatTask(t *testing.T) {
	config.InitLocalConfig()
	client.InitClients()

	result := GetChatTask("task-d1r2ccjim0ic4ebhu970")
	if !result.Success {
		t.Errorf("GetChatTask failed: %v", result)
		return
	}
	log.Info("GetChatTask result=%v", result)
}

func TestGetChatTaskBootLog(t *testing.T) {
	config.InitLocalConfig()
	client.InitClients()

	result := GetChatTaskBootLog("task-d1rghmp0ed757v9ruj60")
	if !result.Success {
		t.Errorf("GetChatTaskBootLog failed: %v", result)
		return
	}
	log.Info("GetChatTaskBootLog result=%v", util.ToJsonStr(result))
}

func TestListChatTasks(t *testing.T) {
	config.InitLocalConfig()
	client.InitClients()

	listTaskParams := &definition.ListChatTaskParams{
		Project: "/Users/<USER>/GithubWorksapce/mall",
		//ExcludedProject: "/Users/<USER>/GithubWorksapce/mall",
		InProgress: boolPtr(false),
		Page:       1,
		PageSize:   10,
	}

	result := ListProjectTasks(listTaskParams)
	if !result.Success {
		t.Errorf("ListProjectTasks failed: %v", result)
		return
	}
	log.Info("ListProjectTasks result=%v", result)
}

func TestExecuteChatTask(t *testing.T) {
	config.InitLocalConfig()
	client.InitClients()
	config.Remote.BigModelEndpoint = "http://test-api-ap-southeast-1.qoder.ai/algo"
	config.Remote.BigModelHost = "http://test-api-ap-southeast-1.qoder.ai"

	// TODO：替换成 本地可以拷贝到沙箱里的目录
	workspaceuri := "/Users/<USER>/GithubWorksapce/mall"
	ctx := context.Background()
	ctx = context.WithValue(ctx, definition.ContextKeyWorkspace, definition.WorkspaceInfo{
		WorkspaceFolders: []definition.WorkspaceFolder{
			{
				URI: workspaceuri,
			},
		},
	})

	// 创建一个新的task
	taskId := createChatTask(ctx)

	time.Sleep(5 * time.Second)

	result1 := UpdateChatTaskStatus(ctx, taskId, "Designing", nil)
	if !result1.Success {
		t.Errorf("UpdateChatTaskStatus failed: %v", result1)
		return
	}
	log.Info("UpdateChatTaskStatus result=%v", result1)

	executeTaskParams := &definition.ExecuteTaskParams{
		Id:                 taskId,
		SourceBranch:       "master",
		ExecutionSessionId: fmt.Sprintf("%s", uuid.New()),
		ExecutionRequestId: fmt.Sprintf("%s", uuid.New()),
	}

	result := ExecuteTask(context.Background(), executeTaskParams)
	if !result.Success {
		t.Errorf("ExecuteTask failed: %v", result)
		return
	}
	log.Info("ExecuteTask result=%v", result)
	// 等待任务执行完成，等待时间20分钟。
	time.Sleep(20 * 60 * time.Second)
}

func TestExecuteChatTaskOnly(t *testing.T) {
	config.InitLocalConfig()
	client.InitClients()
	config.Remote.BigModelEndpoint = "http://test-api-ap-southeast-1.qoder.ai/algo"
	config.Remote.BigModelHost = "http://test-api-ap-southeast-1.qoder.ai"

	// TODO：替换成 本地可以拷贝到沙箱里的目录
	workspaceuri := "/Users/<USER>/GithubWorksapce/mall"
	ctx := context.Background()
	ctx = context.WithValue(ctx, definition.ContextKeyWorkspace, definition.WorkspaceInfo{
		WorkspaceFolders: []definition.WorkspaceFolder{
			{
				URI: workspaceuri,
			},
		},
	})

	executeTaskParams := &definition.ExecuteTaskParams{
		Id:                 "task-d1rl5itedgqhos9dcds0",
		SourceBranch:       "master",
		ExecutionSessionId: fmt.Sprintf("%s", uuid.New()),
		ExecutionRequestId: fmt.Sprintf("%s", uuid.New()),
	}

	result := ExecuteTask(context.Background(), executeTaskParams)
	log.Info("ExecuteTask result=%v", result)

	// 等待任务执行完成，等待时间20分钟。
	time.Sleep(20 * 60 * time.Second)
}

func TestUpdateChatTaskStatus(t *testing.T) {
	global.DebugMode = true
	config.InitLocalConfig()
	client.InitClients()
	prompt.InitializeRepo()
	sls.InitReporter()
	config.Remote.BigModelEndpoint = "http://test-api-ap-southeast-1.qoder.ai/algo"
	config.Remote.BigModelHost = "http://test-api-ap-southeast-1.qoder.ai"

	// 创建一个新的task
	taskId := createChatTask(context.Background())

	time.Sleep(5 * time.Second)
	//taskId = "d1nnuudiffn3ibl9ma1g"

	result := UpdateChatTaskStatus(context.Background(), taskId, definition.ChatTaskStatusAccepted, []func(context.Context, string, string) error{OnRemoteStatusChanged})
	if !result.Success {
		t.Errorf("UpdateChatTaskStatus failed: %v", result)
		return
	}
	log.Info("UpdateChatTaskStatus result=%v", result)
	// sleep 300s
	time.Sleep(5 * 60 * time.Second)
}

func TestDeleteChatTaskStatus(t *testing.T) {
	config.InitLocalConfig()
	client.InitClients()
	config.Remote.BigModelEndpoint = "http://test-api-ap-southeast-1.qoder.ai/algo"
	config.Remote.BigModelHost = "http://test-api-ap-southeast-1.qoder.ai"

	result := DeleteChatTask("task-d1j42rrb1om8m0gip3bg")
	if !result.Success {
		t.Errorf("DeleteChatTask failed: %v", result)
		return
	}
	log.Info("DeleteChatTask result=%v", result)
}

func TestCancelChatTaskStatus(t *testing.T) {
	config.InitLocalConfig()
	client.InitClients()
	config.Remote.BigModelEndpoint = "http://test-api-ap-southeast-1.qoder.ai/algo"
	config.Remote.BigModelHost = "http://test-api-ap-southeast-1.qoder.ai"

	result := CancelChatTask("task-d1n349vl7k91lq7a4pl0")
	if !result.Success {
		t.Errorf("CancelChatTask failed: %v", result)
		return
	}
	log.Info("CancelChatTask result=%v", result)
}

// 辅助函数用于创建 *bool
func boolPtr(b bool) *bool {
	return &b
}
