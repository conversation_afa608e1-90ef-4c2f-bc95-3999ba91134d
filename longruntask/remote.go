package longruntask

import (
	"context"
	"cosy/definition"
	"cosy/log"
	"cosy/remote"
	"cosy/user"
	"cosy/util"
	"errors"
	"fmt"
	"golang.org/x/crypto/ssh"
	"io"
	"math/rand"
	"net"
	"sync"
	"time"
)

// portForwardMap 以taskId缓存已经建立的port-forward信息，当链接关闭时移除缓存
var portForwardMap = sync.Map{}

func tryGetWebsocketEndpoint(taskId string) string {
	if v, ok := portForwardMap.Load(taskId); ok {
		return v.(string)
	}
	return ""
}

func cacheWebsocketEndpoint(taskId string, addr string) {
	portForwardMap.Store(taskId, addr)
}

func removeWebsocketEndpointCache(taskId string) {
	portForwardMap.Delete(taskId)
}

func GetTaskWebsocketEndpoint(ctx context.Context, taskId string) (string, error) {
	if v := tryGetWebsocketEndpoint(taskId); v != "" {
		return v, nil
	}
	taskResp := GetChatTask(taskId)
	if !taskResp.Success {
		return "", errors.New(taskResp.ErrorMessage)
	}
	chatTask, ok := taskResp.Data.(definition.ChatTask)
	if !ok {
		return "", errors.New("invalid chat task")
	}
	sshAddress := chatTask.SSHRelayAddress
	if sshAddress == "" {
		return "", errors.New("ssh address is empty")
	}
	sshPassword, err := remote.CalculateSSHPassword(taskId)
	if err != nil {
		log.Warn("CalculateSSHPassword error: %s", err)
		return "", err
	}
	rand.Int()
	localPort := util.GetFreePort(util.RandomIntBetween(30000, 40000), 30000, 40000, 5)
	if localPort <= 0 {
		return "", errors.New("can not get local free port")
	}
	remotePort := 8080
	cosyUser := user.GetCachedUserInfo()
	if err := portForward(taskId, sshAddress, cosyUser.Uid, sshPassword, localPort, remotePort); err != nil {
		log.Errorf("port forward error: %s", err)
		return "", err
	}
	addr := fmt.Sprintf("ws://127.0.0.1:%d", localPort)
	cacheWebsocketEndpoint(taskId, addr)
	// 检查任务有没有开启任务状态变更等事件的监听，如果没有开启，则尝试开启
	EnsureWatchingTaskEvent(ctx, taskId)
	return addr, nil
}

func portForward(taskId string, sshHost, sshUser, sshPassword string, localPort int, remotePort int) error {
	cfg := &ssh.ClientConfig{
		User: sshUser,
		Auth: []ssh.AuthMethod{
			ssh.Password(sshPassword),
		},
		HostKeyCallback: ssh.InsecureIgnoreHostKey(),
	}

	cli, err := ssh.Dial("tcp", sshHost, cfg)
	if err != nil {
		return fmt.Errorf("failed to dial SSH server: %v", err)
	}
	localAddr := fmt.Sprintf("127.0.0.1:%d", localPort)
	remoteAddr := fmt.Sprintf("127.0.0.1:%d", remotePort)
	listener, err := net.Listen("tcp", localAddr)
	if err != nil {
		return fmt.Errorf("failed to listen on local: %s, err:%v", localAddr, err)
	}

	log.Infof("Forwarding remote %s to local %s", remoteAddr, localAddr)

	activeConnections := make(map[net.Conn]struct{})
	var mu sync.Mutex

	timeout := time.Minute * 5
	timer := time.NewTimer(timeout)

	util.GoSafeRoutine(func() {
		for {
			localConn, err := listener.Accept()
			if err != nil {
				if errors.Is(err, net.ErrClosed) {
					// 优化一下日志
					log.Warnf("Port forward closed, remote %s -> local %s", remoteAddr, localAddr)
				} else {
					log.Errorf("Failed to accept local connection: %v", err)
				}
				return
			}

			mu.Lock()
			activeConnections[localConn] = struct{}{}
			mu.Unlock()

			util.GoSafeRoutine(func() {
				handleConnection(cli, localConn, remoteAddr)
				mu.Lock()
				delete(activeConnections, localConn)
				mu.Unlock()
			})

			timer.Reset(timeout)
		}
	})
	util.GoSafeRoutine(func() {
		defer cli.Close()
		defer listener.Close()
		defer timer.Stop()
		defer removeWebsocketEndpointCache(taskId)

		for {
			select {
			case <-timer.C:
				mu.Lock()
				if len(activeConnections) == 0 {
					mu.Unlock()
					log.Infof("No active connections for 5 minutes, closing port forward, remote %s -> local %s", remoteAddr, localAddr)
					return
				}
				mu.Unlock()
				timer.Reset(timeout)
			}
		}
	})
	return nil
}
func handleConnection(sshClient *ssh.Client, localConn net.Conn, remoteAddr string) {
	defer localConn.Close()

	remoteConn, err := sshClient.Dial("tcp", remoteAddr)
	if err != nil {
		log.Errorf("Failed to dial remote port: %v", err)
		return
	}
	defer remoteConn.Close()

	var wg sync.WaitGroup
	wg.Add(2)

	util.GoSafeRoutine(func() {
		defer wg.Done()
		io.Copy(remoteConn, localConn)
		remoteConn.Close()
	})

	util.GoSafeRoutine(func() {
		defer wg.Done()
		io.Copy(localConn, remoteConn)
		localConn.Close()
	})

	wg.Wait()
}
