package longruntask

import (
	"context"
	"cosy/config"
	"cosy/definition"
	"cosy/remote"
	"cosy/util"
	"fmt"
	"net/http"
)

type GetActionFlowParams struct {
	TaskId string `json:"taskId"`
	Stream string `json:"stream"`
}

type ActionFlow struct {
	MarkdownContent string `json:"markdownContent"`
	Content         string `json:"content"`
	TotalTasks      int    `json:"totalTasks"`
	CompletedTasks  int    `json:"completedTasks"`
}

type ActionFlowUpdate struct {
	TaskID          string `json:"taskId"`
	Content         string `json:"content"`
	TotalTasks      int    `json:"totalTasks"`
	CompletedTasks  int    `json:"completedTasks"`
	MarkdownContent string `json:"markdownContent"`
	Timestamp       int64  `json:"timestamp"`
}

type UpdateActionFlowParams struct {
	MarkdownContent string `json:"markdownContent"`
	Content         string `json:"content"`
	TotalTasks      int    `json:"totalTasks"`
	CompletedTasks  int    `json:"completedTasks"`
}

func (t *TaskManager) UpdateActionFlow(ctx context.Context, taskId, markdownContent, content string, totalTasks, completedTasks int) (TaskServiceCommonResponse, error) {
	updateActionFlowResp := TaskServiceCommonResponse{}
	params := UpdateActionFlowParams{
		MarkdownContent: markdownContent,
		Content:         content,
		TotalTasks:      totalTasks,
		CompletedTasks:  completedTasks,
	}
	httpPayload := remote.HttpPayload{
		Payload:       util.ToJsonStr(params),
		EncodeVersion: config.Remote.MessageEncode,
	}
	endPoint := fmt.Sprintf(definition.ActionFlowEndpoint, taskId)
	if err := t.commonTaskCall(ctx, http.MethodPost, endPoint, httpPayload, &updateActionFlowResp); err != nil {
		return updateActionFlowResp, err
	}
	if updateActionFlowResp.Code != 200 {
		return updateActionFlowResp, fmt.Errorf("qoder service return non-success. error code: %s, message: %s, requestId: %s", updateActionFlowResp.ErrorCode, updateActionFlowResp.Message, updateActionFlowResp.RequestId)
	}

	return updateActionFlowResp, nil
}

func (t *TaskManager) GetActionFlow(ctx context.Context, taskId string) (TaskServiceCommonResponse, error) {
	commonResp := TaskServiceCommonResponse{}

	endPoint := fmt.Sprintf(definition.ActionFlowEndpoint, taskId)
	if err := t.commonTaskCall(ctx, http.MethodGet, endPoint, nil, &commonResp); err != nil {
		return commonResp, err
	}

	return commonResp, nil
}
