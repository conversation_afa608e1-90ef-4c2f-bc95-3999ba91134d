package longruntask

import (
	"cosy/client"
	"cosy/config"
	"cosy/definition"
	cosyError "cosy/errors"
	"cosy/log"
	"cosy/remote"
	"cosy/user"
	"cosy/util"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/go-git/go-git/v5"
	"io"
	"net"
	"net/http"
	"path/filepath"
	"time"
)

// ProjectWorkspaceMap 存储项目路径和工作空间ID的映射关系
var ProjectWorkspaceMap = map[string]string{}

// 先查询workspace
func GetOrInitWorkspace(userUid string, projectPath string) (string, error) {
	if projectPath == "" {
		return "", nil
	}
	// 先从ProjectWorkspaceMap里面获取projectPath对应的workspaceId。workspaceId跟用户有关，不同用户，同一project，workspace不一样
	cacheKey := userUid + "_" + projectPath
	workspaceId, ok := ProjectWorkspaceMap[cacheKey]
	if ok {
		return workspaceId, nil
	}

	// 获取gitRemotes
	gitRemotes, err := getGitRemotes(projectPath)
	if err != nil {
		log.Errorf("get git remotes error, the err: %v", err)
	}

	// 获取本机mac
	mac, err := GetMACAddress()
	if err != nil {
		log.Errorf("get mac error, the err: %v", err)
	}

	// 如果缓存中没有的话，从服务器allocate一个
	workspaceId, err = allocateWorkspace(projectPath, gitRemotes, mac)
	if err != nil {
		return "", err
	}
	// 存到缓存中
	ProjectWorkspaceMap[cacheKey] = workspaceId
	return workspaceId, nil
}

func allocateWorkspace(workspaceUri string, gitRemotes []string, mac string) (string, error) {
	userInfo := user.GetCachedUserInfo()
	if userInfo == nil {
		return cosyError.QuestUserNotLoginError, errors.New("user not login")
	}

	workspaceInfo, err := json.Marshal(map[string]interface{}{
		"projectPath": workspaceUri,
	})
	if err != nil {
		return "", err
	}
	createWorkspaceParams := definition.AllocateWorkspaceParams{
		MachineId:     mac,
		FilePath:      workspaceUri,
		VcsType:       "Git",
		VcsUrls:       gitRemotes,
		WorkspaceInfo: string(workspaceInfo),
	}

	payload := remote.HttpPayload{
		Payload:       util.ToJsonStr(createWorkspaceParams),
		EncodeVersion: config.Remote.MessageEncode,
	}

	req, err := remote.BuildBigModelAuthRequest(http.MethodPost, definition.AllocateWorkspaceEndpoint, payload)
	if err != nil {
		log.Warnf("Build request error, error=%v", err)
		return "", err
	}
	beginTimeStamp := time.Now()
	resp, err := client.GetRemoteAgentClient().Do(req)
	elapsedTime := time.Since(beginTimeStamp).Seconds()
	log.Infof("allocate workspace time %fs", elapsedTime)
	if err != nil {
		log.Errorf("do big model auth request error when allocate workspace, the err: %v", err)
		return "", err
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		log.Warnf("Failed to allocate workspace, url=%s, status=%s", req.URL, resp.Status)
		return "", fmt.Errorf("failed to allocate workspace, status=%s", resp.Status)
	}

	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Errorf("read response body error when allocate workspace, the err: %v", err)
		return "", err
	}

	var result definition.AllocateWorkspaceResponse
	err = json.Unmarshal(bodyBytes, &result)
	if err != nil {
		log.Errorf("unmarshal response body error when allocate workspace, the err: %v", err)
		return "", err
	}
	return result.Data.WorkspaceId, nil
}

func getGitRemotes(workspacePath string) ([]string, error) {
	// 确保路径是绝对路径
	absPath, err := filepath.Abs(workspacePath)
	if err != nil {
		return nil, fmt.Errorf("failed to get absolute path: %w", err)
	}

	// 打开仓库
	repo, err := git.PlainOpen(absPath)
	if err != nil {
		return nil, fmt.Errorf("failed to open git repository: %w", err)
	}

	// 获取远程仓库信息
	remotes, err := repo.Remotes()
	if err != nil {
		return nil, fmt.Errorf("failed to get remotes: %w", err)
	}

	for _, remote := range remotes {
		config := remote.Config()
		if len(config.URLs) > 0 {
			return config.URLs, nil
		}
	}
	return nil, fmt.Errorf("failed to get remotes: remotes is empty")
}

// 获取本机MAC地址
func GetMACAddress() (string, error) {
	interfaces, err := net.Interfaces()
	if err != nil {
		return "", err
	}

	for _, i := range interfaces {
		if i.Flags&net.FlagLoopback == 0 && i.HardwareAddr != nil {
			return i.HardwareAddr.String(), nil
		}
	}
	return "", fmt.Errorf("no valid MAC address found")
}
