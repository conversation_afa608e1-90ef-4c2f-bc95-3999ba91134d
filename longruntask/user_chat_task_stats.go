package longruntask

import (
	"context"
	"cosy/client"
	"cosy/config"
	"cosy/definition"
	cosyError "cosy/errors"
	"cosy/log"
	"cosy/remote"
	"cosy/sse"
	"cosy/user"
	"cosy/util"
	"cosy/websocket"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"runtime"
	"sync/atomic"
	"time"
)

const (
	MaxRetryCount = 5
)

var retryCount = 0

var running atomic.Bool

// GetUserChatTaskStats 获取用户任务统计信息
func GetUserChatTaskStats(ctx context.Context) *definition.Response {
	userInfo := user.GetCachedUserInfo()
	if userInfo == nil {
		return buildErrorResponse(cosyError.QuestUserNotLoginError, "user not login")
	}
	requestUrl := util.BuildUrlQuery(definition.GetUserChatTaskStatsEndpoint, map[string]any{
		"Encode": config.Remote.MessageEncode,
	})
	log.Debugf("GetUserChatTaskStats, url=%s", requestUrl)

	req, err := remote.BuildBigModelAuthRequest(http.MethodGet, requestUrl, nil)
	if err != nil {
		log.Warnf("Build request error, error=%v", err)
		return buildErrorResponse("", err.Error())
	}
	beginTimeStamp := time.Now()
	resp, err := client.GetRemoteAgentClient().Do(req)
	elapsedTime := time.Since(beginTimeStamp).Seconds()
	log.Infof("get user chat task stats time %fs", elapsedTime)
	if err != nil {
		log.Errorf("do big model auth request error when get user chat task stats, the err: %v", err)
		return buildErrorResponse("", err.Error())
	}
	defer resp.Body.Close()

	// 读取响应
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Errorf("read response body error when get user chat task stats, the err: %v", err)
		return buildErrorResponse("", err.Error())
	}
	log.Debugf("get user chat task stats response body: %s", string(bodyBytes))

	// 解析响应
	var result definition.GetUserChatTaskStatsResponse
	err = json.Unmarshal(bodyBytes, &result)
	if err != nil {
		if resp.StatusCode != http.StatusOK {
			log.Warnf("Failed to get user chat task stats, url=%s, status=%s", req.URL, resp.Status)
			return buildErrorResponse("", fmt.Errorf("failed to get user chat task stats, status=%s", resp.Status).Error())
		}
		log.Errorf("unmarshal response body error get user chat task stats, the err: %v", err)
		return buildErrorResponse("", err.Error())
	}

	response := &definition.Response{
		Success:      result.Code == 200,
		ErrorCode:    result.ErrorCode,
		ErrorMessage: result.Message,
		Data:         result.Data,
	}
	if result.Code == 200 {
		// 触发用户task监听
		WatchingUserTaskStatsEvents(ctx)
	}
	return response
}

func WatchingUserTaskStatsEvents(ctx context.Context) {
	if config.IsRemoteAgentMode() {
		// 远程模式下，不需要同步代码库
		return
	}
	go watchEvents(ctx)
}

func watchEvents(ctx context.Context) {
	userInfo := user.GetCachedUserInfo()
	if userInfo == nil {
		return
	}
	requestUrl := util.BuildUrlQuery(definition.WatchUserEventsEndpoint, map[string]any{
		"Encode": config.Remote.MessageEncode,
	})
	req, requestError := remote.BuildBigModelAuthRequest(http.MethodGet, requestUrl, nil)
	if requestError != nil {
		log.Warnf("Build request error, url=%s, error=%v", requestUrl, requestError)
		return
	}
	sseClient := sse.NewSseRemoteAgentClient(map[string]string{})
	// 定义处理函数
	handleFun := handleEvent(ctx)
	// 使用原子操作的CompareAndSwap确保只有一个连接处于运行状态
	if !running.CompareAndSwap(false, true) {
		log.Warnf("watching user task stats events is already running")
		return
	}
	// 确保函数退出时重置running状态
	defer func() {
		running.Store(false)
		log.Debugf("watchEvents: running status reset to false")
	}()
	// 发送请求&读取数据
	err := sseClient.SubscribeWithContext(ctx, time.Duration(definition.WatchTaskEventSSETimeout)*time.Second, req, handleFun, func(req *http.Request, rsp *http.Response) {
		log.Errorf("watch user task stats event timeout")
	})
	if err != nil {
		log.Errorf("watch user task stats event timeout, err=%v", err)
		log.Error("watch user task stats event timeout, retry...")
		retryCount++
		if retryCount <= MaxRetryCount {
			// sleep 1  second
			time.Sleep(time.Duration(1) * time.Second)
			watchEvents(ctx)
		} else {
			log.Errorf("watch user task stats event max retry count reached, giving up")
		}
	}
}

func handleEvent(ctx context.Context) func(req *http.Request, rsp *http.Response, msg *sse.Event, closeChan chan error) {
	return func(req *http.Request, rsp *http.Response, msg *sse.Event, closeChan chan error) {
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("watch user task stats events triggered panic")

				// 获取当前堆栈信息
				stack := make([]byte, 4096)
				stack = stack[:runtime.Stack(stack, false)]
				log.Errorf("recover from crash. err: %+v, stack: %s", r, stack)
			}
		}()
		log.Debugf("watch user task stats events. type=%s, data=%s", msg.Event, string(msg.Data))
		// 有数据过来，重新连接的次数重置为0
		retryCount = 0
		switch string(msg.Event) {
		case "error":
			log.Warnf("watch user task stats events error, reason=%s", msg.Data)
			return
		case "finish":
			log.Warnf("watch user task stats events finish, reason=%s", msg.Data)
			return
		case definition.ChatTaskTypeUserTaskStatsEvent:
			var userChatTaskStats = definition.UserChatTaskStats{}
			err := json.Unmarshal(msg.Data, &userChatTaskStats)
			if err != nil {
				log.Error("Unmarshal sse msg data error: ", err)
				return
			}
			// 同步状态变更的消息给ide
			syncUserChatTaskStats(ctx, &userChatTaskStats)
			return
		case definition.ChatTaskTypeInProgressTaskUpdate:
			inProgressTask := &definition.RemoteChatTask{}
			err := json.Unmarshal(msg.Data, inProgressTask)
			if err != nil {
				log.Error("Unmarshal sse msg data error: ", err)
				return
			}
			syncInProgressTaskEventStats(ctx, inProgressTask)
			return

		}
	}
}

func syncUserChatTaskStats(ctx context.Context, stats *definition.UserChatTaskStats) {
	log.Debugf("syncUserChatTaskStats, status=%s", util.ToJsonStr(stats))

	// 检查websocket是否有链接的客户端，有才发送
	if websocket.WsInst == nil {
		log.Warn("syncUserChatTaskStats webSocket instance is not initialized")
		return
	}
	clients := websocket.WsInst.GetClientList()
	hasClients := len(clients) > 0
	if hasClients {
		e := websocket.SendRequestWithTimeout(ctx, "user/task/stats/sync",
			stats, nil, 10*time.Second)
		if e != nil {
			log.Error("syncUserChatTaskStats user/task/stats/sync: ", e)
		}
	}
}

func GetUserChatTaskQuotas(ctx context.Context, resource string) *definition.Response {
	userInfo := user.GetCachedUserInfo()
	if userInfo == nil {
		return buildErrorResponse("", "user not login")
	}
	requestUrl := util.BuildUrlQuery(definition.GetUserQuotasEndpoint, map[string]any{
		"resource": resource,
		"Encode":   config.Remote.MessageEncode,
	})
	log.Debugf("GetUserChatTaskQuotas, url=%s", requestUrl)

	req, err := remote.BuildBigModelAuthRequest(http.MethodGet, requestUrl, nil)
	if err != nil {
		log.Warnf("Build request error, error=%v", err)
		return buildErrorResponse("", err.Error())
	}
	beginTimeStamp := time.Now()
	resp, err := client.GetRemoteAgentClient().Do(req)
	elapsedTime := time.Since(beginTimeStamp).Seconds()
	log.Infof("get user chat task quotas time %fs", elapsedTime)
	if err != nil {
		log.Errorf("do big model auth request error when get user chat task quotas, the err: %v", err)
		return buildErrorResponse("", err.Error())
	}
	defer resp.Body.Close()

	// 读取响应
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Errorf("read response body error when get user chat task quotas, the err: %v", err)
		return buildErrorResponse("", err.Error())
	}
	log.Debugf("get user chat task quotas response body: %s", string(bodyBytes))

	// 解析响应
	var result definition.GetUserChatTaskQuotasResponse
	err = json.Unmarshal(bodyBytes, &result)
	if err != nil {
		if resp.StatusCode != http.StatusOK {
			log.Warnf("Failed to get user chat task quotas, url=%s, status=%s", req.URL, resp.Status)
			return buildErrorResponse("", fmt.Errorf("failed to get user chat task stats, status=%s", resp.Status).Error())
		}
		log.Errorf("unmarshal response body error get user chat task quotas, the err: %v", err)
		return buildErrorResponse("", err.Error())
	}

	response := &definition.Response{
		Success:      result.Code == 200,
		ErrorCode:    result.ErrorCode,
		ErrorMessage: result.Message,
		Data:         result.Data,
	}
	return response
}
func syncInProgressTaskEventStats(ctx context.Context, task *definition.RemoteChatTask) {
	log.Debugf("syncInProgressTaskEventStats, status=%s", util.ToJsonStr(task))

	// 检查websocket是否有链接的客户端，有才发送
	if websocket.WsInst == nil {
		log.Warn("syncInProgressTaskEventStats webSocket instance is not initialized")
		return
	}
	clients := websocket.WsInst.GetClientList()
	hasClients := len(clients) > 0
	if hasClients {
		e := websocket.SendRequestWithTimeout(ctx, "user/inprogress/task/sync", TransRemoteTaskToTask(task), nil, 10*time.Second)
		if e != nil {
			log.Error("syncInProgressTaskEventStats user/task/sync: ", e)
		}
	}
}
