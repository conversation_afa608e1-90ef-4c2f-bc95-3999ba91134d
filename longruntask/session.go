package longruntask

import (
	"context"
	"cosy/config"
	"cosy/definition"
	"cosy/remote"
	"cosy/util"
	"encoding/json"
	"fmt"
	"net/http"
)

type CreateSessionParams struct {
	OrgId        string `json:"orgId"`
	UserId       string `json:"userId"`
	UserName     string `json:"userName"`
	SessionId    string `json:"sessionId"`
	SessionTitle string `json:"sessionTitle"`
	ProjectId    string `json:"projectId"`
	ProjectUri   string `json:"projectUri"`
	ProjectName  string `json:"projectName"`
	GmtCreate    int64  `json:"gmtCreate"`
	GmtModified  int64  `json:"gmtModified"`
	SessionType  string `json:"sessionType"`
	Mode         string `json:"mode"`
	Version      string `json:"version"`
}

type CreateExecutionSessionResp struct {
	SessionId string `json:"sessionId"`
	TaskId    string `json:"taskId"`
	Status    string `json:"status"`
}

type CreateChatRecordResp struct {
	RequestId string `json:"requestId"`
	SessionId string `json:"sessionId"`
	Status    string `json:"status"`
}

type GetSessionRecordsResp struct {
	PageNumber int                     `json:"pageNumber"`
	PageSize   int                     `json:"pageSize"`
	TotalSize  int                     `json:"totalSize"`
	Items      []definition.ChatRecord `json:"items"`
}

type CreateMessageResp struct {
	Id string `json:"id"`
}

type GetSessionMessagesResp struct {
	PageNumber int                      `json:"pageNumber"`
	PageSize   int                      `json:"pageSize"`
	TotalSize  int                      `json:"totalSize"`
	Items      []definition.ChatMessage `json:"items"`
}

type UpdateMessageToolResultParams struct {
	Id         string `json:"id"`
	ToolResult string `json:"toolResult"`
}

func (t *TaskManager) CreateExecutionSession(ctx context.Context, taskId string, params definition.ChatSession) (CreateExecutionSessionResp, error) {
	return t.CreateSession(ctx, taskId, "executions", params)
}

func (t *TaskManager) CreateDesignSession(ctx context.Context, taskId string, params definition.ChatSession) (CreateExecutionSessionResp, error) {
	return t.CreateSession(ctx, taskId, "designs", params)
}

func (t *TaskManager) CreateSession(ctx context.Context, taskId string, sessionType string, params definition.ChatSession) (CreateExecutionSessionResp, error) {
	createSessionResp := CreateExecutionSessionResp{}
	commonResp := TaskServiceCommonResponse{}

	httpPayload := remote.HttpPayload{
		Payload:       util.ToJsonStr(params),
		EncodeVersion: config.Remote.MessageEncode,
	}
	endPoint := fmt.Sprintf(definition.ExecutionSessionEndpoint, taskId, sessionType)
	if err := t.commonTaskCall(ctx, http.MethodPost, endPoint, httpPayload, &commonResp); err != nil {
		return createSessionResp, err
	}
	if commonResp.Code != 200 {
		return createSessionResp, fmt.Errorf("qoder service return non-success. error code: %s, message: %s, requestId: %s", commonResp.ErrorCode, commonResp.Message, commonResp.RequestId)
	}
	jsonStr, err := json.Marshal(commonResp.Data)
	if err != nil {
		return createSessionResp, fmt.Errorf("unmarshal execution session data failed:%s", err.Error())
	}
	if err := json.Unmarshal(jsonStr, &createSessionResp); err != nil {
		return createSessionResp, err
	}

	return createSessionResp, nil
}

func (t *TaskManager) CreateChatRecord(ctx context.Context, sessionId string, params definition.ChatRecord) (CreateChatRecordResp, error) {
	createChatRecordResp := CreateChatRecordResp{}
	commonResp := TaskServiceCommonResponse{}

	httpPayload := remote.HttpPayload{
		Payload:       util.ToJsonStr(params),
		EncodeVersion: config.Remote.MessageEncode,
	}
	endPoint := fmt.Sprintf(definition.RemoteSessionRecordEndpoint, sessionId)
	if err := t.commonTaskCall(ctx, http.MethodPost, endPoint, httpPayload, &commonResp); err != nil {
		return createChatRecordResp, err
	}
	if commonResp.Code != 200 {
		return createChatRecordResp, fmt.Errorf("qoder service return non-success. error code: %s, message: %s, requestId: %s", commonResp.ErrorCode, commonResp.Message, commonResp.RequestId)
	}
	jsonStr, err := json.Marshal(commonResp.Data)
	if err != nil {
		return createChatRecordResp, fmt.Errorf("unmarshal session record data failed:%s", err.Error())
	}
	if err := json.Unmarshal(jsonStr, &createChatRecordResp); err != nil {
		return createChatRecordResp, err
	}
	return createChatRecordResp, nil
}

func (t *TaskManager) GetSessionRecords(ctx context.Context, sessionId string, pageSize, pageNumber int) (GetSessionRecordsResp, error) {
	getSessionRecordsResp := GetSessionRecordsResp{}
	commonResp := TaskServiceCommonResponse{}

	if pageSize < 10 || pageSize > 100 {
		pageSize = 10
	}
	if pageNumber <= 0 {
		pageNumber = 1
	}

	endPoint := fmt.Sprintf(definition.RemoteSessionRecordEndpoint, sessionId)
	endPoint = fmt.Sprintf("%s?pageNumber=%d&pageSize=%d", endPoint, pageNumber, pageSize)
	if err := t.commonTaskCall(ctx, http.MethodGet, endPoint, nil, &commonResp); err != nil {
		return getSessionRecordsResp, err
	}
	if commonResp.Code != 200 {
		return getSessionRecordsResp, fmt.Errorf("qoder service return non-success. error code: %s, message: %s, requestId: %s", commonResp.ErrorCode, commonResp.Message, commonResp.RequestId)
	}
	jsonStr, err := json.Marshal(commonResp.Data)
	if err != nil {
		return getSessionRecordsResp, fmt.Errorf("unmarshal session records data failed:%s", err.Error())
	}
	if err := json.Unmarshal(jsonStr, &getSessionRecordsResp); err != nil {
		return getSessionRecordsResp, err
	}

	return getSessionRecordsResp, nil
}

func (t *TaskManager) UpdateSessionMessages(ctx context.Context, sessionId string, message definition.ChatMessage) (CreateMessageResp, error) {
	result := CreateMessageResp{}
	commonResp := TaskServiceCommonResponse{}
	httpPayload := remote.HttpPayload{
		Payload:       util.ToJsonStr(message),
		EncodeVersion: config.Remote.MessageEncode,
	}
	endPoint := fmt.Sprintf(definition.SessionMessagesEndpoint, sessionId)
	if err := t.commonTaskCall(ctx, http.MethodPost, endPoint, httpPayload, &commonResp); err != nil {
		return result, err
	}
	if commonResp.Code != 200 {
		return result, fmt.Errorf("qoder service return non-success. error code: %s, message: %s, requestId: %s", commonResp.ErrorCode, commonResp.Message, commonResp.RequestId)
	}
	jsonStr, err := json.Marshal(commonResp.Data)
	if err != nil {
		return result, fmt.Errorf("unmarshal session records data failed:%s", err.Error())
	}
	if err := json.Unmarshal(jsonStr, &result); err != nil {
		return result, err
	}

	return result, nil
}

func (t *TaskManager) UpdateSessionMessagesToolResult(ctx context.Context, id, sessionId, toolResult string) (CreateMessageResp, error) {
	result := CreateMessageResp{}
	commonResp := TaskServiceCommonResponse{}
	updateParams := UpdateMessageToolResultParams{
		Id:         id,
		ToolResult: toolResult,
	}
	httpPayload := remote.HttpPayload{
		Payload:       util.ToJsonStr(updateParams),
		EncodeVersion: config.Remote.MessageEncode,
	}
	endPoint := fmt.Sprintf(definition.SessionMessagesEndpoint, sessionId)
	if err := t.commonTaskCall(ctx, http.MethodPost, endPoint, httpPayload, &commonResp); err != nil {
		return result, err
	}
	if commonResp.Code != 200 {
		return result, fmt.Errorf("qoder service return non-success. error code: %s, message: %s, requestId: %s", commonResp.ErrorCode, commonResp.Message, commonResp.RequestId)
	}
	jsonStr, err := json.Marshal(commonResp.Data)
	if err != nil {
		return result, fmt.Errorf("unmarshal session records data failed:%s", err.Error())
	}
	if err := json.Unmarshal(jsonStr, &result); err != nil {
		return result, err
	}

	return result, nil
}

func (t *TaskManager) GetSessionMessages(ctx context.Context, sessionId string, role string, pageSize, pageNumber int) (GetSessionMessagesResp, error) {
	result := GetSessionMessagesResp{}
	commonResp := TaskServiceCommonResponse{}

	if pageSize < 10 || pageSize > 100 {
		pageSize = 10
	}
	if pageNumber <= 0 {
		pageNumber = 1
	}

	endPoint := fmt.Sprintf(definition.SessionMessagesEndpoint, sessionId)
	endPoint = fmt.Sprintf("%s?pageNumber=%d&pageSize=%d", endPoint, pageNumber, pageSize)
	if role != "" {
		endPoint = endPoint + "&role=" + role
	}
	if err := t.commonTaskCall(ctx, http.MethodGet, endPoint, nil, &commonResp); err != nil {
		return result, err
	}
	if commonResp.Code != 200 {
		return result, fmt.Errorf("qoder service return non-success. error code: %s, message: %s, requestId: %s", commonResp.ErrorCode, commonResp.Message, commonResp.RequestId)
	}
	jsonStr, err := json.Marshal(commonResp.Data)
	if err != nil {
		return result, fmt.Errorf("unmarshal session records data failed:%s", err.Error())
	}
	if err := json.Unmarshal(jsonStr, &result); err != nil {
		return result, err
	}

	return result, nil
}
