package longruntask

import (
	"context"
	"cosy/client"
	"cosy/config"
	"cosy/definition"
	"cosy/remote"
	"cosy/util"
	"fmt"
	"net/http"
)

type TaskManager struct {
	httpClient *http.Client
}

func NewTaskManager() TaskManager {
	return TaskManager{
		httpClient: client.GetRemoteAgentClient(),
	}
}

type SyncDesignContentParams struct {
	TaskId     string `json:"taskId"`
	DesignData string `json:"designData"`
}

type TaskServiceCommonResponse struct {
	Code      int            `json:"code"`
	ErrorCode string         `json:"errorCode"`
	Message   string         `json:"message"`
	RequestId string         `json:"requestId"`
	Data      map[string]any `json:"data"`
}

type FileChangesResponse struct {
	Code      int                  `json:"code"`
	ErrorCode string               `json:"errorCode"`
	Message   string               `json:"message"`
	RequestId string               `json:"requestId"`
	Data      PagedFileChangesResp `json:"data"`
}

func (t *TaskManager) SyncDesignDoc(ctx context.Context, taskId string, designData string) (TaskServiceCommonResponse, error) {
	commonResp := TaskServiceCommonResponse{}
	params := SyncDesignContentParams{
		TaskId:     taskId,
		DesignData: designData,
	}
	httpPayload := remote.HttpPayload{
		Payload:       util.ToJsonStr(params),
		EncodeVersion: config.Remote.MessageEncode,
	}
	endPoint := fmt.Sprintf(definition.DesignEndpoint, params.TaskId)
	if err := t.commonTaskCall(ctx, http.MethodPost, endPoint, httpPayload, &commonResp); err != nil {
		return commonResp, err
	}

	return commonResp, nil
}

func (t *TaskManager) GetDesignDoc(ctx context.Context, taskId string) (TaskServiceCommonResponse, error) {
	commonResp := TaskServiceCommonResponse{}

	endPoint := fmt.Sprintf(definition.DesignEndpoint, taskId)
	if err := t.commonTaskCall(ctx, http.MethodGet, endPoint, nil, &commonResp); err != nil {
		return commonResp, err
	}

	return commonResp, nil
}
