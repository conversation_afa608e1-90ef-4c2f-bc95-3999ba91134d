package longruntask

import (
	"context"
	"cosy/definition"
	"encoding/json"
	"fmt"
	"net/http"
)

type Message struct {
	MessageId  string `json:"messageId"`
	Content    string `json:"content"`
	Type       string `json:"type"`
	ReportedAt int64  `json:"reportedAt"`
}

type GetMessageParams struct {
	TaskId     string `json:"taskId"`
	PageSize   int    `json:"pageSize"`
	PageNumber int    `json:"pageNumber"`
}

type GetMessageResp struct {
	PageNumber int       `json:"pageNumber"`
	PageSize   int       `json:"pageSize"`
	TotalSize  int       `json:"totalSize"`
	Messages   []Message `json:"messages"`
}

func (t *TaskManager) GetMessages(ctx context.Context, taskId string, pageSize, pageNumber int) (GetMessageResp, error) {
	resp := GetMessageResp{}
	messages := []Message{}
	commonResp := TaskServiceCommonResponse{}
	commonPagedResp := TaskServicePagedCommonResponse{}

	endPoint := fmt.Sprintf(definition.MessageEndpoint, taskId)
	if pageSize < 10 || pageSize > 100 {
		pageSize = 10
	}
	if pageNumber <= 0 {
		pageNumber = 1
	}
	endPoint = fmt.Sprintf("%s?pageNumber=%d&pageSize=%d", endPoint, pageNumber, pageSize)
	if err := t.commonTaskCall(ctx, http.MethodGet, endPoint, nil, &commonResp); err != nil {
		return resp, err
	}
	if commonResp.Code != 200 {
		return resp, fmt.Errorf("qoder service return non-success. error code: %s, message: %s, requestId: %s", commonResp.ErrorCode, commonResp.Message, commonResp.RequestId)
	}
	jsonStr, err := json.Marshal(commonResp.Data)
	if err != nil {
		return resp, fmt.Errorf("marshal common resp data failed:%s", err.Error())
	}
	if err := json.Unmarshal(jsonStr, &commonPagedResp); err != nil {
		return resp, fmt.Errorf("unmarshal commonPagedResp data failed:%s", err.Error())
	}
	messagesJsonStr, err := json.Marshal(commonPagedResp.Items)
	if err != nil {
		return resp, fmt.Errorf("marshal messages data failed:%s", err.Error())
	}
	if err := json.Unmarshal(messagesJsonStr, &messages); err != nil {
		return resp, fmt.Errorf("unmarshal messages data failed:%s", err.Error())
	}
	resp.Messages = messages
	resp.PageNumber = commonPagedResp.PageNumber
	resp.PageSize = commonPagedResp.PageSize
	resp.TotalSize = commonPagedResp.TotalSize
	return resp, nil
}
