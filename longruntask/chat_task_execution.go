package longruntask

import (
	"context"
	"cosy/client"
	"cosy/config"
	"cosy/definition"
	cosyError "cosy/errors"
	"cosy/log"
	"cosy/remote"
	"cosy/sls"
	"cosy/user"
	"cosy/util"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

const (
	RemoteAgent = "RemoteAgent"
)

func ExecuteTask(ctx context.Context, executeParams *definition.ExecuteTaskParams) *definition.Response {
	userInfo := user.GetCachedUserInfo()
	if userInfo == nil {
		return buildErrorResponse(cosyError.QuestUserNotLoginError, "user not login")
	}

	// 确保监听任务事件
	EnsureWatchingTaskEvent(ctx, executeParams.Id)

	requestUrl := fmt.Sprintf(definition.ExecuteTaskEndpoint, executeParams.Id)
	executeParams.AgentClass = RemoteAgent

	payload := remote.HttpPayload{
		Payload:       util.ToJsonStr(executeParams),
		EncodeVersion: config.Remote.MessageEncode,
	}

	log.Infof("Start execute task,taskId=%s", executeParams.Id)
	eventData := make(map[string]string)
	eventData["taskId"] = executeParams.Id
	eventData["userId"] = userInfo.Uid
	sls.Report(sls.EventTypeQuestTaskExecution, executeParams.ExecutionRequestId, eventData)
	req, err := remote.BuildBigModelAuthRequest(http.MethodPost, requestUrl, payload)
	if err != nil {
		log.Warnf("Build request error, url=%s, error=%v", requestUrl, err)
		return buildErrorResponse("", err.Error())
	}
	beginTimeStamp := time.Now()
	resp, err := client.GetRemoteAgentClient().Do(req)
	elapsedTime := time.Since(beginTimeStamp).Seconds()
	log.Infof("execute task time %fs", elapsedTime)
	if err != nil {
		log.Errorf("do big model auth request error when execute task, the err: %v", err)
		return buildErrorResponse("", err.Error())
	}
	defer resp.Body.Close()
	// 读取响应
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Errorf("read response body error when execute task, the err: %v", err)
		return buildErrorResponse("", err.Error())
	}
	// 解析响应
	var result definition.ExecuteTaskResponse
	err = json.Unmarshal(bodyBytes, &result)
	if err != nil {
		if resp.StatusCode != http.StatusOK {
			log.Warnf("Failed to execute task, url=%s, status=%s", requestUrl, resp.Status)
			return buildErrorResponse("", fmt.Errorf("failed to execute chat task, status=%s", resp.Status).Error())
		}
		log.Errorf("unmarshal response body error when execute task, the err: %v", err)
		return buildErrorResponse("", err.Error())
	}
	// 构建响应
	response := &definition.Response{
		Success:      result.Code == 200,
		ErrorCode:    result.ErrorCode,
		ErrorMessage: result.Message,
	}
	return response
}

func UpdateExecution(ctx context.Context, taskId string, updateParams *definition.UpdateExecutionParams) *definition.Response {
	userInfo := user.GetCachedUserInfo()
	if userInfo == nil {
		return buildErrorResponse(cosyError.QuestUserNotLoginError, "user not login")
	}

	requestUrl := fmt.Sprintf(definition.ExecuteTaskEndpoint, taskId)
	payload := remote.HttpPayload{
		Payload:       util.ToJsonStr(updateParams),
		EncodeVersion: config.Remote.MessageEncode,
	}
	req, err := remote.BuildBigModelAuthRequest(http.MethodPut, requestUrl, payload)
	if err != nil {
		log.Warnf("Build request error, url=%s, error=%v", requestUrl, err)
		return buildErrorResponse("", err.Error())
	}
	resp, err := client.GetRemoteAgentClient().Do(req)
	if err != nil {
		log.Errorf("do big model auth request error when execute task, the err: %v", err)
		return buildErrorResponse("", err.Error())
	}
	defer resp.Body.Close()
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Errorf("read response body error when execute task, the err: %v", err)
		return buildErrorResponse("", err.Error())
	}
	// 解析响应
	var result definition.UpdateExecutionResponse
	err = json.Unmarshal(bodyBytes, &result)
	if err != nil {
		if resp.StatusCode != http.StatusOK {
			log.Warnf("Failed to update execution, url=%s, status=%s", requestUrl, resp.Status)
			return buildErrorResponse("", fmt.Errorf("failed to update execution, resp status=%s", resp.Status).Error())
		}
		log.Errorf("unmarshal response body error when execute task, the err: %v", err)
		return buildErrorResponse("", err.Error())
	}
	// 构建响应
	response := &definition.Response{
		Success:      result.Code == 200,
		ErrorCode:    result.ErrorCode,
		ErrorMessage: result.Message,
	}
	return response
}

func PostStartUpdateRemoteAgentTaskExecution(ctx context.Context, taskId string) {
	headCommitId, err := util.GetGitHeadCommitId(config.GetRemoteAgentWorkspacePath())
	if err != nil {
		log.Errorf("Failed to get git head commit id: %v", err)
		return
	}
	updateParams := &definition.UpdateExecutionParams{
		HeadCommitId: headCommitId,
	}
	resp := UpdateExecution(ctx, taskId, updateParams)
	log.Infof("Update execution response: %v", resp)
}
