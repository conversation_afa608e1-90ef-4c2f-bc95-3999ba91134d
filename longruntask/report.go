package longruntask

import (
	"context"
	"cosy/config"
	"cosy/definition"
	"cosy/remote"
	"cosy/util"
	"fmt"
	"net/http"
)

type GetReportParams struct {
	TaskId string `json:"taskId"`
}

type Report struct {
	Summary string `json:"summary"`
}

type UpdateReportParams struct {
	TaskId  string `json:"taskId"`
	Summary string `json:"summary"`
}

func (t *TaskManager) UpdateReport(ctx context.Context, taskId, summary string) (TaskServiceCommonResponse, error) {
	commonResp := TaskServiceCommonResponse{}
	params := UpdateReportParams{
		TaskId:  taskId,
		Summary: summary,
	}
	httpPayload := remote.HttpPayload{
		Payload:       util.ToJsonStr(params),
		EncodeVersion: config.Remote.MessageEncode,
	}
	endPoint := fmt.Sprintf(definition.ReportEndpoint, params.TaskId)
	if err := t.commonTaskCall(ctx, http.MethodPost, endPoint, httpPayload, &commonResp); err != nil {
		return commonResp, err
	}
	if commonResp.Code != 200 {
		return commonResp, fmt.Errorf("qoder service return non-success. error code: %s, message: %s, requestId: %s", commonResp.ErrorCode, commonResp.Message, commonResp.RequestId)
	}

	return commonResp, nil
}

func (t *TaskManager) GetReport(ctx context.Context, taskId string) (TaskServiceCommonResponse, error) {
	commonResp := TaskServiceCommonResponse{}

	endPoint := fmt.Sprintf(definition.ReportEndpoint, taskId)

	if err := t.commonTaskCall(ctx, http.MethodGet, endPoint, nil, &commonResp); err != nil {
		return commonResp, err
	}

	return commonResp, nil
}
