package longruntask

import (
	"context"
	"cosy/client"
	"cosy/config"
	"cosy/log"
	"cosy/remote"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestTaskManager_GetActionFlow(t *testing.T) {
	tests := []struct {
		TaskId string
	}{
		{
			TaskId: "task-d1ii1jo6psgaesl57rog",
		},
	}
	config.InitConfig()
	client.InitClients()
	remote.InitRegionFailoverService()

	config.Remote.BigModelEndpoint = "http://test-api-ap-southeast-1.qoder.ai/algo"
	taskManager := NewTaskManager()
	for _, tt := range tests {
		resp, err := taskManager.GetActionFlow(context.Background(), tt.TaskId)
		if err != nil {
			assert.Fail(t, "error", err.Error())
		}
		log.Infof("%+v", resp)
	}
}

func TestTaskManager_UpdateActionFlow(t *testing.T) {
	tests := []struct {
		TaskId          string
		MarkdownContent string
		Content         string
		TotalTasks      int
		CompletedTasks  int
	}{
		{
			TaskId:          "task-d1ii1jo6psgaesl57rog",
			MarkdownContent: "## Doing",
			Content:         "Doing",
			TotalTasks:      10,
			CompletedTasks:  1,
		},
	}
	config.InitConfig()
	client.InitClients()
	remote.InitRegionFailoverService()

	config.Remote.BigModelEndpoint = "http://test-api-ap-southeast-1.qoder.ai/algo"
	taskManager := NewTaskManager()
	for _, tt := range tests {
		resp, err := taskManager.UpdateActionFlow(context.Background(), tt.TaskId, tt.MarkdownContent, tt.Content, tt.TotalTasks, tt.CompletedTasks)
		if err != nil {
			assert.Fail(t, "error", err.Error())
		}
		log.Infof("%+v", resp)
	}
}
