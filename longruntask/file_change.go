package longruntask

import (
	"context"
	"cosy/client"
	"cosy/config"
	"cosy/definition"
	"cosy/log"
	"cosy/remote"
	"cosy/util"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
)

type TaskServicePagedCommonResponse struct {
	PageNumber int              `json:"pageNumber"`
	PageSize   int              `json:"pageSize"`
	TotalSize  int              `json:"totalSize"`
	Items      []map[string]any `json:"items"`
}

type FileChange struct {
	FilePath        string `json:"filePath"`
	OriginalContent string `json:"originalContent"`
	ModifiedContent string `json:"modifiedContent"`
	EditMode        string `json:"editMode"`
}

type PagedFileChangesResp struct {
	PageNumber  int          `json:"pageNumber"`
	PageSize    int          `json:"pageSize"`
	TotalSize   int          `json:"totalSize"`
	FileChanges []FileChange `json:"fileChanges"`
}

func UpdateFileChange(taskId string, fileChange *definition.UpdateFileChangeParam) error {
	uri := fmt.Sprintf(definition.TaskFileChangeEndpoint, taskId)

	payload := remote.HttpPayload{
		Payload:       util.ToJsonStr(fileChange),
		EncodeVersion: config.Remote.MessageEncode,
	}

	req, err := remote.BuildBigModelAuthRequest(http.MethodPost, uri, payload)
	if err != nil {
		log.Warnf("Build request error, url=%s, error=%v", req.URL, err)
		return err
	}
	resp, err := client.GetRemoteAgentClient().Do(req)
	if err != nil {
		log.Errorf("update file change api error, url=%s, error=%v", req.URL, err)
		return err
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		v, _ := io.ReadAll(resp.Body)
		log.Warnf("Failed to get response, url=%s, response body: %s status=%s", req.URL, v, resp.Status)
		return fmt.Errorf("failed to get response, status=%s", resp.Status)
	}
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Errorf("read response body error when create chat ask, the err: %v", err)
		return err
	}
	log.Debugf("update file change response body: %s", string(bodyBytes))
	return nil
}

func (t *TaskManager) GetFileChanges(ctx context.Context, taskId string, pageSize, pageNumber int) (*PagedFileChangesResp, error) {
	var fileChanges []FileChange
	commonResp := TaskServiceCommonResponse{}
	commonPagedResp := TaskServicePagedCommonResponse{}

	endPoint := fmt.Sprintf(definition.TaskFileChangeEndpoint, taskId)
	if pageSize < 10 || pageSize > 100 {
		pageSize = 10
	}
	if pageNumber <= 0 {
		pageNumber = 1
	}
	endPoint = fmt.Sprintf("%s?pageNumber=%d&pageSize=%d", endPoint, pageNumber, pageSize)
	if err := t.commonTaskCall(ctx, http.MethodGet, endPoint, nil, &commonResp); err != nil {
		return nil, err
	}
	if commonResp.Code != 200 {
		return nil, fmt.Errorf("qoder service return non-success. error code: %s, message: %s, requestId: %s", commonResp.ErrorCode, commonResp.Message, commonResp.RequestId)
	}
	jsonStr, err := json.Marshal(commonResp.Data)
	if err != nil {
		return nil, fmt.Errorf("marshal common resp data failed:%s", err.Error())
	}
	if err := json.Unmarshal(jsonStr, &commonPagedResp); err != nil {
		return nil, fmt.Errorf("unmarshal commonPagedResp data failed:%s", err.Error())
	}
	changesJsonStr, err := json.Marshal(commonPagedResp.Items)
	if err != nil {
		return nil, fmt.Errorf("marshal filechanges data failed:%s", err.Error())
	}
	if err = json.Unmarshal(changesJsonStr, &fileChanges); err != nil {
		return nil, fmt.Errorf("unmarshal file changes data failed:%s", err.Error())
	}
	var filteredFileChanges []FileChange
	for _, change := range fileChanges {
		// 过滤掉前后一致的文件，这里可能导致响应给前端的条目数减少，与前端确认暂时不会依赖items的数量做校验
		if change.OriginalContent == change.ModifiedContent {
			continue
		}
		filteredFileChanges = append(filteredFileChanges, change)
	}
	filePagedResp := &PagedFileChangesResp{
		FileChanges: filteredFileChanges,
		PageSize:    commonPagedResp.PageSize,
		PageNumber:  commonPagedResp.PageNumber,
		TotalSize:   commonPagedResp.TotalSize,
	}
	return filePagedResp, nil
}

func ClearTaskFileChanges(taskId string) error {
	uri := util.BuildUrlQuery(fmt.Sprintf(definition.TaskFileChangeEndpoint, taskId), map[string]any{"Encode": config.Remote.MessageEncode})
	req, err := remote.BuildBigModelAuthRequest(http.MethodDelete, uri, nil)
	if err != nil {
		log.Warnf("Build request error, url=%s, error=%v", uri, err)
		return err
	}
	resp, err := client.GetRemoteAgentClient().Do(req)
	if err != nil {
		log.Errorf("clear file changes api error, url=%s, error=%v", req.URL, err)
		return err
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		v, _ := io.ReadAll(resp.Body)
		log.Warnf("Failed to get response, url=%s, response body: %s status=%s", req.URL, v, resp.Status)
		return fmt.Errorf("failed to get response, status=%s", resp.Status)
	}
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Errorf("read response body error when create chat ask, the err: %v", err)
		return err
	}
	log.Debugf("clear file changes response body: %s", string(bodyBytes))
	return nil
}

func UploadGitFileChanges() {
	if !config.IsRemoteAgentMode() {
		return
	}
	// 清空当前task下的所有文件修改，重新推送最终变更
	taskId, _ := config.GetRemoteModeTaskId()
	if err := ClearTaskFileChanges(taskId); err != nil {
		log.Errorf("Failed to clear task file changes: %v", err)
		// 不阻塞同步新变更
	}
	fileChanges, err := util.GetWorkspaceFileChanges(config.GetRemoteAgentWorkspacePath())
	if err != nil {
		log.Errorf("Failed to get workspace file changes: %v", err)
		return
	}
	for _, fileVO := range fileChanges {
		err := UpdateFileChange(taskId, &definition.UpdateFileChangeParam{
			FilePath:        fileVO.FilePath,
			OriginalContent: fileVO.OriginalContent,
			ModifiedContent: fileVO.ModifiedContent,
			EditMode:        fileVO.EditMode,
		})
		if err != nil {
			log.Errorf("Failed to update git working space file %s: %v", fileVO.FilePath, err)
		}
	}
	log.Infof("Successfully upload git file changes for task %s, fileCount %d", taskId, len(fileChanges))
}
