package longruntask

import (
	"context"
	"cosy/client"
	"cosy/config"
	"cosy/log"
	"cosy/util"
	"testing"
	"time"
)

func TestGetUserChatTaskStats(t *testing.T) {
	config.InitLocalConfig()
	client.InitClients()

	result := GetUserChatTaskStats(context.Background())
	if !result.Success {
		t.<PERSON><PERSON>rf("GetUserChatTaskStats failed: %v", result)
		return
	}
	log.Info("GetUserChatTaskStats result=%s", util.ToJsonStr(result))

	// 等待任务执行完成，等待时间20分钟。
	time.Sleep(20 * 60 * time.Second)
}

func TestGetUserChatTaskQuotas(t *testing.T) {
	config.InitLocalConfig()
	client.InitClients()

	result := GetUserChatTaskQuotas(context.Background(), "task.user.running")
	if !result.Success {
		t.Errorf("GetUserChatTaskQuotas failed: %v", result)
		return
	}
	log.Info("GetUserChatTaskQuotas result=%s", util.ToJsonStr(result))
}
