package longruntask

import (
	"sync"
)

// TaskEventState 封装每个 taskId 的并发状态
type TaskEventState struct {
	mu         sync.Mutex
	watching   bool
	retryCount int
}

var taskStates = sync.Map{} // map[string]*TaskEventState

func getTaskState(taskId string) *TaskEventState {
	state, _ := taskStates.LoadOrStore(taskId, &TaskEventState{})
	return state.(*TaskEventState)
}

func setTaskWatching(taskId string, watching bool) {
	state := getTaskState(taskId)
	state.mu.Lock()
	defer state.mu.Unlock()
	state.watching = watching
}

func getTaskWatching(taskId string) bool {
	state := getTaskState(taskId)
	state.mu.Lock()
	defer state.mu.Unlock()
	return state.watching
}

func incrementRetryCount(taskId string) int {
	state := getTaskState(taskId)
	state.mu.Lock()
	defer state.mu.Unlock()
	state.retryCount++
	return state.retryCount
}

func getRetryCount(taskId string) int {
	state := getTaskState(taskId)
	state.mu.Lock()
	defer state.mu.Unlock()
	return state.retryCount
}

func resetRetryCount(taskId string) {
	state := getTaskState(taskId)
	state.mu.Lock()
	defer state.mu.Unlock()
	state.retryCount = 0
}

func deleteTaskState(taskId string) {
	taskStates.Delete(taskId)
}
