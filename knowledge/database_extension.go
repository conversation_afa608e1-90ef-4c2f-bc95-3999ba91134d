package knowledge

import (
	"context"
	"cosy/log"
	"database/sql"
	"fmt"
)

// DatabaseExtension 数据库扩展，用于在现有数据库中添加knowledge表
type DatabaseExtension struct{}

// NewDatabaseExtension 创建数据库扩展
func NewDatabaseExtension() *DatabaseExtension {
	return &DatabaseExtension{}
}

// CreateKnowledgeTables 在指定数据库中创建knowledge相关表
func (ext *DatabaseExtension) CreateKnowledgeTables(ctx context.Context, db *sql.DB) error {
	tables := ext.getCreateTableStatements()

	for _, query := range tables {
		if _, err := db.ExecContext(ctx, query); err != nil {
			return fmt.Errorf("failed to create knowledge table: %w", err)
		}
	}

	return nil
}

// getCreateTableStatements 获取建表语句
func (ext *DatabaseExtension) getCreateTableStatements() []string {
	return []string{
		// CodeSnippet 表
		`CREATE TABLE IF NOT EXISTS agent_code_snippet (
			id TEXT PRIMARY KEY,
			path TEXT,
			line_range VARCHAR(50),
			gmt_create DATETIME,
			gmt_modified DATETIME
		)`,

		// SourceFile 表
		`CREATE TABLE IF NOT EXISTS agent_source_file (
			id TEXT PRIMARY KEY,
			path TEXT,
			filename TEXT,
			gmt_create DATETIME,
			gmt_modified DATETIME
		)`,

		// Commit 表（用于版本控制和wiki关联）
		`CREATE TABLE IF NOT EXISTS agent_commit (
			id TEXT PRIMARY KEY,
			message TEXT,
			gmt_create DATETIME,
			gmt_modified DATETIME
		)`,

		// 统一关系表（核心表）
		`CREATE TABLE IF NOT EXISTS agent_knowledge_relation (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			source_id VARCHAR(50) NOT NULL,
			target_id VARCHAR(50) NOT NULL,
			source_type VARCHAR(50) NOT NULL,
			target_type VARCHAR(50) NOT NULL,
			relationship_type VARCHAR(50) NOT NULL,
			extra TEXT,
			gmt_create DATETIME,
			gmt_modified DATETIME
		)`,

		// 创建索引
		`CREATE INDEX IF NOT EXISTS idx_agent_knowledge_source 
		 ON agent_knowledge_relation(source_id, source_type)`,

		`CREATE INDEX IF NOT EXISTS idx_agent_knowledge_target 
		 ON agent_knowledge_relation(target_id, target_type)`,

		`CREATE INDEX IF NOT EXISTS idx_agent_knowledge_relation_type 
		 ON agent_knowledge_relation(relationship_type)`,

		// 创建唯一索引，防止重复关系
		`CREATE UNIQUE INDEX IF NOT EXISTS idx_agent_knowledge_unique_relation 
		 ON agent_knowledge_relation(source_id, target_id, source_type, target_type, relationship_type)`,
	}
}

// CheckTablesExist 检查knowledge表是否已存在
func (ext *DatabaseExtension) CheckTablesExist(ctx context.Context, db *sql.DB) (bool, error) {
	// 检查核心表是否存在
	query := `SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name='agent_knowledge_relation'`
	var count int
	err := db.QueryRowContext(ctx, query).Scan(&count)
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// InitializeKnowledgeExtension 初始化knowledge扩展（供deepwiki调用）
func InitializeKnowledgeExtension(ctx context.Context, db *sql.DB) error {
	ext := NewDatabaseExtension()

	// 检查表是否已存在
	exists, err := ext.CheckTablesExist(ctx, db)
	if err != nil {
		return fmt.Errorf("failed to check table existence: %w", err)
	}

	if !exists {
		// 创建表
		if err := ext.CreateKnowledgeTables(ctx, db); err != nil {
			return fmt.Errorf("failed to create knowledge tables: %w", err)
		}
		log.Debugf("Knowledge tables created successfully")
	} else {
		log.Debugf("Knowledge tables already exist")
	}

	return nil
}
