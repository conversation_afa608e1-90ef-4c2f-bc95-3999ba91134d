package chat

import (
	"context"
	"cosy/config"
	"cosy/definition"
	"cosy/filter"
	"cosy/log"
	"cosy/prompt"
	"cosy/remote"
	"cosy/sse"
	"cosy/systemrule"
	"cosy/tokenizer"
	"cosy/user"
	"cosy/util"
	"cosy/websocket"
	"encoding/json"
	"errors"
	"net/http"
	"regexp"
	"strings"
	"time"
)

const (
	// AgentCommitMsg 通用 32k service
	AgentCommitMsg = "agent_commit_message"

	// 生成的 commit message 语言
	PreferredLangChinese = "中文"
	PreferredLangEnglish = "英文"
)

func GenerateCommitMsg(ctx context.Context, params definition.CommitMsgGenerateParam) definition.CommitMsgGenerateResult {
	cachedUser := user.GetCachedUserInfo()
	if cachedUser == nil || cachedUser.Uid == "" {
		return definition.CommitMsgGenerateResult{
			RequestId: params.RequestId,
			ErrorMsg:  "user not login",
			IsSuccess: false,
		}
	}

	filteredParams, status, err := filter.FilterCommitMsgGenerateParam(ctx, params)
	if err != nil {
		return definition.CommitMsgGenerateResult{
			RequestId: params.RequestId,
			ErrorMsg:  "filter error",
			IsSuccess: false,
		}
	}
	if status == filter.StatusBlocked {
		return definition.CommitMsgGenerateResult{
			RequestId: params.RequestId,
			ErrorMsg:  "blocked by filter",
			IsSuccess: false,
		}
	}
	fixCommitMsgFormat(&filteredParams)

	remoteReq, err := buildRemoteQuest(filteredParams, ctx)
	if err != nil {
		return definition.CommitMsgGenerateResult{
			RequestId: params.RequestId,
			ErrorMsg:  "system error",
			IsSuccess: false,
		}
	}
	// 根据是否开启system rule来判断是否需要切换至通用模型
	req, err := buildCallModelRequest(ctx, params, remoteReq)

	if err != nil {
		var tokenExpiredError *definition.TokenExpiredError
		if errors.As(err, &tokenExpiredError) {
			return definition.CommitMsgGenerateResult{
				RequestId: params.RequestId,
				ErrorMsg:  "login token expired",
				IsSuccess: false,
			}
		}
		log.Error("Build new request to generate commit msg failed")
		return definition.CommitMsgGenerateResult{
			RequestId: params.RequestId,
			ErrorMsg:  err.Error(),
			IsSuccess: false,
		}
	}
	if params.Stream {
		go func(ctx context.Context, req *http.Request, params definition.CommitMsgGenerateParam) {
			_ = doPostCommitMsgGenerate(ctx, req, params)
		}(ctx, req, filteredParams)
	} else {
		//TODO 暂不支持
		return definition.CommitMsgGenerateResult{
			RequestId: params.RequestId,
			ErrorMsg:  "none stream is not unsupported",
			IsSuccess: false,
		}
	}
	return definition.CommitMsgGenerateResult{
		RequestId: params.RequestId,
		ErrorMsg:  "",
		IsSuccess: true,
	}
}

func buildCallModelRequest(ctx context.Context, params definition.CommitMsgGenerateParam, remoteReq definition.CommitMsgGenerateRequest) (*http.Request, error) {
	if systemrule.IsSystemRuleExists(ctx) && params.PluginPayloadConfig.IsEnableProjectRule {
		remoteAsk := definition.RemoteChatAsk{
			RequestId:               params.RequestId,
			ChatRecordId:            params.RequestId,
			Stream:                  params.Stream,
			ChatPrompt:              remoteReq.ChatPrompt,
			Version:                 "2",
			CustomSystemRoleContent: remoteReq.SystemRoleContent,
			SystemRoleContent:       remoteReq.SystemRoleContent,
			TaskId:                  definition.AgentTaskCommonChat,
			AgentId:                 definition.AgentIdAIChat,
		}

		log.Debugf("gen commit msg remoteAsk is: %v", util.ToJsonStr(remoteAsk))
		svcRequest := remote.BigModelSvcRequest{
			ServiceName: "agent_chat_ask",
			FetchKey:    "llm_model_result",
			Async:       params.Stream,
			RequestBody: remoteAsk,
			RequestID:   remoteReq.RequestId,
			AgentID:     definition.AgentIdAIChat,
		}
		return remote.BuildBigModelSvcRequestWithConfig(svcRequest, nil, nil)
	}

	svcRequest := remote.BigModelSvcRequest{
		ServiceName: AgentCommitMsg,
		FetchKey:    "llm_model_result",
		Async:       params.Stream,
		RequestBody: remoteReq,
		RequestID:   remoteReq.RequestId,
		AgentID:     "",
	}
	return remote.BuildBigModelSvcRequestWithConfig(svcRequest, nil, nil)
}

func doPostCommitMsgGenerate(ctx context.Context, req *http.Request, params definition.CommitMsgGenerateParam) error {
	lastCommitMsgData := ""
	sseClient := sse.NewSseChatClient(map[string]string{})

	log.Debugf("Async generate commit msg, request id: %s", params.RequestId)

	err := sseClient.Subscribe(req, 115*time.Second, func(msg *sse.Event) {
		var response definition.ChatResponse
		if string(msg.Event) == "error" {
			log.Warnf("Answer finish error, reason=%s", msg.Data)

			commitMsgFinish := definition.CommitMsgFinish{
				RequestId:  params.RequestId,
				Reason:     "{\"message\":\"CommitMsg generate error\"}",
				StatusCode: 408,
			}
			e := websocket.SendRequestWithTimeout(ctx, "commitMsg/finish",
				commitMsgFinish, nil, 3*time.Second)
			if e != nil {
				log.Error("Generate commit message interrupted, send request commitMsg/finish error:", e)
				return
			}

			return
		}
		if string(msg.Event) == "finish" {

			log.Debugf("commit msg generate finish. requestId=%s, commitMsg: %s", params.RequestId, lastCommitMsgData)

			commitMsgFinish := definition.CommitMsgFinish{
				RequestId:  params.RequestId,
				Reason:     "success",
				StatusCode: 200,
			}

			// 执行后置过滤
			doPostCommitMsgFilter(ctx, params, lastCommitMsgData)

			// 结束对话
			e := websocket.SendRequestWithTimeout(ctx, "commitMsg/finish",
				commitMsgFinish, nil, 3*time.Second)
			if e != nil {
				log.Warn("Generate commit message success, send request commitMsg/finish error:", e)
			}
			return
		}
		err := json.Unmarshal(msg.Data, &response)
		if err != nil {
			log.Error("Unmarshal sse msg data error: ", err)
			return
		}
		if response.StatusCodeValue != 200 {
			message := response.Body
			commitMsgFinish := definition.CommitMsgFinish{
				RequestId:  params.RequestId,
				Reason:     message,
				StatusCode: response.StatusCodeValue,
			}
			log.Debug("Answer finished, reason: " + message)
			// 异常结束对话
			e := websocket.SendRequestWithTimeout(ctx, "commitMsg/finish",
				commitMsgFinish, nil, 3*time.Second)
			if e != nil {
				log.Error("Generate commit message failed, send request commitMsg/finish error:", e)
			}
			return
		}
		body, err := definition.NewChatBody(response.Body)
		if err != nil {
			log.Error("Unmarshal commitMsg body error: ", err)
			return
		}
		text := body.GetOutputText()
		appendText := strings.TrimPrefix(text, lastCommitMsgData)
		lastCommitMsgData = text
		commitMsgAnswer := definition.CommitMsgAnswer{
			RequestId: params.RequestId,
			Text:      appendText,
			Timestamp: time.Now().UnixMicro(),
		}

		// 普通回复
		e := websocket.SendRequestWithTimeout(ctx, "commitMsg/answer",
			commitMsgAnswer, nil, 3*time.Second)
		if e != nil {
			log.Error("Send request commitMsg/answer error:", e)
			return
		}
	}, func() {
		log.Error("CommitMsg generate timeout")
		// 超时，结束对话
		commitMsgFinish := definition.CommitMsgFinish{
			RequestId:  params.RequestId,
			Reason:     "{\"message\":\"CommitMsg generate timeout\"}",
			StatusCode: 408,
		}
		e := websocket.SendRequestWithTimeout(ctx, "commitMsg/finish",
			commitMsgFinish, nil, 3*time.Second)
		if e != nil {
			log.Error("Generate commit message timeout, send request commitMsg/finish error:", e)
			return
		}
	})
	return err
}

// doPostCommitMsgFilter 执行commitMsg后置过滤
func doPostCommitMsgFilter(ctx context.Context, params definition.CommitMsgGenerateParam, commitMsg string) {
	err := filter.PostFilterCommitMsg4ModelResponse(ctx, params, commitMsg)
	if err != nil {
		log.Warnf("Post filter commit msg failed, err=%s", err.Error())
		return
	}
}

func buildRemoteQuest(params definition.CommitMsgGenerateParam, ctx context.Context) (definition.CommitMsgGenerateRequest, error) {
	var preferredLanguage string
	if strings.Contains(params.PreferredLanguage, "zh") {
		preferredLanguage = PreferredLangChinese
	} else {
		preferredLanguage = PreferredLangEnglish
	}

	if len(params.CommitMessages) >= 3 {
		params.CommitMessages = params.CommitMessages[:3]
	}

	promptInput := prompt.CommitMsgGeneratePromptInput{
		BaseInput: prompt.BaseInput{
			RequestId: params.RequestId,
		},
		CodeDiffs:         params.CodeDiffs,
		CommitMsgs:        params.CommitMessages,
		PreferredLanguage: preferredLanguage,
	}
	//截断长度
	promptInput = truncateLength(promptInput)

	promptText, err := prompt.Engine.RenderCommitMsgGeneratePrompt(promptInput)
	if err != nil {
		log.Errorf("build prompt failed, err: %s", err)
		return definition.CommitMsgGenerateRequest{}, err
	}

	userPrompt, err := prompt.Engine.RenderCommitMsgGenerateUserPrompt(promptInput)
	if err != nil {
		log.Errorf("build commit gen user prompt failed, err: %s", err)
		return definition.CommitMsgGenerateRequest{}, err
	}
	log.Debugf("build commit msg prompt: %s", promptText)

	r := definition.CommitMsgGenerateRequest{
		SystemRoleContent: promptText,
		ChatPrompt:        userPrompt,
		Stream:            params.Stream,
		RequestId:         params.RequestId,
		Parameters: map[string]any{
			"temperature": 0.9,
		},
		AgentId: "commit_message",
	}

	// fetch system rules
	customSystemRoleContent := systemrule.AppendSystemRules(ctx, systemrule.AppendSystemRuleReq{
		OriginSystemPrompt:  promptText,
		SessionId:           "",
		RequestId:           params.RequestId,
		IsEnableProjectRule: params.PluginPayloadConfig.IsEnableProjectRule,
		Scene:               systemrule.CommitMsg,
	})
	if customSystemRoleContent != "" {
		r.SystemRoleContent = customSystemRoleContent
	}

	return r, nil
}

// 删除特定开头的行
// "Index: projex-ai-biz/src/main/java/com/alibaba/projex/ai/biz/es/TestNewFile.java
// ===================================================================
// diff --git a/projex-ai-biz/src/main/java/com/alibaba/projex/ai/biz/es/TestNewFile.java b/projex-ai-biz/src/main/java/com/alibaba/projex/ai/biz/es/TestNewFile.java
// "new file mode "
// "deleted file mode "
// "index "
// "old mode "
// "new mode "
// "similarity index "
// --- /dev/null和+++ /dev/null 替换成 None
// "\ No newline"

// 编译正则表达式，用于匹配行尾的"(date 数字)"格式
var lineEndBracketPostfixReg = regexp.MustCompile(`(\(date \d+\))$`)

func fixCommitMsgFormat(params *definition.CommitMsgGenerateParam) {
	if len(params.CodeDiffs) > 0 {
		fixedDiffs := make([]string, 0)
		for _, diff := range params.CodeDiffs {
			// 删除特定开头的行
			filteredLines := make([]string, 0)
			lines := strings.Split(diff, "\n")
			for _, line := range lines {
				if strings.HasPrefix(line, "Index:") || strings.HasPrefix(line, "diff --git") ||
					strings.HasPrefix(line, "==========================") ||
					strings.HasPrefix(line, "new file mode ") ||
					strings.HasPrefix(line, "deleted file mode ") ||
					strings.HasPrefix(line, "index ") ||
					strings.HasPrefix(line, "old mode ") ||
					strings.HasPrefix(line, "new mode ") ||
					strings.HasPrefix(line, "similarity index ") ||
					strings.HasPrefix(line, "new mode ") ||
					strings.HasPrefix(line, "\\ No newline") {

					continue
				} else if strings.HasPrefix(line, "--- /dev/null") || strings.HasPrefix(line, "+++ /dev/null") {
					filteredLines = append(filteredLines, "None")
				} else {
					// 使用正则表达式的ReplaceAllString函数移除匹配到的部分
					line = lineEndBracketPostfixReg.ReplaceAllString(line, "")
					line = strings.TrimSpace(line)

					filteredLines = append(filteredLines, line)
				}
			}
			newDiff := strings.Join(filteredLines, "\n")
			if strings.Trim(newDiff, " \t\r\n") != "" {
				fixedDiffs = append(fixedDiffs, newDiff)
			}
		}
		if len(fixedDiffs) <= 0 {
			//没有任何内容时，一般是vsc新建空文件导致
			//兜底
			fixedDiffs = append(fixedDiffs, params.CodeDiffs...)
		}
		params.CodeDiffs = fixedDiffs
	}
}

func truncateLength(input prompt.CommitMsgGeneratePromptInput) prompt.CommitMsgGeneratePromptInput {
	t, err := tokenizer.NewQwenTokenizer(false)
	if err != nil {
		return input
	}
	newDiffs := []string{}
	tokenCount := 0

	tokenLimit := config.GlobalTruncateConfig.GetChunkLimit(config.TruncateKeCommitMsg)
	for _, diff := range input.CodeDiffs {
		tokens, err := t.Tokenize(diff)
		if err != nil {
			log.Warnf("tokenize commit msg error: %v", err)
			continue
		}
		tokenCount = len(tokens) + tokenCount
		if tokenCount > tokenLimit {
			break
		} else {
			newDiffs = append(newDiffs, diff)
		}
	}
	return prompt.CommitMsgGeneratePromptInput{
		BaseInput:         input.BaseInput,
		CommitMsgs:        input.CommitMsgs,
		CodeDiffs:         newDiffs,
		PreferredLanguage: input.PreferredLanguage,
	}
}
