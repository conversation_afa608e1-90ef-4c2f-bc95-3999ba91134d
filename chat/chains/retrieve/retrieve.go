package retrieve

import (
	"context"
	chainUtil "cosy/chat/chains/chain"
	"cosy/chat/chains/common"
	"cosy/components"
	"cosy/definition"
	"cosy/experiment"
	"cosy/indexing"
	"cosy/lang/indexer"
	"cosy/lang/indexer/rag"
	"cosy/log"
	"cosy/tokenizer"
	"cosy/util/flow"
	"errors"
)

// ChatBm25RetrieveFromCodebase 问答关键词检索
func ChatBm25RetrieveFromCodebase(env *flow.FlowEnvironment) error {
	requestId := env.Get(common.KeyRequestId).(string)
	fileIndexer, ok := env.GetContextValue(definition.ContextKeyFileIndexer).(*indexing.ProjectFileIndex)
	if !ok {
		return errors.New("no client found in context")
	}
	//refinedQueryResult, ok := env.GetWithFound(common.KeyRequirementAnalysisResult)
	//if !ok {
	//	return errors.New("no requirement result found")
	//}
	//analysisResult, _ := refinedQueryResult.(common.RequirementAnalysisResult)

	// 随便构建一个query
	analysisResult := common.RequirementAnalysisResult{}
	query := buildRetrieveQuery(env, analysisResult)

	chatFileIndexer, ok := fileIndexer.GetChatRetrieveFileTextIndexer()
	if !ok {
		return errors.New("no chat retrieve file indexer found")
	}
	retrieveEngine, err := chatFileIndexer.GetTextRetrieveEngine(true)
	if err != nil {
		log.Warnf("[workspace rag] get text retrieve engine error. requestId: %s", requestId)
		return errors.New("retrieve chunk error")
	}

	textRetrieveResult, err := retrieveEngine.Retrieve(context.Background(), query)
	if err != nil {
		log.Warnf("[workspace rag] text retrieve error. requestId: %s", requestId)
		return errors.New("retrieve chunk error")
	}
	statChunkTokenCount(env, chainUtil.ConvertToCodeChunks(textRetrieveResult.Chunks))
	env.Set(common.KeyWorkspaceTextRetrieveResult, textRetrieveResult)
	return nil
}

func buildRetrieveQuery(env *flow.FlowEnvironment, r common.RequirementAnalysisResult) rag.TextQuery {
	var query string
	var ok bool
	if query, ok = env.Get(common.KeyUserInputQuery).(string); !ok {
		return rag.TextQuery{}
	}

	if len(query) > 0 {
		query = definition.ReplaceAllContextInfo(query)
	}

	return rag.TextQuery{
		Fields: []string{"*"},
		Conditions: []rag.TextCondition{
			{
				FieldName: "index_content",
				Query:     query,
				Boost:     1.5,
			},
			{
				FieldName: "index_focus",
				Query:     query,
				Boost:     3.0,
			},
			//{
			//	FieldName: "index_content",
			//	Query:     r.RefinedQuestion,
			//	Boost:     1.0,
			//},
			//{
			//	FieldName: "index_focus",
			//	Query:     r.RefinedQuestion,
			//	Boost:     2.0,
			//},
			//{
			//	FieldName: "index_focus",
			//	Query:     strings.Join(r.Keywords, " "),
			//	Boost:     2.0,
			//},
			{
				FieldName: "code_category",
				Query:     indexer.NormalCategory,
				Boost:     1.0,
			},
		},
		Operator: rag.MatchOr,
		Size:     80,
		From:     0,
	}
}

// ChatIndexChunkEmbedding 索引并embedding检索到的代码块
func ChatIndexChunkEmbedding(env *flow.FlowEnvironment) error {
	requestId := env.Get(common.KeyRequestId).(string)
	var relevantFileChunks []indexer.CodeChunk
	refinedQueryResult, ok := env.GetWithFound(common.KeyRequirementAnalysisResult)
	if !ok {
		return errors.New("no requirement result found")
	}
	analysisResult, _ := refinedQueryResult.(common.RequirementAnalysisResult)
	fileIndexer, ok := env.GetContextValue(definition.ContextKeyFileIndexer).(*indexing.ProjectFileIndex)
	if !ok {
		return errors.New("no client found in context")
	}
	chatFileIndexer, ok := fileIndexer.GetChatRetrieveFileTextIndexer()
	if !ok {
		return errors.New("no chat retrieve file indexer found")
	}
	textRetrieveEngine, err := chatFileIndexer.GetTextRetrieveEngine(true)
	if err != nil {
		log.Warnf("[workspace rag] get text retrieve engine error. requestId: %s", requestId)
		return errors.New("retrieve chunk error")
	}
	if len(analysisResult.RelevantFiles) > 0 {
		analysisResult.RelevantFiles = RepairFilepaths(fileIndexer, analysisResult.RelevantFiles)
		chunks, err := textRetrieveEngine.BatchGetFileChunks(analysisResult.RelevantFiles, 20)
		if err != nil {
			log.Errorf("[workspace rag] get file chunks error. requestId: %s, error: %v", requestId, err)
		}
		if len(chunks) > 0 {
			env.Set(common.KeyWorkspaceRelevantFileChunks, chunks)
			relevantFileChunks = append(relevantFileChunks, chunks...)
		}
	} else {
		env.Set(common.KeyWorkspaceRelevantFileChunks, make([]indexer.CodeChunk, 0))
	}

	return nil
}

func RepairFilepaths(f *indexing.ProjectFileIndex, filepathes []string) []string {
	if len(filepathes) <= 0 {
		return filepathes
	}

	fileIndexer, ok := f.GetWorkspaceTreeFileIndexer()
	if !ok {
		return filepathes
	}
	var repaired []string
	for _, path := range filepathes {
		r, err := fileIndexer.WorkspaceTree.GetClosestFilePath(path)
		if err != nil {
			log.Errorf("[workspace rag] get closest file path error. path: %s, error: %v", path, err)
			continue
		}
		repaired = append(repaired, r)
	}
	return repaired
}

type VectorRetrieveNode struct {
	Query     string
	ResultKey string
	Embedder  *components.LingmaEmbedder
}

func (n *VectorRetrieveNode) Run(env *flow.FlowEnvironment) error {
	if n.Embedder == nil {
		return nil
	}
	requestId := env.Get(common.KeyRequestId).(string)
	queryEmbeddings, err := n.Embedder.CreateEmbedding(env.GetContext(), []string{n.Query}, components.TextTypeQuery)
	if err != nil {
		log.Warnf("[workspace rag] fetch embedding error. requestId: %s", requestId)
		return nil
	}
	if len(queryEmbeddings) == 0 {
		log.Warnf("[workspace rag] create embedding error. requestId: %s", requestId)
		return nil
	}

	fileIndexer, ok := env.GetContextValue(definition.ContextKeyFileIndexer).(*indexing.ProjectFileIndex)
	if !ok {
		return errors.New("no client found in context")
	}
	chatFileIndexer, ok := fileIndexer.GetChatRetrieveFileVectorIndexer()
	if !ok {
		return errors.New("no chat retrieve file indexer found")
	}
	vectorRetrieveEngine, err := chatFileIndexer.GetServerVectorRetrieveEngine()
	if err != nil {
		log.Errorf("[workspace rag] get vector retrieve engine error. requestId: %s err: %v", requestId, err)
		return nil
	} else {
		vectorRetrieveScore := experiment.ConfigService.GetDoubleConfigValue(definition.ExperimentKeyWorkspaceRagVectorRetrieveScoreThreshold,
			experiment.ConfigScopeClient, common.WorkspaceRagVectorRetrieveScoreThreshold)

		vectorRetrieveResult, retrieveErr := vectorRetrieveEngine.Retrieve(definition.QueryCondition{
			Query:          n.Query,
			QueryEmbedding: queryEmbeddings[0],
			TopK:           80,
			ScoreThreshold: vectorRetrieveScore,
		})
		if retrieveErr != nil {
			log.Warnf("[workspace rag] vector retrieve error. requestId: %s, error: %v", requestId, retrieveErr)
			return nil
		}

		statChunkTokenCount(env, chainUtil.ConvertToCodeChunks(vectorRetrieveResult.Chunks))
		env.Set(n.ResultKey, vectorRetrieveResult)
	}
	return nil
}

// statChunkTokenCount 统计检索到的代码块的token数量
func statChunkTokenCount(env *flow.FlowEnvironment, chunks []indexer.CodeChunk) {
	totalTokenCnt := 0
	for _, chunk := range chunks {
		tokenCount, err := tokenizer.CalQwenTokenCount(chunk.Content)
		if err != nil {
			log.Debugf("[workspace retrieval] embedding retrieval chunk tokenize error: %v", err)
		}
		totalTokenCnt += tokenCount
	}
	tokenCount, ok := env.Get(common.KeyStatChunkTokenCountUsage).(int)
	if ok {
		env.Set(common.KeyStatChunkTokenCountUsage, tokenCount+totalTokenCnt)
	} else {
		env.Set(common.KeyStatChunkTokenCountUsage, totalTokenCnt)
	}
}
