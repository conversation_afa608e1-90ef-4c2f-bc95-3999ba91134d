package quest

import (
	"context"
	"cosy/chat/agents/coder"
	"cosy/chat/agents/quest/design"
	"cosy/chat/agents/quest/longrunning"
	"cosy/chat/agents/quest/research"
	"cosy/chat/agents/quest/support"
	agentSupport "cosy/chat/agents/support"
	chainUtil "cosy/chat/chains/chain"
	"cosy/chat/chains/common"
	"cosy/definition"
	"cosy/errors"
	"cosy/log"
	"cosy/websocket"
	"fmt"
	"runtime/debug"
	"time"

	"github.com/tmc/langchaingo/chains"
	"github.com/tmc/langchaingo/memory"
	"github.com/tmc/langchaingo/schema"
)

type Quest<PERSON>hain struct {
}

func (c QuestChain) Call(ctx context.Context, inputs map[string]any, options ...chains.ChainCallOption) (map[string]any, error) {
	agentInput := inputs
	rawInputParams := inputs[common.KeyChatAskParams].(*definition.AskParams)
	sessionId := rawInputParams.SessionId
	requestId := rawInputParams.RequestId
	mode := rawInputParams.Mode
	targetAgent := longrunning.BuilderIdentifier
	if mode == definition.SessionModeLongRunning {
		targetAgent = longrunning.BuilderIdentifier
	} else if mode == definition.SessionModeDesign {
		targetAgent = design.BuilderIdentifier
	}
	log.Debugf("start agent=%s, sessionId=%s, requestId=%s", targetAgent, sessionId, requestId)

	agent, err := agentSupport.MakeAgent(requestId, targetAgent)
	if err != nil {
		log.Errorf("create agent error, agent=%s, requestId=%s, error=%v", targetAgent, requestId, err)
		return nil, err
	}
	support.CreateSessionAndRecord(ctx, rawInputParams)
	preferredLanguage := chainUtil.GetPreferredLanguage(rawInputParams.ChatContext)
	ctx = context.WithValue(ctx, common.KeyPreferredLanguage, preferredLanguage)
	// TODO 这个先写回去，防止本地没办法调试
	rawInputParams.SessionType = definition.SessionTypeAssistant
	workspace, ok := ctx.Value(definition.ContextKeyWorkspace).(definition.WorkspaceInfo)
	if !ok {
		return nil, fmt.Errorf("workspace info not found in context")
	}
	projectPath, _ := workspace.GetWorkspaceFolder()
	support.TriggerFileIndexer(ctx, projectPath)

	// TODO 先用if-else进行判断
	if targetAgent == longrunning.BuilderIdentifier {
		// 在agent循环中处理外部发送的通知
		noticeProcessor := &longrunning.NoticeProcessor{
			StateInChan: make(chan agentSupport.StateInNotice, 10),
		}
		agentSupport.AddNoticeProcessor(requestId, noticeProcessor)
		defer agentSupport.RemoveNoticeProcessor(requestId)
		agentInput["agent_state_in_chan"] = noticeProcessor.StateInChan
		ctx, err = longrunning.InitAgentContext(ctx, sessionId, requestId, rawInputParams)
	} else if targetAgent == research.BuilderIdentifier {
		ctx, err = research.InitAgentContext(ctx, sessionId, requestId)
	} else if targetAgent == design.BuilderIdentifier {
		ctx, err = design.InitAgentContext(ctx, sessionId, requestId)
	} else {
		log.Errorf("unknown agent=%s, requestId=%s", targetAgent, requestId)
		return nil, fmt.Errorf("unknown agent=%s", targetAgent)
	}
	if err != nil {
		log.Errorf("init context error, agent=%s, requestId=%s, error=%v", targetAgent, requestId, err)
		return nil, err
	}
	//目前 RequestSetId 和 RequestId 一致
	ctx = context.WithValue(ctx, common.KeyRequestId, requestId)
	ctx = context.WithValue(ctx, common.KeyRequestSetId, requestId)
	ctx = context.WithValue(ctx, common.KeySessionId, sessionId)
	ctx = context.WithValue(ctx, common.KeyChatAskParams, rawInputParams)

	var runErr error
	func() {
		defer func() {
			if r := recover(); r != nil {
				stack := debug.Stack()
				errMsg := fmt.Sprintf("Fatal error in agent.RunSync: %v\n%s", r, stack)
				log.Error(errMsg)
				runErr = fmt.Errorf("fatal error: %v", r)
			}
		}()
		runErr = agent.RunSync(ctx, agentInput)
	}()

	askResult := definition.AskResult{
		RequestId: requestId,
		ErrorMsg:  "",
		IsSuccess: runErr == nil,
	}
	inputs[common.KeyChatAskResult] = askResult

	if runErr != nil {
		log.Errorf("run sync error, agent=%s, requestId=%s, err=%v", targetAgent, requestId, runErr)
		// 全部结束了发送ChatFinish
		chatFinish := definition.ChatFinish{
			RequestId:  rawInputParams.RequestId,
			SessionId:  rawInputParams.SessionId,
			Reason:     runErr.Error(),
			StatusCode: errors.SystemError,
		}
		if chatFinish.Extra == nil {
			chatFinish.Extra = make(map[string]any)
		}
		chatFinish.Extra["sessionType"] = rawInputParams.SessionType
		chatFinish.Extra["intentionType"] = definition.AIDeveloperIntentDetectCommonAgent
		chatFinish.Extra["mode"] = definition.SessionModeAgent

		// 结束对话
		e2 := websocket.SendRequestWithTimeout(ctx, "chat/finish",
			chatFinish, nil, 3*time.Second)
		if e2 != nil {
			log.Error("Chat success, send request chat/finish error:", e2)
		}
		coder.ReportError(ctx, rawInputParams.SessionId, rawInputParams.RequestId, errors.SystemError, runErr, true)
		return nil, runErr
	}

	log.Debugf("finish chat, agent=%s, sessionId=%s, requestId=%s", targetAgent, sessionId, requestId)
	return inputs, nil
}

func (c QuestChain) GetMemory() schema.Memory {
	return memory.NewSimple()
}

func (c QuestChain) GetInputKeys() []string {
	return []string{
		common.KeyChatAskParams,
	}
}

func (c QuestChain) GetOutputKeys() []string {
	return []string{}
}
