package chains

import (
	"context"
	chainUtil "cosy/chat/chains/chain"
	"cosy/chat/chains/common"
	"cosy/chat/service"
	"cosy/client"
	"cosy/components"
	"cosy/config"
	"cosy/definition"
	"cosy/log"
	"cosy/remote"
	"cosy/stable"
	"cosy/tokenizer"
	"cosy/util"
	"cosy/websocket"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/tmc/langchaingo/chains"
	"github.com/tmc/langchaingo/memory"
	"github.com/tmc/langchaingo/schema"
)

const analysisStep = 1

// 处理几个历史问题
const historyLength = 10
const timeout = 20 * time.Second

var teamDocPrefix = "#team docs "

type RefineQueryInput struct {
	Query   string    `json:"query"`
	History []History `json:"history"`
}

type History struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

type RefineHistoryOutput struct {
	RequestID *string `json:"requestId"`
	Data      Data    `json:"data"`
	DebugInfo *string `json:"debugInfo"`
	Message   *string `json:"message"`
	Status    int     `json:"status"`
}

type Data struct {
	ConversationRewriter []string `json:"conversationRewriter"`
	OutputTokens         int      `json:"outputTokens"`
	TimeCost             float64  `json:"timeCost"`
	InputTokens          int      `json:"inputTokens"`
}

type KnowledgeAnalysisPromptInput struct {
	UserInputQuery string
}

type KnowledgeRagRequirementAnalysisChain struct {
	docRetriever schema.Retriever
}

func NewKnowledgeRagRequirementAnalysisChain() KnowledgeRagRequirementAnalysisChain {
	docRetriever := components.NewLingmaDocRetrever()
	return KnowledgeRagRequirementAnalysisChain{
		docRetriever: docRetriever,
	}
}

func (c KnowledgeRagRequirementAnalysisChain) Call(ctx context.Context, inputs map[string]any, options ...chains.ChainCallOption) (map[string]any, error) {
	beginTime := time.Now()
	sessionId := inputs[common.KeySessionId].(string)
	requestId := inputs[common.KeyRequestId].(string)
	localeLanguage := inputs[common.KeyLocaleLanguage].(string)

	enabledKnowledgeRag := chainUtil.GetBooleanValue(inputs, common.KeyEnableKnowledgeRag, false)
	if !enabledKnowledgeRag {
		log.Debugf("knowledge rag not enabled, skip requirement analysis. requestId: %s", requestId)
		inputs[common.KeyRefinedHistoryQuery] = ""
		return inputs, nil
	}

	websocket.WsInst.RequestClient(ctx, "chat/process_step_callback", definition.ChatProcessStep{
		SessionId:   sessionId,
		RequestId:   requestId,
		Step:        definition.ChatStepRefineQuery,
		Description: common.ChatProcessDescriptionLoader.Translate(localeLanguage, definition.ChatStepRefineQuery),
		Status:      definition.ChatStepStatusDoing,
	}, nil, websocket.ClientTimeout)

	var wg sync.WaitGroup
	wg.Add(analysisStep)
	errorChannel := make(chan error, analysisStep)

	var refineQuery string
	go func(refineQuery *string) {
		defer wg.Done()
		understand, err := c.refineQuery(ctx, inputs, sessionId)
		if err != nil {
			errorChannel <- err

			go stable.ReportCommonError(ctx, definition.MonitorErrorKeyKnowledgeRag, requestId, err, nil)
			return
		}
		if len(understand.Data.ConversationRewriter) > 0 {
			*refineQuery = understand.Data.ConversationRewriter[0]
		}
	}(&refineQuery)

	wg.Wait()
	close(errorChannel)

	for err := range errorChannel {
		if err != nil {
			log.Warn("rag requirement analysis got err", err)
		}
	}

	websocket.WsInst.RequestClient(ctx, "chat/process_step_callback", definition.ChatProcessStep{
		SessionId:   sessionId,
		RequestId:   requestId,
		Step:        definition.ChatStepRefineQuery,
		Description: common.ChatProcessDescriptionLoader.Translate(localeLanguage, definition.ChatStepRefineQuery),
		Status:      definition.ChatStepStatusDone,
	}, nil, websocket.ClientTimeout)

	if refineQuery != "" {
		inputs[common.KeyRefinedHistoryQuery] = refineQuery
	}

	log.Debugf("knowledge rag query analysis. refined query: %s", refineQuery)

	finishTime := time.Now()

	inputs[definition.RAGReportKeyKnowledgeRequirementAnalysisTimeCostMs] = strconv.FormatInt(finishTime.Sub(beginTime).Milliseconds(), 10)

	tokenizeStartTime := time.Now()

	totalTokenCnt := 0
	tokenCount, err := tokenizer.CalQwenTokenCount(refineQuery)
	if err != nil {
		log.Errorf("[knowledge requirement analysis] get refineQuery error. requestId: %s, error: %v", requestId, err)
	}
	totalTokenCnt += tokenCount

	refineInput, err := parseHistoryToRefineRequest(ctx, inputs, sessionId)
	if err != nil || refineInput == nil {
		log.Errorf("[knowledge requirement analysis] parse history to refine query failed. requestId: %s, error: %v", requestId, err)
	}

	if refineInput != nil {
		tokenCount, err = tokenizer.CalQwenTokenCount(refineInput.Query)
		if err != nil {
			log.Errorf("[knowledge requirement analysis] tokenize refine query error. requestId: %s, error: %v", requestId, err)
		}
		totalTokenCnt += tokenCount

		for _, history := range refineInput.History {
			tokenCount, err = tokenizer.CalQwenTokenCount(history.Content)
			if err != nil {
				log.Errorf("[knowledge requirement analysis] tokenize history content error. requestId: %s, error: %v", requestId, err)
			}
			totalTokenCnt += tokenCount
		}
	}

	inputs[definition.RAGReportKeyKnowledgeRequirementAnalysisTokenCost] = strconv.Itoa(totalTokenCnt)

	tokenizerFinishTime := time.Now()

	log.Infof("[knowledge requirement analysis] tokenize takes %d ms", tokenizerFinishTime.Sub(tokenizeStartTime))

	return inputs, nil
}

func (c KnowledgeRagRequirementAnalysisChain) refineQuery(ctx context.Context, inputs map[string]any, sessionId string) (*RefineHistoryOutput, error) {
	refineInput, err := parseHistoryToRefineRequest(ctx, inputs, sessionId)
	refineResult := RefineHistoryOutput{}
	if err != nil {
		return nil, err
	}
	httpPayload := remote.HttpPayload{
		Payload:       util.ToJsonStr(refineInput),
		EncodeVersion: config.Remote.MessageEncode,
	}

	refineRequest, err := remote.BuildBigModelAuthRequest(http.MethodPost, definition.UrlPathRefineQueryEndpoint, httpPayload)
	if err != nil {
		var tokenExpiredError *definition.TokenExpiredError
		if errors.As(err, &tokenExpiredError) {
			status := definition.AuthStatusResult{
				Status:          definition.AuthStatusInit,
				Quota:           0,
				WhitelistStatus: definition.WhitelistUnknown,
			}
			websocket.SendBroadcastWithTimeout(ctx, "auth/report", status, nil)
		}
		return nil, err
	}

	resp, err := client.GetDocEmbeddingClient().Do(refineRequest)
	if err != nil {
		return nil, err
	}

	defer resp.Body.Close()
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("refine query failed. satatus code: %d. response body: %s", resp.StatusCode, bodyBytes)
	}

	err = json.Unmarshal(bodyBytes, &refineResult)
	if err != nil {
		return nil, err
	}

	return &refineResult, nil
}

func parseHistoryToRefineRequest(ctx context.Context, inputs map[string]any, sessionId string) (*RefineQueryInput, error) {
	askParams, ok := inputs[common.KeyChatAskParams].(*definition.AskParams)
	if !ok {
		return nil, fmt.Errorf("refine query parse KeyChatAskParams failed")
	}
	//chatContext, ok := askParams.ChatContext.(map[string]interface{})
	//if !ok {
	//	return nil, fmt.Errorf("refine query parse ChatContext failed")
	//}
	userInputQuery := chainUtil.GetUserInputQueryTrimCode(askParams.ChatContext)
	userInputQuery = definition.ReplaceAllContextInfo(userInputQuery)
	result := &RefineQueryInput{
		Query: strings.TrimSpace(userInputQuery),
	}
	if result.Query == "" {
		return nil, fmt.Errorf("refine query parse Query failed")
	}

	var histories []History
	records, err := service.SessionServiceManager.GetSessionRecordsForHistoryBuild(sessionId)
	if err != nil {
		return nil, err
	}
	if len(records) > historyLength {
		records = records[len(records)-historyLength-1 : len(records)-1]
	}
	for _, r := range records {
		userInput := r.Question
		if strings.HasPrefix(userInput, teamDocPrefix) {
			userInput = strings.Replace(r.Question, teamDocPrefix, "", 1)
		}
		user := History{
			Role:    "user",
			Content: userInput,
		}
		assistant := History{
			Role:    "assistant",
			Content: r.Answer,
		}
		histories = append(histories, user, assistant)
	}
	result.History = histories
	return result, nil
}

func (c KnowledgeRagRequirementAnalysisChain) GetOutputKeys() []string {
	return []string{}
}

func (c KnowledgeRagRequirementAnalysisChain) GetInputKeys() []string {
	return []string{}
}

func (c KnowledgeRagRequirementAnalysisChain) GetMemory() schema.Memory {
	return memory.NewSimple()
}
