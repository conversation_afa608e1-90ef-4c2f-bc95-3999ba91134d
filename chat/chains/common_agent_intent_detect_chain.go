package chains

import (
	"context"

	chainUtil "cosy/chat/chains/chain"
	"cosy/chat/chains/common"
	chatUtil "cosy/chat/util"
	"cosy/config"
	"cosy/definition"
	cosyErrors "cosy/errors"
	"cosy/global"
	"cosy/log"
	"cosy/prompt"
	"cosy/remote"
	"cosy/stable"
	"cosy/user"
	"cosy/util"

	"github.com/tmc/langchaingo/chains"
	"github.com/tmc/langchaingo/memory"
	"github.com/tmc/langchaingo/schema"
)

type CommonAgentIntentDetectChain struct {
}

func (c CommonAgentIntentDetectChain) Call(ctx context.Context, inputs map[string]any, options ...chains.ChainCallOption) (map[string]any, error) {
	log.Debug("CommonAgentIntentDetectChain called. ")

	rawInputParams := inputs[common.KeyChatAskParams].(*definition.AskParams)
	// 非assistant场景不做通用agent的意图识别校验
	if rawInputParams.SessionType != definition.SessionTypeAssistant && rawInputParams.SessionType != definition.SessionTypeCoder {
		log.Debugf("not assistant, ignore common agent intent detect.")
		return inputs, nil
	}

	// 判断当前工程是否为空工程，如果是空工程，则路由到ask链路
	workspace := getCurrentWorkspace(ctx)
	if workspace == "" {
		log.Debug("current workspace is empty, common agent intent detect to common ask.")
		inputs[common.KeyCommonAssistantIntentDetectResult] = common.CommonAgentIntentDetectionResult{
			AgentName: definition.AgentNameCommonChat,
			Intent:    definition.AIDeveloperIntentDetectChat,
		}
		return inputs, nil
	}

	// 判断是否为retry请求，retry请求不再额外去做意图识别
	// CONTINUE_TASK任务也不做意图识别，使用上一次记录的意图，目前只有agent模型下有CONTINUE_TASK
	if util.IsRetryRemoteAsk(rawInputParams) || rawInputParams.ChatTask == definition.CONTINUE_TASK {
		// 获取历史记录
		record, err := chatUtil.GetLastChatRecord(rawInputParams)
		if err != nil {
			log.Warnf("get history error. sessionId: %s, err: %+v", rawInputParams.SessionId, err)
			return inputs, &cosyErrors.Error{
				Code:    definition.ChatFinishStatusCodeRetryError,
				Message: "get history error, empty history for retry task. sessionId: " + rawInputParams.SessionId,
			}
		}
		if record.IntentionType == definition.AIDeveloperIntentDetectCommonAgent {
			// 内置通用agent场景
			inputs[common.KeyCommonAssistantIntentDetectResult] = common.CommonAgentIntentDetectionResult{
				AgentName: definition.AgentNameCommonDev,
				Intent:    definition.AIDeveloperIntentDetectCommonAgent,
			}
			return inputs, nil
		} else if record.IntentionType == definition.AIDeveloperIntentDetectUI2FeCode || record.IntentionType == definition.AIDeveloperIntentDetectDev {
			// 编码场景
			inputs[common.KeyAIDeveloperIntentDetectResult] = common.AIDeveloperIntentDetectionResult{
				Intent: record.IntentionType,
			}
			inputs[common.KeyCommonAssistantIntentDetectResult] = common.CommonAgentIntentDetectionResult{
				AgentName: definition.AgentNameDev,
				Intent:    record.IntentionType,
			}
			return inputs, nil
		}
		inputs[common.KeyAIDeveloperIntentDetectResult] = common.AIDeveloperIntentDetectionResult{
			Intent: record.IntentionType,
		}
		inputs[common.KeyCommonAssistantIntentDetectResult] = common.CommonAgentIntentDetectionResult{
			AgentName: definition.AgentNameCommonChat,
			Intent:    record.IntentionType,
		}
		return inputs, nil
	}

	contextProviderExtras := inputs[common.KeyContextProviderExtra].([]definition.CustomContextProviderExtra)
	hasImage := hasImageContext(contextProviderExtras)

	// 优先基于mode判断意图识别的路由链路
	mode := rawInputParams.Mode
	if mode == definition.SessionModeChat {
		if !config.GetAskModeUseTools() {
			// 没有开启agent模式，走通用ask链路
			inputs[common.KeyCommonAssistantIntentDetectResult] = common.CommonAgentIntentDetectionResult{
				AgentName: definition.AgentNameCommonChat,
				Intent:    definition.AIDeveloperIntentDetectChat,
			}
			return inputs, nil
		}
		// Lingma：判断是否为多模态链路，如果是多模态链路，维持原有逻辑，Qoder直接走agent模式
		if hasImage && !global.IsQoderProduct() {
			// 多模态场景维持原有逻辑，直接走通用ask链路
			inputs[common.KeyCommonAssistantIntentDetectResult] = common.CommonAgentIntentDetectionResult{
				AgentName: definition.AgentNameCommonChat,
				Intent:    definition.AIDeveloperIntentDetectChat,
			}
			return inputs, nil
		}

		// 自定义指令路由到通用agent
		taskDefinitionType := rawInputParams.TaskDefinitionType
		if taskDefinitionType == definition.TaskDefinitionTypeCustom {
			inputs[common.KeyCommonAssistantIntentDetectResult] = common.CommonAgentIntentDetectionResult{
				AgentName: definition.AgentNameCommonDev,
				Intent:    definition.AIDeveloperIntentDetectCommonAgent,
			}
			return inputs, nil
		}

		// 所有系统预置任务路由到通用agent
		if util.IsSystemCommandTask(rawInputParams.ChatTask) {
			inputs[common.KeyCommonAssistantIntentDetectResult] = common.CommonAgentIntentDetectionResult{
				AgentName: definition.AgentNameCommonDev,
				Intent:    definition.AIDeveloperIntentDetectCommonAgent,
			}
			return inputs, nil
		}

		// 默认使用通用内置agent
		inputs[common.KeyCommonAssistantIntentDetectResult] = common.CommonAgentIntentDetectionResult{
			AgentName: definition.AgentNameCommonDev,
			Intent:    definition.AIDeveloperIntentDetectCommonAgent,
		}
		return inputs, nil
	} else if mode == definition.SessionModeEdit {
		// edit场景优先判断是否为预制任务或自定义指令，预制任务与自定义指令走原自由问答链路
		taskDefinitionType := rawInputParams.TaskDefinitionType
		if taskDefinitionType == definition.TaskDefinitionTypeCustom {
			inputs[common.KeyCommonAssistantIntentDetectResult] = common.CommonAgentIntentDetectionResult{
				AgentName: definition.AgentNameCommonChat,
				Intent:    definition.AIDeveloperIntentDetectChat,
			}
			return inputs, nil
		}
		chatTaskType := rawInputParams.ChatTask
		if chatTaskType == definition.EXPLAIN_CODE || chatTaskType == definition.GENERATE_TESTCASE || chatTaskType == definition.OPTIMIZE_CODE || chatTaskType == definition.CODE_GENERATE_COMMENT || chatTaskType == definition.CODE_PROBLEM_SOLVE {
			inputs[common.KeyCommonAssistantIntentDetectResult] = common.CommonAgentIntentDetectionResult{
				AgentName: definition.AgentNameCommonChat,
				Intent:    definition.AIDeveloperIntentDetectChat,
			}
			return inputs, nil
		}
		// 调用意图识别判断当前意图
		if hasImage {
			// 多模态链路，额外判断是否为图生码路由
			detectResult := callIntentDetect(ctx, inputs, true)

			// 多模态图生码请求走特殊路由
			if detectResult.Intent == definition.AIDeveloperIntentDetectUI2FeCode {
				inputs[common.KeyCommonAssistantIntentDetectResult] = common.CommonAgentIntentDetectionResult{
					AgentName: definition.AgentNameDev,
					Intent:    definition.AIDeveloperIntentDetectUI2FeCode,
				}
				inputs[common.KeyAIDeveloperIntentDetectResult] = common.AIDeveloperIntentDetectionResult{
					Intent: definition.AIDeveloperIntentDetectUI2FeCode,
				}
				return inputs, nil
			}
			// 多模态编码场景走AIDeveloper路由
			if detectResult.Intent == definition.AIDeveloperIntentDetectDev {
				inputs[common.KeyCommonAssistantIntentDetectResult] = common.CommonAgentIntentDetectionResult{
					AgentName: definition.AgentNameDev,
					Intent:    definition.AIDeveloperIntentDetectDev,
				}
				inputs[common.KeyAIDeveloperIntentDetectResult] = common.AIDeveloperIntentDetectionResult{
					Intent: definition.AIDeveloperIntentDetectDev,
				}
				return inputs, nil
			}
		}

		// 非多模态链路通过意图识别进行决策判断，编码场景走AI Develop
		detectResult := callIntentDetect(ctx, inputs, false)
		if detectResult.Intent == definition.AIDeveloperIntentDetectUnittest {
			// 如果是生成单测的诉求，edit模式下走testAgent
			// 走TestAgent需要走到testAgent为Ai Developer适配的链路，为了不改造Ai Developer链路，适配所需的意图识别参数
			inputs[common.KeyAIDeveloperIntentDetectResult] = common.AIDeveloperIntentDetectionResult{
				Intent: definition.AIDeveloperIntentDetectUnittest,
			}
			return inputs, nil
		}
		if detectResult.Intent == definition.AIDeveloperIntentDetectDev {
			inputs[common.KeyCommonAssistantIntentDetectResult] = common.CommonAgentIntentDetectionResult{
				AgentName: definition.AgentNameDev,
				Intent:    definition.AIDeveloperIntentDetectDev,
			}
			inputs[common.KeyAIDeveloperIntentDetectResult] = common.AIDeveloperIntentDetectionResult{
				Intent: definition.AIDeveloperIntentDetectDev,
			}
			return inputs, nil
		}
		// edit模式下意图识别为非编码场景，走通用问答
		inputs[common.KeyCommonAssistantIntentDetectResult] = common.CommonAgentIntentDetectionResult{
			AgentName: definition.AgentNameCommonChat,
			Intent:    definition.AIDeveloperIntentDetectChat,
		}
		return inputs, nil
	}

	// mode是agent
	// Lingma：判断是否为多模态链路，如果是多模态链路，走特殊判断逻辑，Qoder：直接走agent模式
	if hasImage && !global.IsQoderProduct() {
		// 多模态场景判断模型是否支持多模态，如果支持则路由支agent链路
		modelConfig := chatUtil.PrepareModelConfig(*rawInputParams)
		if modelConfig != nil && modelConfig.IsVl {
			// 支持多模态
			log.Debugf("multi-modal support in agent mode. mode key: %s, name: %s", modelConfig.Key, modelConfig.DisplayName)

			inputs[common.KeyCommonAssistantIntentDetectResult] = common.CommonAgentIntentDetectionResult{
				AgentName: definition.AgentNameCommonDev,
				Intent:    definition.AIDeveloperIntentDetectCommonAgent,
			}
			return inputs, nil
		}

		// 调用意图识别服务判断是否为图生码请求
		detectResult := callIntentDetect(ctx, inputs, true)
		// 图生码请求走特殊路由
		if detectResult.Intent == definition.AIDeveloperIntentDetectUI2FeCode {
			inputs[common.KeyCommonAssistantIntentDetectResult] = common.CommonAgentIntentDetectionResult{
				AgentName: definition.AgentNameDev,
				Intent:    definition.AIDeveloperIntentDetectUI2FeCode,
			}
			inputs[common.KeyAIDeveloperIntentDetectResult] = common.AIDeveloperIntentDetectionResult{
				Intent: definition.AIDeveloperIntentDetectUI2FeCode,
			}
			return inputs, nil
		}

		// 多模态编码请求
		if detectResult.Intent == definition.AIDeveloperIntentDetectDev {
			inputs[common.KeyCommonAssistantIntentDetectResult] = common.CommonAgentIntentDetectionResult{
				AgentName: definition.AgentNameDev,
				Intent:    definition.AIDeveloperIntentDetectDev,
			}
			inputs[common.KeyAIDeveloperIntentDetectResult] = common.AIDeveloperIntentDetectionResult{
				Intent: definition.AIDeveloperIntentDetectDev,
			}
			return inputs, nil
		}

		// 默认的多模态问答请求走通用ask链路
		inputs[common.KeyCommonAssistantIntentDetectResult] = common.CommonAgentIntentDetectionResult{
			AgentName: definition.AgentNameCommonChat,
			Intent:    definition.AIDeveloperIntentDetectChat,
		}
		return inputs, nil
	}

	// 自定义指令维持原有逻辑，路由到ask链路
	taskDefinitionType := rawInputParams.TaskDefinitionType
	if taskDefinitionType == definition.TaskDefinitionTypeCustom {
		inputs[common.KeyCommonAssistantIntentDetectResult] = common.CommonAgentIntentDetectionResult{
			AgentName: definition.AgentNameCommonChat,
			Intent:    definition.AIDeveloperIntentDetectChat,
		}
		return inputs, nil
	}

	// 预制任务-代码解释路由到ask链路
	chatTaskType := rawInputParams.ChatTask
	if chatTaskType == definition.EXPLAIN_CODE {
		inputs[common.KeyCommonAssistantIntentDetectResult] = common.CommonAgentIntentDetectionResult{
			AgentName: definition.AgentNameCommonChat,
			Intent:    definition.AIDeveloperIntentDetectChat,
		}
		return inputs, nil
	}

	// 预制任务-生成单元测试，生成注释，优化代码路由到内置通用agent处理
	if chatTaskType == definition.GENERATE_TESTCASE || chatTaskType == definition.CODE_GENERATE_COMMENT || chatTaskType == definition.OPTIMIZE_CODE {
		inputs[common.KeyCommonAssistantIntentDetectResult] = common.CommonAgentIntentDetectionResult{
			AgentName: definition.AgentNameCommonDev,
			Intent:    definition.AIDeveloperIntentDetectCommonAgent,
		}
		return inputs, nil
	}

	// 默认使用通用内置agent
	inputs[common.KeyCommonAssistantIntentDetectResult] = common.CommonAgentIntentDetectionResult{
		AgentName: definition.AgentNameCommonDev,
		Intent:    definition.AIDeveloperIntentDetectCommonAgent,
	}
	return inputs, nil
}

func isUseTestAgent(ctx context.Context, inputs map[string]any) bool {
	cachedUserInfo := user.GetCachedUserInfo()
	// 非企业专属版，不使用testAgent
	if cachedUserInfo.UserType != definition.UserTypeEnterpriseDedicated {
		return false
	}
	// 企业专属版场景调用意图识别服务进行判断是否要路由到testAgent
	detectionResult := callIntentDetect(ctx, inputs, false)
	return detectionResult.Intent == definition.AIDeveloperIntentDetectUnittest
}

// 发起意图识别
func callIntentDetect(ctx context.Context, inputs map[string]any, isMultiModal bool) common.AIDeveloperIntentDetectionResult {
	// buildRequest
	rawInputParams := inputs[common.KeyChatAskParams].(*definition.AskParams)
	requestId := rawInputParams.RequestId
	input := buildBasicIntentInput(ctx, inputs, isMultiModal)

	// 多模态链路处理
	if isMultiModal {
		renderedText, err := prompt.Engine.RenderAiDevelopMultiModalIntentDetectPrompt(input)
		if err != nil {
			log.Warnf("Failed to render multi modal prompt: %v", err)
			return common.AIDeveloperIntentDetectionResult{
				// 默认走dev_agent
				Intent: definition.AIDeveloperIntentDetectDev,
			}
		}
		contextProviderExtras := inputs[common.KeyContextProviderExtra].([]definition.CustomContextProviderExtra)
		imageUrls := chatUtil.FindImageUrlsFromContext(contextProviderExtras)
		request := MultimodalIntentDetectRequest{
			ChatPrompt: renderedText,
			Stream:     true,
			RequestId:  requestId,
			Parameters: map[string]any{
				"temperature": 0.1,
			},
			AgentId:     definition.AgentAIDeveloper,
			TaskId:      definition.AgentTaskMultimodalIntentDetect,
			Version:     "2",
			SessionType: definition.SessionTypeDeveloper,
			ImageUrls:   imageUrls,
		}
		// 多模态意图请求构建
		outputResp, err := remote.ExecuteMultimodalAgentRequest(definition.AgentChatAskService, "llm_model_result", request,
			requestId, definition.AgentAIDeveloper)
		if err != nil {
			log.Warnf("Failed to detect CommonAgent intent: %v", err)
			go stable.ReportAgentRequestError(definition.AgentAIDeveloper, definition.AgentTaskMultimodalIntentDetect, requestId, err, nil)
			return common.AIDeveloperIntentDetectionResult{
				// 默认走dev_agent
				Intent: definition.AIDeveloperIntentDetectDev,
			}
		}
		detectionResult, err := parseIntentDetectOutput2(outputResp.Text)
		if err != nil {
			log.Warnf("Failed to parse CommonAgent intent: %v", err)
			return common.AIDeveloperIntentDetectionResult{
				// 默认走dev_agent
				Intent: definition.AIDeveloperIntentDetectDev,
			}
		}
		return detectionResult
	}

	// 常规链路处理
	renderedText, err := prompt.Engine.RenderAiDevelopIntentDetectPrompt(input)
	if err != nil {
		log.Warnf("Failed to render prompt: %v", err)
		return common.AIDeveloperIntentDetectionResult{}
	}
	request := IntentDetectRequest{
		ChatPrompt: renderedText,
		Stream:     true,
		RequestId:  requestId,
		Parameters: map[string]any{
			"temperature": 0.1,
		},
		AgentId:     definition.AgentAIDeveloper,
		TaskId:      definition.AgentTaskIntentDetect,
		Version:     "2",
		SessionType: definition.SessionTypeDeveloper,
	}

	// do Request
	outputResp, err := remote.ExecuteAgentRequest(definition.AgentChatAskService, "llm_model_result", request,
		requestId, definition.AgentAIDeveloper)
	if err != nil {
		log.Errorf("Failed to detect CommonAgent intent: %v", err)
		go stable.ReportAgentRequestError(definition.AgentAIDeveloper, definition.AgentTaskIntentDetect, requestId, err, nil)
		return common.AIDeveloperIntentDetectionResult{
			// 默认走dev_agent
			Intent: definition.AIDeveloperIntentDetectDev,
		}
	}

	detectionResult, err2 := parseIntentDetectOutput2(outputResp.Text)
	if err2 != nil {
		log.Errorf("Failed to parse detection result: %v", err2)
		return common.AIDeveloperIntentDetectionResult{
			// 默认走dev_agent
			Intent: definition.AIDeveloperIntentDetectDev,
		}
	}
	return detectionResult
}

func buildBasicIntentInput(ctx context.Context, inputs map[string]any, isMultiModal bool) prompt.AIDevelopIntentDetectPromptInput {
	rawInputParams := inputs[common.KeyChatAskParams].(*definition.AskParams)
	userQuery := chainUtil.GetUserInputQuery(rawInputParams.ChatContext)
	if parsedUserQueryValue, ok := inputs[common.KeyParsedUserInputQueryWithContexts]; ok {
		parsedUserQuery := parsedUserQueryValue.(string)
		userQuery = parsedUserQuery
	}
	input := prompt.AIDevelopIntentDetectPromptInput{
		BaseInput: prompt.BaseInput{
			RequestId: rawInputParams.RequestId,
			SessionId: rawInputParams.SessionId,
		},
		UserInputQuery: userQuery,
	}

	ideInfo := ctx.Value(definition.ContextKeyIdeConfig)
	var ideConfig *definition.IdeConfig
	if ideInfo != nil {
		ideConfig = ideInfo.(*definition.IdeConfig)
	}

	contextProviderExtras := inputs[common.KeyContextProviderExtra].([]definition.CustomContextProviderExtra)
	if len(contextProviderExtras) > 0 {
		contextDetails := chatUtil.ConvertContextProviderExtras(contextProviderExtras)
		input.ContextDetails = contextDetails
	}
	_, workspaceLanguage := guessIfJavaWorkspace(ctx, inputs)
	if !isMultiModal && isExcludeUnittestAgent(ctx, rawInputParams.SessionId, contextProviderExtras, ideConfig, workspaceLanguage) {
		input.ExcludeTestAgent = true
	}
	if workspaceLanguage != "" {
		input.WorkspaceLanguage = workspaceLanguage
	}
	if ideConfig != nil {
		input.IdeInfo = *ideConfig
	}
	return input
}

func getCurrentWorkspace(ctx context.Context) string {
	workspace := ""
	workspaceCtxValue := ctx.Value(definition.ContextKeyWorkspace)
	if workspaceCtxValue != nil {
		workspaceInfo := workspaceCtxValue.(definition.WorkspaceInfo)
		if workspaceRootPath, ok := workspaceInfo.GetWorkspaceFolder(); ok {
			workspace = workspaceRootPath
		}
	}
	return workspace
}

func (c CommonAgentIntentDetectChain) GetMemory() schema.Memory {
	return memory.NewSimple()
}

func (c CommonAgentIntentDetectChain) GetInputKeys() []string {
	return []string{}
}

func (c CommonAgentIntentDetectChain) GetOutputKeys() []string {
	return []string{}
}
