package chains

import (
	"context"
	chainUtil "cosy/chat/chains/chain"
	"cosy/chat/chains/common"
	chatUtil "cosy/chat/util"
	"cosy/definition"
	"cosy/global"
	"cosy/indexing"
	"cosy/log"
	"cosy/prompt"
	"cosy/remote"
	"cosy/util"
	"errors"
	"strconv"
	"strings"
	"time"

	"github.com/tmc/langchaingo/chains"
	"github.com/tmc/langchaingo/memory"
	"github.com/tmc/langchaingo/schema"
)

type IntentDetectChain struct {
}

type IntentDetectRequest struct {
	ChatPrompt        string         `json:"chat_prompt"`
	RequestId         string         `json:"request_id"`
	Stream            bool           `json:"stream"`
	Parameters        map[string]any `json:"parameters"`
	SystemRoleContent string         `json:"system_role_content,omitempty"`
	AgentId           string         `json:"agent_id"`
	TaskId            string         `json:"task_id"`
	Version           string         `json:"version,omitempty"`      //会话本地化后为2
	SessionType       string         `json:"session_type,omitempty"` //agent类型
}

type MultimodalIntentDetectRequest struct {
	ChatPrompt        string         `json:"chat_prompt"`
	RequestId         string         `json:"request_id"`
	Stream            bool           `json:"stream"`
	Parameters        map[string]any `json:"parameters"`
	SystemRoleContent string         `json:"system_role_content,omitempty"`
	AgentId           string         `json:"agent_id"`
	TaskId            string         `json:"task_id"`
	Version           string         `json:"version,omitempty"`      //会话本地化后为2
	SessionType       string         `json:"session_type,omitempty"` //agent类型
	ImageUrls         []string       `json:"image_urls"`             //多模态场景下，图片链接
}

func (c IntentDetectChain) Call(ctx context.Context, inputs map[string]any, options ...chains.ChainCallOption) (map[string]any, error) {
	var err error
	beginTime := time.Now()
	requestId := inputs[common.KeyRequestId].(string)
	rawInputParams := inputs[common.KeyChatAskParams].(*definition.AskParams)
	inputs[common.KeyCoderIntentDetectResult] = false
	if !util.NeedIntentDetection(rawInputParams) {
		log.Debugf("Intent detection not needed, requestId=%s", requestId)
		return inputs, nil
	}

	// 如果有图片上下文，Lingma：走图生前端代码的意图识别，Qoder：直接走普通的意图识别，不区分是否有图片上下文
	contextProviderExtras := inputs[common.KeyContextProviderExtra].([]definition.CustomContextProviderExtra)
	if !global.IsQoderProduct() && hasImageContext(contextProviderExtras) {
		inputs, err = c.GetUItoCodeIntent(ctx, inputs)
		if err != nil {
			log.Warnf("UI to code intent detection error, reason: %v, requestId: %s", err, requestId)
		} else {
			finishTime := time.Now()
			inputs[definition.IntentionDetectTimeCostMs] = strconv.FormatInt(finishTime.Sub(beginTime).Milliseconds(), 10)
			log.Debugf("UI to code intent result. coder_intent: %t, consumed: %s ms", inputs[common.KeyUIToCodeIntentDetectResult], inputs[definition.IntentionDetectTimeCostMs])
			return inputs, nil
		}
	}

	request, err := c.buildDetectRequest(ctx, inputs)
	if err != nil {
		log.Warnf("Intent detection build request error, reason=%v, requestId=%s", err, requestId)
		return inputs, nil
	}

	detectionResponse, err := c.fetchDetectionResult(ctx, request)
	if err != nil {
		log.Warnf("Intent detection fetch result error, reason=%v, requestId=%s", err, requestId)
		return inputs, nil
	}
	if log.IsDebugEnabled() {
		log.Debugf("Intent detection finish. requestId: %s, model requestId: %s, success: %v, usage: %v, content: %s", detectionResponse.RequestId, detectionResponse.ModelRequestId, detectionResponse.Success, detectionResponse.Usage, detectionResponse.Text)
	} else {
		log.Debugf("Intent detection finish. requestId: %s, model requestId: %s, success: %v, usage: %v", detectionResponse.RequestId, detectionResponse.ModelRequestId, detectionResponse.Success, detectionResponse.Usage)
	}

	if !detectionResponse.Success || detectionResponse.Text == "" {
		log.Warnf("intent detection error, reason: %v, requestId: %s, model requestId: %s", err, requestId, detectionResponse.ModelRequestId)
		return inputs, nil
	}

	r, err := parseIntentDetectOutput(detectionResponse.Text)
	if err != nil {
		//json解析异常
		//用默认值继续，避免阻断用户请求
		log.Warnf("intent detection not json output, rollback to default, requestId: %s, model requestId: %s, err: %v, text: %s", requestId, detectionResponse.ModelRequestId, err, detectionResponse.Text)

		r = common.IntentDetectionResult{
			IsCoderIntention: false,
		}
	}
	inputs[common.KeyCoderIntentDetectResult] = r.IsCoderIntention

	finishTime := time.Now()
	inputs[definition.IntentionDetectTimeCostMs] = strconv.FormatInt(finishTime.Sub(beginTime).Milliseconds(), 10)
	log.Debugf("Coder intent result. coder_intent: %t, consumed: %s ms", inputs[common.KeyCoderIntentDetectResult], inputs[definition.IntentionDetectTimeCostMs])
	return inputs, nil
}

func (c IntentDetectChain) GetMemory() schema.Memory {
	return memory.NewSimple()
}

func (c IntentDetectChain) GetInputKeys() []string {
	return []string{}
}

func (c IntentDetectChain) GetOutputKeys() []string {
	return []string{}
}

func (c IntentDetectChain) GetUItoCodeIntent(ctx context.Context, inputs map[string]any) (map[string]any, error) {
	requestId := inputs[common.KeyRequestId].(string)
	request, err := c.buildUItoCodeDetectRequest(ctx, inputs)
	if err != nil {
		log.Warnf("ui to code detection build request error, reason=%v, requestId=%s", err, requestId)
	} else {
		uiToCodeDetectionResponse, err := c.fetchDetectionResult(ctx, request)
		if err != nil {
			log.Warnf("UI to code detection fetch result error, reason=%v, requestId=%s", err, requestId)
			return inputs, nil
		}

		if !uiToCodeDetectionResponse.Success || uiToCodeDetectionResponse.Text == "" {
			log.Warnf("UI to code intent detection error, reason: %v, requestId: %s, model requestId: %s", err, requestId, uiToCodeDetectionResponse.ModelRequestId)
		} else {
			r, err := parseIntentDetectOutput(uiToCodeDetectionResponse.Text)
			if err != nil {
				log.Warnf("UI to code detection not json output, rollback to default, requestId: %s, model requestId: %s, err: %v, text: %s", requestId, uiToCodeDetectionResponse.ModelRequestId, err, uiToCodeDetectionResponse.Text)
				inputs[common.KeyUIToCodeIntentDetectResult] = false
			} else {
				inputs[common.KeyUIToCodeIntentDetectResult] = r.IsCoderIntention
				inputs[common.KeyCoderIntentDetectResult] = r.IsCoderIntention
				return inputs, nil
			}
		}
	}
	return inputs, errors.New("ui to code detection error")
}

func (c IntentDetectChain) buildUItoCodeDetectRequest(ctx context.Context, inputs map[string]any) (IntentDetectRequest, error) {
	requestId := inputs[common.KeyRequestId].(string)

	askParams, ok := inputs[common.KeyChatAskParams].(*definition.AskParams)
	if !ok {
		log.Warnf("user query is nil")
		return IntentDetectRequest{}, errors.New("get user ask params fail")
	}
	userQuestionText := chainUtil.GetUserInputQuery(askParams.ChatContext)
	contextProviderExtras := inputs[common.KeyContextProviderExtra].([]definition.CustomContextProviderExtra)
	promptQuery := ""
	if hasImageContext(contextProviderExtras) {
		// 走多模态的意图识别prompt
		promptInput := prompt.UIToCodeIntentDetectPromptInput{
			BaseInput: prompt.BaseInput{
				RequestId: requestId,
				SessionId: askParams.SessionId,
			},
			UserInputQuery: userQuestionText,
			ContextDetails: chatUtil.ConvertContextProviderExtras(contextProviderExtras),
		}
		if fileIndexer, ok := ctx.Value(definition.ContextKeyFileIndexer).(*indexing.ProjectFileIndex); ok {
			var workspaceLanguages []string
			if langStat, ok := fileIndexer.GetLangStatFileIndexer(); ok {
				workspaceLanguages = langStat.GetMostLanguages(0.2)
			}
			if len(workspaceLanguages) > 0 {
				promptInput.WorkspaceLanguage = strings.Join(workspaceLanguages, ", ")
			}
		}

		promptQuery, _ = prompt.Engine.RenderUIToCodeIntentDetectPrompt(promptInput)
		inputs[common.KeyIntentDetectChatPrompt] = promptQuery
	} else {
		return IntentDetectRequest{}, nil
	}

	r := IntentDetectRequest{
		ChatPrompt: promptQuery,
		Stream:     true,
		RequestId:  requestId,
		Parameters: map[string]any{
			"temperature": 0.1,
		},
		AgentId:     "intent_detect",
		SessionType: definition.SessionTypeChat,
	}
	return r, nil
}

func (c IntentDetectChain) buildDetectRequest(ctx context.Context, inputs map[string]any) (IntentDetectRequest, error) {
	requestId := inputs[common.KeyRequestId].(string)

	askParams, ok := inputs[common.KeyChatAskParams].(*definition.AskParams)
	if !ok {
		log.Warnf("user query is nil")
		return IntentDetectRequest{}, errors.New("get user ask params fail")
	}
	userQuestionText := chainUtil.GetUserInputQuery(askParams.ChatContext)

	promptInput := prompt.IntentDetectPromptInput{
		BaseInput: prompt.BaseInput{
			RequestId: askParams.RequestId,
			SessionId: askParams.SessionId,
		},
		UserInputQuery: userQuestionText,
	}
	promptQuery, _ := prompt.Engine.RenderIntentDetectPrompt(promptInput)
	inputs[common.KeyIntentDetectChatPrompt] = promptQuery

	r := IntentDetectRequest{
		ChatPrompt: promptQuery,
		Stream:     true,
		RequestId:  requestId,
		Parameters: map[string]any{
			"temperature": 0.1,
		},
		AgentId:     "intent_detect",
		SessionType: definition.SessionTypeChat,
	}
	return r, nil
}

func (c IntentDetectChain) fetchDetectionResult(ctx context.Context, request IntentDetectRequest) (common.IntentDetectResponse, error) {
	agentResp, err := remote.ExecuteAgentRequest(definition.AgentChatAskService, "llm_model_result",
		request, request.RequestId, "")
	if err != nil {
		log.Warnf("Intent detection request error, reason=%v", err)

		return common.IntentDetectResponse{}, err
	}
	result := common.IntentDetectResponse{
		Text:    agentResp.Text,
		Success: true,
	}
	return result, err
}

/*
* 需求分析模型输出格式
* 预期只有单个字符 Y或者N，如果不是Y，则走兜底的非代码生成判断
 */
func parseIntentDetectOutput(outputText string) (common.IntentDetectionResult, error) {
	outputText = strings.Trim(outputText, "\n ")
	if len(outputText) == 1 && outputText == "Y" {
		return common.IntentDetectionResult{
			IsCoderIntention: true,
		}, nil
	}
	return common.IntentDetectionResult{
		IsCoderIntention: false,
	}, nil
}
