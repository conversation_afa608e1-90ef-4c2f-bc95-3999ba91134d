package chat

import (
	agentDefinition "code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"context"
	"cosy/chat/agents/support/markdown"
	chain2 "cosy/chat/chains/chain"
	"cosy/chat/chains/common"
	"cosy/chat/service"
	chatUtil "cosy/chat/util"
	"cosy/extension/rule"
	"cosy/global"
	"strconv"

	"cosy/definition"
	cosyErrors "cosy/errors"
	"cosy/extension"
	"cosy/filter"
	"cosy/indexing"
	"cosy/log"
	"cosy/memory"
	"cosy/prompt"
	"cosy/remote"
	"cosy/sls"
	"cosy/stable"
	"cosy/systemrule"
	"cosy/user"
	"cosy/util"
	"cosy/websocket"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"runtime"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/mitchellh/mapstructure"
	"github.com/samber/lo"
)

const (
	summaryPromptTemplate = "历史中存在几种类型的会话：\ntest_agent：用于生成Java语言的单元测试\ndev_agent：用于对工程代码生成修改建议\ncommon：普通问答\n\n会话总结：\n$summary"

	fallbackSummaryTemplate = "$lastSummary \n\n在这之后，用户提问：\n$currentQuestion \n通过调用$agentType类型的agent，得到了解答。"

	maxReadSize = 100 * 1024
)

const (
	chatStepInit           = "init"
	chatStepReasoningStart = "reasoning_start"
	chatStepReasoningEnd   = "reasoning_end"
	chatFinish             = "done"
)

//
//```toolCall::search_codebase::call_1731de8fdb5844ad8a6b89::INIT
//```
// 正则表达式匹配如下格式：
// ```toolCall::任意字符::INIT
// ...（可能多行内容）
// ```

var (
	toolCallRegex = regexp.MustCompile("(?s)```toolCall::.*?::INIT\n.*?```")
)

// hasUnpairedBackticks 检测文本中是否有未配对的backtick
// 只考虑非连续的单个backtick，连续的backtick（如```）被视为完整的markdown语法
func hasUnpairedBackticks(text string) bool {
	if text == "" {
		return false
	}

	inCodeSpan := false
	i := 0

	for i < len(text) {
		if text[i] == '`' {
			// 计算连续的backtick数量
			backtickCount := 0
			for i < len(text) && text[i] == '`' {
				backtickCount++
				i++
			}

			// 如果是单个backtick，则切换代码段状态
			if backtickCount == 1 {
				inCodeSpan = !inCodeSpan
			}
			// 如果是多个连续backtick（如```），跳过处理，因为这通常是代码块标记
		} else {
			i++
		}
	}

	// 如果最后还在代码段中，说明有未配对的单个backtick
	return inCodeSpan
}

func PushChatFinishMessage(ctx context.Context, chatFinish definition.ChatFinish) {
	log.Info("Answer finished, reason: " + chatFinish.Reason)

	// 异常结束对话
	e := websocket.FinishChatRequest(ctx, chatFinish, 3*time.Second)
	if e != nil {
		log.Error("Send request chat/finish error:", e)
	}
}

func isCustomCommandByChatExtra(extra string) bool {
	if extra == "" {
		return false
	}
	var extraMap map[string]any
	util.UnmarshalToObject(extra, &extraMap)
	if extraMap == nil || len(extra) == 0 {
		return false
	}
	if _, ok := extraMap[definition.ChatExtraKeyCommand]; !ok {
		return false
	}
	return true
}

func buildCustomRemoteAsk(chatAsk definition.RemoteChatAsk, commandExtra definition.CustomCommandExtra, customPrompt string, fileIndexer *indexing.ProjectFileIndex) definition.RemoteChatAsk {
	chatContext := chatAsk.ChatContext
	freeInputContext, _ := chatUtil.TransformFreeInputChatContext(chatContext, fileIndexer)
	freeInputContext.ChatPrompt = customPrompt
	chatAsk.ChatPrompt = customPrompt

	var command *extension.Command
	for _, c := range extension.GlobalExtensionConfig.Commands {
		if c.Identifier == commandExtra.Identifier {
			command = &c
			break
		}
	}
	if command != nil {
		if command.SystemPrompt != "" {
			chatAsk.SystemRoleContent = command.SystemPrompt
			chatAsk.CustomSystemRoleContent = command.SystemPrompt
		}
	}
	chatAsk.ChatContext = freeInputContext
	// 自定义指令的taskId使用common_chat
	chatAsk.TaskId = definition.AgentTaskCommonChat
	return chatAsk
}

func buildCommandInvocationContext(params *definition.AskParams, fileIndexer *indexing.ProjectFileIndex) (definition.ExecutionOptions, error) {
	commandContext := definition.ExecutionOptions{
		RequestId: params.RequestId,
	}
	freeInputContext, err := chatUtil.TransformFreeInputChatContext(params.ChatContext, fileIndexer)
	if err != nil {
		log.Error("Failed to transform freeInputContext ", err)
		return definition.ExecutionOptions{}, errors.New("build commandContext error")
	}
	contextExtraValue, ok := params.AttachedInputs[common.KeyContextProviderExtra]
	if ok {
		contextExtras := contextExtraValue.([]definition.CustomContextProviderExtra)
		if len(contextExtras) > 0 {
			for _, extra := range contextExtras {
				if len(extra.ParsedContextItems) <= 0 {
					continue
				}
				providerName := extra.Name
				for _, contextItem := range extra.ParsedContextItems {
					commandContext.AssociatedContexts = append(commandContext.AssociatedContexts, definition.ContextItem{
						ContextProviderName: providerName,
						Identifier:          contextItem.Identifier,
						Key:                 contextItem.Key,
						Value:               contextItem.Value,
						Name:                contextItem.Name,
					})
				}
			}
		}
	}

	commandContext.UserInputText = freeInputContext.Text
	commandContext.SelectedCode = freeInputContext.Code

	return commandContext, err
}

func buildFreeInputChatPrompt(ctx context.Context, inputs map[string]any, chatAsk definition.RemoteChatAsk, contextProviderExtras []definition.CustomContextProviderExtra, fileIndexer *indexing.ProjectFileIndex) definition.RemoteChatAsk {
	freeInputContext, err := chatUtil.TransformFreeInputChatContext(chatAsk.ChatContext, fileIndexer)
	if err != nil {
		log.Error("Failed to transform freeInputContext ", err)
		return chatAsk
	}
	if len(contextProviderExtras) == 0 {
		freeInputContext.ChatPrompt = freeInputContext.Text
		chatAsk.ChatPrompt = freeInputContext.Text
		chatAsk.ChatContext = freeInputContext
		return chatAsk
	}
	contextDetails := chatUtil.ConvertContextProviderExtras(contextProviderExtras)
	contextDetails = appendSystemContextDetails(contextDetails)
	// 计算当前剩余的余量,需要最少保留单次的历史的answer与当前轮次用户的问题
	historyRemainLength := getFreeInputContextHistoryLimitLength(chatAsk.PromptHistory)
	isMultimodal := len(freeInputContext.ImageUrls) > 0
	tokenTotalLimit := getChatTokenTotalLimit(isMultimodal, definition.SessionTypeChat, chatAsk.ModelConfig)
	availableLength := tokenTotalLimit*tokenToStringLengthRatio - historyRemainLength - len(freeInputContext.Text)
	// 执行上下文截断
	truncateContextDetails(ctx, contextDetails, availableLength, chatAsk.RequestId)

	if freeInputContext.Extra[common.KeyUI2CodeMode] == true {
		chatPrompt, err := buildUItoCodePrompt(ctx, inputs, chatAsk.RequestId, chatAsk.SessionId)
		if err != nil {
			log.Error("Failed to build UItoCode prompt ", err)
			return chatAsk
		}
		chatAsk.CustomSystemRoleContent = chatPrompt
	} else {
		input := prompt.FreeChatWithContextsPromptInput{
			BaseInput: prompt.BaseInput{
				RequestId: chatAsk.RequestId,
				SessionId: chatAsk.SessionId,
			},
			UserInputQuery: freeInputContext.Text,
			ContextDetails: contextDetails,
		}
		chatPrompt, err := prompt.Engine.RenderFreeChatWithContextsPrompt(input)
		if err != nil {
			log.Error("Failed to render freeInputContext ", err)
			return chatAsk
		}
		freeInputContext.ChatPrompt = chatPrompt
	}

	input := prompt.FreeChatWithContextsPromptInput{
		BaseInput: prompt.BaseInput{
			RequestId: chatAsk.RequestId,
			SessionId: chatAsk.SessionId,
		},
		UserInputQuery: freeInputContext.Text,
		ContextDetails: contextDetails,
	}
	chatPrompt, err := prompt.Engine.RenderFreeChatWithContextsPrompt(input)
	if err != nil {
		log.Error("Failed to render freeInputContext ", err)
		return chatAsk
	}
	freeInputContext.ChatPrompt = chatPrompt
	chatAsk.ChatPrompt = chatPrompt
	chatAsk.ChatContext = freeInputContext
	return chatAsk
}

// AI Developer模式下，通用问答
func buildAIDeveloperChatPrompt(ctx context.Context, chatAsk definition.RemoteChatAsk, contextProviderExtras []definition.CustomContextProviderExtra, fileIndexer *indexing.ProjectFileIndex, extraParams definition.ChatAskExtraParams, askParamExtra map[string]any) definition.RemoteChatAsk {
	freeInputContext, err := chatUtil.TransformFreeInputChatContext(chatAsk.ChatContext, fileIndexer)
	if err != nil {
		log.Error("Failed to transform freeInputContext ", err)
		return chatAsk
	}
	contextDetails := chatUtil.ConvertContextProviderExtras(contextProviderExtras)
	contextDetails = appendSystemContextDetails(contextDetails)
	// 计算当前剩余的余量,需要兜底保留问答历史的summary内容与当前轮次的问题
	historyRemainLength := getAiDevelopContextHistoryLimitLength(chatAsk.PromptHistory)
	isMultimodal := len(freeInputContext.ImageUrls) > 0
	availableLength := getChatTokenTotalLimit(isMultimodal, definition.SessionTypeDeveloper, chatAsk.ModelConfig)*tokenToStringLengthRatio - historyRemainLength - len(freeInputContext.Text)

	// 执行上下文截断
	truncateContextDetails(ctx, contextDetails, availableLength, chatAsk.RequestId)

	input := prompt.FreeChatWithContextsPromptInput{
		BaseInput: prompt.BaseInput{
			RequestId: chatAsk.RequestId,
			SessionId: chatAsk.SessionId,
		},
		UserInputQuery: freeInputContext.Text,
		ContextDetails: contextDetails,
	}
	chatPrompt, err := prompt.Engine.RenderFreeChatWithContextsPrompt(input)
	if err != nil {
		log.Error("Failed to render freeInputContext ", err)
		return chatAsk
	}
	systemInput := prompt.AIDevelopSystemPromptForCommonInput{
		BaseInput: prompt.BaseInput{
			RequestId: chatAsk.RequestId,
			SessionId: chatAsk.SessionId,
			IsQoder:   global.IsQoderProduct(),
		},
		WorkspaceLanguages: freeInputContext.WorkspaceLanguages,
		PreferredLanguage:  freeInputContext.PreferredLanguage,
	}
	if systemPrompt, err2 := prompt.Engine.RenderAiDevelopCommonSystemPrompt(systemInput); err2 != nil {
		log.Error("Failed to render aiDevelopCommonSystemPrompt ", err2)
	} else {
		chatAsk.SystemRoleContent = systemPrompt
		// 追加rules
		projectRuleContext, ok := askParamExtra[common.KeyProjectRuleContext].(rule.ProjectRuleContext)
		if ok {
			chatAsk.SystemRoleContent = appendUserRulesForChat(ctx, &projectRuleContext, chatAsk.SystemRoleContent)
		}
		chatAsk.SystemRoleContent = memory.AppendMemoryPromptForChat(chatAsk.SystemRoleContent, askParamExtra)
	}

	freeInputContext.ChatPrompt = chatPrompt
	chatAsk.ChatPrompt = chatPrompt
	chatAsk.ChatContext = freeInputContext
	return chatAsk
}

// AI Developer模式下，通用问答
func buildAIDeveloperTestFixEnvPrompt(ctx context.Context, chatAsk definition.RemoteChatAsk, contextProviderExtras []definition.CustomContextProviderExtra, fileIndexer *indexing.ProjectFileIndex, askParamExtra map[string]any) definition.RemoteChatAsk {
	freeInputContext, err := chatUtil.TransformFreeInputChatContext(chatAsk.ChatContext, fileIndexer)
	if err != nil {
		log.Error("Failed to transform freeInputContext ", err)
		return chatAsk
	}

	extraInfoMap, ok := askParamExtra["extraInfo"].(map[string]interface{})
	if !ok {
		extraInfoMap = make(map[string]interface{})
	}
	var fixEnvParam struct {
		CheckItemKey         string   `json:"checkItemKey"`
		CheckItemDescription string   `json:"checkItemDescription"`
		ConfigurationFiles   []string `json:"configurationFiles"`
		RelatedInfo          struct {
			OsVersion  string `json:"osVersion"`
			IdeName    string `json:"ideName"`
			IdeVersion string `json:"ideVersion"`
		} `json:"relatedInfo"`
	}
	if err := mapstructure.Decode(extraInfoMap, &fixEnvParam); err != nil {
		return chatAsk
	}
	input := prompt.AIDevelopTestAgentFixEnvPromptInput{
		BaseInput: prompt.BaseInput{
			RequestId: chatAsk.RequestId,
			SessionId: chatAsk.SessionId,
			IsQoder:   global.IsQoderProduct(),
		},
		WorkspaceLanguages:   freeInputContext.WorkspaceLanguages,
		PreferredLanguage:    freeInputContext.PreferredLanguage,
		UserInputQuery:       freeInputContext.Text,
		CheckItemKey:         fixEnvParam.CheckItemKey,
		CheckItemDescription: fixEnvParam.CheckItemDescription,
		FilePaths:            fixEnvParam.ConfigurationFiles,
		OsVersion:            fixEnvParam.RelatedInfo.OsVersion,
		IdeName:              fixEnvParam.RelatedInfo.IdeName,
		IdeVersion:           fixEnvParam.RelatedInfo.IdeVersion,
	}
	chatPrompt, err := prompt.Engine.RenderAiDevelopTestAgentFixEnvPrompt(input)
	if err != nil {
		log.Error("Failed to render fixEnvContext ", err)
		return chatAsk
	}
	chatAsk.SystemRoleContent = ""
	chatAsk.ChatPrompt = chatPrompt
	chatAsk.ChatContext = freeInputContext
	return chatAsk
}

// appendSystemContextDetails 增加系统自动添加的上下文
func appendSystemContextDetails(contextDetails []*prompt.ContextDetail) []*prompt.ContextDetail {
	// 有框选代码时，自动带上文件
	return addFileContextWhenSelectedCode(contextDetails)
}

// addFileContextWhenSelectedCode 当有selectedCode的时候，如果没有对应的#file，则将它加上
func addFileContextWhenSelectedCode(contextDetails []*prompt.ContextDetail) []*prompt.ContextDetail {
	var selectedCodeFilePaths []string
	var existingFilePaths []string
	for _, contextDetail := range contextDetails {
		if contextDetail.ProviderName == definition.PlatformContextProviderSelectedCode {
			if contextDetail.ContextItems != nil && len(contextDetail.ContextItems) > 0 {
				extra := contextDetail.ContextItems[0].Extra
				if extra != nil {
					if filePath, ok := extra["filePath"].(string); ok {
						selectedCodeFilePaths = append(selectedCodeFilePaths, filePath)
					} else {
						log.Warn("filePath is not a string or does not exist in Extra")
					}
				} else {
					log.Warn("contextDetail.ContextItems[0].Extra is nil when adding file context")
				}
			} else {
				log.Warn("ContextItems is nil or empty when adding file context")
			}
		}

		if contextDetail.ProviderName == "file" {
			existingFilePaths = append(existingFilePaths, contextDetail.ContextItems[0].Identifier)
		}
	}
	needAppendFileContentList := util.GetExclusiveList(selectedCodeFilePaths, existingFilePaths)
	// 遍历needAppendFileContentList获取fileContent（使用util.GetFileContent(方法待确认)）
	for i := 0; i < len(needAppendFileContentList); i++ {
		fileContent, err2 := util.ReadFileContentByFilePath(needAppendFileContentList[i], maxReadSize)
		if err2 != nil {
			log.Error("Failed to get file content ", err2)
			continue
		}
		fileName := filepath.Base(needAppendFileContentList[i])
		itemContent := fmt.Sprintf("%s\n%s", fileName, fileContent)
		contextDetails = append(contextDetails, &prompt.ContextDetail{
			Identifier:     "7a0c2792-b1ed-49ec-a82b-d97b16a242b4",
			ProviderName:   "file",
			RequiredPrompt: "",
			ContextItems: []*prompt.ContextItemDetail{
				{
					Identifier:  needAppendFileContentList[i],
					ItemKey:     needAppendFileContentList[i],
					ItemContent: itemContent,
					Chunk:       definition.ChunkItem{},
					Extra: map[string]any{
						"source": "system",
					},
				},
			},
		})
	}
	return contextDetails
}

func buildAiDeveloperDevPrompt(ctx context.Context, chatAsk definition.RemoteChatAsk, contextProviderExtras []definition.CustomContextProviderExtra, fileIndexer *indexing.ProjectFileIndex, extraParams definition.ChatAskExtraParams, askParamExtra map[string]any) definition.RemoteChatAsk {
	freeInputContext, err := chatUtil.TransformFreeInputChatContext(chatAsk.ChatContext, fileIndexer)
	if err != nil {
		log.Error("Failed to transform freeInputContext ", err)
		return chatAsk
	}
	var contextDetails []*prompt.ContextDetail
	for _, contextProviderExtra := range contextProviderExtras {
		var requiredPrompt string

		contextProviderMeta, err2 := extension.GetRegisteredContextProvider(contextProviderExtra.Identifier, true)
		if err2 == nil {
			requiredPrompt = contextProviderMeta.RequiredPrompt
		}

		var contextDetail = prompt.ContextDetail{
			Identifier:     contextProviderExtra.Identifier,
			ProviderName:   contextProviderExtra.Name,
			RequiredPrompt: requiredPrompt,
		}
		var contextItemDetails []*prompt.ContextItemDetail
		for _, contextItem := range contextProviderExtra.ParsedContextItems {
			detail := prompt.ContextItemDetail{
				Identifier: contextItem.Identifier,
				ItemKey:    contextItem.Key,
				Extra:      contextItem.Extra,
			}
			if contentVal, ok := contextItem.Value.(string); ok {
				detail.ItemContent = contentVal
			}
			if contextItem.Chunk != nil {
				detail.Chunk = chatUtil.ConvertDocumentToChunk(*contextItem.Chunk)
			}
			contextItemDetails = append(contextItemDetails, &detail)
		}
		contextDetail.ContextItems = contextItemDetails

		contextDetails = append(contextDetails, &contextDetail)
	}
	contextDetails = appendSystemContextDetails(contextDetails)
	// 计算当前剩余的余量,需要兜底保留问答历史的summary内容与当前轮次的问题
	historyRemainLength := getAiDevelopContextHistoryLimitLength(chatAsk.PromptHistory)
	isMultimodal := len(freeInputContext.ImageUrls) > 0
	availableLength := getChatTokenTotalLimit(isMultimodal, definition.SessionTypeDeveloper, chatAsk.ModelConfig)*tokenToStringLengthRatio - historyRemainLength - len(freeInputContext.Text)
	// 执行上下文截断
	truncateContextDetails(ctx, contextDetails, availableLength, chatAsk.RequestId)

	workspaceLanguages := strings.Join(freeInputContext.WorkspaceLanguages, ",")
	preferredLanguage := freeInputContext.PreferredLanguage
	if preferredLanguage == definition.LocaleEn {
		preferredLanguage = "英文"
	} else {
		preferredLanguage = "中文"
	}
	// 获取当前打开文件路径
	activeFilePath := ""
	if chatContextMap, ok := chatAsk.ChatContext.(map[string]interface{}); ok {
		activeFilePath, _ = chatContextMap["activeFilePath"].(string)
	} else if chatContext, ok := chatAsk.ChatContext.(definition.FreeInputChatContext); ok {
		activeFilePath = chatContext.ActiveFilePath
	}
	// 填充项目路径
	workspaceInfo, _ := ctx.Value(definition.ContextKeyWorkspace).(definition.WorkspaceInfo)
	targetProjectUri := ""
	if len(workspaceInfo.WorkspaceFolders) > 0 {
		targetProjectUri = workspaceInfo.WorkspaceFolders[0].URI
	}
	for _, folder := range workspaceInfo.WorkspaceFolders {
		if strings.HasPrefix(activeFilePath, folder.URI) {
			targetProjectUri = folder.URI
			break
		}
	}
	//_, projectUri, _ := chain.CreateProjectInfo(workspaceInfo)
	input := prompt.AIDevelopWithContextsPromptInput{
		BaseInput: prompt.BaseInput{
			RequestId: chatAsk.RequestId,
			SessionId: chatAsk.SessionId,
			IsQoder:   global.IsQoderProduct(),
		},
		UserInputQuery:     freeInputContext.Text,
		ContextDetails:     contextDetails,
		ProjectUri:         targetProjectUri,
		WorkspaceLanguages: workspaceLanguages,
		PreferredLanguage:  preferredLanguage,
		ActiveFilePath:     activeFilePath,
	}
	if extraParams.IntentionType == definition.AIDeveloperIntentDetectUI2FeCode {
		input.IsUItoCode = true
	}
	input, err = prompt.Engine.PrepareInputForAiDevelopDevPromptRender(input)
	userPrompt, err := prompt.Engine.RenderAiDevelopDevPrompt(input)

	if err != nil {
		log.Error("Failed to render freeInputContext ", err)
		return chatAsk
	}
	// 判断有没有传图片，如果传图了，需要设置 system prompt，
	// 目前 vl 模型在图片过于简单时效果不理想，补充提示词做一下优化
	if len(freeInputContext.ImageUrls) > 0 {
		var chatAskSystemRoleContent string
		if extraParams.IntentionType == definition.AIDeveloperIntentDetectUI2FeCode {
			baseInput := buildFeUIParams(fileIndexer)

			imageSystemPromptInput := prompt.AIDevelopDevWithImageUItoCodeSystemPromptInput{
				BaseInput: prompt.BaseInput{
					RequestId: chatAsk.RequestId,
					SessionId: chatAsk.SessionId,
					IsQoder:   global.IsQoderProduct(),
				},
				PreferredLanguage:    preferredLanguage,
				BaseUIToCodeDepInput: baseInput,
			}
			chatAskSystemRoleContent, err = prompt.Engine.RenderAiDevelopDevWithImageUItoCodeSystemPrompt(imageSystemPromptInput)
		} else {
			imageSystemPromptInput := prompt.AIDevelopDevWithImageSystemPromptInput{
				BaseInput: prompt.BaseInput{
					RequestId: chatAsk.RequestId,
					SessionId: chatAsk.SessionId,
					IsQoder:   global.IsQoderProduct(),
				},
				PreferredLanguage:  preferredLanguage,
				WorkspaceLanguages: workspaceLanguages,
			}
			chatAskSystemRoleContent, err = prompt.Engine.RenderAiDevelopDevWithImageSystemPrompt(imageSystemPromptInput)
		}
		if err != nil {
			log.Error("Failed to render imageSystemPrompt ", err)
		} else {
			chatAsk.SystemRoleContent = chatAskSystemRoleContent
		}
	} else {
		systemPrompt, err := prompt.Engine.RenderAiDevelopDevSystemPrompt(input)
		if err != nil {
			log.Error("Failed to render dev_agent system prompt ", err)
			return chatAsk
		}
		chatAsk.SystemRoleContent = systemPrompt
	}
	// 追加rules
	projectRuleContext, ok := askParamExtra[common.KeyProjectRuleContext].(rule.ProjectRuleContext)
	if ok {
		chatAsk.SystemRoleContent = appendUserRulesForChat(ctx, &projectRuleContext, chatAsk.SystemRoleContent)
	}
	chatAsk.SystemRoleContent = memory.AppendMemoryPromptForChat(chatAsk.SystemRoleContent, askParamExtra)

	freeInputContext.ChatPrompt = userPrompt
	chatAsk.ChatContext = freeInputContext
	chatAsk.ChatPrompt = userPrompt
	return chatAsk
}

type ChatOutputDecorator interface {
	// GetOutputText 获取全量输出
	GetOutputText() string

	//获取增量内容
	GetAppendText() string

	UpdateOutputText(output string, finish bool)

	UpdateReasoningText(reasoning string)

	GetAppendReasoningText() string

	GetReasoningText() string

	Finish()
}

// CommonStreamChatOutputDecorator 通用流式输出
type CommonStreamChatOutputDecorator struct {
	fullOutput        string
	lastOutput        string
	reasoningText     string
	lastReasoningText string
}

func (c *CommonStreamChatOutputDecorator) GetOutputText() string {
	return c.fullOutput
}

func (c *CommonStreamChatOutputDecorator) GetAppendText() string {
	appendText := strings.TrimPrefix(c.fullOutput, c.lastOutput)
	c.lastOutput = c.fullOutput
	return appendText
}

func (c *CommonStreamChatOutputDecorator) UpdateOutputText(output string, finish bool) {
	c.fullOutput = output
}

func (c *CommonStreamChatOutputDecorator) Finish() {
}

func (c *CommonStreamChatOutputDecorator) GetAppendReasoningText() string {
	appendText := strings.TrimPrefix(c.reasoningText, c.lastReasoningText)
	c.lastReasoningText = c.reasoningText
	return appendText
}

func (c *CommonStreamChatOutputDecorator) UpdateReasoningText(reasoning string) {
	c.reasoningText = reasoning
}

func (c *CommonStreamChatOutputDecorator) GetReasoningText() string {
	return c.reasoningText
}

// OpenaiStreamChatOutputDecorator 通用流式输出
type OpenaiStreamChatOutputDecorator struct {
	fullOutput        string
	lastOutput        string
	reasoningText     string
	lastReasoningText string
}

func (o *OpenaiStreamChatOutputDecorator) GetOutputText() string {
	return o.fullOutput
}

func (o *OpenaiStreamChatOutputDecorator) GetAppendText() string {
	appendText := strings.TrimPrefix(o.fullOutput, o.lastOutput)
	o.lastOutput = o.fullOutput
	return appendText
}

func (o *OpenaiStreamChatOutputDecorator) UpdateOutputText(output string, finish bool) {
	// finish的时候，给的全量的输出，非finish的时候，是增量的输出，
	if finish {
		o.fullOutput = output
	} else {
		o.fullOutput = o.fullOutput + output
	}
}

func (o *OpenaiStreamChatOutputDecorator) Finish() {
}

func (o *OpenaiStreamChatOutputDecorator) GetAppendReasoningText() string {
	appendText := strings.TrimPrefix(o.reasoningText, o.lastReasoningText)
	o.lastReasoningText = o.reasoningText
	return appendText
}

func (o *OpenaiStreamChatOutputDecorator) UpdateReasoningText(reasoning string) {
	o.reasoningText = o.reasoningText + reasoning
}

func (o *OpenaiStreamChatOutputDecorator) GetReasoningText() string {
	return o.reasoningText
}

const (
	lineSeparator = "\n"
)

// DiffBlockBasedOutputDecorator Diff模式下，基于diff block的输出
type DiffBlockBasedOutputDecorator struct {
	RequestId string

	IntentType string

	output string //全量模型输出

	lastOutput string

	reasoningText     string //全量推理文本
	lastReasoningText string

	//包装转换后的输出
	decoratedOutput *strings.Builder

	blocks []definition.DiffBlock

	ChatRecordId       string
	BlockIdentifierMap map[string]string
	BlockCostMap       map[string]int64
	Ctx                context.Context
	SessionId          string
	ProjectUris        []string
	ContextFilePaths   []string
	FileIndexer        *indexing.ProjectFileIndex
	PathHandleMap      map[string]string

	// 用于存储部分未完成的代码块
	partialCodeBlock  string
	isInsideCodeBlock bool
	workingSpaceFile  definition.WorkingSpaceFile

	// 记录前面是否有未输出的内容
	isHasUnprocessedContent bool
}

func (d *DiffBlockBasedOutputDecorator) GetOutputText() string {
	return d.output
}

func (d *DiffBlockBasedOutputDecorator) GetAppendText() string {
	// TODO: \n怎么处理
	validOutput := d.decoratedOutput.String()

	if len(d.lastOutput) >= len(validOutput) {
		return ""
	}

	appendText := strings.TrimPrefix(strings.TrimSuffix(validOutput, lineSeparator), strings.TrimSuffix(d.lastOutput, lineSeparator))
	if !d.isInsideCodeBlock {
		// 判断是否有未配对的backtick，如果有，则需要等待后续内容
		if !d.isHasUnprocessedContent {
			if hasUnpairedBackticks(appendText) {
				d.isHasUnprocessedContent = true
				return ""
			}
		}
		d.isHasUnprocessedContent = false
		appendText = markdown.ProcessBackticks(d.Ctx, appendText)
	}
	d.lastOutput = validOutput
	return appendText
}

func (d *DiffBlockBasedOutputDecorator) UpdateOutputText(output string, finish bool) {
	d.output = output

	lines := strings.Split(output, lineSeparator)
	d.isInsideCodeBlock = false
	d.partialCodeBlock = ""
	d.workingSpaceFile = definition.WorkingSpaceFile{}
	d.blocks = []definition.DiffBlock{}

	//每次从接收全量输出
	d.decoratedOutput.Reset()

	for lineNum, line := range lines {
		//跳过最后一行可能是```开头的非完整行
		if isLastIncompleteLine(output, line, lineNum) {
			break
		}

		// 如果是部分代码块开头行，没包含完整代码行时，无法精确识别 比如 ```java:xxxx
		// 先中断buffer追加内容
		if strings.HasPrefix(line, "```") && !d.isInsideCodeBlock {
			//最后非完整代码block头行，则忽略
			if isLastIncompleteCodeBlockLine(output, line, lineNum) {
				break
			}

			// 开始一个新的代码块
			if isCompleteCodeBlockLine(output, line, lineNum) && isDiffCodeBlockLine(line) {
				//当前行输出的结尾片段时，肯定不满足
				d.isInsideCodeBlock = true
				d.partialCodeBlock = line + lineSeparator

				language, path := parseDiffBlockLine(line, d.ProjectUris, d.ContextFilePaths, d.FileIndexer, d.PathHandleMap, d.RequestId, d.SessionId)

				// 模型异常返回，直接忽略
				if strings.HasSuffix(path, "{{ file_full_path }}") || !util.CanNewFile(path) {
					d.decoratedOutput.WriteString("路径生成异常，请指定具体需要修改的文件路径后重试")
					continue
				}

				id := ""
				var err error

				// 如果尚未处理该代码块
				if d.BlockIdentifierMap[path] == "" {
					d.BlockCostMap[path] = time.Now().UnixMilli()
					// 上锁，避免处理过程太慢导致重复处理
					d.BlockIdentifierMap[path] = "LOCK"
					// 创建工作区文件
					currentSnapshot, exists := service.CurrentSnapshotMap[d.SessionId]
					if !exists {
						if snapshots, err := service.WorkingSpaceServiceManager.ListSnapshot(d.SessionId); err == nil && len(snapshots) > 0 {
							currentSnapshot = snapshots[0]
						}
					}
					if currentSnapshot.ChatRecordId != d.ChatRecordId {
						// 如果未创建新的快照
						if service.WorkingSpaceServiceManager != nil {
							service.WorkingSpaceServiceManager.CreateSnapshot(d.Ctx, d.SessionId, d.ChatRecordId, definition.SessionModeEdit)
							// 间隔 100 ms，由于目前 websocket 消息不能保证消息顺序，避免工作区文件消息先于快照信息到达
							time.Sleep(100 * time.Millisecond)
						} else {
							log.Error("WorkingSpaceServiceManager not exist")
						}
					}
					if id, err = service.WorkingSpaceServiceManager.CreateWorkingSpaceFile(d.Ctx, d.SessionId, path, language, "", service.GENERATING, "", definition.SessionModeEdit); err == nil {
						d.BlockIdentifierMap[path] = id
					} else {
						log.Errorf("Failed to create workingSpaceFile for sessionId: %s, path: %s, err: %s", d.SessionId, path, err.Error())
					}
					log.Debugf("parseCodeSnippet:  ```%s::%s::%s%s```%s", language, path, id, lineSeparator, lineSeparator)
				} else {
					id = d.BlockIdentifierMap[path]
					d.workingSpaceFile, _ = service.WorkingSpaceServiceManager.GetWorkingSpaceFile(id)
				}

				d.decoratedOutput.WriteString(fmt.Sprintf("```%s::%s::%s%s```%s", language, path, id, lineSeparator, lineSeparator))

				continue
			}
		}

		/**
		** 有些场景模型特定结构输出，需要排除下
		** ### 文件结构
		   ```
		   book_management_system/
		   │
		   ├── app.py
		   └── templates/
			   └── index.html
		   ```
		** 兼容 ``` 后面带空格的情况
		*/
		if d.isInsideCodeBlock && strings.EqualFold("```", strings.TrimSpace(line)) {
			d.partialCodeBlock = d.partialCodeBlock + "```"

			// 结束当前代码块
			codeSnippet, err := parseCodeSnippet(d.partialCodeBlock, d.ProjectUris, d.ContextFilePaths, d.FileIndexer, d.PathHandleMap, d.RequestId, d.SessionId)
			if err != nil || strings.HasSuffix(codeSnippet.Path, "{{ file_full_path }}") {
				log.Errorf("Failed to parse codeSnippet. codeBlock: %s, err: %v", d.partialCodeBlock, err)
			} else {
				d.blocks = append(d.blocks, definition.DiffBlock{
					Language: codeSnippet.Language,
					FilePath: codeSnippet.Path,
					CodeText: codeSnippet.Code,
				})
				if d.IntentType == definition.AIDeveloperIntentDetectUI2FeCode {
					chatUI2feCodeMap[d.RequestId] = UI2FeCodeBlocks{
						blocks: d.blocks,
					}
				}

				// 完成代码块
				id := d.BlockIdentifierMap[codeSnippet.Path]
				if id != "" {
					if status, ok := service.WorkingSpaceFileIngMap.Load(id); ok && status == service.GENERATING.String() {
						service.WorkingSpaceFileIngMap.Store(id, service.PENDING.String())
						if generatingContent, exist := service.WorkingSpaceFileGeneratingContentMap[id]; exist && generatingContent != nil {
							generatingContent.FullContent = codeSnippet.Code
						}
						log.Debugf("Handle Block %s, Path: %s, Cost: %s", id, codeSnippet.Path, time.Since(time.UnixMilli(d.BlockCostMap[codeSnippet.Path])))
						log.Debugf("generate CodeSnippet id: %s content: %s", id, codeSnippet.Code)

						// 触发 Apply
						service.WorkingSpaceServiceManager.OperateWorkingSpaceFile(d.Ctx, definition.WorkingSpaceFileOperateParams{
							Id:     id,
							OpType: service.APPLY.String(),
						})
					}
				}
			}

			d.partialCodeBlock = ""
			d.isInsideCodeBlock = false
			d.workingSpaceFile = definition.WorkingSpaceFile{}

			d.decoratedOutput.WriteString("\n")

			continue
		}

		if d.isInsideCodeBlock {
			d.partialCodeBlock += line + lineSeparator

			blockLines := strings.Split(d.partialCodeBlock, lineSeparator)
			_, path := parseDiffBlockLine(blockLines[0], d.ProjectUris, d.ContextFilePaths, d.FileIndexer, d.PathHandleMap, d.RequestId, d.SessionId)
			// 模型异常返回，直接忽略
			if strings.HasSuffix(path, "{{ file_full_path }}") {
				continue
			}
			status, ok := service.WorkingSpaceFileIngMap.Load(d.BlockIdentifierMap[path])
			if ok && status == service.GENERATING.String() && d.BlockIdentifierMap[path] != "" && d.BlockIdentifierMap[path] == d.workingSpaceFile.Id {
				id := d.BlockIdentifierMap[path]
				generatingContent, exist := service.WorkingSpaceFileGeneratingContentMap[id]
				oldContent := ""
				if exist && generatingContent != nil {
					oldContent = generatingContent.FullContent
				}
				newContent := strings.Join(blockLines[1:], lineSeparator)
				trimOldContent := strings.TrimSuffix(oldContent, lineSeparator)
				trimNewContent := strings.TrimSuffix(newContent, lineSeparator)
				appendText := strings.TrimPrefix(trimNewContent, trimOldContent)
				if strings.Index(trimOldContent, trimNewContent) == -1 && appendText != "" {
					if generatingContent, exist := service.WorkingSpaceFileGeneratingContentMap[id]; exist && generatingContent != nil {
						generatingContent.FullContent = newContent
					}
					service.WorkingSpaceServiceManager.SyncWorkingSpaceFile(d.Ctx, d.workingSpaceFile, service.MODIFIED, 0, definition.DiffInfo{}, appendText, "", "")
				}
			}

		} else {
			d.decoratedOutput.WriteString(line + lineSeparator)
		}
	}
}

func (d *DiffBlockBasedOutputDecorator) Finish() {
	if d.workingSpaceFile.Id != "" {
		if status, ok := service.WorkingSpaceFileIngMap.Load(d.workingSpaceFile.Id); ok && status == service.GENERATING.String() {
			// 触发取消
			service.WorkingSpaceServiceManager.OperateWorkingSpaceFile(d.Ctx, definition.WorkingSpaceFileOperateParams{
				Id:     d.workingSpaceFile.Id,
				OpType: service.CANCEL.String(),
				Params: map[string]interface{}{
					service.NO_RECORD: true,
				},
			})
		}
	}
}

func (c *DiffBlockBasedOutputDecorator) GetAppendReasoningText() string {
	appendText := strings.TrimPrefix(c.reasoningText, c.lastReasoningText)
	c.lastReasoningText = c.reasoningText
	return appendText
}

func (c *DiffBlockBasedOutputDecorator) UpdateReasoningText(reasoning string) {
	c.reasoningText = reasoning
}

func (c *DiffBlockBasedOutputDecorator) GetReasoningText() string {
	return c.reasoningText
}

func (d *DiffBlockBasedOutputDecorator) GetDiffs() []definition.DiffBlock {
	return d.blocks
}

type AutoApplyOutputDecorator struct {
	publishedCodeDiffs []definition.DiffBlock
	DiffBlockBasedOutputDecorator
}

// CodeSnippet 表示代码片段的结构体
type CodeSnippet struct {
	Language string
	Path     string
	Code     string
}

var (
	// 定义正则表达式来匹配输入格式
	rawCodeBlockRegex = regexp.MustCompile("(?s)```([\\w.-]+):(.+?)\n(.*?)\n```")

	//解析后的问答中的代码块
	wrappedCodeBlockRegex = regexp.MustCompile("```[a-zA-Z.-]+::([^:]+)::[0-9a-fA-F-]+\n```")
)

// parseCodeSnippet 解析输入字符串并返回 CodeSnippet 结构体
func parseCodeSnippet(input string, projectUris []string, contextFilePaths []string, fileIndexer *indexing.ProjectFileIndex, pathHandleMap map[string]string, requestId string, sessionId string) (*CodeSnippet, error) {
	matches := rawCodeBlockRegex.FindStringSubmatch(input)

	if len(matches) != 4 {
		return nil, fmt.Errorf("invalid input format")
	}

	language := matches[1]
	path := matches[2]
	code := matches[3]

	originalPath := path

	if afterPath, ok := pathHandleMap[path]; ok && afterPath != "" {
		path = afterPath
	} else {
		afterPath = fixPath(path, projectUris, contextFilePaths, fileIndexer, requestId, sessionId)
		pathHandleMap[path] = afterPath
		path = afterPath
	}

	// 如果路径无法创建，则返回异常
	canNewFile := util.CanNewFile(path)
	if !canNewFile || originalPath != path {
		// 如果路径发生了修正，说明模型生成路径的指令遵循效果不好，埋点记录
		eventData := map[string]string{
			"session_id":    sessionId,
			"request_id":    requestId,
			"original_path": originalPath,
			"path":          path,
			"canNewFile":    strconv.FormatBool(canNewFile),
			"chat_mode":     definition.SessionModeEdit,
		}
		go sls.Report(sls.EventTypeChatAiDeveloperFixPath, requestId, eventData)
		if !canNewFile {
			return nil, fmt.Errorf("can not create path")
		}
	}

	// 去除 code 中的多余空格
	code = strings.TrimSpace(code)

	return &CodeSnippet{
		Language: language,
		Path:     path,
		Code:     code,
	}, nil
}

func isDiffCodeBlockLine(line string) bool {
	if strings.HasPrefix(line, "```") && strings.Index(line, ":") > 0 {
		return true
	}
	return false
}

/**
* 修正路径，保障路径为当前工程下的正确路径
 */
func fixPath(path string, projectUris []string, contextFilePaths []string, fileIndexer *indexing.ProjectFileIndex, requestId string, sessionId string) string {
	path = strings.TrimSpace(path)

	// 处理 {{ path }} 的情况
	if strings.HasPrefix(path, "{{") {
		path = strings.TrimPrefix(path, "{{")
	}
	if strings.HasSuffix(path, "}}") {
		path = strings.TrimSuffix(path, "}}")
	}
	path = strings.TrimSpace(path)

	// 处理 :path 的情况
	if strings.HasPrefix(path, ":") {
		path = strings.TrimPrefix(path, ":")
	}

	// 移除示例根目录
	if strings.HasPrefix(path, "/Users/<USER>/workspace/xxx/") {
		path = strings.TrimPrefix(path, "/Users/<USER>/workspace/xxx/")
	} else if strings.HasPrefix(path, "/Users/<USER>/workspace/") {
		path = strings.TrimPrefix(path, "/Users/<USER>/workspace/")
	} else if strings.HasPrefix(path, "/path/to/your/project/") {
		path = strings.TrimPrefix(path, "/path/to/your/project/")
	} else if strings.HasPrefix(path, "/path/to/your/workspace/") {
		path = strings.TrimPrefix(path, "/path/to/your/workspace/")
	}

	// 修复 filex name="xxx" 的场景
	idx := strings.Index(path, " name=\"")
	if idx > -1 {
		path = path[idx+len(" name=\""):]
		path = strings.TrimSuffix(path, "\"")
	}

	// 将相对路径填充成绝对路径
	if len(path) > 0 && len(projectUris) > 0 {
		relative := true
		for _, projectUri := range projectUris {
			// 处理 windows 下 /{绝对路径} 的问题，将 /{绝对路径} 替换成 {绝对路径}
			if !(strings.HasPrefix(projectUri, string(os.PathSeparator)) || strings.HasPrefix(projectUri, "/")) &&
				(strings.HasPrefix(path, string(os.PathSeparator)) || strings.HasPrefix(path, "/")) &&
				util.IsWindowsPath(projectUri) {
				path = path[1:]
			}

			// 处理漏了 / 的情况
			if strings.HasPrefix("/"+path, projectUri) {
				path = "/" + path
			}
			// windows 系统下/\转换问题
			if projectUri != "" && strings.Count(projectUri, string(os.PathSeparator)) > 0 &&
				strings.Count(path, string(os.PathSeparator)) == 0 {
				path = strings.ReplaceAll(path, "/", string(os.PathSeparator))
			}

			// 处理 windows 下大小写不匹配的问题
			idx = strings.Index(path, ":")
			if idx >= 0 && idx <= 5 {
				idx2 := strings.Index(projectUri, ":")
				if idx2 >= 0 && strings.ToUpper(path[:idx]) == strings.ToUpper(projectUri[:idx2]) {
					path = projectUri[:idx2] + path[idx:]
				}
			}

			// 处理 windows 下大小写不匹配且丢失了:的情况
			if strings.Index(projectUri, ":") >= 0 &&
				len(path) > 1 && len(projectUri) > 1 &&
				strings.ToUpper(path[:1]) == strings.ToUpper(projectUri[:1]) {
				_path := strings.Replace(path[1:], ":", "", 1)
				_projectUri := strings.Replace(projectUri[1:], ":", "", 1)
				if strings.HasPrefix(_path, _projectUri) {
					idx = strings.Index(path, _projectUri)
					if idx >= 0 {
						path = projectUri + path[idx+len(_projectUri):]
					}
				}
			}

			if strings.HasPrefix(path, projectUri) {
				relative = false
				break
			}
		}
		if relative {
			originFileName := util.GetFileName(path)
			targetProjectUri := projectUris[0]
			for _, projectUri := range projectUris {
				rootPath := filepath.Join(projectUri, path)
				// 先尝试匹配是否与存在的路径匹配
				workspaceTreeIndexer, ok := fileIndexer.GetWorkspaceTreeFileIndexer()
				if !util.FileExists(rootPath) && originFileName != path && fileIndexer != nil && ok &&
					workspaceTreeIndexer != nil && workspaceTreeIndexer.WorkspaceTree != nil {
					pathWithoutName := strings.TrimSuffix(path, originFileName)
					fileList := workspaceTreeIndexer.WorkspaceTree.TreeToList()
					flag := false
					for _, filePath := range fileList {
						fileName := util.GetFileName(filePath)
						if fileName == filePath {
							continue
						}
						filePathWithoutName := strings.TrimSuffix(filePath, fileName)
						if strings.HasSuffix(filePathWithoutName, pathWithoutName) {
							path = filepath.Join(filePathWithoutName, originFileName)
							flag = true
							break
						}
					}
					if flag {
						targetProjectUri = projectUri
						break
					}
				} else if util.FileExists(rootPath) {
					targetProjectUri = projectUri
					break
				}
			}
			// 兜底拼上工程根目录
			path = filepath.Join(targetProjectUri, path)
		}
	}

	// 尝试判断路径是否出现基本的出错
	if !util.FileExists(path) {
		fileName := util.GetFileName(path)
		if len(fileName) > 0 {
			for _, filePath := range contextFilePaths {
				if len(path) == len(filePath) {
					oldFileName := util.GetFileName(filePath)
					if fileName == oldFileName {
						same := true
						for i := len(path) - 1; i >= 0; i-- {
							if path[i] == filePath[i] {
								continue
							}
							if (path[i] == '.' || path[i] == '/' || path[i] == '\\') && (filePath[i] == '.' || filePath[i] == '/' || filePath[i] == '\\') {
								continue
							}
							same = false
							break
						}
						if same {
							path = filePath
							break
						}
					}
				}
			}
		}
	}

	return path
}

func parseDiffBlockLine(line string, projectUris []string, contextFilePaths []string, fileIndexer *indexing.ProjectFileIndex, pathHandleMap map[string]string, requestId string, sessionId string) (string, string) {
	if !strings.HasPrefix(line, "```") || strings.Index(line, ":") < 0 {
		return "", ""
	}
	trimmedLine := strings.TrimPrefix(line, "```")
	firstIndex := strings.Index(trimmedLine, ":")

	language := trimmedLine[:firstIndex]
	path := trimmedLine[firstIndex+1:]

	if afterPath, ok := pathHandleMap[path]; ok && afterPath != "" {
		path = afterPath
	} else {
		afterPath = fixPath(path, projectUris, contextFilePaths, fileIndexer, requestId, sessionId)
		pathHandleMap[path] = afterPath
		path = afterPath
	}

	return language, path
}

// 是否是完整代码行，流式输出场景可能只有部分
// 部分行不会有\n字符
func isCompleteCodeBlockLine(output, line string, lineNum int) bool {
	lines := strings.Split(output, lineSeparator)
	if lineNum < len(lines)-1 {
		//中间行肯定是完整行
		return true
	}
	if strings.HasSuffix(output, lineSeparator) {
		return true
	}
	return false
}

// 是否是完整代码行，流式输出场景可能只有部分
// 部分行不会有\n字符
func isLastIncompleteCodeBlockLine(output, line string, lineNum int) bool {
	lines := strings.Split(output, lineSeparator)
	if lineNum == len(lines)-1 && !strings.HasSuffix(line, lineSeparator) {
		//中间行肯定是完整行
		return true
	}
	return false
}

// 判断是否最后一行，且 ``` 开头的代码行场景
func isLastIncompleteLine(output, line string, lineNum int) bool {
	lines := strings.Split(output, lineSeparator)
	if lineNum == len(lines)-1 && !strings.HasSuffix(line, lineSeparator) {
		// 判断是否 ``` 开头
		prefix := "```"
		if len(line) < 3 {
			prefix = prefix[:len(line)]
		}
		return strings.HasPrefix(line, prefix)
	}
	return false
}

type chatAnswerController struct {
	answerDecorators map[string]ChatOutputDecorator
}

func (c *chatAnswerController) initDecorator(requestId string, decorator ChatOutputDecorator) {
	if c.answerDecorators == nil {
		c.answerDecorators = make(map[string]ChatOutputDecorator)
	}
	c.answerDecorators[requestId] = decorator
}

func (c *chatAnswerController) getDecorator(requestId string) ChatOutputDecorator {
	return c.answerDecorators[requestId]
}

func (c *chatAnswerController) deleteDecorator(requestId string) {
	delete(c.answerDecorators, requestId)
}

func BuildCommonChatRemoteChatAskParam(ctx context.Context, params *definition.AskParams, extraParams definition.ChatAskExtraParams) (definition.RemoteChatAsk, error) {
	//会话历史
	validRecords, _ := service.SessionServiceManager.GetSessionRecordsForHistoryBuild(params.SessionId)
	fileIndexer, _ := ctx.Value(definition.ContextKeyFileIndexer).(*indexing.ProjectFileIndex)

	remoteChatAskParam := definition.RemoteChatAsk{
		RequestId: params.RequestId,
		//AI Developer本期与requestId一致
		RequestSetId:            params.RequestId,
		ChatRecordId:            params.RequestId,
		ChatTask:                params.ChatTask,
		ChatContext:             params.ChatContext,
		IsReply:                 params.IsReply,
		SessionId:               params.SessionId,
		CodeLanguage:            params.CodeLanguage,
		Source:                  params.Source,
		Stream:                  params.Stream,
		Version:                 "2",
		CustomSystemRoleContent: params.CustomSystemRoleContent,
		Parameters:              params.Parameters,
		UserType:                user.GetUserType(),
		TaskDefinitionType:      params.TaskDefinitionType,
		SessionType:             params.SessionType,
		AgentId:                 definition.AgentIdAIChat,
		Mode:                    params.Mode,
		Passkey:                 params.Passkey,
	}
	if !util.IsSystemCommandTask(params.ChatTask) && extraParams.ImageUrls != nil {
		//非系统指令走agent chat service，需要传图片urls在外面
		remoteChatAskParam.ImageUrls = extraParams.ImageUrls
	}

	modelConfig := chatUtil.PrepareModelConfig(*params)
	if modelConfig != nil {
		remoteChatAskParam.ModelConfig = *modelConfig
	}

	isUseLocalChatPrompt := false
	if params.TaskDefinitionType == definition.TaskDefinitionTypeCustom {
		commandExtraValue, ok := params.Extra[definition.ChatExtraKeyCommand]
		if !ok {
			log.Errorf("invalid command invocation: %+v", params.Extra)
			return definition.RemoteChatAsk{}, errors.New("invalid command invocation")
		}
		commandExtra := definition.CustomCommandExtra{}
		if err := util.UnmarshalToObject(util.ToJsonStr(commandExtraValue), &commandExtra); err != nil {
			log.Errorf("invalid command invocation: %+v", params.Extra)
			return definition.RemoteChatAsk{}, errors.New("invalid command invocation")
		}

		ideSdk := extension.BuildSdkTool(ctx)
		commandContext, err := buildCommandInvocationContext(params, fileIndexer)
		if err != nil {
			return definition.RemoteChatAsk{}, errors.New("build command error")
		}
		customPrompt, invokeErr := extension.ApiExecutor.InvokeCommand(ctx, commandExtra.Identifier, commandContext, ideSdk)
		if invokeErr != nil {
			log.Errorf("invoke command error: %+v", params.Extra)

			chatFinish := definition.ChatFinish{
				RequestId:  params.RequestId,
				SessionId:  params.SessionId,
				Reason:     "{\"reason\":\"command execute error\"}",
				StatusCode: cosyErrors.CommandExecuteError,
			}
			go PushChatFinishMessage(ctx, chatFinish)

			return definition.RemoteChatAsk{}, errors.New("invoke command error")
		}
		remoteChatAskParam = buildCustomRemoteAsk(remoteChatAskParam, commandExtra, customPrompt, fileIndexer)

		//自定义指令
		commandConfig := extension.GlobalExtensionConfig.GetCommandConfig(commandExtra.Identifier)
		if commandConfig == nil {
			log.Errorf("invalid command config is nil. identifier: %s", commandExtra.Identifier)

			chatFinish := definition.ChatFinish{
				RequestId:  params.RequestId,
				SessionId:  params.SessionId,
				Reason:     "{\"reason\":\"command not found\"}",
				StatusCode: cosyErrors.CommandNotFound,
			}
			go PushChatFinishMessage(ctx, chatFinish)

			return definition.RemoteChatAsk{}, errors.New("invalid command config")
		}
		//带上历史
		remoteChatAskParam.PromptHistory = buildChatSummaryHistory(validRecords)

		return remoteChatAskParam, nil
	} else {
		if util.IsRetryRemoteAsk(params) {
			if validRecords == nil || len(validRecords) <= 0 {
				//非正常情况
				log.Warnf("retry task without history records. sessionId=%s", params.SessionId)
				return remoteChatAskParam, errors.New("retry task without history records")
			}
			//重新组装context
			lastChatRecord := validRecords[len(validRecords)-1]
			remoteChatAskParam.ChatContext = lastChatRecord.ChatContext
			randParams := make(map[string]interface{})
			randParams["seed"] = util.NextRandNumber(10000)
			randParams["temperature"] = float32(1.0)
			remoteChatAskParam.Parameters = randParams

			restChatRecords := validRecords[0 : len(validRecords)-1]
			remoteChatAskParam = newRetryRemoteChatAsk(ctx, false, remoteChatAskParam, lastChatRecord, restChatRecords)

			if modelConfig != nil {
				remoteChatAskParam.ModelConfig = *modelConfig
			}

			if restChatRecords != nil && len(restChatRecords) > 0 {
				remoteChatAskParam.IsReply = true
				remoteChatAskParam.PromptHistory = buildChatHistory(restChatRecords)
			} else {
				remoteChatAskParam.IsReply = false
				remoteChatAskParam.ChatHistory = nil
			}
		} else if util.IsSystemCommandTask(params.ChatTask) {
			//预制任务，不走自定义上下文本地prompt

			// fetchSystemRules
			//customSystemRoleContent := systemrule.AppendSystemRules(ctx, systemrule.AppendSystemRuleReq{
			//	OriginSystemPrompt:  "",
			//	SessionId:           params.SessionId,
			//	RequestId:           params.RequestId,
			//	IsEnableProjectRule: params.PluginPayloadConfig.IsEnableProjectRule,
			//	Scene:               systemrule.SystemCommand,
			//})
			var customSystemRoleContent string
			// 追加rules
			projectRuleContext, ok := params.Extra[common.KeyProjectRuleContext].(rule.ProjectRuleContext)
			if ok {
				customSystemRoleContent = appendUserRulesForChat(ctx, &projectRuleContext, "")
			}
			if customSystemRoleContent != "" {
				customSystemRoleContent = memory.AppendMemoryPromptForChat(customSystemRoleContent, params.Extra)
				remoteChatAskParam.CustomSystemRoleContent = customSystemRoleContent
			}
		} else {
			//从attach区域取
			contextExtraValue, ok := params.AttachedInputs[common.KeyContextProviderExtra]
			remoteChatAskParam.PromptHistory = buildChatHistory(validRecords)

			if ok {
				isUseLocalChatPrompt = true
				contextExtras := contextExtraValue.([]definition.CustomContextProviderExtra)
				remoteChatAskParam = buildFreeInputChatPrompt(ctx, params.AttachedInputs, remoteChatAskParam, contextExtras, fileIndexer)

				chatContext, ok := remoteChatAskParam.ChatContext.(definition.FreeInputChatContext)
				input := prompt.FreeChatSystemPromptInput{
					BaseInput: prompt.BaseInput{
						RequestId: params.RequestId,
						SessionId: params.SessionId,
						IsQoder:   global.IsQoderProduct(),
					},
				}
				if !ok {
					input.WorkspaceLanguages = ""
					input.PreferredLanguage = "中文"
				} else {
					input.WorkspaceLanguages = systemrule.FetchWorkspaceLanguages(chatContext)
					input.PreferredLanguage = systemrule.FetchPreferredLanguage(chatContext)
				}
				systemPrompt, err := prompt.Engine.RenderFreeChatSystemPrompt(input)
				if err != nil {
					log.Error("Failed to render freeInputContext systemPrompt", err)
				}

				// fetch systemRule
				systemPromptWithRules := systemPrompt
				projectRuleContext, ok := params.Extra[common.KeyProjectRuleContext].(rule.ProjectRuleContext)
				if ok {
					systemPromptWithRules = appendUserRulesForChat(ctx, &projectRuleContext, systemPromptWithRules)
				}
				systemPromptWithRules = memory.AppendMemoryPromptForChat(systemPromptWithRules, params.Extra)

				remoteChatAskParam.SystemRoleContent = systemPromptWithRules
			}
		}
		if !util.IsWorkspaceRagFeatureEnabled(&remoteChatAskParam) && !isUseLocalChatPrompt {
			//workspace及rag截断逻辑 see chat/chains/chat_ask.go:257
			//截断普通问答
			chain2.TruncateRemoteAskParam(&remoteChatAskParam)
		}

		routeTaskId := routeTaskIdForAIChatFreeInputTask(params, &remoteChatAskParam, extraParams)
		remoteChatAskParam.TaskId = routeTaskId
		if !util.IsSystemCommandTask(params.ChatTask) {
			// 是否为多模态链路
			isMultimodal := len(remoteChatAskParam.ImageUrls) > 0
			// 执行兜底截断保护
			historyRemainLength := getFreeInputContextHistoryLimitLength(remoteChatAskParam.PromptHistory)
			tokenTotalLimit := getChatTokenTotalLimit(isMultimodal, definition.SessionTypeChat, remoteChatAskParam.ModelConfig)
			historyRemainToken := historyRemainLength / tokenToStringLengthRatio
			systemPromptTokenLength := getTokenLength(remoteChatAskParam.SystemRoleContent)
			chatPromptTokenLimit := tokenTotalLimit - historyRemainToken - systemPromptTokenLength
			truncatedChatPrompt := remoteChatAskParam.ChatPrompt
			truncatedChatPrompt = SafeTruncatePrompt(ctx, truncatedChatPrompt, chatPromptTokenLimit, params.RequestId)
			remoteChatAskParam.ChatPrompt = truncatedChatPrompt

			// 执行回话历史截断
			if len(remoteChatAskParam.PromptHistory) > 0 {
				maxHistoryLimit := chatPromptTokenLimit - getTokenLength(truncatedChatPrompt)
				if maxHistoryLimit < 0 {
					maxHistoryLimit = historyRemainToken
				}
				truncateHistoryItems := TruncateAgentHistoryIfNecessary(remoteChatAskParam.PromptHistory, maxHistoryLimit)
				remoteChatAskParam.PromptHistory = truncateHistoryItems
			}

			if global.IsQoderProduct() {
				// 设置messages
				var messages []*agentDefinition.Message
				// 处理下历史消息
				if len(remoteChatAskParam.PromptHistory) > 0 {
					// 遍历历史消息，
					for _, historyItem := range remoteChatAskParam.PromptHistory {
						if len(historyItem.UserImageUrls) > 0 {
							// 多模态的用户message
							chatPartText := agentDefinition.ChatMessagePart{
								Type: agentDefinition.ChatMessagePartTypeText,
								Text: historyItem.User,
							}
							chatPartImage := agentDefinition.ChatMessagePart{
								Type: agentDefinition.ChatMessagePartTypeImageURL,
								ImageURL: &agentDefinition.ChatMessageImageURL{
									URL:      historyItem.UserImageUrls[0],
									MIMEType: "image/png",
								},
							}
							multiContent := []agentDefinition.ChatMessagePart{chatPartText, chatPartImage}
							userMessage := &agentDefinition.Message{Role: agentDefinition.RoleTypeUser, Content: historyItem.User, MultiContent: multiContent}
							messages = append(messages, userMessage)
							// assistant message
							assistantMessage := &agentDefinition.Message{Role: agentDefinition.RoleTypeAssistant, Content: historyItem.Bot}
							messages = append(messages, assistantMessage)
						} else {
							userMessage := &agentDefinition.Message{Role: agentDefinition.RoleTypeUser, Content: historyItem.User}
							messages = append(messages, userMessage)
							assistantMessage := &agentDefinition.Message{Role: agentDefinition.RoleTypeAssistant, Content: historyItem.Bot}
							messages = append(messages, assistantMessage)
						}
					}
				}
				// 系统消息
				systemMessage := &agentDefinition.Message{Role: agentDefinition.RoleTypeSystem, Content: remoteChatAskParam.SystemRoleContent}
				messages = append(messages, systemMessage)
				// 最新一轮用户消息，注意也要区分下是否多模态
				if len(remoteChatAskParam.ImageUrls) > 0 {
					imageUrl := remoteChatAskParam.ImageUrls[0]
					chatPartText := agentDefinition.ChatMessagePart{
						Type: agentDefinition.ChatMessagePartTypeText,
						Text: remoteChatAskParam.ChatPrompt,
					}
					chatPartImage := agentDefinition.ChatMessagePart{
						Type: agentDefinition.ChatMessagePartTypeImageURL,
						ImageURL: &agentDefinition.ChatMessageImageURL{
							URL:      imageUrl,
							MIMEType: "image/png",
						},
					}
					multiContent := []agentDefinition.ChatMessagePart{chatPartText, chatPartImage}
					userMessage := &agentDefinition.Message{Role: agentDefinition.RoleTypeUser, Content: remoteChatAskParam.ChatPrompt, MultiContent: multiContent}
					messages = append(messages, userMessage)
				} else {
					userMessage := &agentDefinition.Message{Role: agentDefinition.RoleTypeUser, Content: remoteChatAskParam.ChatPrompt}
					messages = append(messages, userMessage)
				}
				remoteChatAskParam.Messages = messages
				remoteChatAskParam.PromptHistory = nil
				// agentId从ai_chat改成agent_common
				remoteChatAskParam.AgentId = definition.AgentCommonAgentId
				remoteChatAskParam.TaskId = "common"

				// 发起问答的时间检查了是否有图片，是否是多模态模型 // TODO 上线前可以去掉
				//if support.IsMultiVlMessageRequest(messages) {
				//	modelConfig := chatUtil.PrepareModelConfig(*params)
				//	if modelConfig == nil || !modelConfig.IsVl || "openai" != modelConfig.Format {
				//		remoteChatAskParam.AgentId = definition.AgentCommonAgentService
				//		remoteChatAskParam.TaskId = definition.TaskIdVlCommon
				//	}
				//}
			}
		}

		return remoteChatAskParam, nil
	}
}

func buildUItoCodePrompt(ctx context.Context, inputs map[string]any, requestId, sessionId string) (string, error) {
	userInputQuery := inputs[common.KeyUserInputQuery].(string)
	contextProviderExtras := inputs[common.KeyContextProviderExtra].([]definition.CustomContextProviderExtra)
	preferredLanguage, ok := ctx.Value(common.KeyPreferredLanguage).(string)
	if !ok || preferredLanguage == "" {
		preferredLanguage = definition.LocaleZh
	}

	fileIndexer, _ := ctx.Value(definition.ContextKeyFileIndexer).(*indexing.ProjectFileIndex)
	baseInput := buildFeUIParams(fileIndexer)

	input := prompt.UIToCodePromptInput{
		BaseInput: prompt.BaseInput{
			RequestId: requestId,
			SessionId: sessionId,
			IsQoder:   global.IsQoderProduct(),
		},
		UserInputQuery:       userInputQuery,
		ContextDetails:       chatUtil.ConvertContextProviderExtras(contextProviderExtras),
		BaseUIToCodeDepInput: baseInput,
		PreferredLanguage:    "回答中非代码的解释性内容请使用" + preferredLanguage + "回答。",
	}

	var renderedText string
	var err error

	renderedText, err = prompt.Engine.RenderUIToCodePrompt(input)
	if err != nil {
		return "", err
	}
	return renderedText, nil
}

func BuildAIDeveloperChatRemoteChatAskParam(ctx context.Context, params *definition.AskParams, extraParams definition.ChatAskExtraParams) (definition.RemoteChatAsk, error) {
	fileIndexer, _ := ctx.Value(definition.ContextKeyFileIndexer).(*indexing.ProjectFileIndex)
	//从attach区域取
	var adjustedContextExtras []definition.CustomContextProviderExtra
	var contextProviderExtras []definition.CustomContextProviderExtra

	if contextExtraValue, ok := params.AttachedInputs[common.KeyContextProviderExtra]; ok {
		contextProviderExtras = contextExtraValue.([]definition.CustomContextProviderExtra)

		if !util.IsRetryRemoteAsk(params) {
			adjustedContextExtras = updateWorkspaceFileContentForContexts(ctx, params.SessionId, contextProviderExtras)
		}
	}

	agentId := definition.AgentAIDeveloper
	taskId := ""

	imageUrls := make([]string, 0)

	if extraParams.IntentionType == definition.AIDeveloperIntentDetectChat {
		taskId = definition.AgentTaskCommonChat
		imageUrls = chatUtil.FindImageUrlsFromContext(contextProviderExtras)
		if len(imageUrls) > 0 {
			taskId = definition.AgentTaskVlChat
		}
	} else if extraParams.IntentionType == definition.AIDeveloperIntentDetectDev {
		taskId = definition.AgentTaskDiffSolution
		imageUrls = chatUtil.FindImageUrlsFromContext(contextProviderExtras)
		if len(imageUrls) > 0 {
			taskId = definition.AgentTaskVlDiffSolution
		}
	} else if extraParams.IntentionType == definition.AIDeveloperIntentDetectUI2FeCode {
		taskId = definition.AgentTaskDiffSolution
		imageUrls = chatUtil.FindImageUrlsFromContext(contextProviderExtras)
		if len(imageUrls) > 0 {
			taskId = definition.AgentTaskVlUItoCode
		}
	} else if extraParams.IntentionType == definition.AIDeveloperIntentDetectDev {
		taskId = definition.AgentTaskTestAgentPlanning
	} else {
		log.Errorf("invalid intention type: %s", extraParams.IntentionType)
		return definition.RemoteChatAsk{}, errors.New("invalid intention type")
	}

	remoteChatAskParam := definition.RemoteChatAsk{
		RequestId: params.RequestId,
		//AI Developer本期与requestId一致
		RequestSetId:            params.RequestId,
		ChatRecordId:            params.RequestId,
		ChatTask:                params.ChatTask,
		ChatContext:             params.ChatContext,
		IsReply:                 params.IsReply,
		SessionId:               params.SessionId,
		CodeLanguage:            params.CodeLanguage,
		Source:                  params.Source,
		Stream:                  params.Stream,
		Version:                 "2",
		CustomSystemRoleContent: params.CustomSystemRoleContent,
		Parameters:              params.Parameters,
		UserType:                user.GetUserType(),
		TaskDefinitionType:      params.TaskDefinitionType,
		SessionType:             params.SessionType,
		AgentId:                 agentId,
		TaskId:                  taskId,
		Mode:                    params.Mode,
		Passkey:                 params.Passkey,
	}
	if len(imageUrls) > 0 {
		remoteChatAskParam.ImageUrls = imageUrls
	}

	modelConfig := chatUtil.PrepareModelConfig(*params)
	if modelConfig != nil {
		remoteChatAskParam.ModelConfig = *modelConfig
	}

	//需要放在前面先清理无效的卡片和快照
	chatUtil.ClearInvalidSnapshot(ctx, params.SessionId, definition.SessionModeEdit)

	//会话历史
	validRecords, _ := service.SessionServiceManager.GetSessionRecordsForHistoryBuild(params.SessionId)

	if util.IsRetryRemoteAsk(params) {
		if validRecords == nil || len(validRecords) <= 0 {
			//非正常情况
			log.Warnf("retry task without history records. sessionId=%s", params.SessionId)
			return remoteChatAskParam, errors.New("retry task without history records")
		}
		//重新组装context
		lastChatRecord := validRecords[len(validRecords)-1]
		randParams := make(map[string]interface{})
		randParams["seed"] = util.NextRandNumber(10000)
		randParams["temperature"] = float32(1.0)
		remoteChatAskParam.Parameters = randParams

		restChatRecords := validRecords[0 : len(validRecords)-1]
		remoteChatAskParam = newRetryRemoteChatAsk(ctx, true, remoteChatAskParam, lastChatRecord, restChatRecords)
		if modelConfig != nil {
			remoteChatAskParam.ModelConfig = *modelConfig
		}

		if lastChatRecord.ChatTask == definition.AI_DEVELOPER_TEST_AGENT_FIX_ENV {
			remoteChatAskParam = buildAIDeveloperTestFixEnvRetryParam(ctx, remoteChatAskParam, lastChatRecord, adjustedContextExtras, fileIndexer)
		} else {
			//remoteChatAskParam = buildAiDeveloperDevPrompt(ctx, remoteChatAskParam, adjustedContextExtras, fileIndexer)

			if restChatRecords != nil && len(restChatRecords) > 0 {
				remoteChatAskParam.IsReply = true
				remoteChatAskParam.PromptHistory = buildChatSummaryHistory(restChatRecords)
			} else {
				remoteChatAskParam.IsReply = false
				remoteChatAskParam.PromptHistory = make([]definition.AgentPromptHistoryItem, 0)
			}
		}
	} else if extraParams.IntentionType == definition.AIDeveloperIntentDetectDev {
		remoteChatAskParam.PromptHistory = buildChatSummaryHistory(validRecords)
		remoteChatAskParam = buildAiDeveloperDevPrompt(ctx, remoteChatAskParam, adjustedContextExtras, fileIndexer, extraParams, params.Extra)
	} else if extraParams.IntentionType == definition.AIDeveloperIntentDetectUI2FeCode {
		remoteChatAskParam.PromptHistory = buildChatSummaryHistory(validRecords)
		remoteChatAskParam = buildAiDeveloperDevPrompt(ctx, remoteChatAskParam, adjustedContextExtras, fileIndexer, extraParams, params.Extra)
	} else if params.ChatTask == definition.AI_DEVELOPER_TEST_AGENT_FIX_ENV {
		// AI_DEVELOPER_TEST_AGENT_FIX_ENV 任务，不需要带上历史
		remoteChatAskParam = buildAIDeveloperTestFixEnvPrompt(ctx, remoteChatAskParam, adjustedContextExtras, fileIndexer, params.Extra)
	} else {
		//意图识别出来是 common普通问答
		remoteChatAskParam.PromptHistory = buildChatSummaryHistory(validRecords)
		remoteChatAskParam = buildAIDeveloperChatPrompt(ctx, remoteChatAskParam, adjustedContextExtras, fileIndexer, extraParams, params.Extra)
	}

	if !util.IsWorkspaceRagFeatureEnabled(&remoteChatAskParam) {
		//workspace及rag截断逻辑 see chat/chains/chat_ask.go:257
		//截断普通问答
		chain2.TruncateRemoteAskParam(&remoteChatAskParam)
	}

	return remoteChatAskParam, nil
}

func buildAIDeveloperTestFixEnvRetryParam(ctx context.Context, remoteChatAskParam definition.RemoteChatAsk, lastChatRecord definition.ChatRecord, adjustedContextExtras []definition.CustomContextProviderExtra, fileIndexer *indexing.ProjectFileIndex) definition.RemoteChatAsk {
	remoteChatAskParam.IsReply = true
	// AI_DEVELOPER_TEST_AGENT_FIX_ENV 任务，不需要带上历史
	var extraMap map[string]interface{}
	err := json.Unmarshal([]byte(lastChatRecord.Extra), &extraMap)
	if err != nil {
		log.Errorf("parse lastChatRecord.Extra error")
	}
	remoteChatAskParam = buildAIDeveloperTestFixEnvPrompt(ctx, remoteChatAskParam, adjustedContextExtras, fileIndexer, extraMap)
	return remoteChatAskParam
}

// 追加一个Summary 历史
// params: records 升序排列
func buildChatSummaryHistory(records []definition.ChatRecord) []definition.AgentPromptHistoryItem {
	if len(records) <= 0 {
		return nil
	}
	latestRecord := records[len(records)-1]

	//兜底兼容历史问答无Summary场景
	var summary string
	if latestRecord.Summary == "" {
		summary = latestRecord.Answer
	} else {
		summary = strings.ReplaceAll(summaryPromptTemplate, "$summary", latestRecord.Summary)
	}

	summaryHistory := definition.AgentPromptHistoryItem{
		User: "帮我总结上面的会话历史",
		Bot:  removeToolCalls(summary),
	}

	slice := append([]definition.AgentPromptHistoryItem{summaryHistory})
	return slice
}

func buildChatHistory(records []definition.ChatRecord) []definition.AgentPromptHistoryItem {
	if len(records) <= 0 {
		return nil
	}
	var history []definition.AgentPromptHistoryItem
	for _, r := range records {
		item := definition.AgentPromptHistoryItem{
			User:          getChatPrompt(r),
			Bot:           getChatAnswer(r),
			UserImageUrls: getChatImages(r),
		}
		history = append(history, item)
	}
	return history
}

func getChatPrompt(r definition.ChatRecord) string {
	if r.ChatPrompt != "" {
		return r.ChatPrompt
	}
	chatContext := map[string]any{}
	util.UnmarshalToObject(r.ChatContext, &chatContext)
	if chatPrompt, ok := chatContext["chatPrompt"].(string); ok {
		if chatPrompt != "" {
			return chatPrompt
		}
	}
	if text, ok := chatContext["text"].(string); ok {
		if text != "" {
			return text
		}
	}
	return r.Question
}

func getChatAnswer(r definition.ChatRecord) string {
	if r.Answer != "" {
		return removeToolCalls(r.Answer)
	}
	return removeToolCalls(r.Summary)
}

func getChatImages(r definition.ChatRecord) []string {
	if !global.IsQoderProduct() {
		return nil
	}
	if r.Extra != "" {
		// 将extra转成map对象
		extraMap := map[string]interface{}{}
		err := json.Unmarshal([]byte(r.Extra), &extraMap)
		if err != nil {
			// 如果没有找到上下文提供者信息，则记录日志并返回 nil
			log.Debugf("getChatImages error:%v", err)
			return nil
		}

		context, ok := extraMap[definition.ChatExtraKeyContext]
		if !ok {
			// 如果没有找到上下文提供者信息，则记录日志并返回 nil
			log.Debugf("no contextProvider in params: %v", extraMap)
			return nil
		}
		// 初始化上下文提供者额外信息的切片
		var contextProviderExtras []definition.CustomContextProviderExtra

		// 将提取到的上下文提供者额外信息反序列化到切片中
		if err := util.UnmarshalToObject(util.ToJsonStr(context), &contextProviderExtras); err != nil {
			// 如果反序列化过程中出现错误，则记录警告日志并返回 nil
			log.Warnf("unmarshal contextProvider extras error: %v", err)
			return nil
		}
		return chatUtil.FindImageUrlsFromContext(contextProviderExtras)
	}
	return nil
}

func removeToolCalls(text string) string {
	// 正则表达式匹配如下格式：
	// ```toolCall::任意字符::INIT
	// ...（可能多行内容）
	// ```
	return toolCallRegex.ReplaceAllString(text, "")
}
func updateWorkspaceFileContentForContexts(ctx context.Context, sessionId string, contextExtras []definition.CustomContextProviderExtra) []definition.CustomContextProviderExtra {
	var adjustedContextExtras []definition.CustomContextProviderExtra
	// 工作区文件自动引入
	contextItems, err := service.WorkingSpaceServiceManager.GetCurrentContexts(ctx, sessionId)
	contextItemMap := map[string]definition.ContextItem{}
	if err == nil {
		for _, contextItem := range contextItems {
			contextItemMap[contextItem.Key] = contextItem
		}
	}
	for _, contextExtra := range contextExtras {
		if contextExtra.Name == definition.PlatformContextProviderFile {
			var adjustedItems []definition.ParsedContextItem
			for _, contextItem := range contextExtra.ParsedContextItems {
				if currentContextItem, ok := contextItemMap[contextItem.Key]; ok {
					contextItem.Value = currentContextItem.Value
					delete(contextItemMap, contextItem.Key)
				}
				adjustedItems = append(adjustedItems, contextItem)
			}
			contextExtra.ParsedContextItems = adjustedItems
		}
		adjustedContextExtras = append(adjustedContextExtras, contextExtra)
	}
	for key := range contextItemMap {
		adjustedContextExtras = append(adjustedContextExtras, definition.CustomContextProviderExtra{
			ComponentType: extension.ContextProviderTypeComboBox,
			Name:          definition.PlatformContextProviderFile,
			ParsedContextItems: []definition.ParsedContextItem{
				definition.ParsedContextItem{
					ContextItem: contextItemMap[key],
				},
			},
		})
	}
	return adjustedContextExtras
}

func updateChatSummary(sessionId string, chatRecordId string, chatReqResponse ChatRequestResponse) {
	defer func() {
		if r := recover(); r != nil {
			// 获取当前堆栈信息
			stack := make([]byte, 4096)
			stack = stack[:runtime.Stack(stack, false)]
			log.Warnf("recover from crash. err: %+v, stack: %s", r, stack)
		}
	}()

	// common agent改造，问答/dev都进行Summary
	//chatSession, err := service.SessionServiceManager.GetChatSession(definition.GetSessionParam{
	//	SessionId: sessionId,
	//})
	//if err != nil {
	//	log.Errorf("Get chat session error. sessionId: %s, error: %v", sessionId, err)
	//	return
	//}
	//if chatSession.SessionType != definition.SessionTypeDeveloper {
	//	return
	//}
	validRecords, _ := service.SessionServiceManager.GetSessionRecordsForHistoryBuild(sessionId)
	if len(validRecords) <= 0 {
		return
	}
	currentChat := validRecords[len(validRecords)-1]
	if currentChat.RequestId != chatRecordId {
		return
	}
	var lastChatSummary string
	if len(validRecords) > 1 {
		lastChat := validRecords[len(validRecords)-2]
		lastChatSummary = lastChat.Summary
	}
	answer := fixAnswerForSummary(currentChat.Answer)

	agentType := "common"
	if chatReqResponse.AskExtra.IntentionType == definition.AIDeveloperIntentDetectUnittest {
		agentType = "test_agent"
	} else if chatReqResponse.AskExtra.IntentionType == definition.AIDeveloperIntentDetectDev {
		agentType = "dev_agent"
	}

	fallbackSummary := strings.ReplaceAll(fallbackSummaryTemplate, "$lastSummary", lastChatSummary)
	fallbackSummary = strings.ReplaceAll(fallbackSummary, "$currentQuestion", chatReqResponse.AskExtra.ParsedUserQuery)
	fallbackSummary = strings.ReplaceAll(fallbackSummary, "$agentType", agentType)

	//兜底策略，先存个Summary
	currentChat.Summary = fallbackSummary
	service.SessionServiceManager.UpdateChat(currentChat)

	input := prompt.AIDevelopChatSummaryPromptInput{
		BaseInput: prompt.BaseInput{
			RequestId: chatRecordId,
			SessionId: sessionId,
		},
		UserInputQuery:  currentChat.Question,
		LastChatSummary: lastChatSummary,
		AgentType:       chatUtil.GetAgentNameByIntent(currentChat.IntentionType),
		ChatAnswer:      answer,
	}

	var contextProviderExtras []definition.CustomContextProviderExtra
	if extra, ok := chatReqResponse.AskParam.AttachedInputs[common.KeyContextProviderExtra]; ok {
		contextProviderExtras = extra.([]definition.CustomContextProviderExtra)

		contextDetails := chatUtil.ConvertContextProviderExtras(contextProviderExtras)
		if contextDetails != nil {
			input.ContextDetails = contextDetails
		}
	}

	summaryPrompt, err := prompt.Engine.RenderAiDevelopChatSummaryPrompt(input)
	if err != nil {
		log.Warnf("render ai develop chat summary prompt error. input: %+v", input)
		return
	}

	go func() {
		requestId := uuid.NewString()
		request := definition.ChatSummaryRequest{
			ChatPrompt: summaryPrompt,
			Stream:     true,
			RequestId:  requestId,
			Parameters: map[string]any{
				"temperature": 0.1,
			},
			AgentId:     definition.AgentAIDeveloper,
			TaskId:      definition.AgentTaskChatSummary,
			Version:     "2",
			SessionType: definition.SessionTypeDeveloper,
		}
		outputResp, err2 := remote.ExecuteStreamedAgentRequest(definition.AgentChatAskService, "llm_model_result", request, 60*time.Second, requestId, definition.AgentAIDeveloper)
		go sls.Report(sls.EventTypeChatAgentRequest, requestId, map[string]string{
			"agent_id":       definition.AgentAIDeveloper,
			"task_id":        definition.AgentTaskChatSummary,
			"request_id":     requestId,
			"request_set_id": chatRecordId,
			"chat_record_id": chatRecordId,
			"mode":           chatReqResponse.AskParam.Mode,
		})
		if err2 != nil {
			log.Warnf("ExecuteStreamedAgentRequest error, reason=%v", err2)

			go stable.ReportAgentRequestError(definition.AgentAIDeveloper, definition.AgentTaskChatSummary, requestId, err2, nil)
			return
		}
		currentSummary := outputResp.Text
		if !outputResp.IsSuccess() || currentSummary == "" {
			log.Warnf("ai chat summary response error, reason=%+v", outputResp)
			return
		}
		currentChat.Summary = currentSummary
		service.SessionServiceManager.UpdateChat(currentChat)
	}()
}

// fixAnswerForSummary 修复答案，将代码块替换为文件路径
func fixAnswerForSummary(answer string) string {
	defer func() {
		if r := recover(); r != nil {
			// 获取当前堆栈信息
			stack := make([]byte, 4096)
			stack = stack[:runtime.Stack(stack, false)]
			log.Warnf("recover from crash. err: %+v, stack: %s", r, stack)
		}
	}()

	// 使用正则表达式匹配并提取文件路径
	result := wrappedCodeBlockRegex.ReplaceAllStringFunc(answer, func(match string) string {
		submatches := wrappedCodeBlockRegex.FindStringSubmatch(match)
		if len(submatches) > 1 {
			filePath := submatches[1]
			return filePath
		}
		return match // 如果没有找到匹配项，返回原始字符串
	})
	return result
}

func buildChatExtraParams(params *definition.AskParams) definition.ChatAskExtraParams {
	status := filter.GetFilterStatus(params.RequestId)
	isFiltered := status == filter.StatusFiltered
	isBlocked := status == filter.StatusBlocked

	enabledKnowledgeRag, ok := params.Extra[definition.RAGReportKeyEnableKnowledgeRag].(bool)
	if !ok {
		enabledKnowledgeRag = false
	}

	enabledWorkspaceRag, ok := params.Extra[definition.RAGReportKeyEnableWorkspaceRag].(bool)
	if !ok {
		enabledWorkspaceRag = false
	}

	extraParams := definition.ChatAskExtraParams{
		IsFiltered:          isFiltered,
		IsBlocked:           isBlocked,
		FilterStatus:        status,
		EnableKnowledgeRag:  enabledKnowledgeRag,
		EnableWorkspaceRag:  enabledWorkspaceRag,
		AcceptChunkIds:      nil,
		IsEnableProjectRule: params.PluginPayloadConfig.IsEnableProjectRule,
	}
	if parsedUserQuery, ok := params.AttachedInputs[common.KeyParsedUserInputQueryWithContexts]; ok {
		extraParams.ParsedUserQuery = parsedUserQuery.(string)
	}
	if contextProviderExtras, ok := params.AttachedInputs[common.KeyContextProviderExtra].([]definition.CustomContextProviderExtra); ok {
		imageUrls := chatUtil.FindImageUrlsFromContext(contextProviderExtras)
		if len(imageUrls) > 0 {
			extraParams.ImageUrls = imageUrls
		}
	}

	//设置识别的意图
	if intentDetectResult := params.AttachedInputs[common.KeyAIDeveloperIntentDetectResult]; intentDetectResult != nil {
		t := intentDetectResult.(common.AIDeveloperIntentDetectionResult)
		extraParams.IntentionType = t.Intent
	} else if intentDetectResult = params.AttachedInputs[common.KeyUIToCodeIntentDetectResult]; intentDetectResult != nil {
		if intentDetectResult.(bool) {
			extraParams.IntentionType = common.KeyAIDeveloperIntentUI2FeCode
		}
	}

	if intentDetectResult := params.AttachedInputs[common.KeyCommonAssistantIntentDetectResult]; intentDetectResult != nil {
		t := intentDetectResult.(common.CommonAgentIntentDetectionResult)
		extraParams.CommonAgentName = t.AgentName
		// 如果新链路未设置AIDeveloperIntentDetectionResult意图识别结果，在此兼容
		if extraParams.IntentionType == "" {
			extraParams.IntentionType = t.Intent
		}
	}
	return extraParams
}

// buildFeUIParams 构建前端依赖相关的参数
func buildFeUIParams(fileIndexer *indexing.ProjectFileIndex) prompt.BaseUIToCodeDepInput {
	var dependencyItems []string
	input := prompt.BaseUIToCodeDepInput{
		DependencyItems:      dependencyItems,
		IsModernFeWorkspace:  "false",
		JsFramework:          "react 或者 vue",
		UIComponentFramework: "antd 或者 element-plus 或者 vant",
		UIScaffold:           "@umijs/max 或者 @vue/cli",
	}
	if fileIndexer != nil {
		dependIndexer, ok := fileIndexer.GetDependStatFileIndexer()
		if ok && dependIndexer != nil {
			dependencyItems = chain2.TruncateStringSlice(dependIndexer.WorkspaceLabels.GetDependenciesString(), common.WorkspaceDependenciesLimit)
			if dependencyItems == nil {
				dependencyItems = []string{}
			}
		}
	}
	input.DependencyItems = dependencyItems
	if len(dependencyItems) > 0 {
		count := len(dependencyItems)
		lines := []string{}
		for i, item := range dependencyItems {
			text := "    \"" + item + "\""
			if i < count-1 {
				text = text + ","
			}
			lines = append(lines, text)
		}
		input.DependencyItemsString = strings.Join(lines, "\n")
	}

	if len(dependencyItems) > 0 {
		// dependencyItems中有没有react或者vue
		if lo.ContainsBy(dependencyItems, func(item string) bool {
			return strings.Contains(item, "react")
		}) {
			input.IsModernFeWorkspace = "true"
			input.JsFramework = "react"
			input.UIScaffold = "@umijs/max"
			input.WorkspaceLanguages = definition.JavaScript + "，jsx，" + definition.TypeScript + "，tsx"
			input.UIComponentFramework = "antd"
			if lo.ContainsBy(dependencyItems, func(item string) bool {
				return strings.Contains(item, "antd==5")
			}) {
				input.UIComponentFramework = "antd5"
			}
		} else if lo.ContainsBy(dependencyItems, func(item string) bool {
			return strings.Contains(item, "vue")
		}) {
			input.IsModernFeWorkspace = "true"
			input.JsFramework = "vue"
			input.UIScaffold = "@vue/cli"
			input.WorkspaceLanguages = definition.JavaScript + "，" + definition.TypeScript + "，" + definition.Vue
			input.UIComponentFramework = "vant 或者 element-ui"

			if lo.ContainsBy(dependencyItems, func(item string) bool {
				return strings.Contains(item, "vant")
			}) {
				input.UIComponentFramework = "vant"
			} else if lo.ContainsBy(dependencyItems, func(item string) bool {
				return strings.Contains(item, "element-ui")
			}) {
				input.UIComponentFramework = "element-ui"
			}
		}
	}

	if fileIndexer != nil {
		var workspaceLanguages []string
		langStatIndexer, ok := fileIndexer.GetLangStatFileIndexer()
		if ok && langStatIndexer != nil {
			workspaceLanguages = langStatIndexer.GetMostLanguages(0.2)
		}
		if len(workspaceLanguages) > 0 {
			if lo.ContainsBy(workspaceLanguages, func(item string) bool {
				return item == definition.Vue
			}) {
				// 有ts，走ts，没有的话 包含js
				if lo.ContainsBy(workspaceLanguages, func(item string) bool {
					return item == definition.TypeScript
				}) {
					input.WorkspaceLanguages = definition.TypeScript + "，" + definition.Vue
				} else {
					input.WorkspaceLanguages = definition.JavaScript + "，" + definition.Vue
				}

				input.IsModernFeWorkspace = "true"
				input.JsFramework = "vue"
				input.UIScaffold = "@vue/cli"
				if len(input.UIComponentFramework) == 0 {
					input.UIComponentFramework = "vant 或者 element-ui"
				}
			} else {
				if lo.ContainsBy(workspaceLanguages, func(item string) bool {
					return item == definition.TypeScript
				}) {
					input.IsModernFeWorkspace = "true"
					input.WorkspaceLanguages = definition.TypeScript + "，tsx"
				} else if lo.ContainsBy(workspaceLanguages, func(item string) bool {
					return item == definition.JavaScript
				}) {
					input.IsModernFeWorkspace = "true"
					input.WorkspaceLanguages = definition.JavaScript + "，jsx"
				}
			}
		}
	}

	// 兜底
	if len(input.WorkspaceLanguages) == 0 {
		input.IsModernFeWorkspace = "false"
		input.WorkspaceLanguages = definition.TypeScript + "，tsx 或" + definition.Vue
	}

	return input
}

func (c *ChatAnswerBufferHolder) finishChatThink(ctx context.Context, requestId string, chatThink *definition.ChatThink, reasoningText string) {
	//验证chat answer是否存在
	chatAnswer := c.bufferMap[requestId]
	if chatAnswer == nil {
		return
	}

	if chatAnswer.RequestId != "" && chatAnswer.SessionId != "" {
		var updateChatRecord = definition.ChatRecord{
			RequestId:        chatAnswer.RequestId,
			SessionId:        chatAnswer.SessionId,
			ReasoningContent: reasoningText,
		}
		service.SessionServiceManager.UpdateChatReasoningContent(updateChatRecord)
	}
}

// 自由问答任务，重新路由agent、TaskId
func routeTaskIdForAIChatFreeInputTask(params *definition.AskParams, chatAsk *definition.RemoteChatAsk, extraParams definition.ChatAskExtraParams) string {
	isVlChat := len(extraParams.ImageUrls) > 0

	if chatAsk.Mode == "" {
		//老版插件，只处理自由问答
		if isVlChat {
			return definition.AgentTaskVlChat
		}
		coderIntentDetectResult := chain2.GetBooleanValue(params.AttachedInputs, common.KeyCoderIntentDetectResult, false)
		if coderIntentDetectResult {
			return definition.AgentTaskChatCoder
		}
		return definition.AgentTaskCommonChat
	}
	if chatAsk.Mode == definition.SessionModeChat {
		if isVlChat {
			return definition.AgentTaskVlChat
		}
		coderIntentDetectResult := chain2.GetBooleanValue(params.AttachedInputs, common.KeyCoderIntentDetectResult, false)
		if coderIntentDetectResult {
			return definition.AgentTaskChatCoder
		}
		return definition.AgentTaskCommonChat
	}
	if chatAsk.Mode == definition.SessionModeEdit {
		if isVlChat {
			return definition.AgentTaskVlChat
		}
		return definition.AgentTaskCommonChat
	}
	// mode是agent也可能走到通用问答，因此也需要判断多模态
	if chatAsk.Mode == definition.SessionModeAgent {
		if isVlChat {
			return definition.AgentTaskVlChat
		}
		return definition.AgentTaskCommonChat
	}
	//意图识别到了走历史的问答
	return definition.AgentTaskCommonChat
}

func appendUserRulesForChat(ctx context.Context, projectRuleContext *rule.ProjectRuleContext, systemPrompt string) string {
	result := make([]*rule.ProjectRule, 0)
	if projectRuleContext == nil {
		return systemPrompt
	}
	// 普通问答场景，只传入always_on rule和用户选择的rule，以及明确match glob属性的rule
	if len(projectRuleContext.AllAlwaysRules) > 0 {
		result = append(result, projectRuleContext.AllAlwaysRules...)
	}
	if len(projectRuleContext.UserManualSelectedRules) > 0 {
		result = append(result, projectRuleContext.UserManualSelectedRules...)
	}
	if len(projectRuleContext.GlobMatchedRules) > 0 {
		result = append(result, projectRuleContext.GlobMatchedRules...)
	}
	if len(result) > 0 {
		promptInput := prompt.MultiRulesWithoutToolsPromptInput{
			UserRules: result,
		}
		rulesPrompt, err := prompt.Engine.RenderMultiRulesWithoutToolsSystemPrompt(promptInput)
		if err != nil {
			log.Errorf("render multi rules prompt failed, err: %s", err)
		} else {
			systemPrompt = systemPrompt + "\n" + rulesPrompt
		}
	}
	return systemPrompt
}
