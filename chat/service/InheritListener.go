package service

import (
	"cosy/definition"
	"cosy/log"
	"cosy/sls"
	"cosy/tree"
	"os"
	"strconv"
)

type EventTrackerListener struct{}

func (listener EventTrackerListener) SupportedTypes() []string {
	return []string{
		SYNC_WORKING_SPACE_FILE,
	}
}

func (listener EventTrackerListener) HandleEvent(event Event) {
	if event.Type == SYNC_WORKING_SPACE_FILE {
		result := event.Data.(definition.WorkingSpaceFileSyncResult)
		snapshot, err := WorkingSpaceServiceManager.GetSnapshot(result.WorkingSpaceFile.SnapshotId)
		if err != nil {
			log.Errorf("Event Track Error: Failed to get snapshot, id: %s", result.WorkingSpaceFile.SnapshotId)
		}
		oldStatus, ok := WorkingSpaceFileStatusMap.Load(result.WorkingSpaceFile.Id)
		newStatus := result.WorkingSpaceFile.Status
		// 状态发生变更，说明触发操作事件
		if (!ok && newStatus == GENERATING.String()) || oldStatus != newStatus {
			WorkingSpaceFileStatusMap.Store(result.WorkingSpaceFile.Id, newStatus)
			// 触发埋点
			// 计算操作类型
			op_type := ""
			op_result := "true"
			switch newStatus {
			case GENERATING.String():
				op_type = GENERATE.String()
			case APPLIED.String():
				op_type = APPLY.String()
			case ACCEPTED.String():
				op_type = ACCEPT.String()
				WorkingSpaceFileStatusMap.Delete(result.WorkingSpaceFile.Id)
			case REJECTED.String():
				op_type = REJECT.String()
				WorkingSpaceFileStatusMap.Delete(result.WorkingSpaceFile.Id)
				workspaceTree := tree.NewWorkspaceMerkleTree(result.ProjectPath)
				if workspaceTree == nil {
					log.Errorf("Event Track Error: Failed to get workspace tree, projectPath: %s", result.ProjectPath)
				} else {
					if stat, err := os.Stat(result.WorkingSpaceFile.FileId); err != nil || stat == nil {
						// 文件不存在，以删除触发事件
						log.Debugf("Event Track Error: File not exist, filePath: %s", result.WorkingSpaceFile.FileId)
						workspaceTree.DeleteFiles([]string{result.WorkingSpaceFile.FileId}, false)
					} else {
						log.Debugf("Event Track Error: File exist, filePath: %s", result.WorkingSpaceFile.FileId)
						if !stat.IsDir() {
							workspaceTree.IndexFiles([]string{result.WorkingSpaceFile.FileId})
						}
					}
				}
			case GENERATING_CANCELLED.String(), GENERATING_FAILED.String():
				op_type = GENERATE.String()
				op_result = "false"
				WorkingSpaceFileStatusMap.Delete(result.WorkingSpaceFile.Id)
			case APPLYING_CANCELLED.String(), APPLYING_FAILED.String():
				op_type = APPLY.String()
				op_result = "false"
				WorkingSpaceFileStatusMap.Delete(result.WorkingSpaceFile.Id)
			}
			// 非失败状态下，清空消息表
			if failedStatusMap[newStatus] != 1 {
				if _, exist := WorkingSpaceFileMessageMap[result.WorkingSpaceFile.Id]; exist {
					delete(WorkingSpaceFileMessageMap, result.WorkingSpaceFile.Id)
				}
			}
			// 仅处理特定的状态变更事件
			if op_type != "" {
				data := map[string]string{
					"session_id":     snapshot.SessionId,
					"chat_record_id": snapshot.ChatRecordId,
					"file_path":      result.WorkingSpaceFile.FileId,
					"op_type":        op_type,
					"result":         op_result,
					"add_lines":      strconv.Itoa(result.WorkingSpaceFile.DiffInfo.Add),
					"del_lines":      strconv.Itoa(result.WorkingSpaceFile.DiffInfo.Delete),
					"add_chars":      strconv.Itoa(result.WorkingSpaceFile.DiffInfo.AddChars),
					"del_chars":      strconv.Itoa(result.WorkingSpaceFile.DiffInfo.DelChars),
					"chat_mode":      result.WorkingSpaceFile.ChatMode,
				}
				go sls.Report(sls.EventTypeChatAiDeveloperWorkspaceFileOperation, snapshot.ChatRecordId, data)
			}
		}
	}
}
