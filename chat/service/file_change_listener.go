package service

import (
	"context"
	"cosy/config"
	"cosy/definition"
	"cosy/log"
	"strings"
)

type RemoteAgentFileChangeListener struct {
	reportServerFunc func(ctx context.Context, fileVO *definition.WorkingSpaceFileVO)
	notifyIDEFunc    func(ctx context.Context, fileVO *definition.WorkingSpaceFileVO)
}

func NewRemoteAgentFileChangeListener(
	reportServerFunc func(ctx context.Context, fileVO *definition.WorkingSpaceFileVO),
	notifyIDEFunc func(ctx context.Context, fileVO *definition.WorkingSpaceFileVO)) Listener {

	return &RemoteAgentFileChangeListener{
		reportServerFunc: reportServerFunc,
		notifyIDEFunc:    notifyIDEFunc,
	}
}

func (l *RemoteAgentFileChangeListener) SupportedTypes() []string {
	return []string{
		SYNC_WORKING_SPACE_FILE,
	}
}

func (l *RemoteAgentFileChangeListener) HandleEvent(event Event) {
	if event.Type == SYNC_WORKING_SPACE_FILE {
		l.handleSyncWorkingSpaceFile(event)
	}
}

func (l *RemoteAgentFileChangeListener) handleSyncWorkingSpaceFile(event Event) {
	ctx := context.Background()
	result := event.Data.(definition.WorkingSpaceFileSyncResult)
	fileId := result.WorkingSpaceFile.FileId
	sessionId := result.WorkingSpaceFile.SessionId
	fileDiff, err := WorkingSpaceServiceManager.GetFileChangeBySessionId(ctx, sessionId, fileId)
	if err != nil {
		log.Errorf("GetFileChangeBySessionId, sessionId: %s, fileId: %s, error:%v", sessionId, fileId, err)
		return
	}
	if strings.HasPrefix(fileDiff.FileId, config.GetRemoteAgentWorkspacePath()) {
		// 将沙箱里的绝对路径转换成相对路径
		fileDiff.FileId = strings.Replace(fileDiff.FileId, config.GetRemoteAgentWorkspacePath(), ".", 1)
	}
	// 服务端只上报APPLIED状态
	if result.WorkingSpaceFile.Status == APPLIED.String() {
		if l.reportServerFunc != nil {
			l.reportServerFunc(ctx, fileDiff)
			log.Infof("sync working space file, sessionId: %s, event: %v", sessionId, event)
		}
	}
	// IDE端接受所有状态情况
	if l.notifyIDEFunc != nil {
		fileDiff.Status = result.WorkingSpaceFile.Status
		l.notifyIDEFunc(ctx, fileDiff)
	}
}
