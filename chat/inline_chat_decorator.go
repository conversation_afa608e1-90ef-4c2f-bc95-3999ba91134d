package chat

import (
	"cosy/chat/agents/support"
	"cosy/chat/service"
	"cosy/definition"
	"cosy/log"
	"cosy/util"
	"encoding/json"
	"github.com/sergi/go-diff/diffmatchpatch"
	"strings"
)

type InlineEditOutputDecorator struct {
	fullFileEdit        bool
	fileCode            string
	selectedCode        string
	startSymbol         string
	endSymbol           string
	fullOutput          string
	validOutput         string
	lastOutput          string
	reasoningText       string
	lastReasoningText   string
	modifiedFileContent string
	requestId           string
	finished            bool
	closeTypeWriter     bool
	textSmoother        *support.TextSmoother
	validFormat         bool
}

func (c *InlineEditOutputDecorator) GetOutputText() string {
	return c.fullOutput
}

func (c *InlineEditOutputDecorator) GetAppendText() string {
	if c.validOutput == "" {
		if c.finished && !c.validFormat {
			// 如果会话结束，但是模型返回了非法格式的内容，那么兜底返回文件内容或选中的代码
			if c.fullFileEdit {
				return c.modifiedFileContent
			} else {
				return c.selectedCode
			}
		}
		return ""
	}
	appendText := c.validOutput
	appendText = strings.TrimPrefix(c.validOutput, c.lastOutput)
	c.lastOutput = c.validOutput
	if c.closeTypeWriter && c.fullFileEdit {
		if appendText != "" {
			c.textSmoother.UpdateText(appendText)
		}
		appendText = c.textSmoother.GetNextOutput()
		if c.finished && c.textSmoother.HasRemaining() {
			// 最后一把输出
			appendText = appendText + c.textSmoother.GetAndClearRemainingContent()
		}
	}
	return appendText
}

func (c *InlineEditOutputDecorator) UpdateOutputText(output string, finish bool) {
	if c.fullFileEdit {
		c.fullOutput = output
		lastSearchBlockStr, _ := findLastSearchBlock(output, false)
		modifiedContent, _, err, _ := service.GetApplyFullContentBySearchReplace(c.fileCode, output, "", c.requestId, true)
		if err != nil {
			// 尝试修复下解决方案，主要修复下search块中的空行，有时候qwen3会给空行加上缩进
			fixedLastSearchBlockStr, fixedOutput := findLastSearchBlock(output, true)
			// 再次尝试应用解决方案
			modifiedContent, _, err, _ = service.GetApplyFullContentBySearchReplace(c.fileCode, fixedOutput, "", c.requestId, true)
			lastSearchBlockStr = fixedLastSearchBlockStr
		}
		if finish {
			c.modifiedFileContent = modifiedContent
			c.finished = true
		}
		sendModifiedText := ""
		if err != nil {
			sendModifiedText = getContentToLastSearchBlock(lastSearchBlockStr, c.fileCode)
		} else {
			// apply成功则代表是合法格式
			c.validFormat = true
			sendModifiedText = getSendModifiedText(c.fileCode, modifiedContent, finish)
			if lastSearchBlockStr != "" {
				// 找出fileCode到lastSearchBlockStr之间的内容
				contentToLastSearch := getContentToLastSearchBlock(lastSearchBlockStr, c.fileCode)
				// 对比sendModifiedText和这个内容，如果contentToLastSearch包含sendModifiedText的所有内容，且比它多
				if strings.Contains(contentToLastSearch, sendModifiedText) && len(contentToLastSearch) > len(sendModifiedText) {
					// 将sendModifiedText更新为这个更完整的内容
					sendModifiedText = contentToLastSearch
				}
			}
		}
		lastOutput := c.lastOutput
		if lastOutput == "" {
			c.validOutput = sendModifiedText
		} else {
			// 判断是不是增量的输出
			if strings.HasPrefix(sendModifiedText, lastOutput) {
				c.validOutput = sendModifiedText
			} else {
				log.Debugf("output text is not incremental. sendModifiedText is:%s, lastOutput is:%s", sendModifiedText, lastOutput)
				return
			}
		}
	} else {
		lines := strings.Split(output, lineSeparator)
		validLines := make([]string, 0)
		meetStartSymbol := false
		for index, line := range lines {
			// 最后一行可能不完整，对于停止注释的判断可能不准，先不返回给前端
			if index >= len(lines)-1 {
				break
			}
			trimLine := strings.TrimSpace(line)
			if strings.HasPrefix(trimLine, c.startSymbol) {
				// 遇到开始的注释，下一行开始输出
				meetStartSymbol = true
				continue
			}
			if strings.HasPrefix(trimLine, c.endSymbol) {
				// 遇到停止的注释，就跳出循环
				c.validFormat = true
				break
			}
			if meetStartSymbol {
				validLines = append(validLines, line)
			}
		}
		if validLines != nil && len(validLines) > 0 {
			c.validOutput = strings.Join(validLines, lineSeparator)
		}
		if finish {
			c.finished = true
		}
		c.fullOutput = output
	}
}

func getContentToLastSearchBlock(lastSearchBlockStr, fileCode string) string {
	// 找出fileCode到lastSearchBlockStr之间的内容
	lastSearchIndex := strings.Index(fileCode, lastSearchBlockStr)
	if lastSearchIndex != -1 {
		// 获取从文件开头到lastSearchBlockStr开始位置的内容（不包含lastSearchBlockStr）
		contentToLastSearch := fileCode[:lastSearchIndex]
		return contentToLastSearch
	}
	return ""
}

func findLastSearchBlock(output string, fixLeading bool) (string, string) {
	outputLines := strings.Split(output, lineSeparator)
	enterSearchBlock := false
	var searchStrLines []string
	lastSearchBlock := ""
	for index, line := range outputLines {
		if strings.HasPrefix(line, "<<<<<<< SEARCH") {
			enterSearchBlock = true
			searchStrLines = make([]string, 0)
			continue
		}
		if strings.TrimSpace(line) == "=======" {
			enterSearchBlock = false
			lastSearchBlock = strings.Join(searchStrLines, lineSeparator)
			continue
		}
		if enterSearchBlock {
			if fixLeading {
				// 判断是否是空行
				if strings.TrimSpace(line) == "" && line != "" {
					outputLines[index] = ""
					line = ""
				}
			}
			searchStrLines = append(searchStrLines, line)
		}
	}
	if fixLeading {
		output = strings.Join(outputLines, lineSeparator)
	}
	return lastSearchBlock, output
}

func getHistoryUserParsedQuery(record definition.ChatRecord) string {
	var chatContextMap map[string]interface{}
	err := json.Unmarshal([]byte(record.ChatContext), &chatContextMap)
	if err == nil {
		questionText, ok := chatContextMap["parsedUserQueryWithoutCode"].(string)
		if ok {
			if util.IsSystemCommandTask(record.ChatTask) {
				questionText = getSystemTaskQuestionText(record.ChatTask, questionText)
			}
			return questionText
		}
	}
	return record.Question
}

func (c *InlineEditOutputDecorator) Finish() {
}

func (c *InlineEditOutputDecorator) GetFullAnswer() string {
	return c.modifiedFileContent
}

func (c *InlineEditOutputDecorator) GetAppendReasoningText() string {
	appendText := strings.TrimPrefix(c.reasoningText, c.lastReasoningText)
	c.lastReasoningText = c.reasoningText
	return appendText
}

func (c *InlineEditOutputDecorator) UpdateReasoningText(reasoning string) {
	c.reasoningText = reasoning
}

func (c *InlineEditOutputDecorator) GetReasoningText() string {
	return c.reasoningText
}

// DiffBlock 用于记录一次变化的位置
type DiffBlock struct {
	StartLine1 int    `json:"startLine1"` // 原文变化起始行（A代表原文）
	EndLine1   int    `json:"endLine1"`   // 原文变化结束行
	StartLine2 int    `json:"startLine2"` // 修改后文本变化起始行（B代表新版本）
	EndLine2   int    `json:"endLine2"`   // 修改后文本变化结束行
	DiffType   string `json:"diffType"`   // 变化类型，可选 "insert"、"delete" 、 "equal"、"replace"
	InsertText string `json:"insertText"` // 新增的文本
}

func getSendModifiedText(originalText, modifiedText string, finish bool) string {
	dmp := diffmatchpatch.New()
	diffs := dmp.DiffMain(originalText, modifiedText, false)
	sendModifiedText := strings.Builder{}
	equalTextBuffer := strings.Builder{}
	for i := 0; i < len(diffs); i++ {
		diff := diffs[i]
		switch diff.Type {
		case diffmatchpatch.DiffEqual:
			// 先缓存所有相等的行
			equalTextBuffer.WriteString(diff.Text)
			break
		case diffmatchpatch.DiffInsert, diffmatchpatch.DiffDelete:
			// 当遇到变更的时候（不管是删除还是插入），先把缓存的相等的行追加在前面，再追加变更的行
			// 清空缓存的相等行，等到下一个变更的时候再追加
			sendModifiedText.WriteString(equalTextBuffer.String())
			equalTextBuffer.Reset()
			if diff.Type == diffmatchpatch.DiffInsert {
				sendModifiedText.WriteString(diff.Text)
			}
			break
		}
	}
	if finish {
		// 停止的时候，将缓存的相等行追加到最后
		sendModifiedText.WriteString(equalTextBuffer.String())
	}
	result := sendModifiedText.String()
	if len(result) < len(modifiedText) && !strings.HasSuffix(result, lineSeparator) {
		// 如果result不是全文，也不以换行符结尾，说明这一行没输出完整，把equalLines的第一行加上
		equalText := equalTextBuffer.String()
		equalLines := strings.Split(equalText, lineSeparator)
		if len(equalLines) > 0 {
			result += equalLines[0]
		}
	}
	return result
}
