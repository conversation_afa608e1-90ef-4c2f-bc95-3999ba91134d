package strategy

import (
	"cosy/chat/agents/coder/common"
	"cosy/chat/task"
	"cosy/definition"
	"cosy/errors"
	"encoding/json"
)

type CommonAgentTaskReplyStrategy struct {
}

func (s *CommonAgentTaskReplyStrategy) GetReplyTasks(firstRecord definition.ChatRecord, lastRecord definition.ChatRecord) []definition.DisplayTask {
	displayTasks := make([]definition.DisplayTask, 0)
	status := &common.AgentFinishStatus{}
	if lastRecord.ErrorResult == "" {
		displayTasks = append(displayTasks, task.REGENERATE_TASK)
		//for test
		//displayTasks = append(displayTasks, definition.DisplayTask{
		//	ChatTask:      definition.CONTINUE_TASK,
		//	DisplayText:   "继续",
		//	DisplayEnText: "Continue",
		//	Prompt:        "Continue",
		//})
		return displayTasks
	}
	err := json.Unmarshal([]byte(lastRecord.ErrorResult), status)
	if err != nil {
		displayTasks = append(displayTasks, task.REGENERATE_TASK)
		return displayTasks
	}
	if status.FinishCode == errors.ToolCallOverLimit || status.FinishCode == definition.ChatFinishStatusCodeTimeout || status.FinishCode == errors.ModelResponseTimeout {
		// 这两种情况，进行agent continue的任务
		displayTasks = append(displayTasks, definition.DisplayTask{
			ChatTask:      definition.CONTINUE_TASK,
			DisplayText:   "继续",
			DisplayEnText: "Continue",
			Prompt:        "Continue",
		})
		if status.FinishCode == errors.ModelResponseTimeout {
			displayTasks = append(displayTasks, definition.DisplayTask{
				ChatTask:      definition.CONTINUE_TASK,
				DisplayText:   "重试",
				DisplayEnText: "Retry",
				Prompt:        "Timeout Occurred. Please Retry",
			})
		}
		return displayTasks
	} else {
		displayTasks = append(displayTasks, task.REGENERATE_TASK)
		return displayTasks
	}
}
