package interfaces

import (
	"code.alibaba-inc.com/cosy/lingma-agent-graph/tool"
)

// ResearchAgentToolConfig 定义research agent工具的配置接口
type ResearchAgentToolConfig interface {
	GetExplanationDesc() string
}

// ResearchAgentToolFactory 定义research agent工具工厂接口
type ResearchAgentToolFactory interface {
	CreateResearchAgentTool(config ResearchAgentToolConfig) (tool.BaseTool, error)
}

// DefaultResearchAgentToolConfig 默认的research agent工具配置实现
type DefaultResearchAgentToolConfig struct {
	ExplanationDesc string
}

func (c *DefaultResearchAgentToolConfig) GetExplanationDesc() string {
	return c.ExplanationDesc
}


// Context key for research agent tool factory
const ResearchAgentToolFactoryKey = "research_agent_tool_factory"
