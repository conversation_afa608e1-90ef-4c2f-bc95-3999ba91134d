package web

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/tool"
	"cosy/chat/agents/tool/common"
	"cosy/client"
	"cosy/errors"
	"cosy/log"
	"cosy/util"

	"github.com/<PERSON>mann/html-to-markdown"
	"github.com/PuerkitoBio/goquery"
)

const FetchContentToolName = "fetch_content"

type FetchContentConfig struct {
	MaxContentLength int    // 返回结果的最大数量
	ExplanationDesc  string // explanation字段的描述
}

type FetchContentRequest struct {
	URL   string `json:"url"`
	QUERY string `json:"query"`
}

type FetchContentResponse struct {
	URL       string `json:"url"`
	Content   string `json:"content"`
	Truncated bool   `json:"truncated"`
}

func NewFetchContentTool(config *FetchContentConfig) (tool.InvokableTool, error) {
	maxContentLength := 5000
	if config != nil && config.MaxContentLength > 0 {
		maxContentLength = config.MaxContentLength
	}
	explanationDesc := common.ExplanationDefaultDesc
	if config != nil && config.ExplanationDesc != "" {
		explanationDesc = config.ExplanationDesc
	}
	toolName := FetchContentToolName
	toolDesc := `Fetches the main content from a web page.The Web page must be an HTTP or HTTPS URL that points to a valid internet resource accessible via web browser. This tool is useful for summarizing or analyzing the content of a webpage. You should use this tool when you think the user is looking for information from a specific webpage.
`
	toolDesc = fmt.Sprintf(toolDesc, maxContentLength)
	toolParams := &definition.Schema{
		Type: "object",
		Properties: map[string]*definition.Schema{
			"url": {
				Type:        "string",
				Description: "URL to read content from. Starts with `http://` or `https://`",
			},
			"query": {
				Type:        "string",
				Description: "The query to search for in the web page's content. This should be a clear and concise description of the content you want to find.",
			},
			"explanation": {
				Type:        "string",
				Description: explanationDesc,
			},
		},
		Required: []string{"url"},
	}
	toolInfo := &definition.ToolInfo{
		Name:        toolName,
		Description: toolDesc,
		Parameters:  toolParams,
		ReadOnly:    true,
	}
	fetchContentTool := &fetchContentTool{
		client:           client.GetFetchContentClient(), // 使用统一管理的client
		maxContentLength: maxContentLength,
	}
	return tool.NewInvokableTool(toolInfo, fetchContentTool.readURL, tool.WithOutputConverter(fetchContentTool.convertOutput)), nil
}

type fetchContentTool struct {
	client           *http.Client
	maxContentLength int
	requestId        string
}

func (t *fetchContentTool) readURL(ctx context.Context, request *FetchContentRequest) (*FetchContentResponse, error) {
	// 参数校验
	if request == nil || request.URL == "" {
		return nil, errors.New(errors.ToolInvalidArguments, "url cannot be empty")
	}
	// 确保 client 已经初始化
	if t.client == nil {
		return nil, errors.New(errors.ToolInternalError, "HTTP client is not initialized")
	}

	// 发送HTTP GET请求
	resp, err := t.client.Get(request.URL)
	if err != nil {
		log.Error(err)
		if util.IsValidURL(request.URL) {
			//url格式正确则错误码设置为WebToolStatusError
			return nil, errors.New(errors.WebToolStatusError, "failed to fetch URL: "+err.Error())
		}
		return nil, errors.New(errors.ToolInternalError, "failed to fetch URL: "+err.Error())
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		log.Error(`HTTP request failed with status code:` + strconv.Itoa(resp.StatusCode))
		return nil, errors.New(errors.WebToolStatusError, strconv.Itoa(resp.StatusCode))
	}

	// 获取 Content-Type 头信息
	contentType := resp.Header.Get("Content-Type")

	var content string
	// 根据 Content-Type 类型选择合适的解析方式
	if strings.Contains(contentType, "text/html") {
		// 使用 goquery 解析 HTML 内容
		content, err = t.html2markdown(resp.Body)
		if err != nil {
			log.Error(err)
			return nil, errors.New(errors.ToolInternalError, "failed to read response: "+err.Error())
		}
	} else {
		// 直接读取纯文本内容
		bodyBytes, err := io.ReadAll(resp.Body)
		if err != nil {
			log.Error(err)
			return nil, errors.New(errors.ToolInternalError, "failed to read plain text response: "+err.Error())
		}

		content = string(bodyBytes)
	}

	// 如果提供了查询参数，搜索相关内容
	// query生成的质量不太好，先注释掉
	//if len(content) > t.maxContentLength && request.QUERY != "" {
	//	searchedContent := t.searchContent(content, request.QUERY)
	//	// 如果搜索结果为空，则取前 maxContentLength
	//	if searchedContent != "" {
	//		log.Infof("readURL truncated: len=%d, maxContentLength=%d, query=%s", len(content), t.maxContentLength, request.QUERY)
	//		content = searchedContent
	//	}
	//}

	truncated := false
	if len(content) > t.maxContentLength {
		truncated = true
		content = content[:t.maxContentLength]
	}

	// Create response with request data
	response := &FetchContentResponse{
		URL:       request.URL,
		Content:   content,
		Truncated: truncated,
	}
	return response, nil
}

// html2markdown 将HTML内容转换为markdown格式
func (t *fetchContentTool) html2markdown(reader io.Reader) (string, error) {
	doc, err := goquery.NewDocumentFromReader(reader)
	if err != nil {
		log.Error(err)
		return "", fmt.Errorf("failed to parse HTML: " + err.Error())
	}

	var content strings.Builder

	// 获取标题
	title := doc.Find("title").Text()
	if title != "" {
		content.WriteString(fmt.Sprintf("# %s\n\n", strings.TrimSpace(title)))
	}

	// 移除不需要的元素
	doc.Find("script, style, nav, header, footer, aside, sidebar, .navigation, .ads, #ads, .advertisement").Remove()

	// 提取文章内容
	// 尝试使用自定义选择器找到主要内容
	var contentElement *goquery.Selection
	for _, selector := range []string{"article", ".content", "#content", ".main", "main", ".post", ".post-content", ".article-content", ".entry", ".entry-content"} {
		if element := doc.Find(selector).First(); element.Length() > 0 {
			contentElement = element
			break
		}
	}
	// 如果没有找到特定的内容区域，使用body
	if contentElement == nil {
		contentElement = doc.Find("body")
	}

	// 处理内容
	t.processElementAdvanced(contentElement, &content, 0)

	// 清理内容
	result := t.cleanMarkdownAdvanced(content.String())

	if len(result) == 0 {
		return doc.Text(), nil
	}

	return result, nil
}

// processElementAdvanced 高级元素处理
func (t *fetchContentTool) processElementAdvanced(selection *goquery.Selection, markdown *strings.Builder, depth int) {
	selection.Contents().Each(func(i int, s *goquery.Selection) {
		if goquery.NodeName(s) == "#text" {
			text := strings.TrimSpace(s.Text())
			if text != "" {
				markdown.WriteString(text)
			}
		} else {
			tagName := goquery.NodeName(s)

			switch tagName {
			case "h1":
				markdown.WriteString(fmt.Sprintf("\n# %s\n\n", strings.TrimSpace(s.Text())))
			case "h2":
				markdown.WriteString(fmt.Sprintf("\n## %s\n\n", strings.TrimSpace(s.Text())))
			case "h3":
				markdown.WriteString(fmt.Sprintf("\n### %s\n\n", strings.TrimSpace(s.Text())))
			case "h4":
				markdown.WriteString(fmt.Sprintf("\n#### %s\n\n", strings.TrimSpace(s.Text())))
			case "h5":
				markdown.WriteString(fmt.Sprintf("\n##### %s\n\n", strings.TrimSpace(s.Text())))
			case "h6":
				markdown.WriteString(fmt.Sprintf("\n###### %s\n\n", strings.TrimSpace(s.Text())))
			case "p":
				text := strings.TrimSpace(s.Text())
				if text != "" {
					score := t.calculateInformationDensity(s)
					if score <= 0.05 {
						log.Warnf("fetch_content processElementAdvanced: low information density, skip. text=" + s.Text() + ", score=" + strconv.FormatFloat(score, 'f', 2, 64))
						return
					}
					markdown.WriteString(fmt.Sprintf("%s\n\n", text))
				}
			case "br":
				markdown.WriteString("\n")
			case "strong", "b":
				text := strings.TrimSpace(s.Text())
				if text != "" {
					markdown.WriteString(fmt.Sprintf("**%s**", text))
				}
			case "em", "i":
				text := strings.TrimSpace(s.Text())
				if text != "" {
					markdown.WriteString(fmt.Sprintf("*%s*", text))
				}
			case "code":
				text := strings.TrimSpace(s.Text())
				if text != "" {
					markdown.WriteString(fmt.Sprintf("`%s`", text))
				}
			case "pre":
				text := strings.TrimSpace(s.Text())
				if text != "" {
					// 检查是否有语言标识
					language := ""
					if class, exists := s.Attr("class"); exists {
						if strings.Contains(class, "language-") {
							parts := strings.Split(class, "language-")
							if len(parts) > 1 {
								language = strings.Fields(parts[1])[0]
							}
						}
					}
					markdown.WriteString(fmt.Sprintf("\n```%s\n%s\n```\n\n", language, text))
				}
			case "a":
				href, exists := s.Attr("href")
				text := strings.TrimSpace(s.Text())
				if exists && text != "" {
					markdown.WriteString(fmt.Sprintf("[%s](%s)", text, href))
				} else if text != "" {
					markdown.WriteString(text)
				}
			case "img":
				src, exists := s.Attr("src")
				alt, _ := s.Attr("alt")
				if exists {
					markdown.WriteString(fmt.Sprintf("![%s](%s)\n\n", alt, src))
				}
			case "ul":
				markdown.WriteString("\n")
				s.Find("li").Each(func(i int, li *goquery.Selection) {
					text := strings.TrimSpace(li.Text())
					if text != "" {
						markdown.WriteString(fmt.Sprintf("- %s\n", text))
					}
				})
				markdown.WriteString("\n")
			case "ol":
				markdown.WriteString("\n")
				s.Find("li").Each(func(i int, li *goquery.Selection) {
					text := strings.TrimSpace(li.Text())
					if text != "" {
						markdown.WriteString(fmt.Sprintf("%d. %s\n", i+1, text))
					}
				})
				markdown.WriteString("\n")
			case "blockquote":
				text := strings.TrimSpace(s.Text())
				if text != "" {
					lines := strings.Split(text, "\n")
					for _, line := range lines {
						if strings.TrimSpace(line) != "" {
							markdown.WriteString(fmt.Sprintf("> %s\n", strings.TrimSpace(line)))
						}
					}
					markdown.WriteString("\n")
				}
			case "table":
				t.processTableAdvanced(s, markdown)
			case "hr":
				markdown.WriteString("\n---\n\n")
			case "div", "span", "section":
				// 对于这些容器元素，递归处理子元素
				t.processElementAdvanced(s, markdown, depth+1)
			default:
				// 递归处理其他元素的子元素
				t.processElementAdvanced(s, markdown, depth+1)
			}
		}
	})
}

// processTableAdvanced 高级表格处理
func (t *fetchContentTool) processTableAdvanced(table *goquery.Selection, markdown *strings.Builder) {
	markdown.WriteString("\n")

	rows := table.Find("tr")
	if rows.Length() == 0 {
		return
	}

	// 处理第一行作为表头
	firstRow := rows.First()
	firstRow.Find("th, td").Each(func(i int, cell *goquery.Selection) {
		text := strings.TrimSpace(cell.Text())
		text = strings.ReplaceAll(text, "|", "\\|") // 转义管道符
		markdown.WriteString(fmt.Sprintf("| %s ", text))
	})
	markdown.WriteString("|\n")

	// 添加分隔线
	firstRow.Find("th, td").Each(func(i int, cell *goquery.Selection) {
		markdown.WriteString("| --- ")
	})
	markdown.WriteString("|\n")

	// 处理其余行
	rows.Slice(1, rows.Length()).Each(func(i int, row *goquery.Selection) {
		row.Find("td, th").Each(func(j int, cell *goquery.Selection) {
			text := strings.TrimSpace(cell.Text())
			text = strings.ReplaceAll(text, "|", "\\|") // 转义管道符
			markdown.WriteString(fmt.Sprintf("| %s ", text))
		})
		markdown.WriteString("|\n")
	})
	markdown.WriteString("\n")
}

// cleanMarkdownAdvanced 高级内容清理
func (t *fetchContentTool) cleanMarkdownAdvanced(content string) string {
	// 移除多余的空行
	lines := strings.Split(content, "\n")
	var cleanedLines []string
	var emptyLineCount int

	for _, line := range lines {
		trimmed := strings.TrimSpace(line)
		if trimmed == "" {
			emptyLineCount++
			if emptyLineCount <= 2 { // 最多保留两个连续空行
				cleanedLines = append(cleanedLines, "")
			}
		} else {
			emptyLineCount = 0
			cleanedLines = append(cleanedLines, trimmed)
		}
	}

	// 移除开头和结尾的空行
	for len(cleanedLines) > 0 && cleanedLines[0] == "" {
		cleanedLines = cleanedLines[1:]
	}
	for len(cleanedLines) > 0 && cleanedLines[len(cleanedLines)-1] == "" {
		cleanedLines = cleanedLines[:len(cleanedLines)-1]
	}

	return strings.Join(cleanedLines, "\n")
}

// calculateInformationDensity 计算元素的信息密度
func (t *fetchContentTool) calculateInformationDensity(selection *goquery.Selection) float64 {
	text := strings.TrimSpace(selection.Text())
	if len(text) == 0 {
		return 0.0
	} else if len(text) <= 200 {
		return 1.0 // 短文本无需处理
	}

	// 字符多样性
	uniqueChars := make(map[rune]bool)
	for _, char := range text {
		uniqueChars[char] = true
	}

	// 计算最终密度（字符种类除以文本长度）
	density := float64(len(uniqueChars)) / float64(len(text))

	return density
}

func (t *fetchContentTool) html2markdownV2(reader io.Reader) (string, error) {
	converter := md.NewConverter("", true, nil)
	contentBytes, err := io.ReadAll(reader)
	if err != nil {
		log.Error(err)
		return "", fmt.Errorf("failed to read HTML content: %w", err)
	}
	content, err := converter.ConvertString(string(contentBytes))
	if err != nil {
		log.Error(err)
		return "", fmt.Errorf("failed to convert HTML to markdown: %w", err)
	}
	// 清理内容
	result := t.cleanMarkdownAdvanced(content)
	log.Info("fetch_content extractMainContent html2markdownV2:", result)
	return result, nil
}

// searchContent 在内容中搜索查询相关的部分
func (t *fetchContentTool) searchContent(content, query string) string {
	if query == "" {
		return content
	}

	var result strings.Builder
	query = strings.ToLower(query)
	paragraphs := strings.Split(content, "\n")

	for _, paragraph := range paragraphs {
		if strings.Contains(strings.ToLower(paragraph), query) {
			result.WriteString(paragraph)
			result.WriteString("\n")
		}
	}
	log.Info("fetch_content searchContent:", result.String())
	return result.String()
}

func (t *fetchContentTool) convertOutput(ctx context.Context, output interface{}) (*definition.ToolOutput, error) {
	response, ok := output.(*FetchContentResponse)
	if !ok {
		return nil, errors.New(errors.ToolInternalError, "unexpected output type")
	}
	var outputBuilder strings.Builder

	outputBuilder.WriteString(fmt.Sprintf("Here is some relevant context from the web page %s:\n", response.URL))

	if len(response.Content) == 0 {
		outputBuilder.WriteString("No Content found.")
	} else {
		outputBuilder.WriteString(response.Content)
		// 超长截断
		if response.Truncated {
			outputBuilder.WriteString("...") // 添加省略号表示内容被截断
		}
	}
	return &definition.ToolOutput{
		Content: outputBuilder.String(),
		RawData: response,
	}, nil
}
