package lsp

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"

	toolCommon "cosy/chat/agents/tool/common"
	"cosy/chat/agents/tool/ide"
	"cosy/config"
	cosyDefinition "cosy/definition"
	cosyErrors "cosy/errors"
	"cosy/log"
	"cosy/util"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/tool"
)

const filePathsRegex = `"file_paths": \[.*?\]`

func NewGetProblemsTool(config *ide.GetProblemsConfig) (tool.InvokableTool, error) {
	explanationDesc := toolCommon.ExplanationDefaultDesc
	if config != nil && config.ExplanationDesc != "" {
		explanationDesc = config.ExplanationDesc
	}
	toolName := "get_problems"
	toolDesc := "Get any compile or lint errors in a code file. If the user mentions errors or problems in a file, they may be referring to these. Use the tool to see the same errors that the user is seeing. Also use this tool after editing a file to validate the change.\n" +
		"This is an important tool for validating code syntax errors. When you're uncertain about potential syntax errors in your modified code, you can utilize this tool to verify."
	toolParams := &definition.Schema{
		Type: "object",
		Properties: map[string]*definition.Schema{
			"file_paths": {
				Type: "array",
				Items: &definition.Schema{
					Type:        "string",
					Description: "The absolute paths of mentioned or edited files, multiple files supported",
				},
			},
			"explanation": {
				Type:        "string",
				Description: explanationDesc,
			},
		},
		Required: []string{"file_paths"},
	}
	toolInfo := &definition.ToolInfo{
		Name:        toolName,
		Description: toolDesc,
		Parameters:  toolParams,
		ReadOnly:    true,
	}
	toolInst := &getProblemsTool{
		requestId: config.RequestId,
		timeout:   config.Timeout,
	}
	return tool.NewInvokableTool(toolInfo, toolInst.get, tool.WithInputParser(toolInst.parseInput), tool.WithOutputConverter(toolInst.convertOutput)), nil
}

type getProblemsTool struct {
	requestId string
	timeout   int
}

func GetProblem(ctx context.Context, filePaths []string, requestId string, toolCallId string, timeout int) ([]cosyDefinition.Problem, error) {
	results, err := InvokeLSP(config.GetLspAddr(), filePaths, timeout)
	if err != nil {
		return nil, err
	}
	problems := make([]cosyDefinition.Problem, 0)
	for _, result := range results {
		problems = append(problems, cosyDefinition.Problem{
			FilePath: result.FilePath,
			Message:  result.Message,
			Range: cosyDefinition.ProblemRange{
				Start: cosyDefinition.ProblemPosition{
					Line:      result.Range.Start.Line,
					Character: result.Range.Start.Character,
				},
				End: cosyDefinition.ProblemPosition{
					Line:      result.Range.End.Line,
					Character: result.Range.End.Character,
				},
			},
			Severity:   result.Severity,
			SourceCode: result.SourceCode,
		})
	}
	return problems, nil
}

func (t *getProblemsTool) get(ctx context.Context, request *ide.GetProblemsRequest) (*ide.GetProblemsResponse, error) {
	problems, err := GetProblem(ctx, request.FilePaths, t.requestId, request.ToolCallId, t.timeout)
	if err != nil {
		return nil, err
	}

	return &ide.GetProblemsResponse{
		Problems: problems,
	}, nil
}

func (t *getProblemsTool) parseInput(ctx context.Context, toolInput *definition.ToolInput) (interface{}, error) {
	request := &ide.GetProblemsRequest{}
	parseSuccess := true
	var err error
	if err = json.Unmarshal([]byte(toolInput.Arguments), request); err != nil {
		parseSuccess = false
		// 兜底逻辑
		if arguments, err2 := util.NormalizePathInRawArgument(toolInput.Arguments, filePathsRegex, true); err2 == nil {
			if err3 := json.Unmarshal([]byte(arguments), request); err3 == nil {
				parseSuccess = true
			}
		}
	}
	if !parseSuccess {
		log.Error(err)
		return "", cosyErrors.New(cosyErrors.ToolInvalidArguments, "parse arguments error:"+err.Error())
	}

	toolCallId, ok := toolInput.Extra[cosyDefinition.ToolInputExtraToolCallId].(string)
	if ok {
		request.ToolCallId = toolCallId
	}
	return request, nil
}

func (t *getProblemsTool) convertOutput(ctx context.Context, output interface{}) (*definition.ToolOutput, error) {
	response, ok := output.(*ide.GetProblemsResponse)
	if !ok {
		return nil, errors.New("unexpected output type")
	}
	var outputBuilder strings.Builder
	outputBuilder.WriteString("Problems:\n")
	for _, problem := range response.Problems {
		outputBuilder.WriteString(fmt.Sprintf("%sL%d-L%d\n", problem.FilePath, problem.Range.Start.Line-1, problem.Range.End.Line-1))
		outputBuilder.WriteString(fmt.Sprintf("severity: %s\n", problem.Severity))
		outputBuilder.WriteString(fmt.Sprintf("message: %s\n", problem.Message))
		outputBuilder.WriteString("source code:\n")
		outputBuilder.WriteString("```\n")
		outputBuilder.WriteString(problem.SourceCode)
		outputBuilder.WriteString("\n```\n")
	}

	// Create and return the tool output
	return &definition.ToolOutput{
		Content: outputBuilder.String(),
		RawData: response,
	}, nil
}
