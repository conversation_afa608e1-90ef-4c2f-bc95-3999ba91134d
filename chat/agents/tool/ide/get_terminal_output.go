package ide

import (
	"context"
	"encoding/json"
	"errors"
	"strings"

	toolCommon "cosy/chat/agents/tool/common"
	cosyDefinition "cosy/definition"
	ide2 "cosy/ide/common"
	"cosy/log"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/tool"
)

type GetTerminalOutputConfig struct {
	RequestId       string
	Timeout         int
	ExplanationDesc string // explanation字段的描述
}

type GetTerminalOutputRequest struct {
	TerminalId string `json:"terminal_id"`
	ToolCallId string `json:"tool_call_id"`
}

type GetTerminalOutputResponse struct {
	TerminalId string `json:"terminalId"`
	Content    string `json:"content"`
}

func NewGetTerminalOutputTool(config *GetTerminalOutputConfig) (tool.InvokableTool, error) {
	explanationDesc := toolCommon.ExplanationDefaultDesc
	if config != nil && config.ExplanationDesc != "" {
		explanationDesc = config.ExplanationDesc
	}
	toolName := "get_terminal_output"
	toolDesc := "Get the output of a terminal command previous started with run_in_terminal."
	toolParams := &definition.Schema{
		Type: "object",
		Properties: map[string]*definition.Schema{
			"terminal_id": {
				Type:        "string",
				Description: "The ID of the terminal command output to check.",
			},
			"explanation": {
				Type:        "string",
				Description: explanationDesc,
			},
		},
		Required: []string{"terminal_id"},
	}
	toolInfo := &definition.ToolInfo{
		Name:        toolName,
		Description: toolDesc,
		Parameters:  toolParams,
		ReadOnly:    true,
	}
	toolInst := &getTerminalOutputTool{
		requestId: config.RequestId,
		timeout:   config.Timeout,
		maxLine:   210,
		headLines: 50,
		tailLines: 150,
	}
	return tool.NewInvokableTool(toolInfo, toolInst.get, tool.WithInputParser(toolInst.parseInput), tool.WithOutputConverter(toolInst.convertOutput)), nil
}

type getTerminalOutputTool struct {
	requestId string
	timeout   int

	maxLine   int
	headLines int
	tailLines int
}

func (t *getTerminalOutputTool) get(ctx context.Context, request *GetTerminalOutputRequest) (*GetTerminalOutputResponse, error) {
	ideToolRequest := &cosyDefinition.ToolInvokeRequest{
		RequestId:  t.requestId,
		ToolCallId: request.ToolCallId,
		Name:       "get_terminal_output",
		Parameters: map[string]any{
			"terminalId":  request.TerminalId,
			"terminal_id": request.TerminalId,
		},
		Async: false,
	}
	ideToolResponse, err := ide2.InvokeTool(ctx, ideToolRequest, t.timeout)
	if err != nil {
		log.Error(err)
		return nil, err
	}

	// Convert the result map to JSON and directly parse it into the response
	resultJSON, err := json.Marshal(ideToolResponse.Result)
	if err != nil {
		log.Error(err)
		return nil, errors.New("marshal result to JSON error:" + err.Error())
	}

	// Create response with request data
	response := &GetTerminalOutputResponse{}
	// Unmarshal the JSON directly into the response
	if err := json.Unmarshal(resultJSON, response); err != nil {
		log.Error(err)
		return nil, errors.New("unmarshal JSON result error:" + err.Error())
	}
	return response, nil
}

func (t *getTerminalOutputTool) parseInput(ctx context.Context, toolInput *definition.ToolInput) (interface{}, error) {
	request := &GetTerminalOutputRequest{}
	if err := json.Unmarshal([]byte(toolInput.Arguments), request); err != nil {
		log.Error(err)
		return "", errors.New("parse arguments error:" + err.Error())
	}
	toolCallId, ok := toolInput.Extra[cosyDefinition.ToolInputExtraToolCallId].(string)
	if ok {
		request.ToolCallId = toolCallId
	}
	return request, nil
}

func (t *getTerminalOutputTool) convertOutput(ctx context.Context, output interface{}) (*definition.ToolOutput, error) {
	response, ok := output.(*GetTerminalOutputResponse)
	if !ok {
		return nil, errors.New("unexpected output type")
	}
	truncatedContent, err := ide2.TruncateTerminalLines(response.Content, t.maxLine, t.headLines, t.tailLines, "// This is the omitted part")
	if err != nil {
		truncatedContent = response.Content
	}
	var outputBuilder strings.Builder
	outputBuilder.WriteString("Terminal output:\n")
	outputBuilder.WriteString("```\n")
	outputBuilder.WriteString(truncatedContent)
	outputBuilder.WriteString("\n```\n")

	// Create and return the tool output
	return &definition.ToolOutput{
		Content: outputBuilder.String(),
		RawData: response,
	}, nil
}
