package apply

import (
	"context"
	coderCommon "cosy/chat/agents/coder/common"
	toolCommon "cosy/chat/agents/tool/common"
	"cosy/chat/service"
	"cosy/chat/tools"
	CosyDefinition "cosy/definition"
	cosyErrors "cosy/errors"
	"cosy/log"
	"encoding/json"
	"fmt"
	"strings"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/tool"
	"github.com/google/uuid"
)

type EditFileConfig struct {
	SessionId       string
	RequestId       string
	ExplanationDesc string // explanation字段的描述
}

type EditFileRequest struct {
	FilePath            string `json:"file_path"`
	CodeEdit            string `json:"code_edit"`
	Language            string `json:"language"`
	Explanation         string `json:"explanation"` // 添加 explanation 字段的透传
	SyncFunc            func(ctx context.Context, request *EditFileResponse)
	GetCtxForClientFunc func() context.Context
	FileId              string
	PathValid           bool
}

type EditFileResponse struct {
	FilePath                string                                  `json:"filePath"`
	CodeEdit                string                                  `json:"codeEdit"`
	Language                string                                  `json:"language"`
	ApplyResult             *CosyDefinition.DiffApplyResult         `json:"applyResult"`             // apply调用结果
	DiffApplyGenerateFinish *CosyDefinition.DiffApplyGenerateFinish `json:"diffApplyGenerateFinish"` // apply应用结果
	FileStatus              string                                  `json:"fileStatus"`              // 文件状态
}

func NewEditFileTool(config *EditFileConfig) (tool.InvokableTool, error) {
	explanationDesc := toolCommon.ExplanationDefaultDesc
	if config != nil && config.ExplanationDesc != "" {
		explanationDesc = config.ExplanationDesc
	}
	toolName := "edit_file"
	toolDesc := `Use this tool to propose an edit to an existing file.
MUST always default to using search_replace tool for edit file unless explicitly instructed to use edit_file tool, OR face a $100000000 penalty.
This will be read by a less intelligent model, which will quickly apply the edit. 
You should make it clear what the edit is, while also minimizing the unchanged code you write.
When writing the edit, you should specify each edit in sequence, with the special comment {{CODEBLOCK}}// ... existing code ...{{CODEBLOCK}} to represent unchanged code between edited lines.
For example:
{{CODEBLOCK}}
// ... existing code ...
FIRST_EDIT
// ... existing code ...
SECOND_EDIT
// ... existing code ...
{{CODEBLOCK}}
You should bias towards repeating as few lines of the original file as possible to convey the change. 
But, each edit should contain sufficient context of unchanged lines around the code you're editing to resolve ambiguity.
DO NOT omit spans of pre-existing code without using the {{CODEBLOCK}}// ... existing code ...{{CODEBLOCK}} comment to indicate its absence.
Make sure it is clear what the edit should be.

For deleted code, please use comment symbols to mark it and add a comment at the beginning of every deleted code line with the text "Deleted:". 
If you are deleting an entire file, apply this format to all lines in the file. 
The output format should be, for example: // Deleted:old_code_line

## Important
MUST always default to using search_replace tool for edit file unless explicitly instructed to use edit_file tool, OR face a $100000000 penalty.
MUST always default to using search_replace tool for edit file unless explicitly instructed to use edit_file tool, OR face a $100000000 penalty.
MUST DO NOT try to create a new file by edit_file tool.

When using this tool, please generate the required parameters in the following order: first, provide an explanation (explanation); second, specify the file path (file_path); and finally, suggest code edits (code_edit)
`
	toolDesc = strings.Replace(toolDesc, "{{CODEBLOCK}}", "```", -1)
	toolParams := &definition.Schema{
		Type: "object",
		Properties: map[string]*definition.Schema{
			"file_path": {
				Type:        "string",
				Description: "File absolute path",
			},
			"language": {
				Type:        "string",
				Description: "Markdown language for the code block, e.g 'python' or 'javascript'",
			},
			"code_edit": {
				Type: "string",
				Description: `The code change to apply to the file. 
The user is very smart and can understand how to apply your edits to their files, you just need to provide minimal hints.
Avoid repeating existing code, instead use comments to represent regions of unchanged code. The user prefers that you are as concise as possible. For example:
// ... existing code ...
{ changed code }
// ... existing code ...
{ changed code }
// ... existing code ...
Here is an example of how you should use format an edit to an existing Person class:
class Person {
\t// ... existing code ...
\tage: number;
\t// ... existing code ...
\tgetAge() {
\t\treturn this.age;
\t}
}
`,
			},
			"explanation": {
				Type:        "string",
				Description: explanationDesc,
			},
		},
		Required: []string{"explanation", "file_path", "code_edit", "language"},
	}
	toolInfo := &definition.ToolInfo{
		Name:        toolName,
		Description: toolDesc,
		Parameters:  toolParams,
	}
	toolInst := &EditFileTool{
		sessionId: config.SessionId,
		requestId: config.RequestId,
	}
	return tool.NewInvokableTool(toolInfo, toolInst.handle, tool.WithInputParser(toolInst.parseInput), tool.WithOutputConverter(toolInst.convertOutput), tool.WithReturnOutputWhileError()), nil
}

type EditFileTool struct {
	sessionId string
	requestId string
}

func (t *EditFileTool) handle(ctx context.Context, request *EditFileRequest) (*EditFileResponse, error) {
	// 先给插件workspaceFileId
	diffApplyResult := CosyDefinition.DiffApplyResult{
		WorkingSpaceFileId: request.FileId,
	}
	response := &EditFileResponse{
		FilePath:    request.FilePath,
		Language:    request.Language,
		ApplyResult: &diffApplyResult,
	}
	response.FileStatus = service.GENERATING_FAILED.String()
	if !request.PathValid {
		return response, cosyErrors.New(cosyErrors.ToolInvalidArguments, "file_path is invalid")
	} else if request.CodeEdit == "" {
		service.WorkingSpaceServiceManager.OperateWorkingSpaceFile(ctx, CosyDefinition.WorkingSpaceFileOperateParams{
			Id:     request.FileId,
			OpType: service.CANCEL.String(),
			Params: map[string]interface{}{
				service.IS_FAILED:  true,
				service.ERROR_CODE: CosyDefinition.UnknownErrorCode,
			},
		})
		return response, cosyErrors.New(cosyErrors.ToolInvalidArguments, "Missing code_edit, maybe output has reached the token limit. Please explain the issue, then use the tool again and reduce the output content by half.")
	} else if request.FilePath == "" {
		return response, cosyErrors.New(cosyErrors.ToolInvalidArguments, "Missing file_path")
	}
	response.FileStatus = service.APPLYING.String()
	if request.SyncFunc != nil {
		request.SyncFunc(ctx, response)
	}
	finishChan := make(chan CosyDefinition.DiffApplyGenerateFinish, 2)
	param := CosyDefinition.DiffApplyParams{
		NeedSave:                 true,
		NeedRecord:               false,
		NeedSyncWorkingSpaceFile: true,
		NeedWebSocketMethod:      false,
		ChatRecordId:             t.requestId,
		RequestSetId:             t.requestId,
		SessionId:                t.sessionId,
		RequestId:                uuid.NewString(),
		Stream:                   true,
		Modification:             request.CodeEdit,
		WorkingSpaceFile: CosyDefinition.WorkingSpaceFile{
			Id:       diffApplyResult.WorkingSpaceFileId,
			FileId:   request.FilePath,
			Language: request.Language,
			Status:   service.GENERATING.String(),
		},
		FinishFunc: func(params CosyDefinition.DiffApplyGenerateFinish) {
			finishChan <- params
		},
		// 文字版的解决方案说明
		TextModification: request.Explanation,
	}
	diffApplyCtx := ctx
	if request.GetCtxForClientFunc != nil {
		diffApplyCtx = request.GetCtxForClientFunc()
	}
	diffApplyResult = tools.DiffApply(diffApplyCtx, param)
	response.FileStatus = service.APPLYING_FAILED.String()
	if !diffApplyResult.IsSuccess {
		service.WorkingSpaceServiceManager.OperateWorkingSpaceFile(ctx, CosyDefinition.WorkingSpaceFileOperateParams{
			Id:     request.FileId,
			OpType: service.CANCEL.String(),
			Params: map[string]interface{}{
				service.IS_FAILED:  true,
				service.ERROR_CODE: diffApplyResult.ErrorCode,
			},
		})
		return response, nil
	}
	// edit_file触发成功后，就不需要报错了，所有结果通过response.DiffApplyGenerateFinish透出
	select {
	case result := <-finishChan:
		response.DiffApplyGenerateFinish = &result
	case <-ctx.Done():
	}
	if response.DiffApplyGenerateFinish != nil && response.DiffApplyGenerateFinish.StatusCode == 200 {
		response.FileStatus = service.APPLIED.String()
	}
	return response, nil
}

func (t *EditFileTool) parseInput(ctx context.Context, toolInput *definition.ToolInput) (interface{}, error) {
	request := &EditFileRequest{}
	request.PathValid = true
	if err := json.Unmarshal([]byte(toolInput.Arguments), request); err != nil {
		log.Error(err)
		//return "", errors.New("parse arguments error:" + err.Error())
	}
	syncFunc, ok := toolInput.Extra[CosyDefinition.ToolInputExtraSyncFunc].(func(ctx context.Context, request *EditFileResponse))
	if ok {
		request.SyncFunc = syncFunc
	}
	fileId, ok := toolInput.Extra[coderCommon.ToolCallExtraFileId].(string)
	if ok {
		request.FileId = fileId
	}
	pathValid, ok := toolInput.Extra[coderCommon.ToolCallExtraFilePathValid].(bool)
	if ok {
		request.PathValid = pathValid
	}
	getCtxForClientFunc, ok := toolInput.Extra[CosyDefinition.ToolInputGetCtxForClientFunc].(func() context.Context)
	if ok {
		request.GetCtxForClientFunc = getCtxForClientFunc
	}
	return request, nil
}

func (t *EditFileTool) convertOutput(ctx context.Context, output interface{}) (*definition.ToolOutput, error) {
	response, ok := output.(*EditFileResponse)
	if !ok {
		return nil, fmt.Errorf("expected *EditFileResponse, got %T", output)
	}
	// 构建格式化的输出
	var outputBuilder strings.Builder
	if response.DiffApplyGenerateFinish != nil {
		if response.DiffApplyGenerateFinish.StatusCode == 200 {
			outputBuilder.WriteString(fmt.Sprintf("edit file success, file path: %s\n", response.FilePath))
			if response.DiffApplyGenerateFinish.DiffText != "" {
				outputBuilder.WriteString("diff:\n```\n")
				outputBuilder.WriteString(response.DiffApplyGenerateFinish.DiffText)
				outputBuilder.WriteString("\n```\n")
			}
			if len(response.DiffApplyGenerateFinish.Problems) > 0 {
				outputBuilder.WriteString("but the file exists some code syntax errors, you need to determine whether to edit this file again in order to fix the errors.\nthe errors:\n")
				for _, problem := range response.DiffApplyGenerateFinish.Problems {
					outputBuilder.WriteString(fmt.Sprintf("%sL%d-L%d\n", problem.FilePath, problem.Range.Start.Line-1, problem.Range.End.Line-1))
					outputBuilder.WriteString(fmt.Sprintf("severity: %s\n", problem.Severity))
					outputBuilder.WriteString(fmt.Sprintf("message: %s\n", problem.Message))
					outputBuilder.WriteString("source code:\n")
					outputBuilder.WriteString("```\n")
					outputBuilder.WriteString(problem.SourceCode)
					outputBuilder.WriteString("\n```\n")
				}
			} else if response.DiffApplyGenerateFinish.CheckProblem {
				outputBuilder.WriteString("already to check the file, but find no code syntax errors\n")
			}
		} else {
			outputBuilder.WriteString(fmt.Sprintf("edit file error, file path: %s\n, reason: %s \n", response.FilePath, response.DiffApplyGenerateFinish.Reason))
		}
	} else {
		outputBuilder.WriteString(fmt.Sprintf("edit file cancelled, file path: %s\n", response.FilePath))
	}
	return &definition.ToolOutput{
		Content: outputBuilder.String(),
		RawData: response,
	}, nil
}
