package apply

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	coderCommon "cosy/chat/agents/coder/common"
	toolCommon "cosy/chat/agents/tool/common"
	"cosy/chat/service"
	"cosy/chat/tools"
	CosyDefinition "cosy/definition"
	cosyErrors "cosy/errors"
	"cosy/log"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/tool"
	"github.com/google/uuid"
)

type SearchReplaceConfig struct {
	SessionId       string
	RequestId       string
	ExplanationDesc string // explanation字段的描述
}

type ReplacementChunk struct {
	TargetContent      string `json:"original_text"`
	ReplacementContent string `json:"new_text"`
	ReplaceAll         bool   `json:"replace_all"`
}

type SearchReplaceRequest struct {
	FilePath            string             `json:"file_path"`
	ReplacementChunks   []ReplacementChunk `json:"replacements"`
	Language            string             `json:"language"`
	Explanation         string             `json:"explanation"` // 添加 explanation 字段的透传
	SyncFunc            func(ctx context.Context, request *EditFileResponse)
	GetCtxForClientFunc func() context.Context
	FileId              string
	PathValid           bool
}

func NewSearchReplaceTool(config *SearchReplaceConfig) (tool.InvokableTool, error) {
	explanationDesc := toolCommon.ExplanationDefaultDesc
	if config != nil && config.ExplanationDesc != "" {
		explanationDesc = config.ExplanationDesc
	}
	toolName := "search_replace"
	toolDesc := `
This tool performs efficient string replacements in files with strict requirements for accuracy and safety. Use this tool to make multiple precise modifications to a file in a single operation.

## CRITICAL REQUIREMENTS

### Input Parameters
1. "file_path"" (REQUIRED): Absolute path to the target file. The file must exist
2. "replacements" (REQUIRED): Array of replacement operations, where each contains:
   - "original_text": Text to be replaced
   - "new_text": Replacement text
   - "replace_all": Boolean flag for global replacement (default: false)

### MANDATORY Rules

1. UNIQUENESS:
   - original_text MUST be uniquely identifiable in the file
   - MUST gather enough context to uniquely identify each one
   - DO NOT include excessive context when unnecessary
   - original_text MUST be uniquely identifiable in the file, if not, MUST gather enough context for original_text to be uniquely identify each one

2. EXACT MATCHING:
   - MUST match source text exactly as it appears in the file, including:
     - All whitespace and indentation
     - Line breaks and formatting
     - Special characters
   - MUST match source text exactly as it appears in the file, especially:
     - All whitespace and indentation
     - DO NOT modify the Chinese and English characters
     - DO NOT modify comment content

3. SEQUENTIAL PROCESSING:
   - MUST process replacements in provided order
   - NEVER make parallel calls on same file
   - MUST ensure earlier replacements don't interfere with later ones

4. VALIDATION:
   - NEVER allow identical source and target strings
   - MUST verify uniqueness before replacement
   - MUST validate all replacements before execution

### OPERATIONAL CONSTRAINTS

1. Line Limits:
   - DO NOT exceed 250 lines for each original_text and new_text
   - MUST ensure total line count across all text parameters(original_text and new_text) remains under 800 lines.
   - MUST include maximum possible number of replacements within the line limit during a single call
   - MUST break down large changes into multiple calls

2. Safety Measures:
   - NEVER process multiple parallel calls

## Usage Example
{
	"file_path": "/absolute/path/to/file",
	"replacements": [
		{
			"original_text": "existing_code_here",
			"new_text": "replacement_code",
			"replace_all": false,
		}
	]
}

## WARNING
- The tool will fail if exact matching fails
- All replacements must be valid for operation to succeed
- Plan replacements carefully to avoid conflicts
- Verify changes before committing

Use this tool to make precise, efficient, and safe modifications to your files while maintaining code integrity.
## IMPORTANT
You must generate the following arguments first, before any others: [file_path]
MUST DO NOT try to create a new file, you CAN ONLY use search_replace tool to edit an existing file.
MUST always default to using search_replace tool for edit file unless explicitly instructed to use edit_file tool, OR face a $********* penalty.
DO NOT try to replace the entire existing content with the new content, this is very expensive, OR face a $********* penalty.
DO NOT try to replace the entire existing content with the new content, this is very expensive, OR face a $********* penalty.

When using this tool, please generate the required parameters in the following order: first, provide an explanation (explanation); second, specify the file path (file_path); and finally, replacements
	`
	toolParams := &definition.Schema{
		Type: "object",
		Properties: map[string]*definition.Schema{
			"file_path": {
				Type:        "string",
				Description: "File absolute path, the file must exist",
			},
			"replacements": {
				Type: "array",
				Items: &definition.Schema{
					Type: "object",
					Properties: map[string]*definition.Schema{
						"original_text": {
							Type:        "string",
							Description: "The exact string to be replaced. This must be the exact character-sequence to be replaced, including whitespace otherwise this will not work at all. this must be a unique substring within the file, or else it will error",
						},
						"new_text": {
							Type:        "string",
							Description: "The content to replace the original_text with.",
						},
						"replace_all": {
							Type:        "boolean",
							Description: "Whether to replace all occurrences of the original text or just the first one. Default is false.",
						},
					},
				},
				Description: "MUST generate replacements parameter with valid JSON structure, ensuring proper escaping of quotes and line breaks to prevent parsing errors.",
			},
			"explanation": {
				Type:        "string",
				Description: explanationDesc,
			},
		},
		Required: []string{"explanation", "file_path", "replacements"},
	}
	toolInfo := &definition.ToolInfo{
		Name:        toolName,
		Description: toolDesc,
		Parameters:  toolParams,
	}
	toolInst := &SearchReplaceTool{
		sessionId: config.SessionId,
		requestId: config.RequestId,
	}
	return tool.NewInvokableTool(toolInfo, toolInst.handle, tool.WithInputParser(toolInst.parseInput), tool.WithOutputConverter(toolInst.convertOutput), tool.WithReturnOutputWhileError()), nil
}

type SearchReplaceTool struct {
	sessionId string
	requestId string
}

func (t *SearchReplaceTool) handle(ctx context.Context, request *SearchReplaceRequest) (*EditFileResponse, error) {
	// 先给插件workspaceFileId
	diffApplyResult := CosyDefinition.DiffApplyResult{
		WorkingSpaceFileId: request.FileId,
	}
	response := &EditFileResponse{
		FilePath:    request.FilePath,
		Language:    request.Language,
		ApplyResult: &diffApplyResult,
	}
	response.FileStatus = service.GENERATING_FAILED.String()
	if len(request.ReplacementChunks) == 0 {
		if request.FileId != "" {
			service.WorkingSpaceServiceManager.OperateWorkingSpaceFile(ctx, CosyDefinition.WorkingSpaceFileOperateParams{
				Id:     request.FileId,
				OpType: service.CANCEL.String(),
				Params: map[string]interface{}{
					service.IS_FAILED:  true,
					service.ERROR_CODE: CosyDefinition.UnknownErrorCode,
				},
			})
		}
		return nil, cosyErrors.New(cosyErrors.ToolInvalidArguments, "Missing replacements, MUST generate replacements parameter with valid JSON structure, ensuring proper escaping of quotes and line breaks to prevent parsing errors.")
	} else if !request.PathValid {
		return response, cosyErrors.New(cosyErrors.ToolInvalidArguments, "file_path is invalid")
	} else if request.FilePath == "" {
		return response, cosyErrors.New(cosyErrors.ToolInvalidArguments, "Missing file_path")
	}
	response.FileStatus = service.APPLYING.String()
	if request.SyncFunc != nil {
		request.SyncFunc(ctx, response)
	}
	finishChan := make(chan CosyDefinition.DiffApplyGenerateFinish, 2)
	modification := ""
	for _, replacement_chunk := range request.ReplacementChunks {
		replacePart := "\n>>>>>>> REPLACE\n"
		if replacement_chunk.ReplaceAll {
			replacePart = "\n>>>>>>> REPLACE*\n"
		}
		modification += "<<<<<<< SEARCH\n" + replacement_chunk.TargetContent + "\n=======\n" + replacement_chunk.ReplacementContent + replacePart
	}
	workingSpaceFileId := request.FileId

	response.FileStatus = service.APPLYING.String()
	param := CosyDefinition.DiffApplyParams{
		NeedSave:                 true,
		NeedRecord:               false,
		NeedSyncWorkingSpaceFile: true,
		NeedWebSocketMethod:      false,
		ChatRecordId:             t.requestId,
		SessionId:                t.sessionId,
		RequestId:                uuid.NewString(),
		Stream:                   true,
		Modification:             modification,
		TaskId:                   CosyDefinition.AgentTaskDiffApplyWithSearchReplace,
		WorkingSpaceFile: CosyDefinition.WorkingSpaceFile{
			Id:       workingSpaceFileId,
			FileId:   request.FilePath,
			Language: request.Language,
			Status:   service.GENERATING.String(),
		},
		FinishFunc: func(params CosyDefinition.DiffApplyGenerateFinish) {
			finishChan <- params
		},
	}

	diffApplyCtx := ctx
	if request.GetCtxForClientFunc != nil {
		diffApplyCtx = request.GetCtxForClientFunc()
	}
	diffApplyResult = tools.DiffApply(diffApplyCtx, param)
	response.FileStatus = service.APPLYING_FAILED.String()
	log.Infof("diffApply result: %+v", diffApplyResult)

	if !diffApplyResult.IsSuccess {
		service.WorkingSpaceServiceManager.OperateWorkingSpaceFile(ctx, CosyDefinition.WorkingSpaceFileOperateParams{
			Id:     request.FileId,
			OpType: service.CANCEL.String(),
			Params: map[string]interface{}{
				service.IS_FAILED:  true,
				service.ERROR_CODE: diffApplyResult.ErrorCode,
			},
		})
		return response, nil
	}
	// edit_file触发成功后，就不需要报错了，所有结果通过response.DiffApplyGenerateFinish透出
	select {
	case result := <-finishChan:
		response.DiffApplyGenerateFinish = &result
	case <-ctx.Done():
	}

	if response.DiffApplyGenerateFinish != nil && response.DiffApplyGenerateFinish.StatusCode == 200 {
		response.FileStatus = service.APPLIED.String()
	}

	return response, nil
}

func (t *SearchReplaceTool) parseInput(ctx context.Context, toolInput *definition.ToolInput) (interface{}, error) {
	request := &SearchReplaceRequest{}
	request.PathValid = true
	log.Debugf("arguments: %v", toolInput.Arguments)
	if err := json.Unmarshal([]byte(toolInput.Arguments), request); err != nil {
		log.Error(err)
		//return "", errors.New("parse arguments error:" + err.Error())
	}
	syncFunc, ok := toolInput.Extra[CosyDefinition.ToolInputExtraSyncFunc].(func(ctx context.Context, request *EditFileResponse))
	if ok {
		request.SyncFunc = syncFunc
	}
	fileId, ok := toolInput.Extra[coderCommon.ToolCallExtraFileId].(string)
	if ok {
		request.FileId = fileId
	}
	pathValid, ok := toolInput.Extra[coderCommon.ToolCallExtraFilePathValid].(bool)
	if ok {
		request.PathValid = pathValid
	}
	getCtxForClientFunc, ok := toolInput.Extra[CosyDefinition.ToolInputGetCtxForClientFunc].(func() context.Context)
	if ok {
		request.GetCtxForClientFunc = getCtxForClientFunc
	}
	return request, nil
}

func (t *SearchReplaceTool) convertOutput(ctx context.Context, output interface{}) (*definition.ToolOutput, error) {
	response, ok := output.(*EditFileResponse)
	if !ok {
		return nil, fmt.Errorf("expected *EditFileResponse, got %T", output)
	}
	// 构建格式化的输出
	var outputBuilder strings.Builder
	if response.DiffApplyGenerateFinish != nil {
		if response.DiffApplyGenerateFinish.StatusCode == 200 && response.DiffApplyGenerateFinish.Reason == "" {
			outputBuilder.WriteString(fmt.Sprintf("edit file by search_replace success, file path: %s\n", response.FilePath))
			if response.DiffApplyGenerateFinish.DiffText != "" {
				outputBuilder.WriteString("diff:\n```\n")
				outputBuilder.WriteString(response.DiffApplyGenerateFinish.DiffText)
				outputBuilder.WriteString("\n```\n")
			}
			if len(response.DiffApplyGenerateFinish.Problems) > 0 {
				outputBuilder.WriteString("but the file exists some code syntax errors, you need to determine whether to edit this file again in order to fix the errors.\nthe errors:\n")
				for _, problem := range response.DiffApplyGenerateFinish.Problems {
					outputBuilder.WriteString(fmt.Sprintf("%sL%d-L%d\n", problem.FilePath, problem.Range.Start.Line-1, problem.Range.End.Line-1))
					outputBuilder.WriteString(fmt.Sprintf("severity: %s\n", problem.Severity))
					outputBuilder.WriteString(fmt.Sprintf("message: %s\n", problem.Message))
					outputBuilder.WriteString("source code:\n")
					outputBuilder.WriteString("```\n")
					outputBuilder.WriteString(problem.SourceCode)
					outputBuilder.WriteString("\n```\n")
				}
			} else if response.DiffApplyGenerateFinish.CheckProblem {
				outputBuilder.WriteString("already to check the file, but find no code syntax errors\n")
			}
		} else {
			result := "partial success"
			if response.DiffApplyGenerateFinish.StatusCode != 200 {
				result = "failed"
			}
			errorMsg := response.DiffApplyGenerateFinish.Reason
			// 首次匹配异常，考虑重新读取文件内容
			readFileHint := ""
			if strings.Contains(errorMsg, "Failed to match original_text:") {
				readFileHint = "SHOULD fetch latest content via read_file to prevent invalid string generation, EXCEPT when handling consecutive failed edits where current content was already retrieved.\n"
			}
			retryHint := "MUST generate unique and existing original_text for search_replace retry. MUST use search_replace tool as first choice to retry, falling back to edit_file tool after three consecutive search_replace failures, OR face a $********* penalty..\n"
			outputBuilder.WriteString(fmt.Sprintf("edit file by search_replace %s, file path: %s\n, reason: %s \n%s\n%s\n", result, response.FilePath, errorMsg, readFileHint, retryHint))
		}
	} else {
		outputBuilder.WriteString(fmt.Sprintf("edit file by search_replace cancelled, file path: %s\n", response.FilePath))
	}
	return &definition.ToolOutput{
		Content: outputBuilder.String(),
		RawData: response,
	}, nil
}
