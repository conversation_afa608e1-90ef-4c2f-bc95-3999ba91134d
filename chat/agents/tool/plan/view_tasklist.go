package plan

import (
	"context"
	toolCommon "cosy/chat/agents/tool/common"
	"cosy/definition"
	"errors"
	"fmt"
	"strings"

	"cosy/chat/chains/common"
	"cosy/extension/plan"

	agentDefinition "code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/tool"
)

type ReadTasklistConfig struct {
	ExplanationDesc string // explanation字段的描述
}

type ReadTasklistRequest struct{}

type ReadTasklistResponse struct {
	DetailPlan definition.DetailPlan
}

func NewReadTasklistTool(config *ReadTasklistConfig) (tool.InvokableTool, error) {
	explanationDesc := toolCommon.ExplanationDefaultDesc
	if config != nil && config.ExplanationDesc != "" {
		explanationDesc = config.ExplanationDesc
	}
	toolName := "read_tasklist"
	toolDesc := `View the current task list for the conversation.`
	toolParams := &agentDefinition.Schema{
		Type: "object",
		Properties: map[string]*agentDefinition.Schema{
			"explanation": {
				Description: explanationDesc,
				Type:        "string",
			},
		},
		Required: []string{},
	}
	toolInfo := &agentDefinition.ToolInfo{
		Name:        toolName,
		Description: toolDesc,
		Parameters:  toolParams,
		ReadOnly:    true,
	}
	toolInst := &tasklistReader{}
	return tool.NewInvokableTool(toolInfo, toolInst.ReadTasklist, tool.WithOutputConverter(toolInst.convertOutput)), nil
}

type tasklistReader struct{}

func (p *tasklistReader) ReadTasklist(ctx context.Context, request *ReadTasklistRequest) (*ReadTasklistResponse, error) {
	sessionId, ok := ctx.Value(common.KeySessionId).(string)
	if !ok || sessionId == "" {
		return nil, errors.New("no sessionId found in context")
	}

	detailPlan, exists := plan.GenerateDetailPlan(sessionId)
	if !exists {
		// Return empty task list if no tasks exist for this session
		detailPlan = &definition.DetailPlan{
			MarkdownContent: "No tasks found in current session. You can start by adding some tasks to organize your work.",
		}
	}

	response := &ReadTasklistResponse{
		DetailPlan: *detailPlan,
	}
	return response, nil
}

func (p *tasklistReader) convertOutput(ctx context.Context, output interface{}) (*agentDefinition.ToolOutput, error) {
	response, ok := output.(*ReadTasklistResponse)
	if !ok {
		return nil, fmt.Errorf("expected *ReadTasklistResponse, got %T", output)
	}

	var outputBuilder strings.Builder
	outputBuilder.WriteString("Here are the latest contents of your task list:\n")
	outputBuilder.WriteString(response.DetailPlan.MarkdownContent)
	return &agentDefinition.ToolOutput{
		Content: outputBuilder.String(),
		RawData: response,
	}, nil
}
