package plan

import (
	"context"
	toolCommon "cosy/chat/agents/tool/common"
	"cosy/chat/chains/common"
	"cosy/definition"
	"cosy/extension/plan"
	"errors"
	"fmt"
	"strings"

	agentDefinition "code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/tool"
)

type UpdateTasksConfig struct {
	WriteToFile     bool   // 是否将任务列表写入文件
	ExplanationDesc string // explanation字段的描述
}

type UpdateTaskDetail struct {
	ID      string `json:"id"`
	State   string `json:"status"`
	Content string `json:"content"`
}

type UpdateTasksRequest struct {
	Tasks []UpdateTaskDetail `json:"tasks"`
}

type UpdateTasksResponse struct {
	Results    []definition.AddOrUpdateResult `json:"results"`
	DetailPlan definition.DetailPlan          `json:"detailPlan"`
}

func NewUpdateTasksTool(config *UpdateTasksConfig) (tool.InvokableTool, error) {
	explanationDesc := toolCommon.ExplanationDefaultDesc
	if config != nil && config.ExplanationDesc != "" {
		explanationDesc = config.ExplanationDesc
	}
	toolName := "update_tasks"
	toolDesc := `Update one or more tasks' properties (state, content). Can update a single task or multiple tasks in one call. 
Use this on complex sequences of work to plan, track progress, and manage work.
`
	toolParams := &agentDefinition.Schema{
		Type: "object",
		Properties: map[string]*agentDefinition.Schema{
			"tasks": {
				Type: "array",
				Items: &agentDefinition.Schema{
					Type: "object",
					Properties: map[string]*agentDefinition.Schema{
						"id": {
							Description: "The ID of the task to update.",
							Type:        "string",
						},
						"status": {
							Description: "New task status. Fill in this parameter when you need to update the task status.",
							Type:        "string",
							Enum:        []any{"PENDING", "IN_PROGRESS", "CANCELLED", "COMPLETE", "ERROR"},
						},
						"content": {
							Description: "New Task content. Fill in this parameter when you need to update the task content.",
							Type:        "string",
						},
					},
					Required: []string{"id"},
				},
			},
			"explanation": {
				Description: explanationDesc,
				Type:        "string",
			},
		},
		Required: []string{"tasks"},
	}
	toolInfo := &agentDefinition.ToolInfo{
		Name:        toolName,
		Description: toolDesc,
		Parameters:  toolParams,
	}
	writeToFile := false
	if config != nil && config.WriteToFile {
		writeToFile = true
	}
	toolInst := &tasksUpdater{
		WriteToFile: writeToFile,
	}
	return tool.NewInvokableTool(toolInfo, toolInst.UpdateTasks, tool.WithOutputConverter(toolInst.convertOutput)), nil
}

type tasksUpdater struct {
	WriteToFile bool
}

func (p *tasksUpdater) UpdateTasks(ctx context.Context, request *UpdateTasksRequest) (*UpdateTasksResponse, error) {
	sessionId, ok := ctx.Value(common.KeySessionId).(string)
	if !ok || sessionId == "" {
		return nil, errors.New("no sessionId found in context")
	}

	// Validate request
	if request == nil || len(request.Tasks) == 0 {
		return nil, errors.New("no tasks provided for update")
	}

	// Set pause state
	plan.SetPause(sessionId, false)

	// Convert UpdateTaskDetail to TaskRecord
	var recordsToUpdate []plan.RecordToAddOrUpdate
	for _, taskDetail := range request.Tasks {
		if taskDetail.ID == "" {
			return nil, errors.New("task UUID is required")
		}

		taskRecord := plan.RecordToAddOrUpdate{
			ID: taskDetail.ID,
			// Apply state conversion consistency rule: empty string converts to PENDING
			Status: plan.TaskStatus(func() string {
				if taskDetail.State == "" {
					return string(plan.PENDING)
				} else {
					return taskDetail.State
				}
			}()),
			Content: taskDetail.Content,
		}
		recordsToUpdate = append(recordsToUpdate, taskRecord)
	}

	updateResults := plan.AddOrUpdateTaskRecords(sessionId, recordsToUpdate, false)

	// Generate updated detail plan
	detailPlan, exists := plan.GenerateDetailPlan(sessionId)
	if !exists {
		return nil, errors.New("failed to generate detail plan after adding tasks")
	}
	if p.WriteToFile {
		plan.WritePlanToFile(ctx, detailPlan.MarkdownContent, detailPlan.TaskTreeJson)
	}

	response := &UpdateTasksResponse{
		Results:    updateResults,
		DetailPlan: *detailPlan,
	}
	return response, nil
}

func (p *tasksUpdater) convertOutput(ctx context.Context, output interface{}) (*agentDefinition.ToolOutput, error) {
	response, ok := output.(*UpdateTasksResponse)
	if !ok {
		return nil, fmt.Errorf("expected *UpdateTasksResponse, got %T", output)
	}

	var outputBuilder strings.Builder

	// Build summary of update operations
	if len(response.Results) == 0 {
		outputBuilder.WriteString("No tasks were updated.\n")
	} else {
		successCount := 0
		failureCount := 0

		for _, result := range response.Results {
			if result.Success {
				successCount++
			} else {
				failureCount++
			}
		}

		outputBuilder.WriteString(fmt.Sprintf("Task update completed. %d successful, %d failed.\n\n", successCount, failureCount))
		// Show detailed results
		outputBuilder.WriteString("Update Results:\n")
		for _, result := range response.Results {
			status := "✓"
			if !result.Success {
				status = "✗"
			}
			outputBuilder.WriteString(fmt.Sprintf("%s Task %s: %s\n", status, result.ID, result.Message))
		}

		// Get updated task list from store if session exists
		outputBuilder.WriteString("\nCurrent Task List:\n")
		outputBuilder.WriteString(response.DetailPlan.MarkdownContent)
	}

	return &agentDefinition.ToolOutput{
		Content: outputBuilder.String(),
		RawData: response,
	}, nil
}
