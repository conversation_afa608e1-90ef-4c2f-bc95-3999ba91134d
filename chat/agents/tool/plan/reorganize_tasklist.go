package plan

import (
	"context"
	toolCommon "cosy/chat/agents/tool/common"
	"cosy/definition"
	"fmt"
	"strings"

	agentDefinition "code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/tool"
)

type ReorganizeTasklistConfig struct {
	DetailPlan      *definition.DetailPlan // 保存plan数据的结构体
	ExplanationDesc string                 // explanation字段的描述
}

type ReorganizeTasklistRequest struct {
	Plan    string
	Message string
}

type ReorganizeTasklistResponse struct {
	DetailPlan definition.DetailPlan
}

func NewReorganizeTasklistTool(config *ReorganizeTasklistConfig) (tool.InvokableTool, error) {
	explanationDesc := toolCommon.ExplanationDefaultDesc
	if config != nil && config.ExplanationDesc != "" {
		explanationDesc = config.ExplanationDesc
	}
	toolName := "write_tasklist"
	toolDesc := `Reorganize the task list structure for the current conversation. 
Use this only for major restructuring like reordering tasks, changing hierarchy. For individual task updates, use update_tasks tool.
`
	toolParams := &agentDefinition.Schema{
		Type: "object",
		Properties: map[string]*agentDefinition.Schema{
			"markdown": {
				Description: "The markdown representation of the task list to update. Should be in the format specified by the view_tasklist tool. New tasks should have a NEW UUID.",
				Type:        "string",
			},
			"explanation": {
				Description: explanationDesc,
				Type:        "string",
			},
		},
		Required: []string{"query"},
	}
	toolInfo := &agentDefinition.ToolInfo{
		Name:        toolName,
		Description: toolDesc,
		Parameters:  toolParams,
	}
	toolInst := &planReorganizer{}
	return tool.NewInvokableTool(toolInfo, toolInst.ReorganizeTasklist, tool.WithOutputConverter(toolInst.convertOutput)), nil
}

type planReorganizer struct{}

func (p *planReorganizer) ReorganizeTasklist(ctx context.Context, request *ReorganizeTasklistRequest) (*ReorganizeTasklistResponse, error) {
	response := &ReorganizeTasklistResponse{
		DetailPlan: definition.DetailPlan{
			MarkdownContent: request.Plan,
		},
	}
	return response, nil
}

func (p *planReorganizer) convertOutput(ctx context.Context, output interface{}) (*agentDefinition.ToolOutput, error) {
	response, ok := output.(*ReorganizeTasklistResponse)
	if !ok {
		return nil, fmt.Errorf("expected *ReorganizeTasklistResponse, got %T", output)
	}

	var outputBuilder strings.Builder
	outputBuilder.WriteString("Plan have been modified successfully. Ensure that you continue to use the task list to track your progress. Please proceed with the current tasks if applicable.\n")
	outputBuilder.WriteString("Here are the latest contents of your task list:\n")
	outputBuilder.WriteString(response.DetailPlan.MarkdownContent)
	return &agentDefinition.ToolOutput{
		Content: outputBuilder.String(),
		RawData: response,
	}, nil
}
