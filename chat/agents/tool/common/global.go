package common

import (
	"cosy/definition"
	"sync"
)

// chan for tool confirm(chan *definition.ToolConfirmRequest)
var toolConfirmChanMap sync.Map

func SetToolConfirm<PERSON>han(toolCallId string, ch chan *definition.ToolConfirmRequest) {
	toolConfirmChanMap.Store(toolCallId, ch)
}

func GetToolConfirm<PERSON>han(toolCallId string) chan *definition.ToolConfirmRequest {
	if v, ok := toolConfirmChanMap.Load(toolCallId); ok {
		return v.(chan *definition.ToolConfirmRequest)
	}
	return nil
}

func DeleteTool<PERSON>onfirm<PERSON>han(toolCallId string) {
	toolConfirmChanMap.Delete(toolCallId)
}
