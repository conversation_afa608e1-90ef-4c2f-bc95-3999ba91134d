package human

import (
	"context"
	"fmt"
	"strings"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/tool"
)

type AskUserConfig struct {
}

type AskUserRequest struct {
	Question string `json:"question"`
}

type AskUserResponse struct {
	Question string `json:"question"`
}

func NewAskUserTool(config *AskUserConfig) (tool.InvokableTool, error) {
	toolName := "ask_user"
	toolDesc := `Ask the user a question to gather additional information needed to complete the task. 
This tool should be used when you encounter ambiguities, need clarification, or require more details to proceed effectively. 
It allows for interactive problem-solving by enabling direct communication with the user. Use this tool judiciously to maintain a balance between gathering necessary information and avoiding excessive back-and-forth.
`
	toolParams := &definition.Schema{
		Type: "object",
		Properties: map[string]*definition.Schema{
			"question": {
				Type:        "string",
				Description: "The question to ask the user. This should be a clear, specific question that addresses the information you need.",
			},
		},
		Required: []string{"question"},
	}
	toolInfo := &definition.ToolInfo{
		Name:        toolName,
		Description: toolDesc,
		Parameters:  toolParams,
	}
	asker := &userAsker{}
	return tool.NewInvokableTool(toolInfo, asker.ask, tool.WithOutputConverter(asker.convertOutput)), nil
}

type userAsker struct {
}

func (a *userAsker) ask(ctx context.Context, request *AskUserRequest) (*AskUserResponse, error) {
	return &AskUserResponse{
		Question: request.Question,
	}, nil
}

func (a *userAsker) convertOutput(ctx context.Context, output interface{}) (*definition.ToolOutput, error) {
	response, ok := output.(*AskUserResponse)
	if !ok {
		return nil, fmt.Errorf("expected *ReadFileResponse, got %T", output)
	}
	var outputBuilder strings.Builder
	outputBuilder.WriteString("The question has been sent to the user, and if the user responds, it will be sent as a new message. Do not disclose this information to the user.\n")
	return &definition.ToolOutput{
		Content: outputBuilder.String(),
		RawData: response,
	}, nil
}
