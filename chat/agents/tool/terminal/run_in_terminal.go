package terminal

import (
	"code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/tool"
	"context"
	"cosy/chat/agents/tool/ide"
	"cosy/chat/agents/tool/terminal/engine"
	cosyDefinition "cosy/definition"
	ideCommon "cosy/ide/common"
	"cosy/log"
	"cosy/util"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"strings"
)

var terminalEngine = engine.NewSandboxTerminalEngine("http://localhost:9002")

type runInTerminalTool struct {
	workspacePath string
	requestId     string
	timeout       int

	info               *definition.ToolInfo
	maxLine            int
	headLines          int
	tailLines          int
	frontTerminalIndex int
}

func (t *runInTerminalTool) Info(ctx context.Context) (*definition.ToolInfo, error) {
	return t.info, nil
}

func (t *runInTerminalTool) Invoke(ctx context.Context, input *definition.ToolInput, opts ...tool.Option) (*definition.ToolOutput, error) {
	request, err := t.parseInput(ctx, input)
	if err != nil {
		return nil, err
	}
	if request.Command == "" {
		return nil, &definition.ToolError{ErrorCode: definition.ToolErrorCodeInvalidParameter, ErrorMsg: "command is required"}
	}
	// 前台命令使用terminal-0，最大等待4分钟。 若未执行完，则转换成后台任务
	sessionId := fmt.Sprintf("terminal-%d", t.frontTerminalIndex)
	waitOutputSeconds := 4 * 60
	if request.IsBackground {
		// 后台命令将随机构造一个SessionId
		sessionId = fmt.Sprintf("terminal-%d", util.RandomIntBetween(100, math.MaxInt))
		// 后台任务只等10秒获得结果
		waitOutputSeconds = 10
	}
	output, err := terminalEngine.RunCommandInSession(ctx, sessionId, t.workspacePath, request.Command, waitOutputSeconds)
	if err != nil {
		log.Errorf("run terminal command %s failed, err: %v", request.Command, err)
		return nil, &definition.ToolError{ErrorCode: definition.ToolErrorCodeInternalError, ErrorMsg: "Terminal is unavailable now"}
	}
	if output.ExitCode < 0 {
		// exit code < 0 表示命令还在执行中
		if !request.IsBackground {
			// 前台命令的情况下，切换一个新的terminal交给agent使用
			t.frontTerminalIndex++
		}
	}
	response := &ide.RunInTerminalResponse{
		Command:      request.Command,
		IsBackground: request.IsBackground,
		TerminalId:   output.SessionId,
		Content:      output.Content,
		ExitCode:     output.ExitCode,
	}

	return t.convertOutput(ctx, response)
}

func (t *runInTerminalTool) parseInput(ctx context.Context, toolInput *definition.ToolInput) (*ide.RunInTerminalRequest, error) {
	request := &ide.RunInTerminalRequest{}
	if err := json.Unmarshal([]byte(toolInput.Arguments), request); err != nil {
		log.Error(err)
		return nil, errors.New("parse arguments error:" + err.Error())
	}
	toolCallId, ok := toolInput.Extra[cosyDefinition.ToolInputExtraToolCallId].(string)
	if ok {
		request.ToolCallId = toolCallId
	}
	return request, nil
}

func (t *runInTerminalTool) convertOutput(ctx context.Context, output interface{}) (*definition.ToolOutput, error) {
	response, ok := output.(*ide.RunInTerminalResponse)
	if !ok {
		return nil, errors.New("unexpected output type")
	}
	var outputBuilder strings.Builder
	truncatedContent, err := ideCommon.TruncateTerminalLines(response.Content, t.maxLine, t.headLines, t.tailLines, "// This is the omitted part")
	if err != nil {
		truncatedContent = response.Content
	}
	// 后台命令 或者 退出码 < 0(前台命令未运行完成，自动切换后台）
	bgRunning := (response.IsBackground && response.TerminalId != "") || response.ExitCode < 0
	if bgRunning {
		outputBuilder.WriteString(fmt.Sprintf("Command is running, terminal_id is %s, you can check status and output of this terminal by get_terminal_output if necessary. \n", response.TerminalId))
		outputBuilder.WriteString("Partial output:\n")
		outputBuilder.WriteString("```\n")
		outputBuilder.WriteString(truncatedContent)
		outputBuilder.WriteString("\n```\n")
	} else {
		if response.ExitCode >= 0 {
			outputBuilder.WriteString("Command completed. \n")
		} else {
			// 自定义的错误退出码，由于终端问题触发命令执行失败的情况
			outputBuilder.WriteString("Command execution failed. \n")
		}
		if response.ExitCode > 0 {
			// 正常的退出码，返回给模型
			outputBuilder.WriteString(fmt.Sprintf("ExitCode: %d", response.ExitCode))
		}
		outputBuilder.WriteString("Command output:\n")
		outputBuilder.WriteString("```\n")
		outputBuilder.WriteString(truncatedContent)
		outputBuilder.WriteString("\n```\n")
	}
	// Create and return the tool output
	return &definition.ToolOutput{
		Content: outputBuilder.String(),
		RawData: response,
	}, nil
}

func NewRunInTerminalTool(config *ide.RunInTerminalConfig) (tool.InvokableTool, error) {
	tt, err := ide.NewRunInTerminalTool(config)
	if err != nil {
		return nil, err
	}
	info, err := tt.Info(context.TODO())
	if err != nil {
		return nil, err
	}
	return &runInTerminalTool{
		frontTerminalIndex: 0,
		workspacePath:      config.WorkspacePath,
		requestId:          config.RequestId,
		timeout:            config.Timeout,
		info:               info,
		maxLine:            210,
		headLines:          50,
		tailLines:          150,
	}, nil
}
