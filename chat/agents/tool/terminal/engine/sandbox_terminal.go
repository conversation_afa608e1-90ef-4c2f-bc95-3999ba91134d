package engine

import (
	"bytes"
	"context"
	"cosy/log"
	"encoding/json"
	"fmt"
	"github.com/pkg/errors"
	"io"
	"net/http"
	"net/url"
	"time"
)

type sandboxToolRequest struct {
	Arguments any      `json:"arguments"`
	Name      toolName `json:"name"`
}

type toolName string

const (
	runInTerminalTool     toolName = "run_in_terminal"
	getTerminalOutputTool toolName = "get_terminal_output"
)

type runInTerminalRequest struct {
	Command string `json:"command"`
	// 工作目录
	WorkDir string `json:"work_dir"`
	// 会话ID，若为空则使用新会话
	SessionId string `json:"session_id,omitempty"`
	// 等待输出结果的时间，若不指定该参数则一直等到命令结束后输出，否则至多等待x秒后返回输出。后续使用get_terminal_output获取更多输出内容
	WaitForOutputSeconds int `json:"wait_for_output_seconds,omitempty"`
}

type terminalOutputResponse struct {
	SessionId string `json:"session_id"`
	Type      string `json:"type"`
	Text      string `json:"text"`
	ExitCode  int    `json:"exit_code"`
}

type getTerminalOutputRequest struct {
	SessionId string `json:"session_id"`
}

// sandboxTerminalEngine 沙箱提供了基于tmux的命令行工具
type sandboxTerminalEngine struct {
	endpoint      string
	workspacePath string
}

func (e *sandboxTerminalEngine) getToolCallUrl() string {
	uri := "/api/v1/tools/call"
	fullUrl, err := url.JoinPath(e.endpoint, uri)
	if err != nil {
		log.Errorf("getToolCallUrl err:%v", err)
		return uri
	}
	return fullUrl
}

func (e *sandboxTerminalEngine) RunCommandInSession(ctx context.Context, sessionId string, workDir string, command string, waitForOutputSeconds int) (*TerminalOutput, error) {
	fullUrl := e.getToolCallUrl()

	requestTimeout := time.Second * time.Duration(waitForOutputSeconds+5)
	payload := &sandboxToolRequest{
		Name: runInTerminalTool,
		Arguments: &runInTerminalRequest{
			Command:              command,
			WorkDir:              workDir,
			SessionId:            sessionId,
			WaitForOutputSeconds: waitForOutputSeconds,
		},
	}
	bodyBytes, err := json.Marshal(payload)
	if err != nil {
		return nil, err
	}
	resp, err := invokeHttpWithTimeout[terminalOutputResponse](ctx, http.MethodPost, fullUrl, nil, nil, bodyBytes, requestTimeout)
	if err != nil {
		log.Errorf("RunCommand err:%v", err)
		return nil, err
	}
	return &TerminalOutput{
		ExitCode:  resp.ExitCode,
		SessionId: resp.SessionId,
		Content:   resp.Text,
	}, nil
}

func (e *sandboxTerminalEngine) GetLastCommandOutput(ctx context.Context, sessionId string) (*TerminalOutput, error) {
	fullUrl := e.getToolCallUrl()

	defaultWaitSeconds := 10
	defaultRequestTimeout := time.Second * time.Duration(defaultWaitSeconds+5)
	payload := &sandboxToolRequest{
		Name: getTerminalOutputTool,
		Arguments: &getTerminalOutputRequest{
			SessionId: sessionId,
		},
	}
	bodyBytes, err := json.Marshal(payload)
	if err != nil {
		return nil, err
	}
	resp, err := invokeHttpWithTimeout[terminalOutputResponse](ctx, http.MethodPost, fullUrl, nil, nil, bodyBytes, defaultRequestTimeout)
	if err != nil {
		log.Errorf("RunCommand err:%v", err)
		return nil, err
	}
	return &TerminalOutput{
		ExitCode:  resp.ExitCode,
		SessionId: resp.SessionId,
		Content:   resp.Text,
	}, nil
}

func invokeHttpWithTimeout[T any](ctx context.Context, method string, fullUrl string, headers map[string]string, queryData map[string]string, bodyBytes []byte, timeout time.Duration) (*T, error) {
	log.Infof("request method: %s, url:%v", method, fullUrl)
	result := new(T)
	queryValues := url.Values{}
	for key, value := range queryData {
		queryValues.Add(key, value)
	}
	if len(queryData) > 0 {
		fullUrl += "?" + queryValues.Encode()
	}

	req, err := http.NewRequest(method, fullUrl, bytes.NewBuffer(bodyBytes))
	if err != nil {
		return result, errors.WithStack(err)
	}
	for k, v := range headers {
		req.Header.Set(k, v)
	}

	cli := &http.Client{Timeout: timeout}
	resp, err := cli.Do(req)
	if err != nil {
		log.Errorf("do request err: %v", err)
		return nil, errors.WithStack(err)
	}

	defer resp.Body.Close()
	// 读取响应体
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Errorf("read response body failed, err: %v", err)
		return nil, errors.WithStack(err)
	}
	if resp.StatusCode != 200 && resp.StatusCode != 201 {
		// 记录一下非正常响应
		log.Warnf("invoke http failed, statusCode=%d, fullUrl=%s, method=%s, respBody=%s", resp.StatusCode, fullUrl, method, string(respBody))
		if resp.StatusCode == 404 {
			return nil, fmt.Errorf("http response 404")
		}
		return nil, fmt.Errorf("http response not 200")
	}
	if err := json.Unmarshal(respBody, result); err != nil {
		return nil, errors.WithStack(err)
	}
	return result, nil
}
