package engine

import (
	"context"
)

type TerminalOutput struct {
	ExitCode  int
	SessionId string
	Content   string
}

type TerminalEngine interface {
	// RunCommandInSession 在指定会话窗口中执行命令，若sessionId为空则使用新窗口运行
	RunCommandInSession(ctx context.Context, sessionId string, workDir string, command string, waitForOutputSeconds int) (*TerminalOutput, error)
	// GetLastCommandOutput 获取最新命令的输出结果与错误
	GetLastCommandOutput(ctx context.Context, sessionId string) (*TerminalOutput, error)
}

func NewSandboxTerminalEngine(endpoint string) TerminalEngine {
	if endpoint == "" {
		endpoint = "http://localhost:9002"
	}
	return &sandboxTerminalEngine{endpoint: endpoint}
}
