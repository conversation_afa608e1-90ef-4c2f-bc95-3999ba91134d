package wiki

import (
	"context"
	"fmt"
	"strings"

	"cosy/chat/agents/tool/common"
	"cosy/deepwiki/storage"
	"cosy/log"

	codedf "cosy/definition"

	definition "code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/tool"
)

type WikiListConfig struct {
	WorkspacePath   string // 工作空间路径
	ExplanationDesc string // explanation字段的描述
}

type WikiListRequest struct {
	WorkspacePath string `json:"workspace_path"`
}

type WikiListResponse struct {
	AgentWikiItems []*codedf.AgentWikiItem `json:"agent_wiki_items"`
	Message        string                  `json:"message"`
}

func NewWikiListTool(config *WikiListConfig) (tool.InvokableTool, error) {
	explanationDesc := common.ExplanationDefaultDesc
	if config != nil && config.ExplanationDesc != "" {
		explanationDesc = config.ExplanationDesc
	}

	toolName := "list_wiki"
	toolDesc := `
	Get the wiki items list for the current workspace.

	Always use this tool whenever the user asks a question.

	Return should be a list of wiki items, each item should have the following fields:
	- wikiId: the id of the wiki item
	- title: the title of the wiki item
	`

	toolParams := &definition.Schema{
		Type: "object",
		Properties: map[string]*definition.Schema{
			"workspace_path": {
				Type:        "string",
				Description: "The workspace path to get wiki items for. If not provided, uses the current workspace.",
			},
			"explanation": {
				Type:        "string",
				Description: explanationDesc,
			},
		},
		Required: []string{},
	}

	toolInfo := &definition.ToolInfo{
		Name:        toolName,
		Description: toolDesc,
		Parameters:  toolParams,
		ReadOnly:    true,
	}

	toolInst := &wikiListTool{
		workspacePath: config.WorkspacePath,
	}

	return tool.NewInvokableTool(toolInfo, toolInst.getWikiList, tool.WithOutputConverter(toolInst.convertOutput)), nil
}

type wikiListTool struct {
	workspacePath string
}

func doGetWikiItemsByWorkspacePath(ctx context.Context, workspacePath string) ([]*codedf.AgentWikiItem, error) {

	// storage.GlobalStorageService 查询wiki列表
	wikiItems, err := storage.GlobalStorageService.GetWikiItemsByWorkspacePath(workspacePath)
	if err != nil {
		return nil, fmt.Errorf("failed to get wiki items: %w", err)
	}

	return wikiItems, nil

}
func (w *wikiListTool) getWikiList(ctx context.Context, request *WikiListRequest) (*WikiListResponse, error) {
	// 使用提供的路径或默认工作空间路径
	targetPath := request.WorkspacePath
	if targetPath == "" {
		targetPath = w.workspacePath
	}

	if targetPath == "" {
		return &WikiListResponse{
			AgentWikiItems: []*codedf.AgentWikiItem{},
			Message:        "No workspace path provided",
		}, nil
	}

	// 首先通过workspace path获取wiki仓库
	wikiItems, err := doGetWikiItemsByWorkspacePath(ctx, targetPath)

	if err != nil {
		return nil, fmt.Errorf("failed to get wiki items: %w", err)
	}

	var message string
	if len(wikiItems) == 0 {
		message = fmt.Sprintf("No wiki items found in repository '%s'", targetPath)
	} else {
		message = fmt.Sprintf("Found %d wiki items in repository '%s'", len(wikiItems), targetPath)
	}

	return &WikiListResponse{
		AgentWikiItems: wikiItems,
		Message:        message,
	}, nil
}

func (w *wikiListTool) convertOutput(ctx context.Context, output interface{}) (*definition.ToolOutput, error) {
	response, ok := output.(*WikiListResponse)
	if !ok {
		return nil, fmt.Errorf("expected *WikiListResponse, got %T", output)
	}

	var outputBuilder strings.Builder
	outputBuilder.WriteString(response.Message)
	outputBuilder.WriteString("\n")

	if len(response.AgentWikiItems) > 0 {
		outputBuilder.WriteString("\nWiki Items:\n")
		for i, item := range response.AgentWikiItems {
			outputBuilder.WriteString(fmt.Sprintf("%d. wikiId: %s, title: %s\n", i+1, item.ID, item.Title))
			outputBuilder.WriteString("\n")
		}
	} else {
		outputBuilder.WriteString("\nNo wiki items available.\n")
	}

	log.Infof("wikiList output: %s", outputBuilder.String())
	log.Infof("wikiList response: %+v", response)

	return &definition.ToolOutput{
		Content: outputBuilder.String(),
		RawData: response,
	}, nil
}

// GetWikiList 直接调用函数，供其他流程使用
// 功能与工具调用相同，但可以直接在代码中调用
func GetWikiList(ctx context.Context, workspacePath string) ([]*codedf.AgentWikiItem, error) {
	if workspacePath == "" {
		return []*codedf.AgentWikiItem{}, fmt.Errorf("workspace path is required")
	}

	// 直接调用底层实现函数
	wikiItems, err := doGetWikiItemsByWorkspacePath(ctx, workspacePath)
	if err != nil {
		return nil, fmt.Errorf("failed to get wiki items: %w", err)
	}

	return wikiItems, nil
}

// GetWikiListWithMessage 直接调用函数，返回包含消息的完整响应
// 供需要详细信息的流程使用
func GetWikiListWithMessage(ctx context.Context, workspacePath string) (*WikiListResponse, error) {
	if workspacePath == "" {
		return &WikiListResponse{
			AgentWikiItems: []*codedf.AgentWikiItem{},
			Message:        "No workspace path provided",
		}, nil
	}

	// 调用底层实现
	wikiItems, err := doGetWikiItemsByWorkspacePath(ctx, workspacePath)
	if err != nil {
		return nil, fmt.Errorf("failed to get wiki items: %w", err)
	}

	var message string
	if len(wikiItems) == 0 {
		message = fmt.Sprintf("No wiki items found in repository '%s'", workspacePath)
	} else {
		message = fmt.Sprintf("Found %d wiki items in repository '%s'", len(wikiItems), workspacePath)
	}

	return &WikiListResponse{
		AgentWikiItems: wikiItems,
		Message:        message,
	}, nil
}
