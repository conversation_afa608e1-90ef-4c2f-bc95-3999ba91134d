package wiki

import (
	"context"
	"fmt"
	"strings"

	"cosy/chat/agents/tool/common"
	"cosy/deepwiki/storage"
	"cosy/log"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/tool"
)

type WikiReadConfig struct {
	WorkspacePath   string // 工作空间路径
	ExplanationDesc string // explanation字段的描述
}

type WikiReadRequest struct {
	WikiIDs []string `json:"wiki_ids"` // wiki item IDs to read
}

type WikiReadResponse struct {
	WikiItems []WikiReadItem `json:"wiki_items"`
	Message   string         `json:"message"`
}

type WikiReadItem struct {
	WikiID  string `json:"wiki_id"`
	Content string `json:"content"`
}

func NewWikiReadTool(config *WikiReadConfig) (tool.InvokableTool, error) {
	explanationDesc := common.ExplanationDefaultDesc
	if config != nil && config.ExplanationDesc != "" {
		explanationDesc = config.ExplanationDesc
	}

	toolName := "read_wiki"
	toolDesc := `Read complete knowledge content from specific wiki documents by their exact IDs provided by wiki list.

WHEN TO USE THIS TOOL:
- You have the exact wiki item ID(s) that the user mentioned or requested
- User explicitly asks to "read", "show", "get", or "display" specific documents
- User provides specific document IDs in their query (e.g., "read doc abc123", "show me wiki xyz789")
- You need to retrieve the full, complete content of known wiki documents
- User asks for specific documents by their titles and you can identify the exact wiki items from the wiki list
- The query exactly matches a wiki item title obtained from <project_wiki_catalogues>
- User provides a document name that matches exactly with a wiki item title
- After using 'search_wiki', use read_wiki as much as possible to obtain more detailed and complete knowledge content

WHEN NOT TO USE THIS TOOL:
- User asks general questions that require searching across multiple documents
- You don't have specific wiki IDs to read from
- User needs to find relevant information based on topics or keywords
- The query is exploratory or requires semantic understanding across the wiki corpus
- The query is a general question rather than a specific document request

EXAMPLES OF APPROPRIATE QUERIES:
- "Read wiki document abc123def456"
- "Show me the content of the authentication guide" (if you know the exact ID from wiki list)
- "Get the API documentation wiki"
- "Display the setup instructions document"
- "Authentication Guide" (if this is an exact wiki title from wiki list)
- "API Documentation" (if this is an exact wiki title from wiki list)

PRIORITY RULE: If the query exactly matches a wiki item title from wiki list, ALWAYS use this tool instead of 'search_wiki'.

This tool provides direct, complete access to specific wiki documents when you know exactly which ones to retrieve.`

	toolParams := &definition.Schema{
		Type: "object",
		Properties: map[string]*definition.Schema{
			"wiki_ids": {
				Type: "array",
				Items: &definition.Schema{
					Type: "string",
				},
				Description: "List of wiki item IDs to read content from.",
			},
			"explanation": {
				Type:        "string",
				Description: explanationDesc,
			},
		},
		Required: []string{"wiki_ids"},
	}

	toolInfo := &definition.ToolInfo{
		Name:        toolName,
		Description: toolDesc,
		Parameters:  toolParams,
		ReadOnly:    true,
	}

	toolInst := &wikiReadTool{
		workspacePath: config.WorkspacePath,
	}

	return tool.NewInvokableTool(toolInfo, toolInst.readWiki, tool.WithOutputConverter(toolInst.convertOutput)), nil
}

type wikiReadTool struct {
	workspacePath string
}

func doReadWikiContent(ctx context.Context, wikiIDs []string, workspacePath string) ([]WikiReadItem, error) {
	wikiItems := []WikiReadItem{}
	// 实际实现：根据 wiki IDs 读取内容并转换为 CodeChunk
	for _, wikiID := range wikiIDs {
		wikiID = strings.TrimSuffix(wikiID, ".md")
		wikiID = strings.TrimSpace(wikiID)
		// 通过 storage.GlobalStorageService 读取
		wikiItem, err := storage.GlobalStorageService.GetWikiItemByID(wikiID)
		if err != nil {
			return nil, fmt.Errorf("failed to get wiki item: %w", err)
		}
		if wikiItem == nil {
			return nil, fmt.Errorf("wiki item not found for ID: %s", wikiID)
		}
		wikiItems = append(wikiItems, WikiReadItem{
			WikiID:  wikiItem.ID,
			Content: wikiItem.Content,
		})
	}
	return wikiItems, nil
}

func (w *wikiReadTool) readWiki(ctx context.Context, request *WikiReadRequest) (*WikiReadResponse, error) {
	log.Infof("call readWiki, request: %+v", request)
	// 使用提供的路径或默认工作空间路径
	targetPath := w.workspacePath

	if len(request.WikiIDs) == 0 {
		return &WikiReadResponse{
			WikiItems: []WikiReadItem{},
			Message:   "No wiki IDs provided",
		}, nil
	}

	// 读取指定的wiki内容
	chunks, err := doReadWikiContent(ctx, request.WikiIDs, targetPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read wiki content: %w", err)
	}

	message := fmt.Sprintf("Successfully read %d wiki items, returned %d chunks", len(request.WikiIDs), len(chunks))

	return &WikiReadResponse{
		WikiItems: chunks,
		Message:   message,
	}, nil
}

func (w *wikiReadTool) convertOutput(ctx context.Context, output interface{}) (*definition.ToolOutput, error) {
	response, ok := output.(*WikiReadResponse)
	if !ok {
		return nil, fmt.Errorf("expected *WikiReadResponse, got %T", output)
	}

	var outputBuilder strings.Builder
	outputBuilder.WriteString(response.Message)
	outputBuilder.WriteString("\n")

	if len(response.WikiItems) > 0 {
		outputBuilder.WriteString(fmt.Sprintf("\nRetrieved %d wiki items:\n", len(response.WikiItems)))
		for i, item := range response.WikiItems {
			outputBuilder.WriteString(fmt.Sprintf("%d. Wiki ID: %s\n", i+1, item.WikiID))
			// 只显示内容的前200个字符
			content := item.Content
			outputBuilder.WriteString(fmt.Sprintf("   Content: %s\n", content))
			outputBuilder.WriteString("\n")
		}
	} else {
		outputBuilder.WriteString("\nNo content retrieved.\n")
	}

	log.Infof("wikiRead output: %s", outputBuilder.String())
	log.Infof("wikiRead response: %+v", response)

	return &definition.ToolOutput{
		Content: outputBuilder.String(),
		RawData: response,
	}, nil
}

// ReadWikiContent 直接调用函数，供其他流程使用
// 功能与工具调用相同，但可以直接在代码中调用
func ReadWikiContent(ctx context.Context, wikiIDs []string, workspacePath string) ([]WikiReadItem, error) {
	if len(wikiIDs) == 0 {
		return []WikiReadItem{}, fmt.Errorf("wiki IDs are required")
	}

	// 直接调用底层实现函数
	wikiItems, err := doReadWikiContent(ctx, wikiIDs, workspacePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read wiki content: %w", err)
	}

	return wikiItems, nil
}

// ReadWikiContentWithMessage 直接调用函数，返回包含消息的完整响应
// 供需要详细信息的流程使用
func ReadWikiContentWithMessage(ctx context.Context, wikiIDs []string, workspacePath string) (*WikiReadResponse, error) {
	if len(wikiIDs) == 0 {
		return &WikiReadResponse{
			WikiItems: []WikiReadItem{},
			Message:   "No wiki IDs provided",
		}, nil
	}

	// 调用底层实现
	chunks, err := doReadWikiContent(ctx, wikiIDs, workspacePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read wiki content: %w", err)
	}

	message := fmt.Sprintf("Successfully read %d wiki items, returned %d chunks", len(wikiIDs), len(chunks))

	return &WikiReadResponse{
		WikiItems: chunks,
		Message:   message,
	}, nil
}

// ReadSingleWikiContent 直接调用函数，读取单个wiki文档
// 便于只需要读取单个文档的场景
func ReadSingleWikiContent(ctx context.Context, wikiID string, workspacePath string) (*WikiReadItem, error) {
	if wikiID == "" {
		return nil, fmt.Errorf("wiki ID is required")
	}

	wikiItems, err := doReadWikiContent(ctx, []string{wikiID}, workspacePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read wiki content: %w", err)
	}

	if len(wikiItems) == 0 {
		return nil, fmt.Errorf("wiki item not found: %s", wikiID)
	}

	return &wikiItems[0], nil
}
