package wiki

import (
	"context"
	"fmt"
	"strings"

	"cosy/deepwiki/support"
	cosyDefinition "cosy/definition"
	cosyErrors "cosy/errors"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/tool"
	"github.com/go-git/go-git/v5"
	"github.com/go-git/go-git/v5/plumbing"
	"github.com/go-git/go-git/v5/plumbing/object"
)

type GetCommitDiffConfig struct {
	WorkspacePath   string // 工作空间路径
	ExplanationDesc string // explanation字段的描述
}

type GetCommitDiffRequest struct {
	CommitHash string `json:"commit_hash"`
}

type GetCommitDiffResponse struct {
	CommitHash  string                      `json:"commit_hash"`
	CommitInfo  *cosyDefinition.CommitInfo  `json:"commit_info"`
	FileChanges []cosyDefinition.FileChange `json:"file_changes"`
	Summary     string                      `json:"summary"`
}

func NewGetCommitDiffTool(config *GetCommitDiffConfig) (tool.InvokableTool, error) {
	explanationDesc := "One sentence explanation as to why this tool is being used, and how it contributes to the goal."
	if config != nil && config.ExplanationDesc != "" {
		explanationDesc = config.ExplanationDesc
	}

	toolName := "get_commit_diff"
	toolDesc := `Get detailed diff information for a specific commit by its hash.
This tool is useful for:
1. Understanding what changes were made in a specific commit
2. Analyzing file modifications, additions, and deletions
3. Reviewing commit history and change patterns
4. Investigating specific code changes by commit hash

When using this tool:
- Provide the full commit hash or at least the first 7-8 characters
- The tool will return detailed information about file changes in that commit`

	toolParams := &definition.Schema{
		Type: "object",
		Properties: map[string]*definition.Schema{
			"commit_hash": {
				Type:        "string",
				Description: "The commit hash (full or abbreviated) to get diff information for",
			},
			"explanation": {
				Type:        "string",
				Description: explanationDesc,
			},
		},
		Required: []string{"commit_hash"},
	}

	toolInfo := &definition.ToolInfo{
		Name:        toolName,
		Description: toolDesc,
		Parameters:  toolParams,
		ReadOnly:    true,
	}

	toolInst := &getCommitDiffTool{
		workspacePath: config.WorkspacePath,
	}

	return tool.NewInvokableTool(toolInfo, toolInst.getCommitDiff, tool.WithOutputConverter(toolInst.convertOutput)), nil
}

type getCommitDiffTool struct {
	workspacePath string // 工作空间路径
}

func (t *getCommitDiffTool) getCommitDiff(ctx context.Context, request *GetCommitDiffRequest) (*GetCommitDiffResponse, error) {
	if request.CommitHash == "" {
		return nil, cosyErrors.New(cosyErrors.BadRequest, "commit hash cannot be empty")
	}

	// 初始化 Git 支持
	gitSupport, err := support.NewGitSupport(t.workspacePath)
	if err != nil {
		return nil, cosyErrors.New(cosyErrors.InternalError, fmt.Sprintf("failed to initialize git support: %s", err))
	}

	if !gitSupport.IsAvailable() {
		return nil, cosyErrors.New(cosyErrors.InternalError, "git repository not found in workspace")
	}

	repo := gitSupport.GetRepository()

	// 解析 commit hash
	hash, err := repo.ResolveRevision(plumbing.Revision(request.CommitHash))
	if err != nil {
		return nil, cosyErrors.New(cosyErrors.InternalError, fmt.Sprintf("failed to resolve commit hash %s: %s", request.CommitHash, err))
	}

	// 获取 commit 对象
	commit, err := repo.CommitObject(*hash)
	if err != nil {
		return nil, cosyErrors.New(cosyErrors.InternalError, fmt.Sprintf("failed to get commit object: %s", err))
	}

	// 获取文件变更信息
	fileChanges, err := t.getFileChanges(repo, commit)
	if err != nil {
		return nil, cosyErrors.New(cosyErrors.InternalError, fmt.Sprintf("failed to get file changes: %s", err))
	}

	// 构建 CommitInfo
	commitInfo := &cosyDefinition.CommitInfo{
		Hash:        commit.Hash.String(),
		Message:     strings.TrimSpace(commit.Message),
		Author:      commit.Author.Name,
		Date:        commit.Author.When,
		FileChanges: fileChanges,
	}

	// 生成摘要
	summary := t.generateSummary(commitInfo, fileChanges)

	return &GetCommitDiffResponse{
		CommitHash:  commit.Hash.String(),
		CommitInfo:  commitInfo,
		FileChanges: fileChanges,
		Summary:     summary,
	}, nil
}

func (t *getCommitDiffTool) getFileChanges(repo *git.Repository, commit *object.Commit) ([]cosyDefinition.FileChange, error) {
	var changes []cosyDefinition.FileChange

	// 如果是初始提交（没有父提交）
	if commit.NumParents() == 0 {
		tree, err := commit.Tree()
		if err != nil {
			return nil, err
		}
		err = tree.Files().ForEach(func(file *object.File) error {
			changes = append(changes, cosyDefinition.FileChange{
				Status: "Added",
				Path:   file.Name,
			})
			return nil
		})
		return changes, err
	}

	// 有父提交的情况
	parent, err := commit.Parent(0)
	if err != nil {
		return nil, err
	}

	parentTree, err := parent.Tree()
	if err != nil {
		return nil, err
	}

	currentTree, err := commit.Tree()
	if err != nil {
		return nil, err
	}

	diff, err := parentTree.Diff(currentTree)
	if err != nil {
		return nil, err
	}

	for _, change := range diff {
		fileChange := cosyDefinition.FileChange{
			Path: change.To.Name,
		}

		switch {
		case change.From.Name == "" && change.To.Name != "":
			fileChange.Status = "Added"
		case change.From.Name != "" && change.To.Name == "":
			fileChange.Status = "Deleted"
			fileChange.Path = change.From.Name
		case change.From.Name != change.To.Name:
			fileChange.Status = "Renamed"
			fileChange.OldPath = change.From.Name
		default:
			fileChange.Status = "Modified"
		}

		changes = append(changes, fileChange)
	}

	return changes, nil
}

func (t *getCommitDiffTool) generateSummary(commitInfo *cosyDefinition.CommitInfo, fileChanges []cosyDefinition.FileChange) string {
	var summary strings.Builder

	summary.WriteString(fmt.Sprintf("Commit: %s\n", commitInfo.Hash[:8]))
	summary.WriteString(fmt.Sprintf("Author: %s\n", commitInfo.Author))
	summary.WriteString(fmt.Sprintf("Date: %s\n", commitInfo.Date.Format("2006-01-02 15:04:05")))
	summary.WriteString(fmt.Sprintf("Message: %s\n\n", commitInfo.Message))

	if len(fileChanges) == 0 {
		summary.WriteString("No file changes in this commit.")
		return summary.String()
	}

	// 统计变更类型
	var added, modified, deleted, renamed int
	for _, change := range fileChanges {
		switch change.Status {
		case "Added":
			added++
		case "Modified":
			modified++
		case "Deleted":
			deleted++
		case "Renamed":
			renamed++
		}
	}

	summary.WriteString(fmt.Sprintf("File Changes Summary:\n"))
	if added > 0 {
		summary.WriteString(fmt.Sprintf("- %d files added\n", added))
	}
	if modified > 0 {
		summary.WriteString(fmt.Sprintf("- %d files modified\n", modified))
	}
	if deleted > 0 {
		summary.WriteString(fmt.Sprintf("- %d files deleted\n", deleted))
	}
	if renamed > 0 {
		summary.WriteString(fmt.Sprintf("- %d files renamed\n", renamed))
	}

	summary.WriteString(fmt.Sprintf("\nTotal: %d file(s) changed\n", len(fileChanges)))

	return summary.String()
}

func (t *getCommitDiffTool) convertOutput(ctx context.Context, output interface{}) (*definition.ToolOutput, error) {
	response, ok := output.(*GetCommitDiffResponse)
	if !ok {
		return nil, fmt.Errorf("invalid output type")
	}

	var result strings.Builder

	result.WriteString(fmt.Sprintf("Commit Diff Information for %s:\n\n", response.CommitHash[:8]))
	result.WriteString(response.Summary)

	if len(response.FileChanges) > 0 {
		result.WriteString("\nDetailed File Changes:\n")
		for _, change := range response.FileChanges {
			switch change.Status {
			case "Added":
				result.WriteString(fmt.Sprintf("+ %s\n", change.Path))
			case "Modified":
				result.WriteString(fmt.Sprintf("M %s\n", change.Path))
			case "Deleted":
				result.WriteString(fmt.Sprintf("- %s\n", change.Path))
			case "Renamed":
				result.WriteString(fmt.Sprintf("R %s -> %s\n", change.OldPath, change.Path))
			}
		}
	}

	return &definition.ToolOutput{
		RawData: response,
		Content: result.String(),
	}, nil
}
