package wiki

import (
	"context"
	"fmt"
	"path/filepath"
	"runtime"
	"strings"

	cosyErrors "cosy/errors"
	"cosy/indexing/chat_indexing/workspace_tree_indexing"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/tool"
)

type ListDirConfig struct {
	WorkspacePath   string //  工作空间路径
	ExplanationDesc string // explanation字段的描述
}
type ListDirRequest struct {
	RelativeWorkspacePath string `json:"relative_workspace_path"`
}
type FileInfo struct {
	FileName    string `json:"fileName"`
	Path        string `json:"path"`
	Size        int64  `json:"size"`
	IsDirectory bool   `json:"isDirectory"`
	FileCount   int    `json:"fileCount,omitempty"`
}
type ListDirResponse struct {
	Path     string `json:"path"`
	FileTree string `json:"file_tree"`
}

func NewListDirTool(config *ListDirConfig) (tool.InvokableTool, error) {
	explanationDesc := "One sentence explanation as to why this tool is being used, and how it contributes to the goal."
	if config != nil && config.ExplanationDesc != "" {
		explanationDesc = config.ExplanationDesc
	}
	toolName := "list_dir"
	toolDesc := `List the contents of a directory. Useful to try to understand the file structure before diving deeper into specific files.
When using this tool, the following rules should be followed:
1. Unless requested by the user, do not recursively check directories layer by layer; try to lock the directory location first before viewing.
`
	toolParams := &definition.Schema{
		Type: "object",
		Properties: map[string]*definition.Schema{
			"relative_workspace_path": {
				Type:        "string",
				Description: "Path to list contents of, relative to the workspace root. '.' is the root of the workspace",
			},
			"explanation": {
				Type:        "string",
				Description: explanationDesc,
			},
		},
		Required: []string{"relative_workspace_path"},
	}
	toolInfo := &definition.ToolInfo{
		Name:        toolName,
		Description: toolDesc,
		Parameters:  toolParams,
		ReadOnly:    true,
	}
	toolInst := &listDirTool{
		workspacePath: config.WorkspacePath,
	}
	return tool.NewInvokableTool(toolInfo, toolInst.list, tool.WithOutputConverter(toolInst.convertOutput)), nil
}

type listDirTool struct {
	workspacePath string //  工作空间路径
}

func (t *listDirTool) list(ctx context.Context, request *ListDirRequest) (*ListDirResponse, error) {
	if request.RelativeWorkspacePath == "." {
		request.RelativeWorkspacePath = ""
	}
	fullPath := t.workspacePath
	if request.RelativeWorkspacePath != "" && isSubPath(request.RelativeWorkspacePath, t.workspacePath) {
		// 兼容一下给了绝对路径的情况
		fullPath = request.RelativeWorkspacePath
	} else {
		fullPath = filepath.Join(t.workspacePath, request.RelativeWorkspacePath)
		// 确保 fullPath 仍在工作空间目录下，防止路径穿越
		relPath, err := filepath.Rel(t.workspacePath, fullPath)
		if err != nil {
			return nil, cosyErrors.New(cosyErrors.DirNotFound, fmt.Sprintf("invalid directory path: %s", err))
		}
		if strings.Contains(relPath, "..") {
			return nil, cosyErrors.New(cosyErrors.DirNotFound, "access denied: path is outside of workspace")
		}
	}
	treeIndexer, ok := workspace_tree_indexing.WorkspaceTreeFileIndexers.GetFileIndexer(t.workspacePath)
	if !ok {
		return nil, cosyErrors.New(cosyErrors.DirNotFound, "workspace index tree is nil")
	}
	dirTree, err := treeIndexer.WorkspaceTree.GetSubTree(request.RelativeWorkspacePath, 2000)
	if err != nil {
		return nil, cosyErrors.New(cosyErrors.DirNotFound, fmt.Sprintf("failed to read directory: %s", err))
	}
	return &ListDirResponse{
		Path:     fullPath,
		FileTree: dirTree,
	}, nil
}
func (t *listDirTool) convertOutput(ctx context.Context, output interface{}) (*definition.ToolOutput, error) {
	response, ok := output.(*ListDirResponse)
	if !ok {
		return nil, fmt.Errorf("invalid output type")
	}
	var result strings.Builder
	result.WriteString(fmt.Sprintf("Contents of directory %s:\n", response.Path))
	result.WriteString(response.FileTree)
	return &definition.ToolOutput{
		RawData: response,
		Content: result.String(),
	}, nil
}

// isSubPath 判断是否是子目录，windows系统路径忽略大小写
func isSubPath(child, parent string) bool {
	// 清理路径
	child = filepath.Clean(child)
	parentPath := parent
	// 在 Windows 下转换为小写以实现不区分大小写比较
	if runtime.GOOS == "windows" {
		parentPath = strings.ToLower(parentPath)
		child = strings.ToLower(child)
	}
	// 如果路径完全相同，也算是子路径
	if parentPath == child {
		return true
	}
	return strings.HasPrefix(child, parentPath)
}
