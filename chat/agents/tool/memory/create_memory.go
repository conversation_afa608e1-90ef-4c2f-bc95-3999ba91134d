package memory

import (
	"code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/tool"
	"context"
	"cosy/chat/agents/tool/common"
	cosyDefinition "cosy/definition"
	"cosy/memory"
	"cosy/memory/storage"
)

type CreateMemoryConfig struct {
	RequestId string
	SessionId string
}

type CreateMemoryResponse struct {
	Memory *storage.MemoryRecord
}

func NewCreateMemoryTool(config *CreateMemoryConfig) (tool.InvokableTool, error) {
	toolName := "create_memory"
	toolDesc := `Store important knowledge and lessons learned in the memory database for future reference by you.

Examples of context to save:
- **User Preferences (user_prefer)**:
  - Personal Information: Username, user password (scope: global).
  - Dialogue Preferences: Communication style, Communication language preferences, dialogue process, such as drafting a plan before writing code (scope: global).
  - Project-related preferences: unit test writing preferences, documentation preferences, annotation preferences, etc.(scope: workspace)
- **Project Configuration Information (project_info)**:
  - Technology Stack: Technologies, frameworks, libraries used in the project, such as specified unit testing frameworks, front-end component libraries, web frameworks, etc. (scope: workspace).
  - Project Configuration: Build tools, dependency management, project environment configuration, etc. (scope: workspace).
  - Environment Configuration: Runtime environment configuration, environment variables, dependencies, JDK paths, etc. (scope: workspace).
- **Project Specifications (project_specification)** (scope: workspace):
  - Development Specifications: Code writing standards (coding style, syntax format, naming conventions, etc.), programming practice standards (design patterns, code refactoring, etc.), unit testing standards, Code comment specifications (comment format, comment language preference, etc.), framework/component usage standards, security coding standards, etc.
  - Architecture Specifications: System architecture standards, interface design standards, application architecture standards, data architecture standards, etc.
  - Design Specifications: Component design standards, animation design standards, visual design standards, interaction design standards, etc.
- **Experience and Lessons (experience_lessons)**: (scope: workspace)
  - Common pain points or frustrations to avoid (MUST be specific enough to act on):
    - General optimization experiences from self-criticism.
    - Common pitfalls in tasks and how to avoid them.
  - General Best Practices Learned (excluding specific code or scene details):
    - Common methods discovered for more efficient problem-solving.
    - Better approaches for decomposing complex tasks.
    - Workflow preferences or requirements (MUST include concrete steps or rules)
  - Tool Usage Optimization:
    - Handling strategies after tool invocation failures.
    - Most effective tool combinations for task types.
    - Alternative tools for different scenarios.
    - File permission issues and correct access settings.
- Any other knowledge you think you need to remember that can be referenced in the future.

If USER asks to remember something (such as "以后回答XXX", "记住XXX", "回答都XXX", "不要忘记", "不要再次", "don't forget XXX", "remember XXX", "Keep XXX in mind", etc.), for something to be saved, or to create a memory, you MUST use this tool.
The knowledge USER require to remember needs to remain complete.
Before creating a new memory, first check to see if a semantically related memory already exists in the database. If found, update it instead of creating a duplicate one.

Use this tool to delete incorrect memories when necessary.`
	toolParams := &definition.Schema{
		Type: "object",
		Properties: map[string]*definition.Schema{
			"id": {
				Type:        "string",
				Description: "Id of an existing MEMORY to update or delete. When creating a new MEMORY, leave this blank.",
			},
			"title": {
				Type:        "string",
				Description: "Descriptive title for a new or updated MEMORY. This is required when creating or updating a memory. When deleting an existing MEMORY, leave this blank.",
			},
			"source": {
				Type:        "string",
				Description: "Set to 'user' if USER actively requests it to be remembered, other set ti 'auto'.",
			},
			"scope": {
				Type:        "string",
				Description: "If memory is workspace-related, use 'workspace', if it is globally common across projects, use 'global'.",
			},
			"content": {
				Type:        "string",
				Description: "Content of a new or updated MEMORY. When deleting an existing MEMORY, leave this blank.",
			},
			"keywords": {
				Type:        "string",
				Description: "Keywords to associate with the MEMORY, using English comma separators. These will be used to filter or retrieve the MEMORY. Only used when creating a new MEMORY.",
			},
			"category": {
				Type:        "string",
				Description: "Memory category. Must be one of 'user_prefer', 'project_info', 'project_specification', 'experience_lessons'",
			},
			"action": {
				Type:        "string",
				Description: "The type of action to take on the MEMORY. Must be one of 'create', 'update', or 'delete'",
			},
			"explanation": {
				Type:        "string",
				Description: common.ExplanationDefaultDesc,
			},
		},
		Required: []string{"title", "source", "scope", "content", "keywords", "category", "action"},
	}
	toolInfo := &definition.ToolInfo{
		Name:        toolName,
		Description: toolDesc,
		Parameters:  toolParams,
	}
	toolInst := &createMemoryTool{
		requestId: config.RequestId,
		sessionId: config.SessionId,
	}
	return tool.NewInvokableTool(toolInfo, toolInst.create, tool.WithOutputConverter(toolInst.convertOutput)), nil
}

type createMemoryTool struct {
	requestId string
	sessionId string
}

func (t *createMemoryTool) create(ctx context.Context, request *cosyDefinition.CreateMemoryRequest) (*CreateMemoryResponse, error) {
	shortMemory, err := memory.GlobalMemoryService.SaveMemory(ctx, t.sessionId, t.requestId, request)
	if err != nil {
		return nil, err
	}
	return &CreateMemoryResponse{
		Memory: &shortMemory,
	}, nil
}

func (t *createMemoryTool) convertOutput(ctx context.Context, output interface{}) (*definition.ToolOutput, error) {
	//response, ok := output.(*CreateMemoryResponse)
	//if !ok {
	//	return nil, fmt.Errorf("expected *ListDirResponse, got %T", output)
	//}
	return &definition.ToolOutput{
		Content: "create memory success",
		RawData: output,
	}, nil
}
