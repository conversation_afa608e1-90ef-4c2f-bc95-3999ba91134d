package codebase

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	searchApi "gitlab.alibaba-inc.com/cosy/context-search-engine/pkg/api"
)

func TestNewLingmaRerankProvider(t *testing.T) {
	provider := NewLingmaRerankProvider()
	assert.NotNil(t, provider)

	// Verify it implements the interface
	var _ searchApi.RerankProvider = provider
}

func TestNewLLMRerankProvider(t *testing.T) {
	provider := NewLLMRerankProvider()
	assert.NotNil(t, provider)

	// Verify it implements the interface
	var _ searchApi.RerankProvider = provider
}

func TestLingmaRerankProviderAdapter_EmptyDocuments(t *testing.T) {
	provider := NewLingmaRerankProvider()

	ctx := context.Background()
	results, err := provider.RerankDocuments(ctx, "test query", []string{}, 5)

	assert.NoError(t, err)
	assert.Empty(t, results)
}

func TestLingmaRerankProviderAdapter_LLMEmptyDocuments(t *testing.T) {
	provider := NewLingmaRerankProvider()

	ctx := context.Background()
	results, err := provider.LLMRerankDocuments(ctx, "test query", []string{}, 5)

	assert.NoError(t, err)
	assert.Empty(t, results)
}

// Note: Full integration test would require actual API access
// This test focuses on the adapter interface and basic functionality
func TestLingmaRerankProviderAdapter_Interface(t *testing.T) {
	provider := NewLingmaRerankProvider()
	adapter, ok := provider.(*lingmaRerankProviderAdapter)

	assert.True(t, ok)
	assert.NotNil(t, adapter.reranker)
	assert.NotNil(t, adapter.llmReranker)
}

func TestLingmaRerankProviderAdapter_BothMethods(t *testing.T) {
	provider := NewLingmaRerankProvider()

	// Test that both methods are available
	ctx := context.Background()
	query := "test query"
	documents := []string{}

	// Test RerankDocuments method
	results1, err1 := provider.RerankDocuments(ctx, query, documents, 5)
	assert.NoError(t, err1)
	assert.Empty(t, results1)

	// Test LLMRerankDocuments method
	results2, err2 := provider.LLMRerankDocuments(ctx, query, documents, 5)
	assert.NoError(t, err2)
	assert.Empty(t, results2)
}
