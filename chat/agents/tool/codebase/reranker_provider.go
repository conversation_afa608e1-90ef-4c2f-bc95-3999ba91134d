package codebase

import (
	"context"
	"cosy/components"
	"cosy/log"

	searchApi "gitlab.alibaba-inc.com/cosy/context-search-engine/pkg/api"
)

// lingmaRerankProviderAdapter adapts the LingmaReranker to the context-search-engine RerankProvider interface
type lingmaRerankProviderAdapter struct {
	reranker    *components.LingmaReranker
	llmReranker *components.LLMReranker
}

// llmRerankProviderAdapter adapts the LLMReranker to the context-search-engine RerankProvider interface
type llmRerankProviderAdapter struct {
	llmReranker *components.LLMReranker
}

// NewLingmaRerankProvider creates a new rerank provider adapter using LingmaReranker
func NewLingmaRerankProvider() searchApi.RerankProvider {
	return &lingmaRerankProviderAdapter{
		reranker:    components.NewLingmaReranker(),
		llmReranker: components.NewLLMReranker(),
	}
}

// NewLLMRerankProvider creates a new rerank provider adapter using LLMReranker
func NewLLMRerankProvider() searchApi.RerankProvider {
	return &llmRerankProviderAdapter{
		llmReranker: components.NewLLMReranker(),
	}
}

// RerankDocuments implements the RerankProvider interface using the basic API-based reranker
func (l *lingmaRerankProviderAdapter) RerankDocuments(ctx context.Context, query string, documents []string, limit int) ([]searchApi.RerankResult, error) {
	if len(documents) == 0 {
		return []searchApi.RerankResult{}, nil
	}

	// Pass all documents to the reranker, let it handle the limit
	docsToRerank := documents

	// Call the LingmaReranker
	response, err := l.reranker.RerankDocuments(ctx, query, docsToRerank, limit)
	if err != nil {
		log.Errorf("Failed to rerank documents: %v", err)
		return nil, err
	}

	// Convert the response to the expected format
	results := make([]searchApi.RerankResult, 0, len(response.Output.Results))
	for _, result := range response.Output.Results {
		rerankResult := searchApi.RerankResult{
			Index:          result.Index,
			RelevanceScore: result.RelevanceScore,
		}
		results = append(results, rerankResult)
	}

	// The limit has already been applied by the reranker

	log.Debugf("Reranked %d documents, returned %d results", len(documents), len(results))
	return results, nil
}

// LLMRerankDocuments implements the RerankProvider interface using the LLM-based reranker
func (l *lingmaRerankProviderAdapter) LLMRerankDocuments(ctx context.Context, query string, documents []string, limit int) ([]searchApi.RerankResult, error) {
	if len(documents) == 0 {
		return []searchApi.RerankResult{}, nil
	}

	// Apply limit to input documents if specified and less than document count
	docsToRerank := documents
	if limit > 0 && limit < len(documents) {
		// Don't truncate input - let the LLM reranker handle all documents and return top N
		// The limit will be applied by the reranker's TopN parameter
	}

	// Call the LLMReranker
	response, err := l.llmReranker.RerankDocuments(ctx, query, docsToRerank, limit)
	if err != nil {
		log.Errorf("Failed to LLM rerank documents: %v", err)
		return nil, err
	}

	// Convert the response to the expected format
	results := make([]searchApi.RerankResult, 0, len(response.Output.Results))
	for _, result := range response.Output.Results {
		rerankResult := searchApi.RerankResult{
			Index:          result.Index,
			RelevanceScore: result.RelevanceScore,
		}
		results = append(results, rerankResult)
	}

	// The limit has already been applied by the reranker

	log.Debugf("LLM reranked %d documents, returned %d results", len(documents), len(results))
	return results, nil
}

// RerankDocuments implements the RerankProvider interface using the LLM-based reranker
func (l *llmRerankProviderAdapter) RerankDocuments(ctx context.Context, query string, documents []string, limit int) ([]searchApi.RerankResult, error) {
	return l.LLMRerankDocuments(ctx, query, documents, limit)
}

// LLMRerankDocuments implements the RerankProvider interface using the LLM-based reranker
func (l *llmRerankProviderAdapter) LLMRerankDocuments(ctx context.Context, query string, documents []string, limit int) ([]searchApi.RerankResult, error) {
	if len(documents) == 0 {
		return []searchApi.RerankResult{}, nil
	}

	// Pass all documents to the reranker, let it handle the limit
	docsToRerank := documents

	// Call the LLMReranker (which has built-in fallback to LingmaReranker)
	response, err := l.llmReranker.RerankDocuments(ctx, query, docsToRerank, limit)
	if err != nil {
		log.Errorf("Failed to LLM rerank documents: %v", err)
		return nil, err
	}

	// Convert the response to the expected format
	results := make([]searchApi.RerankResult, 0, len(response.Output.Results))
	for _, result := range response.Output.Results {
		rerankResult := searchApi.RerankResult{
			Index:          result.Index,
			RelevanceScore: result.RelevanceScore,
		}
		results = append(results, rerankResult)
	}

	// The limit has already been applied by the reranker

	log.Debugf("LLM reranked %d documents, returned %d results", len(documents), len(results))
	return results, nil
}
