package codebase

import (
	"context"
	"fmt"
	"path/filepath"

	toolCommon "cosy/chat/agents/tool/common"
	common2 "cosy/chat/chains/common"
	"cosy/codebase/recommend"
	"cosy/definition"
	"cosy/errors"
	"cosy/indexing/chat_indexing/workspace_tree_indexing"
	"cosy/indexing/completion_indexing"
	"cosy/util/session"

	agentDef "code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/tool"
)

type RecommendFileConfig struct {
	WorkspacePath            string // 工作空间路径
	WorkspaceTreeFileIndexer *workspace_tree_indexing.WorkspaceTreeFileIndexer
	MetaFileIndexer          *completion_indexing.MetaFileIndexer
	MaxResultCount           int    // 返回结果的最大数量
	ExplanationDesc          string // explanation字段的描述
}

type RecommendFileRequest struct {
	FilePath     string   `json:"file_path"`
	StartLine    int      `json:"start_line"`
	EndLine      int      `json:"end_line"`
	Query        string   `json:"query"`
	SymbolKey    string   `json:"symbol_key"`
	MaxCount     int      `json:"max_count"`
	ExcludePaths []string `json:"exclude_paths"`
}

type RecommendFileResponse struct {
	RecommendedFiles []RecommendationFileItem `json:"recommended_files"`
	TotalCount       int                      `json:"total_count"`
}

type RecommendationFileItem struct {
	FilePath      string  `json:"file_path"`
	FileContent   string  `json:"file_content"`
	Score         float64 `json:"score"`
	RecommendType string  `json:"recommend_type"`
}

func NewRecommendFileTool(config *RecommendFileConfig) (tool.InvokableTool, error) {
	if config == nil {
		return nil, fmt.Errorf("config cannot be nil")
	}
	if config.WorkspaceTreeFileIndexer == nil {
		return nil, fmt.Errorf("WorkspaceTreeFileIndexer cannot be nil")
	}
	if config.MetaFileIndexer == nil {
		return nil, fmt.Errorf("MetaFileIndexer cannot be nil")
	}

	maxResultCount := 10
	if config.MaxResultCount > 0 {
		maxResultCount = config.MaxResultCount
	}
	explanationDesc := toolCommon.ExplanationDefaultDesc
	if config.ExplanationDesc != "" {
		explanationDesc = config.ExplanationDesc
	}

	toolName := "recommend_file"
	toolDesc := `Recommend related files based on the content and location of an input file. This tool leverages semantic similarity of file content, code reference relationships, and file path similarity to recommend related files.

Use cases:
- Find other files related to the current file
- Discover files that may need to be modified together
- Find files implementing similar functionality
- Discover dependency files based on code reference relationships

Recommendation strategies:
- Recommend based on code references and dependency relationships
- Consider file path similarity
- Score based on reference frequency and hierarchy levels
- Support excluding files from specific paths`

	toolParams := &agentDef.Schema{
		Type: "object",
		Properties: map[string]*agentDef.Schema{
			"file_path": {
				Type:        "string",
				Description: "Absolute or relative path of the file to be analyzed. If it's a relative path, it will be resolved based on the workspace root directory.",
			},
			"start_line": {
				Type:        "number",
				Description: "Starting line number of the analysis range (0-based indexing). If not specified or 0, the entire file will be analyzed.",
			},
			"end_line": {
				Type:        "number",
				Description: "Ending line number of the analysis range (0-based indexing, inclusive). If not specified or 0, the entire file will be analyzed.",
			},
			"query": {
				Type:        "string",
				Description: "Optional query string to further filter recommendation results.",
			},
			"symbol_key": {
				Type:        "string",
				Description: "Optional symbol key for recommendations based on specific symbols.",
			},
			"max_count": {
				Type:        "number",
				Description: fmt.Sprintf("Maximum number of recommended files to return, default is %d.", maxResultCount),
			},
			"exclude_paths": {
				Type: "array",
				Items: &agentDef.Schema{
					Type: "string",
				},
				Description: "List of file paths to exclude, supporting both absolute and relative paths.",
			},
			"explanation": {
				Type:        "string",
				Description: explanationDesc,
			},
		},
		Required: []string{"file_path"},
	}

	toolInfo := &agentDef.ToolInfo{
		Name:        toolName,
		Description: toolDesc,
		Parameters:  toolParams,
		ReadOnly:    true,
	}

	recommender := &fileRecommender{
		workspacePath:            config.WorkspacePath,
		workspaceTreeFileIndexer: config.WorkspaceTreeFileIndexer,
		metaFileIndexer:          config.MetaFileIndexer,
		maxResultCount:           maxResultCount,
	}

	return tool.NewInvokableTool(toolInfo, recommender.recommend, tool.WithOutputConverter(recommender.convertOutput)), nil
}

type fileRecommender struct {
	workspacePath            string
	workspaceTreeFileIndexer *workspace_tree_indexing.WorkspaceTreeFileIndexer
	metaFileIndexer          *completion_indexing.MetaFileIndexer
	maxResultCount           int
}

func (r *fileRecommender) recommend(ctx context.Context, request *RecommendFileRequest) (*RecommendFileResponse, error) {
	if request == nil {
		return nil, errors.New(errors.ToolInvalidArguments, "request cannot be nil")
	}

	if request.FilePath == "" {
		return nil, errors.New(errors.ToolInvalidArguments, "file path cannot be empty")
	}

	// 处理文件路径
	filePath := request.FilePath
	if !filepath.IsAbs(filePath) {
		filePath = filepath.Join(r.workspacePath, filePath)
	}

	// 设置默认的最大数量
	maxCount := r.maxResultCount
	if request.MaxCount > 0 {
		maxCount = request.MaxCount
	}

	// 处理排除路径
	excludePaths := make(map[string]bool)
	if request.ExcludePaths != nil {
		for _, excludePath := range request.ExcludePaths {
			if !filepath.IsAbs(excludePath) {
				excludePath = filepath.Join(r.workspacePath, excludePath)
			}
			excludePaths[excludePath] = true
		}
	}

	// 构建推荐参数
	recommendParams := &recommend.RecommendParams{
		WorkspacePath: r.workspacePath,
		FilePath:      filePath,
		LineRange: definition.LineRange{
			StartLine: uint32(request.StartLine),
			EndLine:   uint32(request.EndLine),
		},
		Query:        request.Query,
		SymbolKey:    request.SymbolKey,
		MaxCount:     maxCount,
		ExcludePaths: excludePaths,
	}

	// 创建推荐器实例
	baseRecommender := recommend.NewBaseRecommender(r.workspaceTreeFileIndexer, r.metaFileIndexer)

	// 调用推荐方法
	err, recommendedFiles := baseRecommender.RecommendFile(ctx, recommendParams)
	if err != nil {
		return nil, errors.New(errors.ToolInternalError, fmt.Sprintf("failed to recommend files: %v", err))
	}

	// 转换结果格式
	var recommendationItems []RecommendationFileItem
	for _, file := range recommendedFiles {
		recommendationItems = append(recommendationItems, RecommendationFileItem{
			FilePath:      file.FilePath,
			FileContent:   file.FileContent,
			Score:         file.Score,
			RecommendType: file.RecommendType,
		})
	}

	// 添加会话上下文
	if sessionId := ctx.Value(common2.KeySessionId); sessionId != nil {
		contexts := make([]session.SessionFlowContext, 0)
		for _, file := range recommendedFiles {
			contexts = append(contexts, session.SessionFlowContext{
				ContextKey:    file.FilePath,
				ContextType:   session.SessionFlowFileContext,
				ContextWeight: 2,
			})
		}
		session.AddSessionContext(sessionId.(string), contexts, true)
	}

	return &RecommendFileResponse{
		RecommendedFiles: recommendationItems,
		TotalCount:       len(recommendationItems),
	}, nil
}

func (r *fileRecommender) convertOutput(ctx context.Context, output interface{}) (*agentDef.ToolOutput, error) {
	response, ok := output.(*RecommendFileResponse)
	if !ok {
		return nil, fmt.Errorf("invalid output type")
	}

	// 构建输出内容
	content := fmt.Sprintf("Found %d related files:\n\n", response.TotalCount)

	for i, file := range response.RecommendedFiles {
		relPath := file.FilePath
		if filepath.IsAbs(file.FilePath) && r.workspacePath != "" {
			if rel, err := filepath.Rel(r.workspacePath, file.FilePath); err == nil {
				relPath = rel
			}
		}

		content += fmt.Sprintf("%d. **%s** (Score: %.3f, Type: %s)\n",
			i+1, relPath, file.Score, file.RecommendType)

		if file.FileContent != "" {
			content += fmt.Sprintf("   Content preview: %s\n",
				truncateString(file.FileContent, 100))
		}
		content += "\n"
	}

	if response.TotalCount == 0 {
		content = "No related files found. Possible reasons:\n- File does not exist or is not accessible\n- File has no related code references\n- All related files were filtered out by exclude paths"
	}

	return &agentDef.ToolOutput{
		Content: content,
		RawData: response,
	}, nil
}

// truncateString 截断字符串到指定长度
func truncateString(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}
	return s[:maxLen] + "..."
}
