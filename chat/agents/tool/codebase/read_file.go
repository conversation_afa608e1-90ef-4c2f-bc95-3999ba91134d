package codebase

import (
	"context"
	"fmt"
	"os"
	"path"
	"path/filepath"
	"sort"
	"strings"

	toolCommon "cosy/chat/agents/tool/common"
	"cosy/chat/agents/tool/file"
	common2 "cosy/chat/chains/common"
	"cosy/codebase"
	"cosy/errors"
	"cosy/indexing"
	"cosy/log"
	"cosy/tokenizer"
	"cosy/util"
	"cosy/util/rag"
	"cosy/util/session"

	"github.com/agext/levenshtein"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/tool"
)

const (
	// MaxViewLines 一次最多可查看的行数
	MaxViewLines = 300
	// MinViewLines 最少需要查看的行数
	MinViewLines = 200
)

type ReadFileConfig struct {
	WorkspacePath        string // 工作空间路径
	MaxLineCount         int    // 返回结果的最大行数
	FileIndexer          *indexing.ProjectFileIndex
	ExplanationDesc      string // explanation字段的描述
	DependencyTokenLimit int    // 依赖信息的token限制，默认500
}
type ReadFileRequest struct {
	FilePath         string `json:"file_path"`
	StartLine        int    `json:"start_line"`
	EndLine          int    `json:"end_line"`
	ViewDependencies bool   `json:"view_dependencies"`
	ReadEntireFile   bool   `json:"read_entire_file"`
}
type ReadFileResponse struct {
	Path           string `json:"path"`
	FileContent    string `json:"fileContent"`
	StartLine      int    `json:"startLine"`
	EndLine        int    `json:"endLine"`
	TotalLineCount int    `json:"totalLineCount"`
	Dependencies   string `json:"dependencies"`
	OutOfRange     bool   `json:"outOfRange"`
}

func NewReadFileTool(config *ReadFileConfig) (tool.InvokableTool, error) {
	maxLineCount := MaxViewLines
	if config != nil && config.MaxLineCount > 0 {
		maxLineCount = config.MaxLineCount
	}
	explanationDesc := toolCommon.ExplanationDefaultDesc
	if config != nil && config.ExplanationDesc != "" {
		explanationDesc = config.ExplanationDesc
	}
	toolName := "read_file"
	toolDesc := `Read the contents of a file and optionally its dependencies.
	The output will include file contents, file path, and line summary.
	Note that this call can view at most %d lines at a time and %d lines minimum.
	
	IMPORTANT: When working with code files, understanding their dependencies is CRITICAL for:
	1. Modifying the file correctly (to maintain compatibility with dependent code)
	2. Generating accurate unit tests (to properly mock dependencies)
	3. Understanding the complete context of the code's functionality
	
	You should always set view_dependencies=true when:
	- You need to modify a file (to avoid breaking existing functionality)
	- You're generating unit tests for a file (to properly understand objects/functions to mock)
	- You need to understand type definitions, interfaces, or imported functions used in the file
	- Working with complex codebases where files have interdependencies
	
	When using this tool, ensure you have the COMPLETE context. This is your responsibility.
	If the retrieved range is insufficient and relevant information might be outside the visible range, call this tool again to fetch additional content.
	You can read the entire file, but this is often wasteful and slow. Reading the entire file is only allowed if it has been edited or manually attached to the conversation by the user.
	If the returned content exceeds %d lines, it will be truncated. Please read the file in sections (e.g., by specifying line ranges)
`
	toolDesc = fmt.Sprintf(toolDesc, MaxViewLines, MinViewLines, config.MaxLineCount)
	toolParams := &definition.Schema{
		Type: "object",
		Properties: map[string]*definition.Schema{
			"file_path": {
				Type:        "string",
				Description: "Absolute path to the file to read.",
			},
			"start_line": {
				Type:        "integer",
				Description: "The line number to start reading from, 0-based.",
			},
			"end_line": {
				Type:        "integer",
				Description: "The inclusive line number to end reading at, 0-based.",
			},
			"view_dependencies": {
				Type:        "boolean",
				Description: "Whether to view the dependencies of the file. Set to TRUE when modifying code, generating tests, or needing to understand imported types, interfaces or functions. Critical for ensuring code compatibility and proper test generation.",
			},
			"explanation": {
				Type:        "string",
				Description: explanationDesc + " Include why you need dependencies if view_dependencies=true.",
			},
			"read_entire_file": {
				Type:        "boolean",
				Description: "Whether to read the entire file. Defaults to false.",
			},
		},
		Required: []string{"file_path", "start_line", "end_line", "read_entire_file"},
	}
	toolInfo := &definition.ToolInfo{
		Name:        toolName,
		Description: toolDesc,
		Parameters:  toolParams,
		ReadOnly:    true,
	}
	dependencyTokenLimit := config.DependencyTokenLimit
	if dependencyTokenLimit <= 0 {
		dependencyTokenLimit = 500 // 默认500 token限制
	}

	reader := &fileReader{
		workspacePath:        config.WorkspacePath,
		maxLineCount:         maxLineCount,
		fileIndexer:          config.FileIndexer,
		dependencyTokenLimit: dependencyTokenLimit,
	}
	return tool.NewInvokableTool(toolInfo, reader.read, tool.WithOutputConverter(reader.convertOutput)), nil
}

type fileReader struct {
	workspacePath        string
	maxLineCount         int
	fileIndexer          *indexing.ProjectFileIndex
	dependencyTokenLimit int
}

func (r *fileReader) read(ctx context.Context, request *ReadFileRequest) (*ReadFileResponse, error) {
	if request == nil {
		return nil, errors.New(errors.ToolInvalidArguments, "request cannot be nil")
	}
	if request.FilePath == "" {
		return nil, errors.New(errors.ToolInvalidArguments, "file path cannot be empty")
	}
	if !filepath.IsAbs(request.FilePath) {
		// 非绝对路径，拼上工程目录
		request.FilePath = filepath.Join(r.workspacePath, request.FilePath)
	}
	// Read the file content
	fileContent, err := readFileContent(ctx, r.workspacePath, request.FilePath)
	if err != nil {
		return nil, err
	}
	// Split the content into lines
	lines := strings.Split(string(fileContent), "\n")
	totalLineCount := len(lines)
	startLine, endLine, outOfRange := getLineRange(request.StartLine, request.EndLine, totalLineCount, r.maxLineCount, request.ReadEntireFile)
	// Extract the requested lines
	var contentBuilder strings.Builder
	for i := startLine; i <= endLine; i++ {
		contentBuilder.WriteString(lines[i])
		if i < endLine {
			contentBuilder.WriteString("\n")
		}
	}
	var dependencies string
	if request.ViewDependencies {
		dependencies, err = r.getDependencies(ctx, request.FilePath)
		if err != nil {
			log.Errorf("failed to get file dependencies. err: %v", err)
		}
	}
	value := ctx.Value(common2.KeySessionId)
	if value != nil {
		session.AddSessionContext(value.(string), []session.SessionFlowContext{
			{
				ContextKey:    request.FilePath,
				ContextType:   session.SessionFlowFileContext,
				ContextWeight: 3,
			},
		}, true)
	}
	return &ReadFileResponse{
		Path:           request.FilePath,
		FileContent:    contentBuilder.String(),
		StartLine:      startLine,
		EndLine:        endLine,
		TotalLineCount: totalLineCount,
		Dependencies:   dependencies,
		OutOfRange:     outOfRange,
	}, nil
}
func (r *fileReader) getDependencies(ctx context.Context, filepath string) (graphInfoPayload string, err error) {
	tks, err := codebase.GetToolKits(r.fileIndexer, nil)
	if err != nil {
		return "", err
	}
	language := util.GetLanguageByFilePath(filepath)
	result := tks.GetFileDependencies(ctx, codebase.CodebaseToolOptions{
		WorkspaceURI:   r.workspacePath,
		FilePath:       filepath,
		SearchLanguage: language,
	})
	if result.Error != "" {
		return "", fmt.Errorf("failed to get file dependencies: %s", result.Error)
	}

	dependencies := result.Result.GraphInfo
	if dependencies == "" {
		return "", nil
	}

	// 检查依赖信息的token数量并进行截断
	return r.truncateDependencies(dependencies), nil
}
func (r *fileReader) convertOutput(ctx context.Context, output interface{}) (*definition.ToolOutput, error) {
	response, ok := output.(*ReadFileResponse)
	if !ok {
		return nil, fmt.Errorf("expected *ReadFileResponse, got %T", output)
	}
	var outputBuilder strings.Builder
	// 拼接文件信息和内容
	if response.OutOfRange {
		outputBuilder.WriteString("Start line exceeds total lines in the file. Showing the end of the file instead.\n")
	}
	headerInfo := fmt.Sprintf("Contents of %s, from line %d-%d (total %d lines)\n",
		response.Path,
		response.StartLine,
		response.EndLine,
		response.TotalLineCount)
	outputBuilder.WriteString(headerInfo)
	outputBuilder.WriteString("```\n")
	outputBuilder.WriteString(response.FileContent)
	outputBuilder.WriteString("\n```\n")
	if response.Dependencies != "" {
		outputBuilder.WriteString("Dependencies:\n")
		outputBuilder.WriteString("```mermaid\n")
		outputBuilder.WriteString(response.Dependencies)
		outputBuilder.WriteString("\n```\n")
	}
	return &definition.ToolOutput{
		Content: outputBuilder.String(),
		RawData: response,
	}, nil
}

// readFileContent 有如下步骤
// 1. 尝试直接读取文件，如果成功则直接返回
// 2. 处理可能存在的模型生成路径错误问题：尝试转换路径，重新读取文件
// 3. 如果父目录存在且位于项目根目录下，则调用 list_dir 查找相似文件
// 4. 调用 search_file 检查该文件名在项目中是否存在
func readFileContent(ctx context.Context, workspacePath string, filePath string) ([]byte, error) {
	fileContent, err := os.ReadFile(filePath)
	if err == nil {
		return fileContent, nil
	}
	readFileErr := err

	// 判断是否是权限问题，抛出权限问题错误
	if os.IsPermission(err) {
		return nil, errors.New(errors.FileNotFound, fmt.Errorf("permission denied: %s", filePath).Error())
	}

	// 尝试转换路径，重新读取文件
	convertedFilePath := util.NormalizePath(filePath)
	if convertedFilePath != "" {
		convertedFileContent, err := os.ReadFile(convertedFilePath)
		if err == nil {
			return convertedFileContent, nil
		}
	}

	fileName := filepath.Base(filePath)
	fileDir := filepath.Dir(filePath)

	// 如果父目录存在且位于项目根目录下，则调用 list_dir 查找相似文件
	if _, err := os.Stat(fileDir); err == nil && util.IsSubPath(fileDir, workspacePath) {
		relPath, _ := filepath.Rel(workspacePath, fileDir)
		if _, fileInfo, err := file.ListDir(workspacePath, relPath); err == nil {
			if similarFiles := getSimilarFileList(fileInfo, fileName); len(similarFiles) > 0 {
				return nil, errors.New(
					errors.FileNotFound,
					fmt.Sprintf("The file does not exist, but the following similar files are found. Ask the user if they want to read?\n %v", similarFiles))
			}
		}
	}

	// 调用 search_file 检查该文件名在项目中是否存在
	if fileInfo, err := file.SearchFile(ctx, fileName, workspacePath, 5); err == nil && len(fileInfo) > 0 {
		sameNameFiles := make([]string, 0, len(fileInfo))
		for _, fi := range fileInfo {
			sameNameFiles = append(sameNameFiles, fi.Path)
		}
		return nil, errors.New(
			errors.FileNotFound,
			fmt.Sprintf("The file does not exist, but the following files with the same name are found. Ask the user if they want to read?\n %v", sameNameFiles))

	}

	// 抛出文件不存在错误
	return nil, errors.New(errors.FileNotFound, fmt.Errorf("failed to read file: %w", readFileErr).Error())
}

func getSimilarFileList(fileInfo []file.FileInfo, fileName string) []string {
	threshold := 0.8
	maxReturnCount := 2
	type fileSimilarity struct {
		name       string
		path       string
		similarity float64
	}
	similarities := make([]fileSimilarity, 0, len(fileInfo))

	// 遍历所有文件计算相似度
	for _, fi := range fileInfo {
		sim := calculateSimilarity(fi.FileName, fileName)
		if sim > threshold {
			similarities = append(similarities, fileSimilarity{fi.FileName, fi.Path, sim})
		}
	}

	// 获取最最相似的文件名
	sort.Slice(similarities, func(i, j int) bool {
		return similarities[i].similarity > similarities[j].similarity
	})
	result := make([]string, 0, maxReturnCount)
	for i := 0; i < len(similarities) && i < maxReturnCount; i++ {
		result = append(result, path.Join(similarities[i].path, similarities[i].name))
	}

	return result
}

// calculateSimilarity 计算两个字符串之间的相似度，基于 Levenshtein 距离，结果范围为 [0, 1]
// 其中 1 表示完全匹配，0 表示完全不同
func calculateSimilarity(s1 string, s2 string) float64 {
	distance := levenshtein.Distance(s1, s2, nil)

	maxLength := len(s1)
	if len(s2) > maxLength {
		maxLength = len(s2)
	}
	if maxLength == 0 {
		return 1.0
	}

	// 相似度 = 1 - (编辑距离 / 最长字符串长度)
	return 1 - float64(distance)/float64(maxLength)
}
func getLineRange(startLine, endLine, totalLineCount, maxLineCount int, readEntireFile bool) (int, int, bool) {
	// Model may use negative numbers to indicate reading from the back to the front.
	if startLine < 0 {
		startLine = totalLineCount + startLine
	}
	if endLine < 0 {
		endLine = totalLineCount + endLine
	}
	// Prevent the start line from being larger than the end line
	if startLine > endLine {
		startLine, endLine = endLine, startLine
	}
	outOfRange := false
	if readEntireFile {
		// 如果读全文
		startLine = 0
		endLine = totalLineCount - 1
	} else {
		// Validate start line numbers
		if startLine >= totalLineCount {
			requestLineCount := endLine - startLine
			if requestLineCount < maxLineCount {
				startLine = totalLineCount - requestLineCount
			} else {
				startLine = totalLineCount - maxLineCount
			}
			outOfRange = true
		}
		if startLine < 0 {
			startLine = 0
		}
		// Validate end line number
		if endLine <= 0 || endLine < startLine || endLine >= totalLineCount {
			endLine = totalLineCount - 1
		}
	}
	// Enforce maximum line count
	if endLine-startLine+1 > maxLineCount {
		endLine = startLine + maxLineCount - 1
	}
	return startLine, endLine, outOfRange
}

// truncateDependencies 根据token限制截断依赖信息，保持行的完整性
func (r *fileReader) truncateDependencies(dependencies string) string {
	if dependencies == "" {
		return ""
	}

	// 使用tokenizer计算token数量
	tokenCount, err := tokenizer.CalQwenTokenCount(dependencies)
	if err != nil {
		// 如果无法计算token，使用简单的长度估算（一般1 token 约3个字符）
		log.Warnf("Failed to calculate token count for dependencies: %v", err)
		estimatedTokens := len(dependencies) / 3
		if estimatedTokens <= r.dependencyTokenLimit {
			return dependencies
		}
		// 简单字符截断
		maxChars := r.dependencyTokenLimit * 3
		if len(dependencies) <= maxChars {
			return dependencies
		}
		if maxChars > 0 {
			truncated := dependencies[:maxChars]
			return truncated + "\n\n[... Dependency information truncated due to " + fmt.Sprintf("%d", r.dependencyTokenLimit) + " token limit ...]"
		}
		return "[... Dependency information truncated due to " + fmt.Sprintf("%d", r.dependencyTokenLimit) + " token limit ...]"
	}

	// 如果没有超过token限制，直接返回
	if tokenCount <= r.dependencyTokenLimit {
		return dependencies
	}

	// 使用util/rag中的GetContentByLimitTokens进行截断，保证行的完整性
	qwenTokenizer, err := tokenizer.NewQwenTokenizer(false)
	if err != nil {
		log.Warnf("Failed to create tokenizer: %v", err)
		// 使用简单的长度估算进行截断
		maxChars := r.dependencyTokenLimit * 3
		if len(dependencies) <= maxChars {
			return dependencies
		}
		if maxChars > 0 {
			truncated := dependencies[:maxChars]
			return truncated + "\n\n[... Dependency information truncated due to " + fmt.Sprintf("%d", r.dependencyTokenLimit) + " token limit ...]"
		}
		return "[... Dependency information truncated due to " + fmt.Sprintf("%d", r.dependencyTokenLimit) + " token limit ...]"
	}

	// 使用rag.GetContentByLimitTokens进行截断
	truncatedContent, actualTokens := rag.GetContentByLimitTokens(dependencies, qwenTokenizer, r.dependencyTokenLimit)

	// 如果内容被截断，添加截断提示
	if actualTokens >= r.dependencyTokenLimit {
		truncatedContent += "\n\n[... Dependency information truncated due to " + fmt.Sprintf("%d", r.dependencyTokenLimit) + " token limit ...]"
	}

	return truncatedContent
}
