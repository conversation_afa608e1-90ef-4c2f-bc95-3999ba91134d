package codebase

import (
	"context"
	"cosy/chat/agents/tool/common"
	"cosy/indexing"
	"cosy/tree"
	"fmt"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/tool"
)

type ListDirConfig struct {
	WorkspacePath string                     //  工作空间路径
	FileIndexer   *indexing.ProjectFileIndex // 工作空间树缓存，存储工作空间的目录结构
	TokenLimit    int
}
type ListDirRequest struct {
	RelativeWorkspacePath string `json:"relative_workspace_path"`
}
type ListDirResponse struct {
	Node    *tree.TreeNode
	Content string
}

func NewListDirTool(config *ListDirConfig) (tool.InvokableTool, error) {
	tokenLimit := 2000
	if config != nil && config.TokenLimit > 0 {
		tokenLimit = config.TokenLimit
	}
	toolName := "list_dir"
	toolDesc := "List the contents of a directory. Useful to try to understand the file structure before diving deeper into specific files."
	toolParams := &definition.Schema{
		Type: "object",
		Properties: map[string]*definition.Schema{
			"relative_workspace_path": {
				Type:        "string",
				Description: "Path to list contents of, relative to the workspace root.",
			},
			"explanation": {
				Type:        "string",
				Description: common.ExplanationDefaultDesc,
			},
		},
		Required: []string{"query"},
	}
	toolInfo := &definition.ToolInfo{
		Name:        toolName,
		Description: toolDesc,
		Parameters:  toolParams,
	}
	workspaceTreeIndexer, ok := config.FileIndexer.GetWorkspaceTreeFileIndexer()
	if !ok {
		return nil, fmt.Errorf("workspace tree indexer not found")
	}
	toolInst := &listDirTool{
		workspacePath: config.WorkspacePath,
		workspaceTree: workspaceTreeIndexer.WorkspaceTree,
		tokenLimit:    tokenLimit,
	}
	return tool.NewInvokableTool(toolInfo, toolInst.list, tool.WithOutputConverter(toolInst.convertOutput)), nil
}

type listDirTool struct {
	workspacePath string           //  工作空间路径
	workspaceTree tree.TreeManager // 工作空间树缓存，存储工作空间的目录结构
	tokenLimit    int
}

func (l *listDirTool) list(ctx context.Context, request *ListDirRequest) (*ListDirResponse, error) {
	relativePath := request.RelativeWorkspacePath
	var content string
	var err error
	if relativePath == "" || relativePath == "." {
		content = l.workspaceTree.GetTree(l.tokenLimit)
	} else {
		content, err = l.workspaceTree.GetSubTree(relativePath, l.tokenLimit)
		if err != nil {
			return nil, err
		}
	}
	return &ListDirResponse{Content: content}, nil
}
func (l *listDirTool) convertOutput(ctx context.Context, output interface{}) (*definition.ToolOutput, error) {
	response, ok := output.(*ListDirResponse)
	if !ok {
		return nil, fmt.Errorf("expected *ListDirResponse, got %T", output)
	}
	return &definition.ToolOutput{
		Content: response.Content,
		RawData: response,
	}, nil
}
