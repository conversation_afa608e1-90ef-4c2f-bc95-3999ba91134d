package codebase

import (
	"context"
	"fmt"
	"path/filepath"
	"strings"
	"time"

	"github.com/google/uuid"

	toolCommon "cosy/chat/agents/tool/common"
	common2 "cosy/chat/chains/common"
	"cosy/codebase"
	"cosy/codebase/semantic"
	"cosy/components"
	"cosy/indexing"
	"cosy/lang/indexer"
	"cosy/log"
	"cosy/sls"
	"cosy/util"
	"cosy/util/encrypt"
	"cosy/util/session"

	"errors"

	cosyDefinition "cosy/definition"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/tool"

	// Import context-search-engine SDK for fusion search
	"cosy/codebase/graph"
	"cosy/codebase/symbol"

	searchApi "gitlab.alibaba-inc.com/cosy/context-search-engine/pkg/api"
	searchService "gitlab.alibaba-inc.com/cosy/context-search-engine/pkg/search"

	// Import memory package for Wiki search
	"cosy/chat/agents/tool/memory"
)

type SearchCodebaseConfig struct {
	FileIndexer     *indexing.ProjectFileIndex
	Embedder        *components.LingmaEmbedder
	GraphSearcher   *graph.BaseGraphSearcher // 图搜索器
	SymbolSearcher  symbol.SymbolSearcher    // 符号搜索器
	RerankProvider  searchApi.RerankProvider // 重排序提供者
	MaxResultCount  int                      // 返回结果的最大数量
	Timeout         int
	ExplanationDesc string // explanation字段的描述
	EnableFusion    bool   // 是否启用融合搜索
}

type SearchCodebaseRequest struct {
	Query           string `json:"query"`
	SearchScope     string `json:"search_scope"`
	UseSymbolSearch bool   `json:"use_symbol_search"`
	Keywords        string `json:"key_words"`
}

func NewSearchCodebaseTool(config *SearchCodebaseConfig) (tool.InvokableTool, error) {
	maxResultCount := 25
	if config != nil && config.MaxResultCount > 0 {
		maxResultCount = config.MaxResultCount
	}
	explanationDesc := toolCommon.ExplanationDefaultDesc
	if config != nil && config.ExplanationDesc != "" {
		explanationDesc = config.ExplanationDesc
	}

	toolName := "search_codebase"
	toolDesc := `An intelligent code search tool that provides two complementary search modes for comprehensive codebase exploration.

**SEARCH MODES:**

1. **Symbol Search Mode** (use_symbol_search: true)
   - **WHEN TO USE**: You see specific identifiers, symbols, or names in the query/conversation
   - **BEST FOR**: 
     • Class names (PmsProduct, UserService, PmsBrand)  
     • Method/function names (getUserById, calculateTotal, validateInput)
     • Variable names, interface names, enum values
     • Multiple related symbols (can search several at once)
   - **STRENGTHS**: Precise, fast, finds exact definitions and references
   - **QUERY FORMAT**: Direct symbol names, space-separated for multiple symbols
   - **EXAMPLES**: "PmsProduct", "UserService.authenticate", "PmsProduct PmsBrand OrderController"

2. **Semantic Search Mode** (use_symbol_search: false, default)
   - **WHEN TO USE**: You need to understand concepts, behaviors, or explore unknown code areas
   - **BEST FOR**:
     • Finding implementations of specific behaviors/features
     • Discovering how concepts are implemented when symbol names are unknown
     • Exploratory searches based on functionality descriptions
     • Complex architectural understanding
   - **STRENGTHS**: Contextual understanding, discovers related code patterns
   - **ENHANCED CAPABILITIES** (semantic mode only):
     • Fusion search combining vector and graph-based retrieval
     • Intelligent result ranking and deduplication
     • Cross-reference discovery through code dependency graphs
     • Multi-strategy scoring for optimal relevance
   - **QUERY FORMAT**: Descriptive, concept-focused keywords and technical terms
   - **EXAMPLES**: "user authentication flow", "payment processing logic", "file upload validation"

**DECISION GUIDE:**
- See class/function names? → Use symbol search (use_symbol_search: true)
- Need to understand "how X works"? → Use semantic search (use_symbol_search: false)
- Exploring unknown code areas? → Use semantic search (use_symbol_search: false)
- Know exact symbols to find? → Use symbol search (use_symbol_search: true)`

	toolParams := &definition.Schema{
		Type: "object",
		Properties: map[string]*definition.Schema{
			"query": {
				Type:        "string",
				Description: "A concise, keyword-rich search string optimized for code retrieval. This should contain technical terms, relevant programming concepts, and implementation-focused language rather than conversational questions. The query should emphasize code functionality, architecture components, and technical specifications without natural language fillers. For effective retrieval, include domain-specific terminology, design patterns, and implementation details relevant to the code being sought.",
			},
			"search_scope": {
				Type:        "string",
				Description: "Target directory to search within (e.g., 'src/components', 'backend/', 'lib/'). Only one directory can be specified. Should be used whenever the query indicates a specific area of the codebase to focus on.",
			},
			"explanation": {
				Type:        "string",
				Description: explanationDesc,
			},
			"use_symbol_search": {
				Type:        "boolean",
				Description: "Determines the type of search: 'true' for precise symbol search; 'false' for broad, conceptual codebase search.",
			},
			"key_words": {
				Type:        "string",
				Description: "Extract maximum three most important keywords from the query, ranked by relevance:\n- Prioritize keywords most relevant to the query's core intent\n- Ignore: generic programming words (code, function, class, method, implementation, etc.)\n- Keep keywords concise and specific\n- Format: keyword1,keyword2\n",
			},
		},
		Required: []string{"query", "key_words"},
	}
	toolInfo := &definition.ToolInfo{
		Name:        toolName,
		Description: toolDesc,
		Parameters:  toolParams,
		ReadOnly:    true,
	}
	toolInst := &searchCodebaseTool{
		fileIndexer:    config.FileIndexer,
		embedder:       config.Embedder,
		graphSearcher:  config.GraphSearcher,
		symbolSearcher: config.SymbolSearcher,
		rerankProvider: config.RerankProvider,
		maxResultCount: maxResultCount,
		enableFusion:   config.EnableFusion,
	}
	return tool.NewInvokableTool(toolInfo, toolInst.search, tool.WithOutputConverter(toolInst.convertOutput)), nil
}

type searchCodebaseTool struct {
	fileIndexer    *indexing.ProjectFileIndex
	embedder       *components.LingmaEmbedder
	graphSearcher  *graph.BaseGraphSearcher
	symbolSearcher symbol.SymbolSearcher
	rerankProvider searchApi.RerankProvider
	maxResultCount int // 返回结果的最大数量
	enableFusion   bool
	searchService  searchApi.ContextSearchService
}

type desensitizationCodeChunk struct {
	EncodeFilePath string `json:"file_path,omitempty"`
	StartLine      uint32 `json:"start_line,omitempty"`
	EndLine        uint32 `json:"end_line,omitempty"`
}

func (t *searchCodebaseTool) search(ctx context.Context, request *SearchCodebaseRequest) (*codebase.CodebaseToolResult[codebase.SemanticSearchResult], error) {
	projectPath, ok := t.fileIndexer.WorkspaceInfo.GetWorkspaceFolder()
	if !ok {
		return nil, errors.New("no workspace found")
	}
	var subdir string

	// 如果SearchScope为空，保持为空
	if request.SearchScope == "" {
		subdir = ""
	} else {
		cleanedSearchScope := filepath.Clean(request.SearchScope)

		if filepath.IsAbs(cleanedSearchScope) {
			// 如果SearchScope是绝对路径，检查是否在projectPath内
			if strings.HasPrefix(cleanedSearchScope, projectPath) {
				subdir = cleanedSearchScope
			} else {
				// 如果不在projectPath内，则使用projectPath
				subdir = projectPath
			}
		} else {
			// 如果是相对路径，则拼接到projectPath
			subdir = filepath.Join(projectPath, cleanedSearchScope)
		}
		subdir = filepath.Clean(subdir)
	}

	var result codebase.CodebaseToolResult[codebase.SemanticSearchResult]
	if request.UseSymbolSearch {
		// 使用符号检索
		log.Debugf("[codebase] search with symbol")
		symbolResult, err := t.searchSymbol(ctx, request, projectPath, subdir)
		if err != nil {
			return nil, err
		}
		result = *symbolResult
	} else if t.enableFusion {
		// 使用融合搜索
		log.Debugf("[codebase] search with fusion: %+v", t.enableFusion)
		fusionResult, err := t.searchWithFusion(ctx, request, projectPath, subdir)
		if err != nil {
			return nil, err
		}
		result = *fusionResult
	} else {
		// 使用原有的语义搜索
		log.Debugf("[codebase] search with fusion: %+v", t.enableFusion)
		tks, err := codebase.GetToolKits(t.fileIndexer, t.embedder)
		if err != nil {
			return nil, err
		}
		semanticOptions := semantic.DefaultRetrieveOptions
		semanticOptions.FilePathPattern = subdir

		result = tks.SemanticSearch(ctx, codebase.CodebaseToolOptions{
			WorkspaceURI:    projectPath,
			RawQuery:        request.Query,
			RefinedQuery:    request.Query,
			Limit:           t.maxResultCount,
			SemanticOptions: semanticOptions,
		})
	}

	// 安全地获取AskParams，避免panic
	var requestId, sessionId string
	if askParamsValue := ctx.Value(common2.KeyChatAskParams); askParamsValue != nil {
		if rawInputParams, ok := askParamsValue.(*cosyDefinition.AskParams); ok {
			requestId = rawInputParams.RequestId
			sessionId = rawInputParams.SessionId
		} else {
			log.Warnf("KeyChatAskParams context value type assertion failed, expected *cosyDefinition.AskParams, got %T", askParamsValue)
		}
	} else {
		log.Info("KeyChatAskParams not found in context, using empty requestId and sessionId")
	}

	if len(result.Result.Documents) > 0 {
		contexts := make([]session.SessionFlowContext, 0)
		for _, symbol := range result.Result.Documents {
			contexts = append(contexts, session.SessionFlowContext{
				ContextKey:    symbol.FilePath,
				ContextType:   session.SessionFlowFileContext,
				ContextWeight: 1,
			})
		}
		if sessionId != "" {
			session.AddSessionContext(sessionId, contexts, true)
		}

		toolCallResultContext := session.SessionFlowContext{
			ContextKey:   session.SessionContextKeySearchCodebase,
			ContextType:  session.SessionFlowToolCallResultContext,
			ContextValue: result.Result.Documents,
		}
		if sessionId != "" {
			session.AddSessionContext(sessionId, []session.SessionFlowContext{toolCallResultContext}, true)
		}
	}

	eventData := map[string]string{
		"session_id":     sessionId,
		"request_id":     requestId,
		"request_set_id": requestId,
		"chat_record_id": requestId,
		"tool_name":      "search_codebase",
		"query":          request.Query,
		"search_results": util.ToJsonStr(desensitizeCodeChunk(result.Result.Documents)),
	}
	go sls.Report(sls.EventTypeChatCodebaseRecommendFileQueryResults, requestId, eventData)

	return &result, nil
}

func desensitizeCodeChunk(documents []indexer.CodeChunk) []desensitizationCodeChunk {
	var result []desensitizationCodeChunk

	for _, doc := range documents {
		// 对 FilePath 进行 MD5 加密
		encodedFilePath := encrypt.Md5Encode(doc.FilePath)

		// 构造脱敏后的结果
		result = append(result, desensitizationCodeChunk{
			EncodeFilePath: encodedFilePath,
			StartLine:      doc.StartLine,
			EndLine:        doc.EndLine,
		})
	}

	return result
}

func (t *searchCodebaseTool) convertOutput(ctx context.Context, output interface{}) (*definition.ToolOutput, error) {
	response, ok := output.(*codebase.CodebaseToolResult[codebase.SemanticSearchResult])
	if !ok {
		return nil, fmt.Errorf("expected *CodebaseToolResult[SemanticSearchResult], got %T", output)
	}

	var outputBuilder strings.Builder
	for i := range response.Result.Documents {
		doc := &response.Result.Documents[i]

		outputBuilder.WriteString(fmt.Sprintf("%s:L%d-L%d\n", doc.FilePath, doc.StartLine, doc.EndLine))
		outputBuilder.WriteString(doc.Content)
		outputBuilder.WriteString("\n\n")
	}

	return &definition.ToolOutput{
		Content: outputBuilder.String(),
		RawData: response,
	}, nil
}

// searchWithFusion 使用融合搜索
func (t *searchCodebaseTool) searchSymbol(ctx context.Context, request *SearchCodebaseRequest, projectPath, subdir string) (*codebase.CodebaseToolResult[codebase.SemanticSearchResult], error) {
	// 使用原有的语义搜索
	tks, err := codebase.GetToolKits(t.fileIndexer, t.embedder)
	if err != nil {
		return nil, err
	}
	symbolResults := tks.SymbolSearch(ctx, codebase.CodebaseToolOptions{
		WorkspaceURI:      projectPath,
		SearchSymbolQuery: request.Query,
		RankResult:        true,
		Limit:             t.maxResultCount,
	})
	symbols := symbolResults.Result.Symbols
	documents := make([]indexer.CodeChunk, 0, len(symbols))
	for _, symbol := range symbols {
		documents = append(documents, indexer.CodeChunk{
			Id:            symbol.SymbolKey + uuid.NewString(),
			Content:       symbol.Snippet,
			StartLine:     symbol.LineRange.StartLine,
			EndLine:       symbol.LineRange.EndLine,
			StartOffset:   symbol.OffsetRange.StartOffset,
			EndOffset:     symbol.OffsetRange.EndOffset,
			FilePath:      symbol.FilePath,
			FileName:      filepath.Base(symbol.FilePath),
			FileExtension: filepath.Ext(symbol.FilePath),
			Language:      util.GetLanguageByFilePath(symbol.FilePath),
		})
	}
	return &codebase.CodebaseToolResult[codebase.SemanticSearchResult]{
		Type: codebase.TypeSemanticSearch,
		Result: codebase.SemanticSearchResult{
			Documents: documents,
		},
		Error: symbolResults.Error,
	}, nil
}

// searchWithFusion 使用融合搜索
func (t *searchCodebaseTool) searchWithFusion(ctx context.Context, request *SearchCodebaseRequest, projectPath, subdir string) (*codebase.CodebaseToolResult[codebase.SemanticSearchResult], error) {
	// 初始化搜索服务
	if err := t.initializeSearchService(); err != nil {
		return nil, fmt.Errorf("failed to initialize search service: %w", err)
	}

	// 准备搜索选项
	searchOptions := searchApi.ContextSearchOptions{
		WorkspaceURI: projectPath,
		Query:        request.Query,
		Limit:        t.maxResultCount,
		Strategy:     searchApi.StrategyFusion, // 使用融合策略
		VectorOptions: &searchApi.VectorSearchOptions{
			ScoreThreshold: 0.1,
		},
		EnableSemanticExpander: true,
		Keywords:               strings.Split(request.Keywords, ","),
	}

	// 处理搜索范围
	if subdir != "" && subdir != projectPath {
		searchOptions.FilePathPattern = subdir
	}

	// 配置融合选项
	searchOptions.FusionOptions = &searchApi.FusionSearchOptions{
		VectorWeight:                    0.7,
		SymbolWeight:                    0.3,
		MaxVectorResults:                25,
		MaxSymbolResults:                25,
		EnableGraphExpansion:            t.graphSearcher != nil, // 如果有图搜索器则启用图扩展
		GraphExpansionDepth:             2,
		MaxExpandedNodes:                500,
		GraphNodeScoreThreshold:         0.1,
		EnableSemanticResultReplacement: true,
		EnableLLMRerank:                 false,
		EnableBatchRerank:               true,
		RerankScoreThreshold:            0.1,
		BatchSize:                       150,
		RelevantFilesWithScore:          map[string]float64{},
	}
	//使用 Wiki 搜索获取相关文件路径
	searchOptions.FusionOptions.RelevantFilesWithScore = t.getRelevantPathWithScoreFromWikiWithTimeout(ctx, request.Query)
	log.Debugf("[codebase] relevant files with score %+v", searchOptions.FusionOptions.RelevantFilesWithScore)
	// 执行搜索
	searchResponse, err := t.searchService.Search(ctx, searchOptions)
	if err != nil {
		return nil, fmt.Errorf("fusion search failed: %w", err)
	}

	// 转换结果为 CodeChunk 格式
	documents := make([]indexer.CodeChunk, 0, len(searchResponse.Results))
	for _, result := range searchResponse.Results {
		chunk := indexer.CodeChunk{
			Content:       result.Content,
			FilePath:      result.FilePath,
			StartLine:     result.StartLine,
			EndLine:       result.EndLine,
			Score:         result.Score,
			Language:      result.Language,
			FileName:      filepath.Base(result.FilePath),
			FileExtension: filepath.Ext(result.FilePath),
		}
		documents = append(documents, chunk)
	}

	log.Debugf("[codebase] fusion search response %d results", len(searchResponse.Results))

	return &codebase.CodebaseToolResult[codebase.SemanticSearchResult]{
		Type: codebase.TypeSemanticSearch,
		Result: codebase.SemanticSearchResult{
			Documents: documents,
		},
	}, nil
}
func (t *searchCodebaseTool) getRelevantPathWithScoreFromWikiWithTimeout(ctx context.Context, query string) map[string]float64 {
	// 添加panic恢复处理
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("[codebase] wiki search panicked for query '%s': %v", query, r)
		}
	}()

	timeout := 1 * time.Second
	timeoutCtx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	// 创建结果通道
	resultChan := make(chan map[string]float64, 1)

	// 启动协程执行Wiki搜索
	go func() {
		// 协程内部也添加panic恢复
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("[codebase] wiki search goroutine panicked for query '%s': %v", query, r)
				// panic发生时，发送空结果避免通道阻塞
				select {
				case resultChan <- map[string]float64{}:
				default:
				}
			}
			close(resultChan)
		}()

		// 用try-catch包装调用
		func() {
			defer func() {
				if r := recover(); r != nil {
					log.Errorf("[codebase] wiki search method panicked for query '%s': %v", query, r)
					return
				}
			}()

			result := t.getRelevantPathWithScoreFromWiki(timeoutCtx, query)
			resultChan <- result
		}()
	}()

	// 标准的select模式：等待结果或context完成
	select {
	case result := <-resultChan:
		if result == nil {
			log.Debugf("[codebase] wiki search returned nil result for query: %s", query)
			return map[string]float64{}
		}
		return result
	case <-timeoutCtx.Done():
		if timeoutCtx.Err() == context.DeadlineExceeded {
			log.Warnf("[codebase] wiki search timed out after %v for query: %s", timeout, query)
		} else {
			log.Warnf("[codebase] wiki search cancelled: %v", timeoutCtx.Err())
		}
		return map[string]float64{}
	}
}

// getRelevantPathWithScoreFromWiki 从 Wiki 搜索结果中提取相关文件路径
func (t *searchCodebaseTool) getRelevantPathWithScoreFromWiki(ctx context.Context, query string) map[string]float64 {
	log.Debugf("[codebase] getRelevantPathWithScoreFromWiki query %s", query)

	// 获取 workspace 路径
	projectPath, ok := t.fileIndexer.WorkspaceInfo.GetWorkspaceFolder()
	if !ok {
		log.Warnf("[codebase] failed to get workspace folder")
		return map[string]float64{}
	}
	response, err := memory.QueryWikiContent(ctx, projectPath, t.fileIndexer, t.embedder, query, t.maxResultCount)
	if err != nil {
		log.Errorf("[codebase] failed to query wiki: %v", err)
		return map[string]float64{}
	}

	chunks := response.CodeChunks
	log.Debugf("[codebase] getRelevantPathWithScoreFromWiki found %d relevant files", len(chunks))

	if chunks == nil || len(chunks) == 0 {
		log.Debugf("[codebase] no relevant files found in wiki search")
		return map[string]float64{}
	}

	relevantFiles := ProcessWikiChunks(chunks, projectPath)
	// 过滤得分低于0.25的结果
	filteredResult := make(map[string]float64)
	for filePath, score := range relevantFiles {
		if score >= 0.25 {
			filteredResult[filePath] = score
		}
	}

	log.Debugf("[codebase] wiki search filtered %d -> %d results (score >= 0.25) for query: %s",
		len(relevantFiles), len(filteredResult), query)

	return filteredResult
}

// initializeSearchService 初始化融合搜索服务
func (t *searchCodebaseTool) initializeSearchService() error {
	if t.searchService != nil {
		return nil
	}

	// Ensure context-search-engine uses the same logger as the main application
	InitContextSearchEngineLogging()

	// 创建语义检索适配器
	semanticRetriever := &semanticRetrieverAdapter{
		fileIndexer: t.fileIndexer,
		embedder:    t.embedder,
	}

	// 创建嵌入提供者适配器
	embeddingProvider := &embeddingProviderAdapter{
		embedder: t.embedder,
	}

	// 使用适配器将 BaseGraphSearcher 适配到 context-search-engine 的接口
	var graphSearchAdapter searchApi.GraphSearcher
	if t.graphSearcher != nil {
		// 使用适配器进行类型转换
		graphSearchAdapter = &baseGraphSearcherAdapter{
			baseSearcher: t.graphSearcher,
		}
	}

	// 创建符号搜索适配器
	var symbolSearchAdapter searchApi.SymbolSearcher
	if t.symbolSearcher != nil {
		// 使用适配器进行类型转换
		symbolSearchAdapter = &symbolSearcherAdapter{
			symbolSearcher: t.symbolSearcher,
		}
	}

	// 初始化提供者
	providers := searchApi.Providers{
		SemanticSearchProvider: semanticRetriever,
		EmbeddingProvider:      embeddingProvider,
		GraphSearchProvider:    graphSearchAdapter,
		SymbolSearchProvider:   symbolSearchAdapter,
		RerankProvider:         t.rerankProvider,
	}

	// 创建搜索服务
	t.searchService = searchService.NewContextSearchService()
	return t.searchService.Initialize(providers)
}

// Adapter implementations

// semanticRetrieverAdapter 适配现有的语义搜索到 SDK 接口
type semanticRetrieverAdapter struct {
	fileIndexer *indexing.ProjectFileIndex
	embedder    *components.LingmaEmbedder
}

func (s *semanticRetrieverAdapter) Retrieve(ctx context.Context, query searchApi.RetrieveQuery, workspaceURI string, topK int, options searchApi.RetrieveOptions) ([]searchApi.CodeChunk, error) {
	tks, err := codebase.GetToolKits(s.fileIndexer, s.embedder)
	if err != nil {
		return nil, err
	}

	// 使用现有的语义搜索
	result := tks.SemanticSearch(ctx, codebase.CodebaseToolOptions{
		WorkspaceURI:    workspaceURI,
		RawQuery:        query.RawQuery,
		RefinedQuery:    query.RefinedQuery,
		Keywords:        query.Keywords,
		CodeCategory:    query.CodeCategory,
		Limit:           topK,
		SemanticOptions: convertToSemanticOptions(options),
	})

	if result.Error != "" {
		return nil, errors.New(result.Error)
	}

	// 转换为 SDK 格式
	chunks := make([]searchApi.CodeChunk, 0, len(result.Result.Documents))
	for _, doc := range result.Result.Documents {
		chunks = append(chunks, searchApi.CodeChunk{
			Content:       doc.Content,
			FilePath:      doc.FilePath,
			StartLine:     doc.StartLine,
			EndLine:       doc.EndLine,
			Score:         doc.Score,
			Language:      doc.Language,
			FileName:      doc.FileName,
			FileExtension: doc.FileExtension,
		})
	}

	return chunks, nil
}

// embeddingProviderAdapter 适配 LingmaEmbedder 到 SDK 接口
type embeddingProviderAdapter struct {
	embedder *components.LingmaEmbedder
}

func (e *embeddingProviderAdapter) GetEmbedding(ctx context.Context, text string) ([]float64, error) {
	embedding, err := e.embedder.EmbedQuery(ctx, text)
	if err != nil {
		return nil, err
	}
	// Convert []float32 to []float64
	result := make([]float64, len(embedding))
	for i, v := range embedding {
		result[i] = float64(v)
	}
	return result, nil
}

func (e *embeddingProviderAdapter) GetBatchEmbeddings(ctx context.Context, texts []string) ([][]float64, error) {
	embeddings, err := e.embedder.EmbedDocuments(ctx, texts)
	if err != nil {
		return nil, err
	}
	// Convert [][]float32 to [][]float64
	result := make([][]float64, len(embeddings))
	for i, embedding := range embeddings {
		result[i] = make([]float64, len(embedding))
		for j, v := range embedding {
			result[i][j] = float64(v)
		}
	}
	return result, nil
}

// convertToSemanticOptions 转换 SDK 选项到现有的语义选项
func convertToSemanticOptions(options searchApi.RetrieveOptions) semantic.RetrieveOptions {
	return semantic.RetrieveOptions{
		Strategy:                options.Strategy,
		VectorScoreThreshold:    options.VectorScoreThreshold,
		RerankScoreThreshold:    options.RerankScoreThreshold,
		LLMRerankScoreThreshold: options.LLMRerankScoreThreshold,
		RelevantFiles:           options.RelevantFiles,
		FilePathPattern:         options.FilePathPattern,
	}
}

// baseGraphSearcherAdapter adapts the local BaseGraphSearcher to the SDK interface
// This is needed because the local types (graph.Node, graph.Graph, etc.)
// are different from the SDK types (searchApi.Node, searchApi.Graph, etc.)
type baseGraphSearcherAdapter struct {
	baseSearcher *graph.BaseGraphSearcher
}

func (g *baseGraphSearcherAdapter) LocateNode(ctx context.Context, query searchApi.LocateNodeQuery) ([]searchApi.Node, error) {
	// Convert query to local format
	localQuery := graph.LocateNodeQuery{
		WorkspacePath: query.WorkspacePath,
		FilePath:      query.FilePath,
		StartLine:     int32(query.StartLine),
		EndLine:       int32(query.EndLine),
	}

	nodes, err := g.baseSearcher.LocateNode(ctx, localQuery)
	if err != nil {
		return nil, err
	}

	// Convert to SDK format
	result := make([]searchApi.Node, 0, len(nodes))
	for _, node := range nodes {
		result = append(result, convertLocalNodeToSDK(node))
	}

	return result, nil
}

func (g *baseGraphSearcherAdapter) TravelGraph(ctx context.Context, params searchApi.TravelGraphQuery) (searchApi.GraphPath, error) {
	// Convert query to local format
	localQuery := graph.TravelGraphQuery{
		WorkspacePath: params.WorkspacePath,
		FilePath:      params.FilePath,
		StartOffset:   int32(params.StartOffset),
		EndOffset:     int32(params.EndOffset),
		Reverse:       params.Reverse,
	}

	path, err := g.baseSearcher.TravelGraph(ctx, localQuery)
	if err != nil {
		return searchApi.GraphPath{}, err
	}

	// Convert to SDK format
	result := searchApi.GraphPath{
		Paths: path.Paths,
		Nodes: make(map[string]searchApi.Node),
	}

	for id, node := range path.Nodes {
		result.Nodes[id] = convertLocalNodeToSDK(node)
	}

	return result, nil
}

func (g *baseGraphSearcherAdapter) ExpandGraph(ctx context.Context, params searchApi.ExpandGraphQuery) (searchApi.Graph, error) {
	// Convert query to local format
	localQuery := graph.ExpandGraphQuery{
		WorkspacePath: params.WorkspacePath,
		FilePath:      params.FilePath,
	}

	localGraph, err := g.baseSearcher.ExpandGraph(ctx, localQuery)
	if err != nil {
		return searchApi.Graph{}, err
	}

	// Convert to SDK format
	result := searchApi.Graph{
		Nodes: make(map[string]searchApi.Node),
		Edges: make([]searchApi.Edge, 0, len(localGraph.Edges)),
	}

	for id, node := range localGraph.Nodes {
		result.Nodes[id] = convertLocalNodeToSDK(node)
	}

	for _, edge := range localGraph.Edges {
		result.Edges = append(result.Edges, searchApi.Edge{
			SourceId: edge.SourceId,
			TargetId: edge.TargetId,
			EdgeType: edge.EdgeType,
		})
	}

	return result, nil
}

// Helper function to convert local Node to SDK format
func convertLocalNodeToSDK(node graph.Node) searchApi.Node {
	return searchApi.Node{
		NodeId:      node.NodeId,
		NodeName:    node.NodeName,
		NodeType:    node.NodeType,
		WorkSpace:   node.WorkSpace,
		Filepath:    node.Filepath,
		StartLine:   uint32(node.StartLine),
		EndLine:     uint32(node.EndLine),
		StartOffset: uint32(node.StartOffset),
		EndOffset:   uint32(node.EndOffset),
		Snippet:     node.Snippet,
	}
}

// symbolSearcherAdapter 适配现有的符号搜索到 SDK 接口
type symbolSearcherAdapter struct {
	symbolSearcher symbol.SymbolSearcher
}

func (s *symbolSearcherAdapter) SearchSymbol(ctx context.Context, params *searchApi.SymbolSearchParams) ([]searchApi.SymbolSearchResult, error) {
	// 转换参数为本地格式
	localParams := &symbol.SymbolSearchParams{
		Language:      params.Language,
		WorkspacePath: params.WorkspacePath,
		SymbolKey:     params.SymbolKey,
		MaxCount:      params.MaxCount,
		RankResult:    params.RankResult,
	}

	// 执行符号搜索
	localResults, err := s.symbolSearcher.SearchSymbol(ctx, localParams)
	if err != nil {
		return nil, err
	}

	// 转换结果为 SDK 格式
	results := make([]searchApi.SymbolSearchResult, 0, len(localResults))
	for _, result := range localResults {
		results = append(results, searchApi.SymbolSearchResult{
			WorkspacePath: result.WorkspacePath,
			FilePath:      result.FilePath,
			LineRange:     searchApi.LineRange{StartLine: result.LineRange.StartLine, EndLine: result.LineRange.EndLine},
			OffsetRange:   searchApi.OffsetRange{StartOffset: result.OffsetRange.StartOffset, EndOffset: result.OffsetRange.EndOffset},
			SymbolKey:     result.SymbolKey,
			SymbolName:    result.SymbolName,
			SymbolType:    result.SymbolType,
		})
	}

	return results, nil
}

func (s *symbolSearcherAdapter) SearchSymbolWithoutIde(ctx context.Context, params *searchApi.SymbolSearchParams) ([]searchApi.SymbolSearchResult, error) {
	// 转换参数为本地格式
	localParams := &symbol.SymbolSearchParams{
		Language:      params.Language,
		WorkspacePath: params.WorkspacePath,
		SymbolKey:     params.SymbolKey,
		MaxCount:      params.MaxCount,
		RankResult:    params.RankResult,
	}

	// 执行符号搜索（不使用IDE）
	localResults, err := s.symbolSearcher.SearchSymbolByBuiltin(ctx, localParams)
	if err != nil {
		return nil, err
	}

	// 转换结果为 SDK 格式
	results := make([]searchApi.SymbolSearchResult, 0, len(localResults))
	for _, result := range localResults {
		results = append(results, searchApi.SymbolSearchResult{
			WorkspacePath: result.WorkspacePath,
			FilePath:      result.FilePath,
			LineRange:     searchApi.LineRange{StartLine: result.LineRange.StartLine, EndLine: result.LineRange.EndLine},
			OffsetRange:   searchApi.OffsetRange{StartOffset: result.OffsetRange.StartOffset, EndOffset: result.OffsetRange.EndOffset},
			SymbolKey:     result.SymbolKey,
			SymbolName:    result.SymbolName,
			SymbolType:    result.SymbolType,
		})
	}

	return results, nil
}
