package codebase

import (
	"context"
	"fmt"
	"github.com/stretchr/testify/assert"
	"os"
	"path/filepath"
	"runtime"
	"testing"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
)

// TestEnvConfigForReadFile 定义测试环境配置
type TestEnvConfigForReadFile struct {
	CreateNoAccessFile bool
}

// buildReadFileTestEnv 初始化测试环境并创建用于测试的目录结构
func buildReadFileTestEnv(t *testing.T, config *TestEnvConfigForReadFile) string {
	// 获取当前文件所在目录，用于构造测试目录的绝对路径
	_, file, _, _ := runtime.Caller(0)
	currentDir := filepath.Dir(file)

	// 构造测试目录路径
	testDir := filepath.Join(currentDir, "test_fixtures_rf")

	// 清理旧目录并创建新目录
	_ = os.RemoveAll(testDir)
	assert.Nil(t, os.Mkdir<PERSON>ll(testDir, 0755), "创建测试目录失败")

	// 添加 defer 清理
	t.Cleanup(func() {
		// 恢复所有目录的权限以便清理
		if config != nil && config.CreateNoAccessFile {
			noAccessFilePath := filepath.Join(testDir, "no_access_file.md")
			_ = os.Chmod(noAccessFilePath, 0755)
		}
		err := os.RemoveAll(testDir)
		if err != nil {
			t.Logf("删除测试目录失败: %v", err)
		}
	})

	// 创建基本目录结构
	if err := createBasicStructureForReadFile(t, testDir); err != nil {
		t.Fatalf("创建基本目录结构失败: %v", err)
	}

	// 根据配置创建其他目录
	if config != nil {
		// 创建无权限文件
		if config.CreateNoAccessFile {
			if err := createNoAccessFileForReadFile(t, testDir); err != nil {
				t.Fatalf("创建无权限文件失败: %v", err)
			}
		}
	}

	// 返回测试目录的绝对路径
	absTestDir, err := filepath.Abs(testDir)
	assert.Nil(t, err, "获取绝对路径失败")
	return absTestDir
}

// createBasicStructureForReadFile 创建基本目录结构
func createBasicStructureForReadFile(t *testing.T, testDir string) error {
	// 定义需要创建的子目录路径
	dirs := []string{
		testDir,
	}

	// 创建所有需要的目录
	for _, dir := range dirs {
		assert.Nil(t, os.MkdirAll(dir, 0755), "创建目录 %s 失败", dir)
	}

	// 定义需要创建的文件及其内容
	files := map[string][]byte{
		filepath.Join(testDir, "hello.go"): []byte(`I'm not java`),

		filepath.Join(testDir, "hello.md"): []byte(`Hello, World.
Hello, read_file tool.
Hello, java`),

		filepath.Join(testDir, "PmsService.java"): []byte(`package com.example.service.sub;

/**
 * Another service class with keyword graph in comment
 */
public class AnotherPmsService {
    // Some method
    public void init() {
        // Initialize something
    }
}`),
	}

	// 创建基础测试文件
	for path, content := range files {
		assert.Nil(t, os.WriteFile(path, content, 0644), "写入文件 %s 失败", path)
	}

	return nil
}

// createNoAccessFileForReadFile 创建无权限文件
func createNoAccessFileForReadFile(t *testing.T, testDir string) error {
	noAccessFilePath := filepath.Join(testDir, "no_access_file.md")
	if err := os.WriteFile(noAccessFilePath, []byte("Hello, access. No Access File"), 0000); err != nil {
		return err
	}
	return os.Chmod(noAccessFilePath, 0000)
}

// TestReadFile_Basic 测试 ReadFileTool 基础功能
func TestReadFile_Basic(t *testing.T) {
	testDir := buildReadFileTestEnv(t, nil)
	tool, _ := NewReadFileTool(&ReadFileConfig{
		WorkspacePath: testDir,
	})
	input := &definition.ToolInput{
		Arguments: `{"file_path":"` + filepath.Join(testDir, "hello.md") + `","start_line":0,"end_line":1}`,
	}
	output, err := tool.Invoke(context.Background(), input)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(output)
	assert.NotNil(t, output)
	assert.Contains(t, output.Content, "Contents of ")
	assert.Contains(t, output.Content, "Hello, World.")
	assert.Contains(t, output.Content, "Hello, read_file tool.")
	assert.NotContains(t, output.Content, "Hello, java")
}

// TestReadFile_ReadEntireFile 测试 ReadFileTool 读取整个文件
func TestReadFile_ReadEntireFile(t *testing.T) {
	testDir := buildReadFileTestEnv(t, nil)
	tool, _ := NewReadFileTool(&ReadFileConfig{
		WorkspacePath: testDir,
	})
	input := &definition.ToolInput{
		Arguments: `{"file_path":"` + filepath.Join(testDir, "hello.md") + `","start_line":0,"end_line":1,"read_entire_file":true}`,
	}
	output, err := tool.Invoke(context.Background(), input)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(output)
	assert.NotNil(t, output)
	assert.Contains(t, output.Content, "Contents of ")
	assert.Contains(t, output.Content, "Hello, World.")
	assert.Contains(t, output.Content, "Hello, read_file tool.")
	assert.Contains(t, output.Content, "Hello, java")
}

// TestReadFile_ReadEntireFileWithMaxLineCount 测试 ReadFileTool 读取整个文件并受 MaxLineCount 限制
func TestReadFile_ReadEntireFileWithMaxLineCount(t *testing.T) {
	testDir := buildReadFileTestEnv(t, nil)
	tool, _ := NewReadFileTool(&ReadFileConfig{
		WorkspacePath: testDir,
		MaxLineCount:  2, // 设置最大行数为2
	})
	input := &definition.ToolInput{
		Arguments: `{"file_path":"` + filepath.Join(testDir, "hello.md") + `","start_line":0,"end_line":10,"read_entire_file":true}`,
	}
	output, err := tool.Invoke(context.Background(), input)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(output)
	assert.NotNil(t, output)
	assert.Contains(t, output.Content, "Contents of ")
	assert.Contains(t, output.Content, "Hello, World.")
	assert.Contains(t, output.Content, "Hello, read_file tool.")
	assert.NotContains(t, output.Content, "Hello, java") // 第三行不应被包含
}

//// TestReadFile_FileNotFound 测试 ReadFileTool 找不到文件
//func TestReadFile_FileNotFound(t *testing.T) {
//	testDir := buildReadFileTestEnv(t, nil)
//	tool, _ := NewReadFileTool(&ReadFileConfig{
//		WorkspacePath: testDir,
//	})
//	input := &definition.ToolInput{
//		Arguments: `{"file_path":"` + filepath.Join(testDir, "one_more_dir", "nonexistent.md") + `","start_line":0,"end_line":10}`,
//	}
//	_, err := tool.Invoke(context.Background(), input)
//	assert.Error(t, err)
//	assert.Equal(t, err.(*errors.Error).Code, errors.FileNotFound)
//	assert.Contains(t, err.Error(), "failed to read file")
//}
//
//// TestReadFile_FileNotFoundButDirExists 测试 ReadFileTool 找不到文件但是目录存在的情况
//func TestReadFile_FileNotFoundButDirExists(t *testing.T) {
//	testDir := buildReadFileTestEnv(t, nil)
//	tool, _ := NewReadFileTool(&ReadFileConfig{
//		WorkspacePath: testDir,
//	})
//	input := &definition.ToolInput{
//		Arguments: `{"file_path":"` + filepath.Join(testDir, "nonexistent.md") + `","start_line":0,"end_line":10}`,
//	}
//	_, err := tool.Invoke(context.Background(), input)
//	assert.Error(t, err)
//	assert.Equal(t, err.(*errors.Error).Code, errors.FileNotFound)
//	assert.Contains(t, err.Error(), "parent directory exists but file not found")
//}
//
//// TestReadFile_StartLineGreaterThanEndLine 测试 ReadFileTool 起始行大于结束行
//func TestReadFile_StartLineGreaterThanEndLine(t *testing.T) {
//	testDir := buildReadFileTestEnv(t, nil)
//	tool, _ := NewReadFileTool(&ReadFileConfig{
//		WorkspacePath: testDir,
//	})
//	input := &definition.ToolInput{
//		Arguments: `{"file_path":"` + filepath.Join(testDir, "hello.md") + `","start_line":5,"end_line":2}`,
//	}
//	_, err := tool.Invoke(context.Background(), input)
//	assert.Error(t, err)
//	assert.Contains(t, err.Error(), "Start line exceeds total lines in the file. Showing the end of the file instead.")
//	assert.Contains(t, err.Error(), "Hello, java")
//}

// TestReadFile_OutOfBounds 测试 ReadFileTool 行超出范围
func TestReadFile_OutOfBounds(t *testing.T) {
	testDir := buildReadFileTestEnv(t, nil)
	tool, _ := NewReadFileTool(&ReadFileConfig{
		WorkspacePath: testDir,
	})
	input := &definition.ToolInput{
		Arguments: `{"file_path":"` + filepath.Join(testDir, "hello.md") + `","start_line":3,"end_line":5}`,
	}
	output, err := tool.Invoke(context.Background(), input)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(output)
	assert.Contains(t, output.Content, "Start line exceeds total lines in the file. Showing the end of the file instead.")
	assert.Contains(t, output.Content, "Hello, java")
}

// TestReadFile_EndLineExceedsMaxLineCount 测试 ReadFileTool end_line 超出 MaxLineCount 的情况
func TestReadFile_EndLineExceedsMaxLineCount(t *testing.T) {
	testDir := buildReadFileTestEnv(t, nil)
	tool, _ := NewReadFileTool(&ReadFileConfig{
		WorkspacePath: testDir,
		MaxLineCount:  1, // 设置最大行数为1
	})
	input := &definition.ToolInput{
		Arguments: `{"file_path":"` + filepath.Join(testDir, "hello.md") + `","start_line":0,"end_line":10}`,
	}
	output, err := tool.Invoke(context.Background(), input)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(output)
	assert.NotNil(t, output)
	assert.Contains(t, output.Content, "Contents of ")
	assert.Contains(t, output.Content, "Hello, World.")
	assert.NotContains(t, output.Content, "Hello, read_file tool.")
	assert.NotContains(t, output.Content, "Hello, java")
}

// TestReadFile_StartLineBeyondMaxLineCount 测试 ReadFileTool start_line 在 MaxLineCount 之后的情况
func TestReadFile_StartLineBeyondMaxLineCount(t *testing.T) {
	testDir := buildReadFileTestEnv(t, nil)
	tool, _ := NewReadFileTool(&ReadFileConfig{
		WorkspacePath: testDir,
		MaxLineCount:  1, // 设置最大行数为1
	})
	input := &definition.ToolInput{
		Arguments: `{"file_path":"` + filepath.Join(testDir, "hello.md") + `","start_line":1,"end_line":10}`, // 期望读取1～2行
	}
	output, err := tool.Invoke(context.Background(), input)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(output)
	assert.NotNil(t, output)
	assert.Contains(t, output.Content, "Contents of ")
	assert.NotContains(t, output.Content, "Hello, World.")
	assert.Contains(t, output.Content, "Hello, read_file tool.")
	assert.NotContains(t, output.Content, "Hello, java")
}

// TestReadFile_NoAccess 测试 ReadFileTool 无法访问文件
func TestReadFile_NoAccess(t *testing.T) {
	config := &TestEnvConfigForReadFile{
		CreateNoAccessFile: true,
	}
	testDir := buildReadFileTestEnv(t, config)
	tool, _ := NewReadFileTool(&ReadFileConfig{
		WorkspacePath: testDir,
	})
	input := &definition.ToolInput{
		Arguments: `{"file_path":"` + filepath.Join(testDir, "no_access_file.md") + `","start_line":0,"end_line":10}`,
	}
	_, err := tool.Invoke(context.Background(), input)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "permission denied")
}

//	func TestReadFileTool(t *testing.T) {
//		tool, _ := NewReadFileTool(&ReadFileConfig{
//			MaxLineCount:  200,
//			WorkspacePath: "/Users/<USER>/Develop/alicode/cosy/lingma-agent-graph",
//		})
//		testDir := "/Users/<USER>/Develop/alicode/cosy/lingma-agent-graph/tool/file/grep_code.go"
//		//testDir := "tool/file/grep_code.go"
//		input := &definition.ToolInput{
//			Arguments: `{"file_path":"` + testDir + `","start_line":0,"end_line":10}`,
//		}
//		output, err := tool.Invoke(context.Background(), input)
//		if err != nil {
//			t.Fatal(err)
//		}
//		t.Log(output)
//	}
//
//	func TestReadFileToolEntire(t *testing.T) {
//		tool, _ := NewReadFileTool(&ReadFileConfig{
//			MaxLineCount:  300,
//			WorkspacePath: "/Users/<USER>/Develop/alicode/cosy/lingma-agent-graph",
//		})
//		testDir := "/Users/<USER>/Develop/alicode/cosy/lingma-agent-graph/tool/file/grep_code.go"
//		//testDir := "tool/file/grep_code.go"
//		input := &definition.ToolInput{
//			Arguments: `{"file_path":"` + testDir + `","start_line":0,"end_line":100,"read_entire_file":true}`,
//		}
//		output, err := tool.Invoke(context.Background(), input)
//		if err != nil {
//			t.Fatal(err)
//		}
//		t.Log(output)
//	}
func TestReadFileTool_LineRange(t *testing.T) {
	arr := []int{-10, -1, 2, 8, 11, 20}
	for _, maxLineCount := range []int{5, 20} {
		for _, start := range arr {
			for _, end := range arr {
				if start == end || end == -10 {
					continue
				}
				startLine, endLine, outOfRange := getLineRange(start, end, 10, maxLineCount, false)
				fmt.Printf("maxLineCount: %d\tstart: %d,\tend: %d\t\tstartLine: %d\tendLine: %d\toutOfRange: %t\n", maxLineCount, start, end, startLine, endLine, outOfRange)
			}
		}
	}
}
