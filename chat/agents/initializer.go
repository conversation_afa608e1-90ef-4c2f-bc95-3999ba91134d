package agents

import (
	"context"
	"cosy/chat/agents/coder"
	coderCommon "cosy/chat/agents/coder/common"
	"cosy/chat/agents/deepwiki/agent"
	"cosy/chat/agents/interfaces"
	"cosy/chat/agents/quest/longrunning"
	"cosy/chat/agents/quest/research"
	"cosy/chat/agents/support"
	"cosy/deepwiki/common"
	"cosy/definition"
)

// Initialize deepwiki 模块初始化
func Initialize() {
	//coder 模块初始化
	coderAgentBuilder, _ := coder.InitCoderExecutorBuilder()
	support.AgentServer.AgentManager.RegisterAgentBuilder(coderCommon.BuilderIdentifier, coderAgentBuilder)

	readmeGenerateAgentBuilder, _ := agent.InitReadmeGenerateExecutorBuilder()
	overviewGenerateAgentBuilder, _ := agent.InitOverviewGenerateExecutorBuilder()
	catalogueGenerateAgentBuilder, _ := agent.InitCatalogueGenerateExecutorBuilder()
	catalogueThinkAgentBuilder, _ := agent.InitCatalogueThinkExecutorBuilder()
	wikiGenerateAgentBuilder, _ := agent.InitWikiGenerateExecutorBuilder()
	wikiUpdateAgentBuilder, _ := agent.InitWikiUpdateExecutorBuilder()
	commitDiffAgentBuilder, _ := agent.InitCommitDiffExecutorBuilder()
	updateWikiWithCodeAgentBuilder, _ := agent.InitUpdateWikiWithCodeExecutorBuilder()

	support.AgentServer.AgentManager.RegisterAgentBuilder(common.ReadmeGenerateAgentBuilderIdentifier, readmeGenerateAgentBuilder)
	support.AgentServer.AgentManager.RegisterAgentBuilder(common.OverviewGenerateAgentBuilderIdentifier, overviewGenerateAgentBuilder)
	support.AgentServer.AgentManager.RegisterAgentBuilder(common.CatalogueGenerateAgentBuilderIdentifier, catalogueGenerateAgentBuilder)
	support.AgentServer.AgentManager.RegisterAgentBuilder(common.CatalogueThinkAgentBuilderIdentifier, catalogueThinkAgentBuilder)
	support.AgentServer.AgentManager.RegisterAgentBuilder(common.WikiGenerateAgentBuilderIdentifier, wikiGenerateAgentBuilder)
	support.AgentServer.AgentManager.RegisterAgentBuilder(common.WikiUpdateAgentBuilderIdentifier, wikiUpdateAgentBuilder)
	support.AgentServer.AgentManager.RegisterAgentBuilder(common.CommitDiffAgentBuilderIdentifier, commitDiffAgentBuilder)
	support.AgentServer.AgentManager.RegisterAgentBuilder(common.UpdateWikiWithCodeAgentBuilderIdentifier, updateWikiWithCodeAgentBuilder)

	// 长期运行agent
	longRunningAgentBuilder, _ := longrunning.InitExecutorBuilder()
	support.AgentServer.AgentManager.RegisterAgentBuilder(longrunning.BuilderIdentifier, longRunningAgentBuilder)
}

// RegisterResearchAgentToolFactory 注册research agent工具工厂到context中
func RegisterResearchAgentToolFactory(ctx context.Context, askParam *definition.AskParams) context.Context {
	researchToolFactory := research.NewResearchAgentToolFactory(askParam)
	return context.WithValue(ctx, interfaces.ResearchAgentToolFactoryKey, researchToolFactory)
}
