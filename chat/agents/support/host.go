package support

import (
	"context"
	markdown2 "cosy/chat/agents/support/markdown"
	toolCommon "cosy/chat/agents/tool/common"
	"cosy/definition"
	"cosy/log"
	"errors"
	"fmt"
	"sync"

	agentLog "code.alibaba-inc.com/cosy/lingma-agent-graph/log"
	"code.alibaba-inc.com/cosy/lingma-agent-manager/executor"
	"code.alibaba-inc.com/cosy/lingma-agent-manager/server"
)

var (
	AgentServer        *server.AgentServer
	noticeProcessorMap = sync.Map{}
)

func Initialize() {
	AgentServer = server.NewAgentServer()
	agentLog.SetLogger(log.GetLogger())

	// Enable Markdown link processor by default
	markdown2.EnableProcessor()
	log.Debugf("Initialized Markdown link processor (enabled: %v)", markdown2.IsEnabled())
}

// EnableMarkdownLinkProcessor enables the Markdown link processor for agent chat
func EnableMarkdownLinkProcessor() {
	markdown2.EnableProcessor()
	log.Debugf("Markdown link processor enabled")
}

// DisableMarkdownLinkProcessor disables the Markdown link processor for agent chat
func DisableMarkdownLinkProcessor() {
	markdown2.DisableProcessor()
	log.Debugf("Markdown link processor disabled")
}

// IsMarkdownLinkProcessorEnabled returns whether the Markdown link processor is enabled
func IsMarkdownLinkProcessorEnabled() bool {
	return markdown2.IsEnabled()
}

// MakeAgent 根据指定的agentName创建agent实例，requestId作为唯一标识符
func MakeAgent(requestId string, agentName string) (executor.Executor, error) {
	if AgentServer == nil {
		return nil, errors.New("agent server not initialized")
	}
	_, ok := AgentServer.SessionManager.Executors[requestId]
	if ok {
		return nil, errors.New("agent exists")
	}
	return AgentServer.CreateAgent(requestId, agentName)
}

// ResumeAgentWithOriginCtx 继续agent的执行，使用原始的上下文
func ResumeAgentWithOriginCtx(ctx context.Context, requestId string, request interface{}) error {
	if AgentServer == nil {
		return errors.New("agent server not initialized")
	}
	agent, ok := AgentServer.SessionManager.Executors[requestId]
	if !ok {
		return errors.New("agent not exists")
	}
	return agent.ResumeWithOriginCtx(ctx, request)
}

// CancelAgent 取消agent的执行
func CancelAgent(ctx context.Context, requestId string) error {
	if AgentServer == nil {
		return errors.New("agent server not initialized")
	}
	agent, ok := AgentServer.SessionManager.Executors[requestId]
	if !ok {
		return errors.New("agent not exists")
	}
	log.Debugf("cancel agent %s", requestId)
	return agent.Cancel(ctx)
}

func AddNoticeProcessor(requestId string, processor NoticeProcessor) {
	noticeProcessorMap.Store(requestId, processor)
}

func RemoveNoticeProcessor(requestId string) {
	noticeProcessorMap.Delete(requestId)
}

func ProcessStateInNotice(ctx context.Context, requestId string, notice StateInNotice) error {
	log.Debugf("Send StateInNotice to agent, requestId=%s, type=%s, args=+%v", requestId, notice.Type, notice)
	if processor, ok := noticeProcessorMap.Load(requestId); ok {
		processor.(NoticeProcessor).ProcessNotice(ctx, notice)
		return nil
	}
	return nil
}


func ConfirmToolCall(ctx context.Context, request *definition.ToolConfirmRequest) error {
	log.Debugf("Confirm tool call, requestId=%s, toolCallId=%s", request.RequestId, request.ToolCallId)
	confirmChan := toolCommon.GetToolConfirmChan(request.ToolCallId)
	if confirmChan == nil {
		return fmt.Errorf("tool confirm chan not found")
	}
	confirmChan <- request
	return nil
}