package support

import "context"

const (
	StateInNoticeTypePause          string = "PAUSE"          // 暂停
	StateInNoticeTypeResume         string = "RESUME"         // 恢复
	StateInNoticeTypeExitForMessage string = "ExitForMessage" // 退出然后接收新消息
)

type StateInNotice struct {
	Type string      `json:"type"` // 通知类型
	Data interface{} `json:"data"` // 通知内容
}

// NoticeProcessor 运行中的agent接收外部信息
type NoticeProcessor interface {
	ProcessNotice(ctx context.Context, notice StateInNotice) error
}
