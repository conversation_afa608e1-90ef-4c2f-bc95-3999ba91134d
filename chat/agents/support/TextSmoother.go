package support

import (
	"cosy/log"
	"math"
	"time"
)

type TextSmoother struct {
	buffer         []rune
	initTime       time.Time
	lastOutputTime time.Time
	outputCount    int64

	lastOutputRate float64
	// 速率计算历史
	recentInputs   []inputRecord
	maxHistorySize int
}

type inputRecord struct {
	timestamp             time.Time
	charCount             int
	updateIntervalSeconds float64
}

func NewTextSmoother() *TextSmoother {
	now := time.Now()
	return &TextSmoother{
		buffer:         make([]rune, 0),
		initTime:       now,
		lastOutputTime: now,
		outputCount:    0,
		recentInputs:   make([]inputRecord, 0),
		maxHistorySize: 50, // 只保留最近50次输入记录
	}
}

func (ts *TextSmoother) UpdateText(newText string) {
	now := time.Now()
	newRunes := []rune(newText)
	charCount := len(newRunes)

	if charCount == 0 {
		return
	}

	// 记录本次输入
	ts.recentInputs = append(ts.recentInputs, inputRecord{
		timestamp: now,
		charCount: charCount,
	})

	// 保持历史记录大小
	if len(ts.recentInputs) > ts.maxHistorySize {
		ts.recentInputs = ts.recentInputs[1:]
	}
	ts.buffer = append(ts.buffer, newRunes...)
}

func (ts *TextSmoother) GetNextOutput() string {
	now := time.Now()
	bufferLen := len(ts.buffer)

	if bufferLen == 0 {
		return ""
	}

	// 根据平均输入速率计算应该输出的字符数
	outputLen := ts.calculateOutputLength(now)

	if outputLen <= 0 {
		return ""
	}

	// 确保不超过缓冲区大小
	if outputLen > bufferLen {
		outputLen = bufferLen
	}

	output := string(ts.buffer[:outputLen])
	ts.buffer = ts.buffer[outputLen:]
	ts.lastOutputTime = now
	ts.outputCount++
	return output
}

// 计算平均输入速率
func (ts *TextSmoother) calculateAvgInputRate(now time.Time) float64 {
	if len(ts.recentInputs) == 0 {
		return 0
	}

	totalChars := 0
	for _, record := range ts.recentInputs {
		totalChars += record.charCount
	}
	totalDuration := now.Sub(ts.initTime)
	avgInputRate := float64(totalChars) / totalDuration.Seconds()
	// 限制速率范围
	if avgInputRate < 1.0 {
		avgInputRate = 1.0
	}
	return avgInputRate
}

// 计算本次应该输出的字符数
func (ts *TextSmoother) calculateOutputLength(now time.Time) int {
	// 计算自上次输出以来的时间
	timeSinceLastOutput := now.Sub(ts.lastOutputTime)
	if ts.outputCount == 0 {
		timeSinceLastOutput = time.Duration(100) * time.Millisecond
	}
	if len(ts.recentInputs) == 0 {
		return 1
	}

	lastRecord := ts.recentInputs[len(ts.recentInputs)-1]
	remainBufferLen := len(ts.buffer)

	// 基础输出速率：基于平均输入速率和缓冲区状态
	baseOutputRate := float64(remainBufferLen) / 3.5 // 计划3.5秒内输出完缓冲区的内容
	// 时间衰减：基于距离上一次输入的时间
	timeSinceLastInput := now.Sub(lastRecord.timestamp).Seconds()
	// 衰减常数3.0表示衰减的时间常数，即当时间间隔等于3秒时，衰减因子会降至原来的1/e (约36.8%)
	// 这个值控制衰减的快慢：数值越小衰减越快，数值越大衰减越慢
	const decayTimeConstant = 3.0 // 衰减时间常数（秒）
	// 使用平滑的S形衰减曲线
	smoothDecayFactor := 1.0 / (1.0 + math.Pow(timeSinceLastInput/decayTimeConstant, 2))
	// 计算期望输出字符数
	expectedChars := baseOutputRate * timeSinceLastOutput.Seconds() * smoothDecayFactor
	outputLen := int(math.Round(expectedChars))

	log.Debugf("timeSinceLastInput: %.3f, timeSinceLastOutput: %.3f, timeDecayFactor: %.3f,  baseOutputRate: %.2f, expectedChars: %.2f, outputLen: %d, remainBufferLen: %d",
		timeSinceLastInput, timeSinceLastOutput.Seconds(), smoothDecayFactor, baseOutputRate, expectedChars, outputLen, remainBufferLen)

	// 确保至少输出1个字符（如果缓冲区不为空）
	if outputLen < 1 {
		outputLen = 1
	}

	return outputLen
}

func (ts *TextSmoother) HasRemaining() bool {
	return len(ts.buffer) > 0
}

func (ts *TextSmoother) GetAndClearRemainingContent() string {
	remainContent := string(ts.buffer)
	ts.buffer = []rune{}
	return remainContent
}
