package support

import (
	"context"
	coderCommon "cosy/chat/agents/coder/common"
	"cosy/chat/agents/tool/apply"
	coderBaseTool "cosy/chat/agents/tool/codebase"
	"cosy/chat/agents/tool/file"
	"cosy/chat/agents/tool/human"
	"cosy/chat/agents/tool/ide"
	"cosy/chat/agents/tool/memory"
	"cosy/chat/agents/tool/plan"
	"cosy/chat/agents/tool/projectrule"
	"cosy/chat/agents/tool/web"
	"cosy/chat/service"
	"cosy/codebase"
	"cosy/definition"
	"cosy/filter"
	"cosy/log"
	"cosy/util"
	"cosy/websocket"
	"encoding/json"
	"strings"
	"time"

	agentDefinition "code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	agentFile "code.alibaba-inc.com/cosy/lingma-agent-graph/tool/file"
	"github.com/mark3labs/mcp-go/mcp"
)

// 定义需要处理的工具名称集合
var toolsRequiringExplanation = map[string]bool{
	"search_replace": true,
	"edit_file":     true,
	"update_tasks":  true,
	"add_tasks":     true,
}

// PushMsgToClient 通过chat/answer往端侧推送增量content
func PushMsgToClient(ctx context.Context, sessionType string, sessionId string, requestId string, content string) {
	isFiltered := filter.GetFilterStatus(requestId) == filter.StatusFiltered
	chatAnswer := definition.ChatAnswer{
		RequestId:  requestId,
		SessionId:  sessionId,
		Text:       content,
		Overwrite:  false,
		IsFiltered: isFiltered,
		Timestamp:  time.Now().UnixMicro(),
	}
	// 端侧靠sessionType识别tab，需要写回
	if chatAnswer.Extra == nil {
		chatAnswer.Extra = make(map[string]any)
	}
	chatAnswer.Extra["sessionType"] = sessionType
	chatAnswer.Extra["intentionType"] = definition.AIDeveloperIntentDetectCommonAgent
	chatAnswer.Extra["mode"] = definition.SessionModeAgent

	// 普通回复
	e := websocket.SendRequestWithTimeout(ctx, "chat/answer",
		chatAnswer, nil, 3*time.Second)
	if e != nil {
		log.Error("[common_dev_agent] SyncError chat/answer error:", e)
	}
}

func PushChatFinishToClient(ctx context.Context, requestId string, sessionId string, reason string, finishCode int, rawInputParams *definition.AskParams) {
	// 全部结束了发送ChatFinish
	chatFinish := definition.ChatFinish{
		RequestId:  requestId,
		SessionId:  sessionId,
		Reason:     reason,
		StatusCode: finishCode,
	}
	if chatFinish.Extra == nil {
		chatFinish.Extra = make(map[string]any)
	}
	chatFinish.Extra["sessionType"] = rawInputParams.SessionType
	chatFinish.Extra["intentType"] = definition.AIDeveloperIntentDetectCommonAgent
	chatFinish.Extra["mode"] = definition.SessionModeAgent
	// 结束对话
	log.Debugf("[common_dev_agent] chat finish, requestId=%s, finishCode=%d", requestId, finishCode)

	e2 := websocket.FinishChatRequest(ctx, chatFinish, 3*time.Second)
	if e2 != nil {
		log.Error("[common_dev_agent] SyncError, chat/finish error:", e2)
	}
}

// PushToolBlockToClient 推送answer中的工具块
func PushToolBlockToClient(ctx context.Context, sessionType string, sessionId string, requestId string, toolBlock string, stage string) {
	log.Debugf("[common_dev_agent] push tool, stage=%s, toolBlock=%s", stage, toolBlock)
	PushMsgToClient(ctx, sessionType, sessionId, requestId, toolBlock)
}

// SyncToolParamsToClient sync tool params to client
func SyncToolParamsToClient(ctx context.Context, sessionId string, requestId string, toolCall agentDefinition.ToolCall, status coderCommon.ToolCallStatus) {
	//Initialize working directory to project root
	projectPath := ""
	workspace, ok := ctx.Value(definition.ContextKeyWorkspace).(definition.WorkspaceInfo)
	if ok {
		projectPath, _ = workspace.GetWorkspaceFolder()
	}

	var parameters map[string]interface{}
	err := json.Unmarshal([]byte(toolCall.Function.Arguments), &parameters)
	if err != nil {
		log.Errorf("requestId=%s, toolCallId=%s, Error unmarshalling arguments: %v", requestId, toolCall.ID, err)
		parameters = make(map[string]interface{})
	}
	// mcp的移除explanation
	if strings.HasPrefix(toolCall.Function.Name, "mcp_") {
		_, exists := parameters[coderCommon.ToolCallArgumentNameExplanation]
		if exists {
			delete(parameters, coderCommon.ToolCallArgumentNameExplanation)
		}
		_, exists = parameters[coderCommon.ToolCallArgumentNameRandomString]
		if exists {
			delete(parameters, coderCommon.ToolCallArgumentNameRandomString)
		}
	}

	request := &coderCommon.ToolCallSyncRequest{
		SessionId:      sessionId,
		RequestId:      requestId,
		ProjectPath:    projectPath,
		ToolCallId:     toolCall.ID,
		ToolCallStatus: status,
		Parameters:     parameters,
	}
	syncToolInfoToClient(ctx, request)

	// 保存工具结果到到chat_message中
	SaveToolCallResult(requestId, request)
}

// SyncToolResultToClient sync tool result to client
func SyncToolResultToClient(ctx context.Context, sessionId string, requestId string, toolCall agentDefinition.ToolCall, toolCallResult *coderCommon.ToolCallResult) *coderCommon.ToolCallSyncRequest {
	//Initialize working directory to project root
	projectPath := ""
	workspace, ok := ctx.Value(definition.ContextKeyWorkspace).(definition.WorkspaceInfo)
	if ok {
		projectPath, _ = workspace.GetWorkspaceFolder()
	}

	var parameters map[string]interface{}
	err := json.Unmarshal([]byte(toolCall.Function.Arguments), &parameters)
	if err != nil {
		log.Errorf("requestId=%s, toolCallId=%s, Error unmarshalling arguments: %v", requestId, toolCall.ID, err)
		parameters = make(map[string]interface{})
	}
	// mcp的移除explanation
	if strings.HasPrefix(toolCall.Function.Name, "mcp_") {
		_, exists := parameters[coderCommon.ToolCallArgumentNameExplanation]
		if exists {
			delete(parameters, coderCommon.ToolCallArgumentNameExplanation)
		}
		_, exists = parameters[coderCommon.ToolCallArgumentNameRandomString]
		if exists {
			delete(parameters, coderCommon.ToolCallArgumentNameRandomString)
		}
	}

	request := &coderCommon.ToolCallSyncRequest{
		SessionId:      sessionId,
		RequestId:      requestId,
		ProjectPath:    projectPath,
		ToolCallId:     toolCall.ID,
		ToolCallStatus: toolCallResult.Status,
		Parameters:     parameters,
		ErrorMsg:       toolCallResult.ErrorMsg,
		ErrorCode:      toolCallResult.ErrorCode,
	}
	if toolCallResult.RawData != nil {
		request.Results = convertResultForClient(toolCall.Function.Name, toolCallResult.RawData)
	}
	syncToolInfoToClient(ctx, request)
	// 保存工具结果到到chat_message中
	if request.ToolCallStatus == coderCommon.ToolCallStatusRunningInBackground {
		// 保存的时候直接改写成FINISHED
		request.ToolCallStatus = coderCommon.ToolCallStatusFinished
	}
	return request
}

// syncToolInfoToClient sync tool info to client
func syncToolInfoToClient(ctx context.Context, request *coderCommon.ToolCallSyncRequest) {
	log.Debugf("[common_dev_agent] syncToolInfoToClient, requestId=%s, toolCallId=%s, toolCallStatus=%s", request.RequestId, request.ToolCallId, request.ToolCallStatus)
	e := websocket.SendRequestWithTimeout(ctx, "tool/call/sync",
		request, nil, 10*time.Second)
	if e != nil {
		log.Error("[common_dev_agent] SyncError tool/call/sync error:", e)
	}
}

// convertResultForClient 转换到插件渲染的格式
func convertResultForClient(toolName string, rawData interface{}) interface{} {
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("Error occurred: %v", r)
		}
	}()
	// If rawData is nil, return it directly
	if rawData == nil {
		return nil
	}
	// Try type assertions for known types
	switch v := rawData.(type) {
	case interface{ GetType() string }:
		toolType := v.GetType()
		if toolType == codebase.TypeSemanticSearch {
			if result, ok := v.(interface{ GetResult() interface{} }); ok {
				if searchResult, ok := result.GetResult().(codebase.SemanticSearchResult); ok {
					formatedResults := make([]*coderCommon.ToolFileResult, 0)
					for _, chunk := range searchResult.Documents {
						formatedResults = append(formatedResults, &coderCommon.ToolFileResult{
							FileName:  chunk.FileName,
							Path:      chunk.FilePath,
							StartLine: chunk.StartLine + 1,
							EndLine:   chunk.EndLine + 1,
						})
					}
					return formatedResults
				}
			}
		} else if toolType == codebase.TypeSymbolSearch {
			if result, ok := v.(interface{ GetResult() interface{} }); ok {
				if searchResult, ok := result.GetResult().(codebase.SymbolSearchResult); ok {
					formatedResults := make([]*coderCommon.ToolFileResult, 0)
					for _, symbol := range searchResult.Symbols {
						formatedResults = append(formatedResults, &coderCommon.ToolFileResult{
							FileName:  util.GetFileName(symbol.FilePath),
							Path:      symbol.FilePath,
							StartLine: symbol.LineRange.StartLine + 1,
							EndLine:   symbol.LineRange.EndLine + 1,
							Type:      symbol.SymbolType,
							ItemName:  symbol.SymbolName,
						})
					}
					return formatedResults
				}
			}
		}
		return v

	case *file.SearchFileResponse:
		formatedResults := make([]*coderCommon.ToolFileResult, 0)
		for _, fileItem := range v.Files {
			formatedResults = append(formatedResults, &coderCommon.ToolFileResult{
				FileName: fileItem.FileName,
				Path:     fileItem.Path,
			})
		}
		return formatedResults

	case *file.GrepCodeResponse:
		formatedResults := make([]*coderCommon.ToolFileResult, 0)
		for _, fileItem := range v.Lines {
			formatedResults = append(formatedResults, &coderCommon.ToolFileResult{
				FileName:  fileItem.FileName,
				Path:      fileItem.Path,
				StartLine: uint32(fileItem.Line) + 1,
				EndLine:   uint32(fileItem.Line) + 1,
			})
		}
		return formatedResults

	case *file.ListDirResponse:
		formatedResults := make([]*coderCommon.ToolFileResult, 0)
		for _, fileItem := range v.Files {
			fileType := "file"
			if fileItem.IsDirectory {
				fileType = "directory"
			}
			formatedResults = append(formatedResults, &coderCommon.ToolFileResult{
				FileName:  fileItem.FileName,
				Path:      fileItem.Path,
				Type:      fileType,
				FileSize:  uint32(fileItem.Size),
				FileCount: uint32(fileItem.FileCount),
			})
		}
		return formatedResults

	case *agentFile.ReadFileResponse:
		formatedResults := make([]*coderCommon.ToolFileResult, 0)
		formatedResults = append(formatedResults, &coderCommon.ToolFileResult{
			FileName:  util.GetFileName(v.Path),
			Path:      v.Path,
			StartLine: uint32(v.StartLine) + 1,
			EndLine:   uint32(v.EndLine) + 1,
		})
		return formatedResults

	case *coderBaseTool.ReadFileResponse:
		formatedResults := make([]*coderCommon.ToolFileResult, 0)
		formatedResults = append(formatedResults, &coderCommon.ToolFileResult{
			FileName:  util.GetFileName(v.Path),
			Path:      v.Path,
			StartLine: uint32(v.StartLine) + 1,
			EndLine:   uint32(v.EndLine) + 1,
		})
		return formatedResults

	case *ide.RunInTerminalResponse:
		formatedResults := make([]*coderCommon.ToolRunInTerminalResult, 0)
		formatedResults = append(formatedResults, &coderCommon.ToolRunInTerminalResult{
			TerminalId: v.TerminalId,
			Content:    v.Content,
			ExitCode:   v.ExitCode,
		})
		return formatedResults

	case *ide.GetProblemsResponse:
		formatedResults := make([]*coderCommon.ToolGetProblemsResult, 0)
		for _, problem := range v.Problems {
			formatedResults = append(formatedResults, &coderCommon.ToolGetProblemsResult{
				FileName: util.GetFileName(problem.FilePath),
				Path:     problem.FilePath,
				Severity: problem.Severity,
				Message:  problem.Message,
				Range:    problem.Range,
			})
		}
		return formatedResults

	case *apply.EditFileResponse:
		formatedResults := make([]*coderCommon.ToolEditFileResult, 0)
		formatedResult := &coderCommon.ToolEditFileResult{
			Language:   v.Language,
			Path:       v.FilePath,
			FileStatus: v.FileStatus,
		}
		if v.ApplyResult != nil {
			formatedResult.FileId = v.ApplyResult.WorkingSpaceFileId
		}
		if v.DiffApplyGenerateFinish != nil {
			formatedResult.DiffInfo = v.DiffApplyGenerateFinish.DiffInfo
		}
		formatedResults = append(formatedResults, formatedResult)
		return formatedResults

	case *memory.CreateMemoryResponse:
		formatedResults := make([]*coderCommon.ToolMemoryResult, 0)
		if v.Memory != nil {
			formatedResults = append(formatedResults, &coderCommon.ToolMemoryResult{
				ID: v.Memory.ID,
			})
		}
		return formatedResults

	case *web.FetchContentResponse:
		formatedResults := make([]*definition.ToolFetchContentResult, 0)
		formatedResults = append(formatedResults, &definition.ToolFetchContentResult{
			URL: v.URL,
		})
		return formatedResults

	case *web.SearchResponse:
		formatedResults := make([]*definition.ToolWebSearchResult, 0)
		if v.PageItems != nil {
			for _, item := range v.PageItems {
				formatedResults = append(formatedResults, &definition.ToolWebSearchResult{
					Hostname: item.Hostname,
					Link:     item.Link,
					Title:    item.Title,
				})
			}
		}
		return formatedResults

	case *ide.RunPreviewResponse:
		formatedResults := make([]*definition.ToolRunPreviewResult, 0)
		formatedResults = append(formatedResults, &definition.ToolRunPreviewResult{
			Name:     v.Name,
			ProxyUrl: v.ProxyUrl,
			Url:      v.Url,
		})
		return formatedResults

	case *mcp.CallToolResult:
		return v.Content

	case *projectrule.FetchRuleResponse:
		formatedResults := make([]*definition.ToolFetchRuleResult, 0)
		for _, rule := range v.Rules {
			formatedResults = append(formatedResults, &definition.ToolFetchRuleResult{
				RuleName: rule.Name,
				Metadata: rule.MetaData,
			})
		}
		return formatedResults

	case *human.AskUserResponse:
		formatedResults := make([]*definition.ToolAskUserResponse, 0)
		formatedResults = append(formatedResults, &definition.ToolAskUserResponse{
			Question: v.Question,
		})
		return formatedResults

	case *plan.AddTasksResponse:
		formatedResults := make([]*definition.ToolAddTasksResult, 0)
		formatedResults = append(formatedResults, &definition.ToolAddTasksResult{
			Results:    v.Results,
			DetailPlan: v.DetailPlan,
		})
		return formatedResults

	case *plan.UpdateTasksResponse:
		formatedResults := make([]*definition.ToolUpdateTasksResult, 0)
		formatedResults = append(formatedResults, &definition.ToolUpdateTasksResult{
			Results:    v.Results,
			DetailPlan: v.DetailPlan,
		})
		return formatedResults
	}

	return nil

}

func SaveToolCallResult(requestId string, request *coderCommon.ToolCallSyncRequest) {
	chatMessages, err := service.SessionServiceManager.GetChatMessageByRequest(requestId)
	if err != nil {
		log.Debugf("Error getting chat messages from database: %v", err)
		return
	}
	for _, chatMessage := range chatMessages {
		if chatMessage.Role != "tool" {
			//system role的信息不用构造返回，每次新的对话会实时组装
			continue
		}
		var message agentDefinition.Message
		err := json.Unmarshal([]byte(chatMessage.Content), &message)
		if err != nil {
			log.Debugf("Error unmarshalling message from JSON: %v", err)
			return
		}
		if request.ToolCallId == message.ToolCallID {
			resultStr, err := json.Marshal(request)
			if err != nil {
				return
			}
			_ = service.SessionServiceManager.UpdateChatMessageToolResult(chatMessage.Id, chatMessage.SessionId, string(resultStr))
		}
	}
}

// EditFileResultSyncer 同步edit_file的中间结果
type EditFileResultSyncer struct {
	SessionId    string
	RequestId    string
	ToolCall     agentDefinition.ToolCall
	CtxForClient context.Context
}

func (s *EditFileResultSyncer) Sync(ctx context.Context, response *apply.EditFileResponse) {
	//Initialize working directory to project root
	projectPath := ""
	workspace, ok := ctx.Value(definition.ContextKeyWorkspace).(definition.WorkspaceInfo)
	if ok {
		projectPath, _ = workspace.GetWorkspaceFolder()
	}

	var parameters map[string]interface{}
	err := json.Unmarshal([]byte(s.ToolCall.Function.Arguments), &parameters)
	if err != nil {
		// Handle error, maybe log it or return
		return
	}

	request := &coderCommon.ToolCallSyncRequest{
		SessionId:      s.SessionId,
		RequestId:      s.RequestId,
		ProjectPath:    projectPath,
		ToolCallId:     s.ToolCall.ID,
		ToolCallStatus: coderCommon.ToolCallStatusRunning,
		Parameters:     parameters,
	}
	request.Results = convertResultForClient(s.ToolCall.Function.Name, response)
	syncToolInfoToClient(s.CtxForClient, request)
}

func (s *EditFileResultSyncer) GetCtxForClient() context.Context {
	return s.CtxForClient
}


