package research

import (
	"context"
	chainUtil "cosy/chat/chains/chain"
	"cosy/chat/chains/common"
	chatUtil "cosy/chat/util"
	"cosy/definition"
	"cosy/global"
	"cosy/indexing"
	"cosy/log"
	"cosy/prompt"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"time"

	"github.com/shirou/gopsutil/v4/host"
)

func BuildPrompt(ctx context.Context, params *definition.AskParams, inputs map[string]any) (string, string) {
	systemPrompt := "You are a helpful assistant that can interact with a computer to solve tasks.\n<IMPORTANT>\n* If user provides a path, you should NOT assume it's relative to the current working directory. Instead, you should explore the file system to find the file before working on it.\n</IMPORTANT>\n如果你认为任务已经完成或者不能继续进展了，请使用finish工具。"
	fileIndexer, _ := ctx.Value(definition.ContextKeyFileIndexer).(*indexing.ProjectFileIndex)
	workspace := ctx.Value(definition.ContextKeyWorkspace).(definition.WorkspaceInfo)
	projectPath, _ := workspace.GetWorkspaceFolder()
	fileInfo, err := os.Stat(projectPath)
	if err == nil && !fileInfo.IsDir() {
		// 使用 filepath.Dir 获取文件所在的目录路径
		log.Debugf("projectPath is not a directory, projectPath :  %s ", projectPath)
		projectPath = filepath.Dir(projectPath)
	}
	//从attach区域取
	//var adjustedContextExtras []definition.CustomContextProviderExtra
	var contextProviderExtras []definition.CustomContextProviderExtra
	if contextExtraValue, ok := params.AttachedInputs[common.KeyContextProviderExtra]; ok {
		contextProviderExtras = contextExtraValue.([]definition.CustomContextProviderExtra)
	}
	userPromptByTask, err := buildUserPromptWithChatTask(ctx, params, contextProviderExtras, fileIndexer, inputs)

	var osInfo = ""
	var osVersion, shell string
	osVersion = runtime.GOOS
	// 获取操作系统信息
	hostInfo, err := host.Info()
	if err == nil {
		osVersion = hostInfo.OS + " " + hostInfo.PlatformVersion
		osInfo = hostInfo.OS
	}
	shell = os.Getenv("SHELL")

	var workspaceLanguages []string
	if langStat, ok := fileIndexer.GetLangStatFileIndexer(); ok {
		workspaceLanguages = langStat.GetMostLanguages(0.2)
	}
	preferredLanguage, ok := ctx.Value(common.KeyPreferredLanguage).(string)
	if !ok || preferredLanguage == "" {
		preferredLanguage = definition.LocaleZh
	}

	ideInfo := ""
	if ideConfig := ctx.Value(definition.ContextKeyIdeConfig); ideConfig != nil {
		ide, ok := ideConfig.(*definition.IdeConfig)
		if ok {
			ideInfo = ide.IdePlatform + " " + ide.IdeVersion
		}
	}

	systemInput := prompt.CoderAgentSystemPromptInput{
		BaseInput: prompt.BaseInput{
			RequestId: params.RequestId,
			SessionId: params.SessionId,
		},
		WorkspacePath:      projectPath,
		WorkspaceLanguages: workspaceLanguages,
		PreferredLanguage:  preferredLanguage,
		OsInfo:             osInfo,
		OsVersion:          osVersion,
		Shell:              shell,
		IdeInfo:            ideInfo,
		CurrentSystemTime:  time.Now().Format("2006-01-02 15:04:05"),
		IsLingmaProduct:    global.IsLingmaProduct(),
	}

	systemPrompt, err = prompt.Engine.RenderResearchAgentSystemPrompt(systemInput)
	if err != nil {
		log.Error("Failed to render ResearchAgentSystemPrompt ", err)
		return systemPrompt, userPromptByTask
	}

	userInputQuery := chainUtil.GetUserInputQueryWithoutCode(params.ChatContext)
	referenceCatalogItemsString := ""
	workspaceTreeIndexer, ok := fileIndexer.GetWorkspaceTreeFileIndexer()
	if ok {
		referenceCatalogItemsString = workspaceTreeIndexer.WorkspaceTree.GetTree(3000)
		if referenceCatalogItemsString == "." {
			referenceCatalogItemsString = ""
		}
	}
	memoryPrompt := ""
	customInstructionsStr := ""
	contextDetails := chatUtil.ConvertContextProviderExtras(contextProviderExtras)

	input := prompt.CoderAgentUserPromptInput{
		BaseInput: prompt.BaseInput{
			RequestId: params.RequestId,
			SessionId: params.SessionId,
		},
		WorkspacePath:               projectPath,
		ReferenceCatalogItemsString: strings.TrimSpace(referenceCatalogItemsString),
		MemoryPrompt:                strings.TrimSpace(memoryPrompt),
		CustomInstructions:          strings.TrimSpace(customInstructionsStr),
		ContextDetails:              contextDetails,
		UserInputQuery:              userInputQuery,
		FirstConversion:             true,
	}
	userPromptByTask, err = prompt.Engine.RenderResearchAgentUserPrompt(input)
	userPromptByTask = strings.TrimSpace(userPromptByTask)
	if err != nil {
		log.Error("Failed to render freeInputContext ", err)
		return systemPrompt, userPromptByTask
	}
	return systemPrompt, userPromptByTask
}

func buildUserPromptWithChatTask(ctx context.Context, params *definition.AskParams, contextProviderExtras []definition.CustomContextProviderExtra, fileIndexer *indexing.ProjectFileIndex, inputs map[string]any) (string, error) {
	switch params.ChatTask {
	case definition.FREE_INPUT:
		_, err := chatUtil.TransformFreeInputChatContext(params.ChatContext, fileIndexer)
		if err != nil {
			return "", err
		}
		userQuery := chainUtil.GetUserInputQuery(params.ChatContext)
		if parsedUserQueryValue, ok := inputs[common.KeyParsedUserInputQueryWithContexts]; ok {
			parsedUserQuery := parsedUserQueryValue.(string)
			userQuery = parsedUserQuery
		}
		return userQuery, nil
	default:
		userQuery := chainUtil.GetUserInputQuery(params.ChatContext)
		return userQuery, nil
	}
	return "", nil
}
