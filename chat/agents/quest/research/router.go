package research

import (
	"context"
	"cosy/chat/agents/coder/common"
	"cosy/chat/agents/support"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
)

// llm调用后的判断逻辑
// 无工具调用 -> FinishNode
// 工具需要用户确认 -> ToolConfirmNode
// 工具不需要用户确认 -> ToolNode
var afterLlmRouter = graph.NewFunctionPathRouter(
	func(ctx context.Context, input graph.State) ([]string, error) {
		agentState := input.(*common.CoderAgentState)
		messages := agentState.ShortTermMemory.Messages()
		lastMessage := messages[len(messages)-1]

		// 判断是否有tool调用
		ok, toolName := hasToolCalls(lastMessage)
		if !ok {
			return []string{"finish"}, nil
		} else if toolName == "finish" {
			return []string{"finishTool"}, nil
		} else if support.IsOverLimit(ctx, agentState.ToolCallCount, lastMessage) {
			return []string{"toolOverLimit"}, nil
		}
		return []string{"tool"}, nil
	})

func hasToolCalls(lastMessage *definition.Message) (bool, string) {
	if lastMessage.ToolCalls != nil {
		return true, lastMessage.ToolCalls[0].Function.Name
	}
	return false, ""
}