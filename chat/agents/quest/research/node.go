package research

import (
	"context"
	coderCommon "cosy/chat/agents/coder/common"
	"cosy/chat/agents/coder/compact"
	"cosy/chat/agents/support"
	toolCommon "cosy/chat/agents/tool/common"
	"cosy/chat/agents/tool/mcp"
	"cosy/chat/chains/common"
	"cosy/definition"
	cosyErrors "cosy/errors"
	"cosy/log"
	"cosy/memory/stm"
	"cosy/util"
	"errors"

	"github.com/google/uuid"

	agentDefinition "code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
)

// 初始化上下文
var initNode = graph.NewNode(
	InitNodeName,
	graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
		agentState := input.(*coderCommon.CoderAgentState)
		// 读一下历史消息
		rawInputParams := agentState.Inputs[common.KeyChatAskParams].(*definition.AskParams)
		requestId := rawInputParams.RequestId
		systemPrompt, userPrompt := BuildPrompt(ctx, rawInputParams, agentState.Inputs)
		shortTermMemory := agentState.ShortTermMemory
		shortTermMemory.Clear()
		systemMessage := &agentDefinition.Message{Role: agentDefinition.RoleTypeSystem, Content: systemPrompt}
		log.Debugf("[search_agent] message, requestId=%s, message=%+v", requestId, systemMessage)
		shortTermMemory.AddMessage(systemMessage)

		userMessage := &agentDefinition.Message{Role: agentDefinition.RoleTypeUser, Content: userPrompt}
		shortTermMemory.AddMessage(userMessage)
		saveMessageHistory(agentState, userMessage)
		return agentState, nil
	}))

// 调用大模型
var llmNode = graph.NewNode(
	LLMNodeName,
	graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
		agentState := input.(*coderCommon.CoderAgentState)
		rawInputParams := agentState.Inputs[common.KeyChatAskParams].(*definition.AskParams)
		agentContext, ok := ctx.Value(common.KeyCoderAgentContext).(*coderCommon.AgentContext)
		if !ok {
			return nil, errors.New("AgentContext not found")
		}

		toolParams := support.ConvertToModelToolParam(ctx, agentContext.Tools)
		//agent下配置mcp tools
		toolParams = append(toolParams, mcp.ListMcpTools()...)

		toolsToken, _ := support.GetToolsToken(toolParams)
		//发起llm调用前做上下文长度进行处理
		messages, _ := compact.GlobalModelCompactor.Compact(ctx, agentState.ShortTermMemory, rawInputParams.PluginPayloadConfig.IsEnableProjectRule, rawInputParams.RequestId, toolsToken)


		var response *agentDefinition.Message
		var err error
		var callServerRequestId = uuid.NewString()

		requestId := rawInputParams.RequestId
		sessionId := rawInputParams.SessionId
		sessionType := rawInputParams.SessionType
		remoteAsk, _ := support.BuildRemoteChatMessageParam(ctx, rawInputParams, messages, toolParams, "")
		modelConfig, found := support.PrepareModelConfig(rawInputParams, agentState.ToolCallCount)
		if found {
			remoteAsk.ModelConfig = modelConfig
		}
		sseCtx, cancelFunc := context.WithCancel(ctx)
		syncer := support.LLMResponseHandler{
			SessionType:   sessionType,
			SessionId:     sessionId,
			RequestId:     requestId,
			ToolCallCount: agentState.ToolCallCount,
			CtxForClient:  agentState.CtxForClient,
			CancelFunc:    cancelFunc,
		}
		remoteAsk.RequestId = callServerRequestId
		response, err = support.SubscribeCommonAgentChat(sseCtx, *rawInputParams, remoteAsk, syncer.OnDeltaContent, syncer.OnToolParseEvent)
		if err != nil {
			log.Infof("[search_agent] finish chat with error, chatRecordId=%s, requestId=%s", requestId, callServerRequestId)
		} else {
			syncer.PostSyncToolCall(sseCtx, response)
			log.Infof("[search_agent] finish chat, chatRecordId=%s, requestId=%s, responseMeta=%+v", requestId, callServerRequestId, response.ResponseMeta)
			if response.ResponseMeta.FinishReason == definition.FinishReasonErrorFinish {
				if response.ToolCalls != nil && util.Contains(toolCommon.EditFileTools, response.ToolCalls[0].Function.Name) {
					response.ResponseMeta.FinishReason = definition.FinishReasonLength
				} else {
					err = cosyErrors.New(cosyErrors.SystemError, response.Content)
				}
			} else if response.Content == "" && response.ToolCallID == "" &&
				len(response.ToolCalls) == 0 && response.ReasoningContent == "" {
				select {
				case <-ctx.Done():
					response.Content = coderCommon.EmptyMessage
				default:
					//如果lastMessage值为空，则直接退出
					log.Error("assistant response is empty.")
					err = cosyErrors.New(cosyErrors.SystemError, "assistant response is empty")
				}
			}
		}
		if err != nil {
			syncer.PostSyncToolCallOnError(sseCtx, response)
		}
		if err != nil {
			// 调用llm失败，转换一下错误，通过err退出
			return agentState, err
		}
		lastMessage := response
		if lastMessage.ReasoningContent != "" {
			thinkStr := "<think>\n" + lastMessage.ReasoningContent + "\n</think>\n"
			lastMessage.Content = thinkStr + lastMessage.Content
		}
		saveMessageHistory(agentState, lastMessage)
		agentState.ShortTermMemory.AddMessage(lastMessage)
		return agentState, nil
	}))

// 工具使用到达上限制
var toolOverLimitNode = graph.NewNode(
	ToolOverLimitNodeName,
	graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
		agentState := input.(*coderCommon.CoderAgentState)
		historyMessages := agentState.ShortTermMemory.Messages()
		lastMessage := historyMessages[len(historyMessages)-1]
		// 取消工具执行，返回工具取消执行的消息
		messages := make([]*agentDefinition.Message, 0, 2)
		toolCall := lastMessage.ToolCalls[0]
		toolCallResult := "The tool invocation was canceled because it reached the limit. If the user continues, you can re-initiate the tool call."
		callResponse := &agentDefinition.Message{
			Role:       agentDefinition.RoleTypeTool,
			ToolCallID: toolCall.ID,
			Name:       toolCall.Function.Name,
			Content:    toolCallResult,
		}
		messages = append(messages, callResponse)
		for _, message := range messages {
			saveMessageHistory(agentState, message)
		}
		agentState.ShortTermMemory.AppendMessages(messages...)
		return agentState, cosyErrors.New(cosyErrors.ToolCallOverLimit, "tool call over limit")
	}))

// 执行工具调用
var toolNode = graph.NewNode(
	ToolNodeName,
	graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
		agentState := input.(*coderCommon.CoderAgentState)
		agentContext, ok := ctx.Value(common.KeyCoderAgentContext).(*coderCommon.AgentContext)
		if !ok {
			return nil, errors.New("AgentContext not found")
		}
		availableTools := agentContext.Tools
		toolMessages := support.ExecuteTool(ctx, availableTools, agentState, nil, nil)
		// 遍历 toolMessages，对每个消息调用 addMessageHistory
		for _, message := range toolMessages {
			saveMessageHistory(agentState, message)
		}
		// 每次执行完置为false，调用次数+1
		agentState.Approval = false
		agentState.ToolCallCount++
		agentState.ShortTermMemory.AppendMessages(toolMessages...)
		return agentState, nil
	}))

var finishToolNode = graph.NewNode(
	FinishToolNodeName,
	graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
		agentState := input.(*coderCommon.CoderAgentState)
		historyMessages := agentState.ShortTermMemory.Messages()
		lastMessage := historyMessages[len(historyMessages)-1]
		agentState.Inputs["resultMessage"] = lastMessage
		return agentState, nil
	}))

func saveMessageHistory(sweBenchState *coderCommon.CoderAgentState, lastMessage *agentDefinition.Message) {
	rawInputParams := sweBenchState.Inputs[common.KeyChatAskParams].(*definition.AskParams)
	sessionId := rawInputParams.SessionId
	requestId := rawInputParams.RequestId
	log.Debugf("[search_agent] message, requestId=%s, message=%+v", requestId, lastMessage)
	if lastMessage.Content == coderCommon.EmptyMessage {
		return
	}
	stm.AddMessageHistory(sessionId, requestId, lastMessage)
}
