package research

import (
	"context"
	"cosy/chat/agents/coder"
	coderCommon "cosy/chat/agents/coder/common"
	"cosy/chat/agents/tool/codebase"
	toolCommon "cosy/chat/agents/tool/common"
	"cosy/chat/agents/tool/file"
	"cosy/chat/agents/tool/web"
	"cosy/chat/chains/common"
	codeGraph "cosy/codebase/graph"
	"cosy/codebase/symbol"
	"cosy/components"
	"cosy/definition"
	"cosy/indexing"
	"cosy/log"
	"fmt"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/tool"
	"code.alibaba-inc.com/cosy/lingma-agent-manager/executor"
)

var BuilderIdentifier = "long-running-research-agent"

const InitNodeName = "InitNode"
const LLMNodeName = "LLMNode"
const ToolNodeName = "ToolNode"
const ToolOverLimitNodeName = "ToolOverLimitNode"
const FinishToolNodeName = "FinishToolNode"

func InitExecutorBuilder() (*executor.SingleGraphExecutorBuilder, error) {
	graphConfig := &executor.GraphConfig{
		Name:             "SearchAgent",
		GraphBuilder:     agentGraphBuilder(),
		ProcessorBuilder: &coder.CommonDevProcessorBuilder{},
		Options:          []graph.CallOption{},
	}
	executorBuilder := executor.NewSingleGraphExecutorBuilder(graphConfig)
	return executorBuilder, nil
}

func agentGraphBuilder() *graph.Graph {
	graphBuilder := graph.NewGraph()

	graphBuilder.AddNode(initNode)
	graphBuilder.AddNode(llmNode)
	graphBuilder.AddNode(toolNode)
	graphBuilder.AddNode(toolOverLimitNode)
	graphBuilder.AddNode(finishToolNode)

	graphBuilder.AddEdge("START", InitNodeName)
	graphBuilder.AddEdge(InitNodeName, LLMNodeName)
	graphBuilder.AddConditionalEdges(LLMNodeName, afterLlmRouter, map[string]string{
		"finishTool":    FinishToolNodeName,
		"tool":          ToolNodeName,
		"toolOverLimit": ToolOverLimitNodeName,
		"finish":        FinishToolNodeName,
	})
	graphBuilder.AddEdge(ToolNodeName, LLMNodeName)
	graphBuilder.AddEdge(ToolOverLimitNodeName, "END")
	graphBuilder.AddEdge(FinishToolNodeName, "END")
	return graphBuilder
}

// InitAgentContext 初始化agent的context，主要是初始化tool
func InitAgentContext(ctx context.Context, sessionId string, requestId string) (context.Context, error) {
	//Initialize working directory to project root
	workspace, ok := ctx.Value(definition.ContextKeyWorkspace).(definition.WorkspaceInfo)
	if !ok {
		return nil, fmt.Errorf("workspace info not found in context")
	}
	projectPath, _ := workspace.GetWorkspaceFolder()
	fileIndexer, _ := ctx.Value(definition.ContextKeyFileIndexer).(*indexing.ProjectFileIndex)

	preferredLanguage, ok := ctx.Value(common.KeyPreferredLanguage).(string)
	if !ok || preferredLanguage == "" {
		preferredLanguage = definition.LocaleZh
	}
	toolExplanationDesc := toolCommon.ExplanationDefaultDesc

	embedder := components.NewLingmaEmbedder()
	// Create graph retriever using lingma-codebase-graph
	graphIndexer, has := fileIndexer.GetGraphFileIndexer()
	if !has {
		log.Warnf("Failed to get graph file indexer for workspace %s", projectPath)
	}

	graphSearcher := codeGraph.NewBaseGraphSearcher(graphIndexer)

	// Create symbol searcher
	symbolSearcher := symbol.NewBaseSymbolSearcher(nil, graphIndexer)

	// Create rerank provider
	rerankProvider := codebase.NewLLMRerankProvider() // Use LLMReranker for fusion mode

	searchCodebaseTool, _ := codebase.NewSearchCodebaseTool(&codebase.SearchCodebaseConfig{
		Embedder:        embedder,
		FileIndexer:     fileIndexer,
		GraphSearcher:   graphSearcher,
		SymbolSearcher:  symbolSearcher,
		RerankProvider:  rerankProvider,
		ExplanationDesc: toolExplanationDesc,
		EnableFusion:    true, // 启用融合搜索
	})
	listDirTool, _ := file.NewListDirTool(&file.ListDirConfig{
		WorkspacePath:   projectPath,
		ExplanationDesc: toolExplanationDesc,
	})
	searchFileTool, _ := file.NewSearchFileTool(&file.SearchFileConfig{
		WorkspacePath:   projectPath,
		MaxResultCount:  25,
		ExplanationDesc: toolExplanationDesc,
	})
	grepCodeTool, _ := file.NewGrepCodeTool(&file.GrepCodeConfig{
		WorkspacePath:   projectPath,
		MaxResultCount:  25,
		ExplanationDesc: toolExplanationDesc,
	})
	readFileTool, _ := codebase.NewReadFileTool(&codebase.ReadFileConfig{
		WorkspacePath:   projectPath,
		MaxLineCount:    1000,
		FileIndexer:     fileIndexer,
		ExplanationDesc: toolExplanationDesc,
	})
	fetchContentTool, _ := web.NewFetchContentTool(&web.FetchContentConfig{
		MaxContentLength: 10000,
		ExplanationDesc:  toolExplanationDesc,
	})
	searchWebTool, _ := web.NewSearchWebTool(&web.SearchWebConfig{
		ExplanationDesc: toolExplanationDesc,
	})
	availableTools := []tool.BaseTool{
		searchCodebaseTool,
		listDirTool,
		searchFileTool,
		grepCodeTool,
		readFileTool,
		fetchContentTool,
		searchWebTool,
	}
	agentContext := coderCommon.AgentContext{
		Tools: availableTools,
	}
	ctx = context.WithValue(ctx, common.KeyCoderAgentContext, &agentContext)
	return ctx, nil
}
