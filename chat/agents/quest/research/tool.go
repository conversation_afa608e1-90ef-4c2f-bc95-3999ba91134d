package research

import (
	"context"
	"fmt"
	"runtime/debug"
	"strings"
	"time"

	"cosy/chat/agents/coder"
	"cosy/chat/agents/interfaces"
	"cosy/chat/agents/quest/support"
	"cosy/chat/chains/common"
	cosyDefinition "cosy/definition"
	"cosy/errors"
	"cosy/log"
	"cosy/websocket"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/tool"
	"code.alibaba-inc.com/cosy/lingma-agent-manager/executor"
	"github.com/google/uuid"
)

type AgentToolConfig struct {
	ExplanationDesc string // explanation字段的描述
	AskParams       *cosyDefinition.AskParams
}

// 实现接口
func (c *AgentToolConfig) GetExplanationDesc() string {
	return c.ExplanationDesc
}

func (c *AgentToolConfig) GetAskParams() *cosyDefinition.AskParams {
	return c.Ask<PERSON>ara<PERSON>
}

type AgentToolRequest struct {
	Description string `json:"description"`
	Prompt      string `json:"prompt"`
}

type AgentToolResponse struct {
	lastMessage definition.Message
}

// ResearchAgentToolFactoryImpl 实现ResearchAgentToolFactory接口
type ResearchAgentToolFactoryImpl struct {
	askParam *cosyDefinition.AskParams
}

func NewResearchAgentToolFactory(askParam *cosyDefinition.AskParams) interfaces.ResearchAgentToolFactory {
	return &ResearchAgentToolFactoryImpl{
		askParam: askParam,
	}
}

func (f *ResearchAgentToolFactoryImpl) CreateResearchAgentTool(config interfaces.ResearchAgentToolConfig) (tool.BaseTool, error) {
	// 将接口转换为具体类型
	agentConfig := &AgentToolConfig{
		ExplanationDesc: config.GetExplanationDesc(),
		AskParams:       f.askParam,
	}

	invokableTool, err := NewAgentTool(agentConfig)
	if err != nil {
		return nil, err
	}

	return invokableTool, nil
}

func NewAgentTool(config *AgentToolConfig) (tool.InvokableTool, error) {
	explanationDesc := "One sentence explanation as to why this tool is being used, and how it contributes to the goal."
	if config != nil && config.ExplanationDesc != "" {
		explanationDesc = config.ExplanationDesc
	}
	toolName := "search_agent"
	toolDesc := `Launch a new agent that has access to the following tools: search_codebase, search_file, grep_code, search_symbol, list_dir, search_web, fetch_content, read_file. When you are searching for a keyword or file and are not confident that you will find the right match in the first few tries, use the agent tool to perform the search for you.
When to use the agent tool:
- If you are searching for a keyword like \"config\" or \"logger\", or for questions like \"which file does X?\", the Agent tool is strongly recommended
When NOT to use the Agent tool:
- If you want to read a specific file path, use the read_file or grep_code tool instead of the Agent tool, to find the match more quickly
- If you are searching for a specific class definition like \"class Foo\", use the search_symbol tool instead, to find the match more quickly
- If you are searching for code within a specific file or set of 2-3 files, use the read_file tool instead of tis agent tool, to find the match more quickly
- Writing code and running bash commands (use other tools for that)
- Other tasks that are not related to searching for a keyword or file
Usage notes:
1. When the agent is done, it will return a single message back to you. The result returned by the agent is not visible to the user. To show the user the result, you should send a text message back to the user with a concise summary of the result.
2. Each agent invocation is stateless. You will not be able to send additional messages to the agent, nor will the agent be able to communicate with you outside of its final report. Therefore, your prompt should contain a highly detailed task description for the agent to perform autonomously and you should specify exactly what information the agent should return back to you in its final and only message to you.
3. The agent's outputs should generally be trusted
4. Clearly tell the agent whether you expect it to write code or just to do research (search, file reads, web fetches, etc.), since it is not aware of the user's intent
`
	toolParams := &definition.Schema{
		Type: "object",
		Properties: map[string]*definition.Schema{
			"description": {
				Description: "A short (3-5 word) description of the task",
				Type:        "string",
			},
			"prompt": {
				Description: "The task for the agent to perform",
				Type:        "string",
			},
			"explanation": {
				Description: explanationDesc,
				Type:        "string",
			},
		},
		Required: []string{"prompt"},
	}
	toolInfo := &definition.ToolInfo{
		Name:        toolName,
		Description: toolDesc,
		Parameters:  toolParams,
		ReadOnly:    true,
	}
	builder, _ := InitExecutorBuilder()
	searchAgent := &searchAgentTool{
		builder:  builder,
		askPrams: config.AskParams,
	}
	return tool.NewInvokableTool(toolInfo, searchAgent.run, tool.WithOutputConverter(searchAgent.convertOutput)), nil
}

type searchAgentTool struct {
	builder  *executor.SingleGraphExecutorBuilder
	askPrams *cosyDefinition.AskParams
}

func (t *searchAgentTool) run(ctx context.Context, request *AgentToolRequest) (*AgentToolResponse, error) {
	sessionId := t.askPrams.SessionId
	requestId := uuid.NewString()
	sessionType := cosyDefinition.SessionTypeMaster
	rawInputParams := &cosyDefinition.AskParams{
		SessionId:   sessionId,
		RequestId:   requestId,
		SessionType: sessionType,
		ChatContext: map[string]any{
			"text": request.Prompt,
		},
		ChatTask: cosyDefinition.FREE_INPUT,
		Extra:    t.askPrams.Extra,
		Stream:   true,
		Mode:     cosyDefinition.SessionModeLongRunningSub,
	}
	inputs := map[string]any{
		common.KeyChatAskParams:  rawInputParams,
		common.KeyUserInputQuery: request.Prompt,
	}
	agent := t.builder.Build()
	support.CreateSessionAndRecord(ctx, rawInputParams)
	ctx, _ = InitAgentContext(ctx, sessionId, requestId)
	//目前 RequestSetId 和 RequestId 一致
	ctx = context.WithValue(ctx, common.KeyRequestId, requestId)
	ctx = context.WithValue(ctx, common.KeyRequestSetId, requestId)
	ctx = context.WithValue(ctx, common.KeySessionId, sessionId)
	ctx = context.WithValue(ctx, common.KeyChatAskParams, rawInputParams)
	var runErr error
	func() {
		defer func() {
			if r := recover(); r != nil {
				stack := debug.Stack()
				errMsg := fmt.Sprintf("Fatal error in agent.RunSync: %v\n%s", r, stack)
				log.Error(errMsg)
				runErr = fmt.Errorf("fatal error: %v", r)
			}
		}()
		runErr = agent.RunSync(ctx, inputs)
	}()
	if runErr != nil {
		log.Errorf("run sync error, agent=%s, requestId=%s, err=%v", BuilderIdentifier, requestId, runErr)
		// 全部结束了发送ChatFinish
		chatFinish := cosyDefinition.ChatFinish{
			RequestId:  rawInputParams.RequestId,
			SessionId:  rawInputParams.SessionId,
			Reason:     runErr.Error(),
			StatusCode: errors.SystemError,
		}
		if chatFinish.Extra == nil {
			chatFinish.Extra = make(map[string]any)
		}
		chatFinish.Extra["sessionType"] = rawInputParams.SessionType
		chatFinish.Extra["intentionType"] = cosyDefinition.AIDeveloperIntentDetectCommonAgent
		chatFinish.Extra["mode"] = cosyDefinition.SessionModeAgent

		// 结束对话
		e2 := websocket.SendRequestWithTimeout(ctx, "chat/finish",
			chatFinish, nil, 3*time.Second)
		if e2 != nil {
			log.Error("Chat success, send request chat/finish error:", e2)
		}
		coder.ReportError(ctx, rawInputParams.SessionId, rawInputParams.RequestId, errors.SystemError, runErr, true)
		return nil, runErr
	}
	message, ok := inputs["resultMessage"].(*definition.Message)
	if !ok {
		return nil, fmt.Errorf("expected definition.Message, got %T", message)
	}
	response := &AgentToolResponse{
		lastMessage: definition.Message{
			Content: message.Content,
		},
	}
	return response, nil
}

func (t *searchAgentTool) convertOutput(ctx context.Context, output interface{}) (*definition.ToolOutput, error) {
	response, ok := output.(*AgentToolResponse)
	if !ok {
		return nil, fmt.Errorf("expected *AgentToolResponse, got %T", output)
	}

	var outputBuilder strings.Builder
	outputBuilder.WriteString(response.lastMessage.Content)
	return &definition.ToolOutput{
		Content: outputBuilder.String(),
		RawData: response,
	}, nil
}
