package support

import (
	"context"
	coderCommon "cosy/chat/agents/coder/common"
	masterCommon "cosy/chat/agents/quest/common"
	"cosy/chat/agents/support"
	"cosy/chat/chains/chain"
	"cosy/chat/chains/common"
	"cosy/chat/service"
	"cosy/compatibility"
	"cosy/config"
	"cosy/definition"
	cosyErrors "cosy/errors"
	"cosy/log"
	"cosy/longruntask"
	"cosy/memory/stm"
	"cosy/sls"
	"cosy/user"
	cosyUti "cosy/util"
	"encoding/json"
	"errors"
	"time"

	agentDefinition "code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
)

func CreateSessionAndRecord(ctx context.Context, rawInputParams *definition.AskParams) {
	//TODO create Session & ChatRecord
	workspaceInfo, _ := ctx.Value(definition.ContextKeyWorkspace).(definition.WorkspaceInfo)
	chatSession, err := NewChatSession(workspaceInfo, *rawInputParams, func(askParams definition.AskParams) string {
		questionText := askParams.QuestionText
		if questionText != "" {
			return questionText
		}
		chatContext, _ := askParams.ChatContext.(map[string]interface{})
		text, _ := chatContext["text"].(string)
		if askParams.ChatTask == definition.FREE_INPUT {
			return text
		} else {
			return askParams.ChatTask
		}
	})
	if err == nil {
		// 将 message 序列化为 JSON
		var chatContextJSON = cosyUti.ToJsonStr(rawInputParams.ChatContext)
		var extra = cosyUti.ToJsonStr(rawInputParams.Extra)
		service.SessionServiceManager.CheckCreateChatSession(chatSession)
		var chatRecord = definition.ChatRecord{
			LikeStatus:        0,
			SessionId:         chatSession.SessionId,
			ChatContext:       chatContextJSON,
			SystemRoleContent: "",
			GmtCreate:         time.Now().UnixMilli(),
			GmtModified:       time.Now().UnixMilli(),
			RequestId:         rawInputParams.RequestId,
			ChatTask:          rawInputParams.ChatTask,
			Question:          rawInputParams.QuestionText,
			Answer:            "",
			CodeLanguage:      rawInputParams.CodeLanguage,
			IntentionType:     definition.AIDeveloperIntentDetectCommonAgent, // 这个写common_dev，端侧根据这个识别错误展示逻辑
			SessionType:       rawInputParams.SessionType,
			Extra:             extra,
			Mode:              rawInputParams.Mode,
		}
		service.SessionServiceManager.CreateChat(chatRecord)
	}
}
func SaveMessageHistory(sweBenchState *masterCommon.MasterAgentState, lastMessage *agentDefinition.Message) {
	rawInputParams := sweBenchState.Inputs[common.KeyChatAskParams].(*definition.AskParams)
	sessionId := rawInputParams.SessionId
	requestId := rawInputParams.RequestId
	log.Debugf("[common_dev_agent] message, requestId=%s, message=%+v", requestId, lastMessage)
	if lastMessage.Content == coderCommon.EmptyMessage {
		return
	}
	stm.AddMessageHistory(sessionId, requestId, lastMessage)
}

func NewChatSession(workspaceInfo definition.WorkspaceInfo, askParams definition.AskParams, titleFunc func(askParams definition.AskParams) string) (definition.ChatSession, error) {
	userInfo := user.GetCachedUserInfo()
	if userInfo == nil {
		return definition.ChatSession{}, errors.New("user not login")
	}
	projectName, projectUri, projectHash := chain.CreateProjectInfo(workspaceInfo)
	var sessionTitle = titleFunc(askParams)
	return definition.ChatSession{
		OrgID:        userInfo.OrgId,
		SessionId:    askParams.SessionId,
		SessionTitle: sessionTitle,
		UserName:     userInfo.Name,
		ChatRecords:  []definition.ChatRecord{},
		ProjectURI:   projectUri,
		ProjectId:    projectHash,
		ProjectName:  projectName,
		UserID:       userInfo.Uid,
		GmtCreate:    time.Now().UnixMilli(),
		GmtModified:  time.Now().UnixMilli(),
		SessionType:  askParams.SessionType,
		Mode:         askParams.Mode,
		Version:      compatibility.AgentChatSessionVersion,
	}, nil
}

// SaveChatRecord 只有retry场景的时候直接保存之前chat record的数据
func SaveRetryChatRecordAndSession(chatRecord definition.ChatRecord, chatSession definition.ChatSession) {
	service.SessionServiceManager.CheckCreateChatSession(chatSession)
	service.SessionServiceManager.CreateChat(chatRecord)
}

func FinishChat(ctx context.Context, payload graph.StatusChangedPayload, state *masterCommon.MasterAgentState) {
	rawInputParams := state.Inputs[common.KeyChatAskParams].(*definition.AskParams)
	requestId := rawInputParams.RequestId
	sessionId := rawInputParams.SessionId
	finishCode := cosyErrors.Success
	reason := "success"
	finishStatus := "Success"
	if payload.Status == graph.StatusError {
		finishStatus = "Error"
		innerError := payload.InnerErr
		if innerError != nil {
			llmError, ok := cosyErrors.IsUnifiedError(innerError)
			if ok {
				if llmError.Code == cosyErrors.ToolCallOverLimit {
					finishStatus = "ToolCallOverLimit"
					finishCode = cosyErrors.ToolCallOverLimit
				} else {
					finishCode = llmError.Code
				}
				reason = llmError.Message
			}
		}
		// 兜底500
		if finishCode == cosyErrors.Success {
			finishCode = cosyErrors.SystemError
		}
		// 各种异常
		ReportError(ctx, sessionId, requestId, finishCode, innerError, false)
		taskId, _ := config.GetRemoteModeTaskId()
		// TODO 报错，先把任务状态置为action required
		if taskId != "" {
			longruntask.UpdateChatTaskStatus(ctx, taskId, definition.ChatTaskStatusActionRequired, nil)
		}
	} else if payload.Status == graph.StatusUserCanceled {
		finishStatus = "UserCanceled"
		taskId, _ := config.GetRemoteModeTaskId()
		// 用户取消，置为结束
		if taskId != "" {
			longruntask.UpdateChatTaskStatus(ctx, taskId, definition.ChatTaskStatusCancelled, nil)
		}
	}

	eventData := map[string]string{
		"session_id":     sessionId,
		"request_id":     requestId,
		"request_set_id": requestId,
		"chat_record_id": requestId,
		"finish_status":  finishStatus,
	}
	go sls.Report(sls.EventTypeAgentProcessFinish, requestId, eventData)
	fileVOs, _ := service.WorkingSpaceServiceManager.ListWorkingSpaceFileVOsByChatRecord(ctx, sessionId, requestId, true)
	go sls.ReportCodeChangeRemainRate(sessionId, requestId, fileVOs)

	updateChatStatus(sessionId, requestId, finishCode, reason)
	support.PushChatFinishToClient(state.CtxForClient, requestId, sessionId, reason, finishCode, rawInputParams)
	//updateAgentSummary(ctx, state)
}

func getMessageLastSummary(messages []*agentDefinition.Message) string {
	for i := len(messages) - 1; i >= 0; i-- {
		if messages[i].Role == agentDefinition.RoleTypeAssistant {
			return messages[i].Content
		}
	}
	return ""
}

func updateChatStatus(sessionId string, requestId string, finishCode int, reason string) {
	if finishCode == cosyErrors.Success {
		return
	}
	queryRecord := definition.ChatRecord{
		RequestId: requestId,
		SessionId: sessionId,
	}
	existChatRecord, _ := service.SessionServiceManager.GetChat(queryRecord)
	if existChatRecord.RequestId != "" {
		existChatRecord.GmtModified = time.Now().UnixMilli()
		finishStatus := &coderCommon.AgentFinishStatus{
			FinishCode: finishCode,
			Reason:     reason,
		}
		extraJson, err := json.Marshal(finishStatus)
		if err != nil {
			return
		}
		existChatRecord.ErrorResult = string(extraJson)
		service.SessionServiceManager.UpdateChat(existChatRecord)
	}
}

// GetLongRunningSessionHistory 获取LongRunningAgent的会话历史
func GetLongRunningSessionHistory(ctx context.Context, sessionId string) ([]*agentDefinition.Message, bool) {
	// 先根据session Id 获取所有的chat_record
	// 如果chat_record的类型不是 Agent 则进行信息转换为一对message
	// 如果chat_record的类型是 Agent 则从chat_message中获取对话历史message
	messages := make([]*agentDefinition.Message, 0)
	records, err := service.SessionServiceManager.GetSessionRecordsForHistoryBuild(sessionId)
	if err != nil {
		log.Errorf("complete feedback context error. err: %v", err)
		return nil, false
	}
	for _, record := range records {
		mode := record.Mode
		if mode != definition.SessionModeLongRunning {
			continue
		}
		chatMessages, err := service.SessionServiceManager.GetChatMessageByRequest(record.RequestId)
		if err != nil {
			log.Errorf("Error getting chat messages from database: %v", err)
			continue
		}
		for _, chatMessage := range chatMessages {
			if string(agentDefinition.RoleTypeSystem) == chatMessage.Role {
				//system role的信息不用构造返回，每次新的对话会实时组装
				continue
			}
			var message agentDefinition.Message
			err := json.Unmarshal([]byte(chatMessage.Content), &message)
			if err != nil {
				log.Errorf("Error unmarshalling message from JSON: %v", err)
				continue // Skip this message and continue with the next one
			}
			if chatMessage.Summary != "" {
				//如果有总结的内容，那么从这个message开始
				messages = make([]*agentDefinition.Message, 0)
				message.Content = chatMessage.Summary
				message.Role = agentDefinition.RoleTypeUser
			}
			messages = append(messages, &message)
		}
		if len(messages) > 0 {
			lastMessage := messages[len(messages)-1]
			// 最近一条的message是assistant同时存在tool call需要补充一个tool的message，不然接口会报错
			if lastMessage.Role == agentDefinition.RoleTypeAssistant {
				toolCalls := lastMessage.ToolCalls
				if toolCalls != nil && len(toolCalls) > 0 {
					toolCall := lastMessage.ToolCalls[0]
					toolCallResult := "The tool invocation was canceled."
					callResponse := &agentDefinition.Message{
						Role:       agentDefinition.RoleTypeTool,
						ToolCallID: toolCall.ID,
						Name:       toolCall.Function.Name,
						Content:    toolCallResult,
					}
					messages = append(messages, callResponse)
				}
			}
		}
	}
	return messages, true
}
