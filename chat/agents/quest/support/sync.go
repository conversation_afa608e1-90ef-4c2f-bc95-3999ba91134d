package support

import (
	agentDefinition "code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"context"
	"cosy/config"
	"cosy/definition"
	"cosy/log"
	"cosy/longruntask"
)

type SummaryLLMResponseSyncer struct {
	content string // content内容
}

func (s *SummaryLLMResponseSyncer) SyncContent(ctx context.Context, contentType definition.StreamingContentType, chunk []byte) error {
	deltaAnswer := string(chunk)
	if deltaAnswer != "" {
		// answer不为空
		//if contentType != definition.StreamingContentTypeReasoning {
		//	// think不影响content是否为空的判断
		//	s.answerNotEmpty = true
		//}
		//if !s.inThink && contentType == definition.StreamingContentTypeReasoning {
		//	s.startThink()
		//} else if s.inThink && contentType == definition.StreamingContentTypeContent {
		//	s.finishThink()
		//}
		s.content = s.content + deltaAnswer
	}
	// TODO 走单独的推流方式给到服务端
	//PushMsgToClient(s.CtxForClient, s.SessionType, s.SessionId, s.RequestId, deltaAnswer)
	return nil
}

func (s *SummaryLLMResponseSyncer) SyncToolCall(ctx context.Context, toolParseEvent *definition.ToolParseEvent) {
	return
}

func (s *SummaryLLMResponseSyncer) SyncPostSummary(ctx context.Context, message *agentDefinition.Message) {
	taskMgr := longruntask.NewTaskManager()
	log.Info("SyncPostSummary", "message", message)
	taskId, _ := config.GetRemoteModeTaskId()
	if taskId == "" {
		return
	}
	_, err := taskMgr.UpdateReport(ctx, taskId, message.Content)
	if err != nil {
		log.Errorf("Failed to update report: %v", err)
	}

	return
}
