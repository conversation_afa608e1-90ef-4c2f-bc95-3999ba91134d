package support

import (
	"context"
	coderCommon "cosy/chat/agents/coder/common"
	chatUtil "cosy/chat/util"
	"cosy/definition"
	cosyErrors "cosy/errors"
	"cosy/log"
	"cosy/sls"
	"cosy/stable"
	"strconv"

	agentDefinition "code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
)

func SlsToolCall(sessionId string, requestId string, assistantMessage *agentDefinition.Message, toolCall agentDefinition.ToolCall, toolCallResult *coderCommon.ToolCallResult) {
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("Error occurred in slsToolCall: %v", r)
		}
	}()
	if toolCallResult == nil {
		return
	}
	modelResponseId := ""
	if assistantMessage != nil {
		modelResponseId = assistantMessage.ResponseMeta.ID
	}
	toolCallStatus := toolCallResult.Status
	if toolCallStatus == coderCommon.ToolCallStatusRunningInBackground {
		// 埋点日志直接改写成FINISHED
		toolCallStatus = coderCommon.ToolCallStatusFinished
	}
	eventData := map[string]string{
		"session_id":               sessionId,
		"request_id":               requestId,
		"request_set_id":           requestId,
		"chat_record_id":           requestId,
		"tool_call_id":             toolCall.ID,
		"tool_call_name":           toolCall.Function.Name,
		"tool_call_status":         string(toolCallStatus),
		"tool_call_error_code":     strconv.Itoa(toolCallResult.ErrorCode),
		"tool_call_error_msg":      toolCallResult.ErrorMsg,
		"tool_call_cost":           strconv.Itoa(toolCallResult.Cost),
		"tool_call_content_length": strconv.Itoa(len(toolCallResult.Content)),
		"model_response_id":        modelResponseId,
	}
	go sls.Report(sls.EventTypeAgentToolCallStatistics, requestId, eventData)
}

func SlsToolCallCancelled(sessionId string, requestId string, assistantMessage *agentDefinition.Message, toolCall agentDefinition.ToolCall) {
	// 用户手动取消，没有errorCode和errorMsg
	toolCallResult := &coderCommon.ToolCallResult{
		Status: coderCommon.ToolCallStatusCancelled,
		Cost:   0,
	}
	SlsToolCall(sessionId, requestId, assistantMessage, toolCall, toolCallResult)
}

func SlsToolCallOverLimit(sessionId string, requestId string, assistantMessage *agentDefinition.Message, toolCall agentDefinition.ToolCall) {
	// 工具使用次数到达上限的取消
	toolCallResult := &coderCommon.ToolCallResult{
		Status:    coderCommon.ToolCallStatusCancelled,
		ErrorCode: cosyErrors.ToolCallOverLimit,
		Cost:      0,
	}
	SlsToolCall(sessionId, requestId, assistantMessage, toolCall, toolCallResult)
}

func ReportError(ctx context.Context, sessionId string, requestId string, finishCode int, innerError error, uncaught bool) {
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("Error occurred in reportError: %v", r)
		}
	}()
	eventData := map[string]string{
		"session_id":     sessionId,
		"request_id":     requestId,
		"request_set_id": requestId,
		"chat_record_id": requestId,
		"finish_code":    strconv.Itoa(finishCode),
		"uncaught":       strconv.FormatBool(uncaught),
	}
	go stable.ReportCommonError(ctx, definition.MonitorErrorKeyCommonDevAgentChat, requestId, innerError, chatUtil.ToMap(eventData))
}
