package support

import (
	"context"
	coderCommon "cosy/chat/agents/coder/common"
	"cosy/chat/agents/support"
	"cosy/chat/agents/tool/apply"
	toolCommon "cosy/chat/agents/tool/common"
	"cosy/chat/service"
	"cosy/chat/tools"
	"cosy/definition"
	"cosy/log"
	"cosy/util"
	"cosy/websocket"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	agentDefinition "code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"github.com/google/uuid"
)

type LLMResponseHandler struct {
	SessionId           string
	RequestId           string
	SessionType         string
	CallServerRequestId string // 请求服务端的requestId
	StartTime           time.Time
	EndTime             time.Time

	ToolCallCount int // 已经使用的工具次数

	toolCallOverLimit    bool           // 工具使用超过上限
	currentToolName      string         // 这次llm调用解析处理的工具名
	currentToolCallExtra map[string]any // 需要保存的额外参数
	syncedArguments      []string       // 已经同步过的参数
	content              string         // content内容
	currentToolBlock     string         // 当前工具block块
	inThink              bool           // 进入think
	hasStart             bool           // 是否回答开始

	CtxForClient context.Context
	CancelFunc   func()
}

// SyncContent 增量推送模型结果的content
// see definition.StreamingContentTypeContent
func (s *LLMResponseHandler) SyncContent(ctx context.Context, contentType definition.StreamingContentType, chunk []byte) error {
	deltaAnswer := string(chunk)
	if strings.HasPrefix(deltaAnswer, "模型无法生成答案") {
		return nil
	}
	if deltaAnswer != "" {
		if !s.inThink && contentType == definition.StreamingContentTypeReasoning {
			s.startThink()
		} else if s.inThink && contentType == definition.StreamingContentTypeContent {
			s.finishThink()
		}
		s.content = s.content + deltaAnswer
	}
	s.syncContent(deltaAnswer)
	return nil
}

func (s *LLMResponseHandler) SyncToolCall(ctx context.Context, toolParseEvent *definition.ToolParseEvent) {
	if s.toolCallOverLimit {
		// 到达上限以后不推送工具信息
		return
	}
	switch toolParseEvent.Type {
	case definition.StartToolParseEvent:
		if s.inThink {
			s.finishThink()
		}

		// 在开始处理工具调用之前，刷新处理器链中的内容
		err := support.FlushProcessorChain(ctx, s.RequestId)
		if err != nil {
			log.Errorf("[LLMResponseHandler] flush processor chain error: %v", err)
		}

		if s.ToolCallCount == GetToolCallLimit() {
			// 工具调用到达上限后，发现存在下一次工具调用
			s.toolCallOverLimit = true
			if s.CancelFunc != nil {
				s.CancelFunc()
			}
			return
		}
		toolCall := toolParseEvent.ToolCall
		s.currentToolName = support.FormatToolName(toolCall.Function.Name)
		s.syncedArguments = make([]string, 0)
		builder := strings.Builder{}
		builder.WriteString(fmt.Sprintf("\n\n```toolCall::%s::%s::%s", s.currentToolName, toolCall.ID, coderCommon.ToolCallStatusInit))
		builder.WriteString("\n```\n\n")
		s.currentToolBlock = builder.String()
		s.syncToolBlock("StartToolParseEvent")
	case definition.DeltaParsingToolParsingEvent:
		if toolParseEvent.ToolCall == nil || toolParseEvent.ToolCall.ID == "" || toolParseEvent.ToolCall.Function.Name == "" {
			return
		}
		arguments := toolParseEvent.ToolCall.Function.Arguments
		if util.Contains(toolCommon.EditFileTools, toolParseEvent.ToolCall.Function.Name) && strings.Contains(arguments, fmt.Sprintf("\"%s\"", coderCommon.ToolCallArgumentNameFilePath)) &&
			!util.Contains(s.syncedArguments, coderCommon.ToolCallArgumentNameFilePath) {
			// edit_file工具解析出了file_path参数
			var args struct {
				FilePath string `json:"file_path"`
			}
			if err := json.Unmarshal([]byte(arguments), &args); err != nil {
				log.Error(err)
				return
			}
			s.syncedArguments = append(s.syncedArguments, coderCommon.ToolCallArgumentNameFilePath)
			log.Debugf("[common_dev_agent] edit_file generating, origin_ars=%s", arguments)
			afterPath, err := tools.CheckPath(args.FilePath, s.CtxForClient, s.RequestId, s.SessionId)
			if err != nil {
				// 文件路径非法
				s.currentToolCallExtra = map[string]any{
					coderCommon.ToolCallExtraFilePathValid: false,
				}
				return
			}
			fileId := uuid.NewString()
			if s.currentToolCallExtra == nil {
				s.currentToolCallExtra = map[string]any{}
			}
			s.currentToolCallExtra[coderCommon.ToolCallExtraFileId] = fileId
			log.Debugf("[common_dev_agent] edit_file generating, file_path=%s, fileId=%s", args.FilePath, fileId)
			// 一起推送arguments里的id_path和result里面的fileId
			editFileSyncer := &support.EditFileResultSyncer{
				SessionId:    s.SessionId,
				RequestId:    s.RequestId,
				ToolCall:     *toolParseEvent.ToolCall,
				CtxForClient: s.CtxForClient,
			}
			editFileSyncer.Sync(ctx, &apply.EditFileResponse{
				FilePath:   args.FilePath,
				FileStatus: service.GENERATING.String(),
				ApplyResult: &definition.DiffApplyResult{
					WorkingSpaceFileId: fileId,
				},
			})
			// 触发generating消息
			param := definition.DiffApplyParams{
				NeedSave:                 true,
				NeedRecord:               false,
				NeedSyncWorkingSpaceFile: true,
				NeedWebSocketMethod:      false,
				ChatRecordId:             s.RequestId,
				RequestSetId:             s.RequestId,
				SessionId:                s.SessionId,
				RequestId:                uuid.NewString(),
				Stream:                   true,
				Modification:             "",
				WorkingSpaceFile: definition.WorkingSpaceFile{
					Id:       fileId,
					FileId:   afterPath,
					Language: "",
				},
			}
			diffApplyResult := tools.DiffApply(s.CtxForClient, param)
			if !diffApplyResult.IsSuccess {
				// TODO：生成工作区异常处理逻辑?
			}
		}
	case definition.EndToolParseEvent:
	}
}

// PostSyncToolCall 最后的补偿，等EndToolParseEvent逻辑实现以后可以去掉
func (s *LLMResponseHandler) PostSyncToolCall(ctx context.Context, message *agentDefinition.Message) {
	if s.inThink {
		s.finishThink()
	}
	// 推送消息结束

	if message.ToolCalls != nil && s.currentToolCallExtra != nil {
		message.ToolCalls[0].Extra = s.currentToolCallExtra
	}
}

// PostSyncToolCallOnError 发生error时，发送一些取消的消息
func (s *LLMResponseHandler) PostSyncToolCallOnError(ctx context.Context, message *agentDefinition.Message) {
	if util.Contains(toolCommon.EditFileTools, s.currentToolName) && s.currentToolCallExtra != nil {
		fileId, ok := s.currentToolCallExtra[coderCommon.ToolCallExtraFileId].(string)
		if ok {
			// 已经触发了edit_file，要触发edit_file的取消消息
			service.WorkingSpaceServiceManager.OperateWorkingSpaceFile(ctx, definition.WorkingSpaceFileOperateParams{
				Id:     fileId,
				OpType: service.CANCEL.String(),
				Params: map[string]interface{}{
					service.IS_FAILED:  true,
					service.ERROR_CODE: definition.UnknownErrorCode,
				},
			})
		}
	}
}

func (s *LLMResponseHandler) startThink() {
	s.inThink = true
	s.content = s.content + "<think>\n"
	s.syncContent("<think>\n")
}

func (s *LLMResponseHandler) finishThink() {
	s.inThink = false
	if strings.HasSuffix(s.content, "</think>") {
		s.content = s.content + "\n"
		s.syncContent("\n")
	} else {
		s.content = s.content + "\n</think>\n"
		s.syncContent("\n</think>\n")
	}
}

func (s *LLMResponseHandler) syncContent(content string) {
	if !s.hasStart {
		s.syncStart()
	}
	support.PushMsgToClient(s.CtxForClient, s.SessionType, s.SessionId, s.RequestId, content)
}

func (s *LLMResponseHandler) syncToolBlock(stage string) {
	if !s.hasStart {
		s.syncStart()
	}
	support.PushToolBlockToClient(s.CtxForClient, s.SessionType, s.SessionId, s.RequestId, s.currentToolBlock, stage)
}

func (s *LLMResponseHandler) syncStart() {
	s.hasStart = true
	s.StartTime = time.Now()

	chatMessageStage := definition.ChatMessageStage{
		SessionId: s.SessionId,
		RequestId: s.RequestId,
		MessageId: s.CallServerRequestId,
		Timestamp: s.StartTime.UnixMilli(),
		Stage:     "START",
	}
	e := websocket.SendRequestWithTimeout(s.CtxForClient, "chat/message/stage",
		chatMessageStage, nil, 3*time.Second)
	if e != nil {
		log.Error("SyncError chat/message/stage error:", e)
	}

	startAnchor := fmt.Sprintf("```message::start::%s::%d```\n", s.CallServerRequestId, s.StartTime.UnixMilli())
	support.PushMsgToClient(s.CtxForClient, s.SessionType, s.SessionId, s.RequestId, startAnchor)
}

func (s *LLMResponseHandler) SyncEndAndFillTime(message *agentDefinition.Message) {
	if !s.hasStart {
		return
	}
	s.EndTime = time.Now()
	endAnchor := fmt.Sprintf("\n```message::end::%s::%d```\n", s.CallServerRequestId, s.EndTime.UnixMilli())
	support.PushMsgToClient(s.CtxForClient, s.SessionType, s.SessionId, s.RequestId, endAnchor)
	chatMessageStage := definition.ChatMessageStage{
		SessionId: s.SessionId,
		RequestId: s.RequestId,
		MessageId: s.CallServerRequestId,
		Timestamp: s.EndTime.UnixMilli(),
		Stage:     "END",
	}
	e := websocket.SendRequestWithTimeout(s.CtxForClient, "chat/message/stage",
		chatMessageStage, nil, 3*time.Second)
	if e != nil {
		log.Error("SyncError chat/message/stage error:", e)
	}

	if message != nil && message.Extra != nil {
		// 记录一下开始、结束时候，后面写db的时候还需要
		message.Extra["startTime"] = s.StartTime
		message.Extra["endTime"] = s.EndTime
	}
}
