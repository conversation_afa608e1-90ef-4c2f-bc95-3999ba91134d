package support

import (
	"context"
	"cosy/definition"
	"cosy/indexing"
)

// TriggerFileIndexer 异步触发索引构建
func TriggerFileIndexer(ctx context.Context, projectPath string) *indexing.ProjectFileIndex {
	fileIndexer, _ := ctx.Value(definition.ContextKeyFileIndexer).(*indexing.ProjectFileIndex)
	//go func() {
	//	if fileIndexer == nil {
	//		log.Warnf("requirement analysis workspace indexer is nil")
	//	} else {
	//		if value, requestIdOk := definition.IndexProgressMap.Load(projectPath); requestIdOk {
	//			indexRequestId := value.(string)
	//			// 获取到上次建立索引的全库索引进度
	//			record := manager.GlobalIndexBuilderManager.ReportIndexBuildingProgress(indexRequestId, manager.VectorIndexTopic)
	//			if record != nil {
	//				// 存在有效的全库索引进度，判定是否索引建立过程是否完成
	//				if !record.Finished {
	//					// 当前全量索引记录还未完成，跳过本次全库建立索引
	//					log.Warnf("skip indexing for %s, last indexing request id: %s", projectPath, indexRequestId)
	//					return
	//				}
	//
	//				// 上一次文件全库索引建立已经完成，判定是否需要本次建立全库索引
	//				vectorEngine, err := rag.GetChatVectorRetrieveEngine(projectPath)
	//				if err != nil {
	//					log.Errorf("init vector engine error: %v", err)
	//					return
	//				}
	//
	//				// 获取当前已建立索引的文件数量
	//				successIndexFileNum := vectorEngine.GetStorageFileNum()
	//				// 计算已索引比例
	//				percent := float64(successIndexFileNum) / float64(record.TotalValidTaskCnt)
	//				if percent >= definition.SkipScanStoragePercentage {
	//					// 存储文件达到90%比例，跳过本次全库索引
	//					log.Warnf("skip indexing for %s, last indexing request id: %s", projectPath, indexRequestId)
	//					return
	//				}
	//			}
	//		}
	//		definition.IndexProgressMtx.Lock()
	//		defer definition.IndexProgressMtx.Unlock()
	//
	//		// double check是否需要建立索引
	//		if value, requestIdOk := definition.IndexProgressMap.Load(projectPath); requestIdOk {
	//			indexRequestId := value.(string)
	//			// 获取到上次建立索引的全库索引进度
	//			record := manager.GlobalIndexBuilderManager.ReportIndexBuildingProgress(indexRequestId, manager.VectorIndexTopic)
	//			if record != nil {
	//				// 存在有效的全库索引进度，判定是否索引建立过程是否完成
	//				if !record.Finished {
	//					// 当前全量索引记录还未完成，跳过本次全库建立索引
	//					log.Warnf("skip indexing for %s, last indexing request id: %s", projectPath, indexRequestId)
	//					return
	//				}
	//
	//				// 上一次文件全库索引建立已经完成，判定是否需要本次建立全库索引
	//				vectorEngine, err := rag.GetChatVectorRetrieveEngine(projectPath)
	//				if err != nil {
	//					log.Errorf("init vector engine error: %v", err)
	//					return
	//				}
	//
	//				// 获取当前已建立索引的文件数量
	//				successIndexFileNum := vectorEngine.GetStorageFileNum()
	//				// 计算已索引比例
	//				percent := float64(successIndexFileNum) / float64(record.TotalValidTaskCnt)
	//				if percent >= definition.SkipScanStoragePercentage {
	//					// 存储文件达到90%比例，跳过本次全库索引
	//					log.Warnf("skip indexing for %s, last indexing request id: %s", projectPath, indexRequestId)
	//					return
	//				}
	//			}
	//		}
	//
	//		log.Infof("start build chat indexing for %s", projectPath)
	//		// 建立全库索引
	//		indexingRequestId := uuid.NewString()
	//		definition.IndexProgressMap.Store(projectPath, indexingRequestId)
	//		param := indexing.NewChatRagProjectIndexParamWithRequestId(indexingRequestId)
	//		err := fileIndexer.IndexWorkspace(param)
	//		if err != nil {
	//			log.Errorf("index building error: %v", err)
	//		}
	//	}
	//}()
	return fileIndexer
}
