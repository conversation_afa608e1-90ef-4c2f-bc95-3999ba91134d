package support

import (
	"context"
	"cosy/config"
	"cosy/extension/plan"
	"cosy/log"
	"cosy/longruntask"
)

type UploadPlanRequest struct {
	Tasks []*plan.TaskNode `json:"tasks"`
}

func UploadPlanToServer(sessionId string) {
	taskStatistics := plan.GetTaskStatistics(sessionId)
	totalCount := taskStatistics.Total
	completeCount := taskStatistics.Complete

	markdown := ""
	jsonStr := ""
	convertPlan, ok := plan.GenerateDetailPlan(sessionId)
	if ok {
		markdown = convertPlan.MarkdownContent
		jsonStr = convertPlan.TaskTreeJson
	}
	taskId, _ := config.GetRemoteModeTaskId()
	if taskId == "" {
		return
	}
	taskManager := longruntask.NewTaskManager()
	log.Debugf("UpdateActionFlow, taskId: %s, markdown: %s, jsonStr: %s", taskId, markdown, jsonStr)
	taskManager.UpdateActionFlow(context.Background(), taskId, markdown, string(jsonStr), totalCount, completeCount)
}
