package longrunning

import (
	"context"
	coderCommon "cosy/chat/agents/coder/common"
	"cosy/chat/agents/tool/apply"
	"cosy/chat/agents/tool/codebase"
	toolCommon "cosy/chat/agents/tool/common"
	"cosy/chat/agents/tool/file"
	"cosy/chat/agents/tool/human"
	"cosy/chat/agents/tool/ide"
	"cosy/chat/agents/tool/lsp"
	"cosy/chat/agents/tool/memory"
	"cosy/chat/agents/tool/plan"
	"cosy/chat/agents/tool/projectrule"
	"cosy/chat/agents/tool/terminal"
	"cosy/chat/agents/tool/web"
	"cosy/chat/chains/common"
	codeGraph "cosy/codebase/graph"
	"cosy/codebase/symbol"
	"cosy/components"
	"cosy/config"
	"cosy/definition"
	"cosy/global"
	"cosy/indexing"
	"cosy/log"
	"fmt"
	"time"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/tool"
	"code.alibaba-inc.com/cosy/lingma-agent-manager/executor"
)

var BuilderIdentifier = "long-running-agent"

const InitNodeName = "InitNode"
const LLMNodeName = "LLMNode"
const ToolNodeName = "ToolNode"
const ToolOverLimitNodeName = "ToolOverLimitNode"
const HandleUserRequestNodeName = "HandleUserRequestNode"
const CheckNodeName = "CheckNode"
const SummaryNodeName = "SummaryNode"
const FinishNodeName = "FinishNode"

const StateExtraHasPlanFinish = "hasFinishPlan"
const StateExtraExitForMessage = "exitForMessage"

func InitExecutorBuilder() (*executor.SingleGraphExecutorBuilder, error) {
	graphConfig := &executor.GraphConfig{
		Name:             "LongRunningAgent",
		GraphBuilder:     agentGraphBuilder(),
		ProcessorBuilder: &ProcessorBuilder{},
		Options:          []graph.CallOption{},
	}
	executorBuilder := executor.NewSingleGraphExecutorBuilder(graphConfig)
	return executorBuilder, nil
}

func agentGraphBuilder() *graph.Graph {
	graphBuilder := graph.NewGraph()

	graphBuilder.AddNode(initNode)
	graphBuilder.AddNode(llmNode)
	graphBuilder.AddNode(toolNode)
	graphBuilder.AddNode(toolOverLimitNode)
	graphBuilder.AddNode(handleUserRequestNode)
	graphBuilder.AddNode(checkNode)
	graphBuilder.AddNode(summaryNode)
	graphBuilder.AddNode(finishNode)

	graphBuilder.AddEdge("START", InitNodeName)
	graphBuilder.AddEdge(InitNodeName, LLMNodeName)
	graphBuilder.AddConditionalEdges(LLMNodeName, afterLlmRouter, map[string]string{
		"tool":          ToolNodeName,
		"toolOverLimit": ToolOverLimitNodeName,
		"check":         CheckNodeName,
	})
	// 工具执行完，查询一下有没有要处理的用户请求
	graphBuilder.AddEdge(ToolNodeName, HandleUserRequestNodeName)
	graphBuilder.AddConditionalEdges(HandleUserRequestNodeName, afterHandleUserRequestRouter, map[string]string{
		"llm":    LLMNodeName,
		"finish": FinishNodeName,
	})

	graphBuilder.AddConditionalEdges(CheckNodeName, afterCheckRouter, map[string]string{
		"summary":  SummaryNodeName,
		"continue": HandleUserRequestNodeName,
	})
	graphBuilder.AddEdge(SummaryNodeName, FinishNodeName)
	graphBuilder.AddEdge(ToolOverLimitNodeName, "END")
	graphBuilder.AddEdge(FinishNodeName, "END")
	return graphBuilder
}

// InitAgentContext 初始化agent的context，主要是初始化tool
func InitAgentContext(ctx context.Context, sessionId string, requestId string, askParams *definition.AskParams) (context.Context, error) {
	//Initialize working directory to project root
	workspace, ok := ctx.Value(definition.ContextKeyWorkspace).(definition.WorkspaceInfo)
	if !ok {
		return nil, fmt.Errorf("workspace info not found in context")
	}
	projectPath, _ := workspace.GetWorkspaceFolder()

	// 检查indexer，直到indexer已经初始化
	retryCount := 0
	for !global.GetIndexerFlag(projectPath) {
		log.Debugf("init agent, indexer not initialized, retry count: %d, projectPath: %s", retryCount, projectPath)
		time.Sleep(1 * time.Second)
		retryCount++
		if retryCount > 60 {
			log.Errorf("indexer not initialized, retry count: %d, projectPath: %s", retryCount, projectPath)
			break
		}
	}

	fileIndexer, _ := ctx.Value(definition.ContextKeyFileIndexer).(*indexing.ProjectFileIndex)

	preferredLanguage, ok := ctx.Value(common.KeyPreferredLanguage).(string)
	if !ok || preferredLanguage == "" {
		preferredLanguage = definition.LocaleZh
	}
	toolExplanationDesc := toolCommon.ExplanationDefaultDesc

	embedder := components.NewLingmaEmbedder()
	// Create graph retriever using lingma-codebase-graph
	graphIndexer, has := fileIndexer.GetGraphFileIndexer()
	if !has {
		log.Warnf("Failed to get graph file indexer for workspace %s", projectPath)
	}

	graphSearcher := codeGraph.NewBaseGraphSearcher(graphIndexer)

	// Create symbol searcher
	symbolSearcher := symbol.NewBaseSymbolSearcher(nil, graphIndexer)

	// Create rerank provider
	rerankProvider := codebase.NewLingmaRerankProvider() // Use LLMReranker for fusion mode

	searchCodebaseTool, _ := codebase.NewSearchCodebaseTool(&codebase.SearchCodebaseConfig{
		Embedder:        embedder,
		FileIndexer:     fileIndexer,
		GraphSearcher:   graphSearcher,
		SymbolSearcher:  symbolSearcher,
		RerankProvider:  rerankProvider,
		ExplanationDesc: toolExplanationDesc,
		EnableFusion:    true, // 启用融合搜索
	})
	listDirTool, _ := file.NewListDirTool(&file.ListDirConfig{
		WorkspacePath:   projectPath,
		ExplanationDesc: toolExplanationDesc,
	})
	searchFileTool, _ := file.NewSearchFileTool(&file.SearchFileConfig{
		WorkspacePath:   projectPath,
		MaxResultCount:  25,
		ExplanationDesc: toolExplanationDesc,
	})
	grepCodeTool, _ := file.NewGrepCodeTool(&file.GrepCodeConfig{
		WorkspacePath:   projectPath,
		MaxResultCount:  25,
		ExplanationDesc: toolExplanationDesc,
	})
	readFileTool, _ := codebase.NewReadFileTool(&codebase.ReadFileConfig{
		WorkspacePath:   projectPath,
		MaxLineCount:    1000,
		FileIndexer:     fileIndexer,
		ExplanationDesc: toolExplanationDesc,
	})
	searchReplaceTool, _ := apply.NewSearchReplaceTool(&apply.SearchReplaceConfig{
		SessionId:       sessionId,
		RequestId:       requestId,
		ExplanationDesc: toolExplanationDesc,
	})
	editFileTool, _ := apply.NewEditFileTool(&apply.EditFileConfig{
		SessionId:       sessionId,
		RequestId:       requestId,
		ExplanationDesc: toolExplanationDesc,
	})
	createFileTool, _ := apply.NewCreateFileTool(&apply.CreateFileConfig{
		SessionId:       sessionId,
		RequestId:       requestId,
		ExplanationDesc: toolExplanationDesc,
	})
	deleteFilesTool, _ := apply.NewDeleteFilesTool(&apply.DeleteFilesConfig{
		SessionId:       sessionId,
		RequestId:       requestId,
		ExplanationDesc: toolExplanationDesc,
	})
	var (
		runInTerminalTool     tool.InvokableTool
		getTerminalOutputTool tool.InvokableTool
		getProblemsTool       tool.InvokableTool
	)
	if config.IsRemoteAgentMode() {
		runInTerminalTool, _ = terminal.NewRunInTerminalTool(&ide.RunInTerminalConfig{
			WorkspacePath:   projectPath,
			RequestId:       requestId,
			Timeout:         30 * 60,
			ExplanationDesc: toolExplanationDesc,
		})
		getTerminalOutputTool, _ = terminal.NewGetTerminalOutputTool(&ide.GetTerminalOutputConfig{
			RequestId:       requestId,
			ExplanationDesc: toolExplanationDesc,
		})
		getProblemsTool, _ = lsp.NewGetProblemsTool(&ide.GetProblemsConfig{
			RequestId:       requestId,
			Timeout:         5 * 60,
			ExplanationDesc: toolExplanationDesc,
		})
	} else {
		runInTerminalTool, _ = ide.NewRunInTerminalTool(&ide.RunInTerminalConfig{
			WorkspacePath:   projectPath,
			RequestId:       requestId,
			Timeout:         30 * 60,
			ExplanationDesc: toolExplanationDesc,
		})
		getTerminalOutputTool, _ = ide.NewGetTerminalOutputTool(&ide.GetTerminalOutputConfig{
			RequestId:       requestId,
			ExplanationDesc: toolExplanationDesc,
		})
		getProblemsTool, _ = ide.NewGetProblemsTool(&ide.GetProblemsConfig{
			RequestId:       requestId,
			Timeout:         5 * 60,
			ExplanationDesc: toolExplanationDesc,
		})
	}
	createMemoryTool, _ := memory.NewCreateMemoryTool(&memory.CreateMemoryConfig{
		SessionId: sessionId,
		RequestId: requestId,
	})
	fetchContentTool, _ := web.NewFetchContentTool(&web.FetchContentConfig{
		MaxContentLength: 10000,
		ExplanationDesc:  toolExplanationDesc,
	})
	searchWebTool, _ := web.NewSearchWebTool(&web.SearchWebConfig{
		ExplanationDesc: toolExplanationDesc,
	})
	searchMemoryTool, _ := memory.NewSearchMemoryTool(&memory.SearchMemoryConfig{
		Embedder:        embedder,
		FileIndexer:     fileIndexer,
		WorkspacePath:   projectPath,
		ExplanationDesc: toolExplanationDesc,
	})
	fetchRuleTool, _ := projectrule.NewFetchRuleTool(&projectrule.FetchRuleConfig{
		WorkspacePath: projectPath,
	})
	//readTasklistTool, _ := plan.NewReadTasklistTool(&plan.ReadTasklistConfig{})
	addTasksTool, _ := plan.NewAddTasksTool(&plan.AddTasksConfig{})
	updateTasksTool, _ := plan.NewUpdateTasksTool(&plan.UpdateTasksConfig{})
	askUserTool, _ := human.NewAskUserTool(&human.AskUserConfig{})
	availableTools := []tool.BaseTool{
		searchCodebaseTool,
		listDirTool,
		searchFileTool,
		grepCodeTool,
		readFileTool,
		searchReplaceTool,
		editFileTool,
		createFileTool,
		deleteFilesTool,
		runInTerminalTool,
		getTerminalOutputTool,
		getProblemsTool,
		createMemoryTool,
		fetchContentTool,
		searchWebTool,
		fetchRuleTool,
		//readTasklistTool,
		searchMemoryTool,
		addTasksTool,
		updateTasksTool,
		askUserTool,
	}
	agentContext := coderCommon.AgentContext{
		Tools: availableTools,
	}
	ctx = context.WithValue(ctx, common.KeyCoderAgentContext, &agentContext)
	return ctx, nil
}
