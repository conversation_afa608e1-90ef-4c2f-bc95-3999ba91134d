package longrunning

import (
	"context"
	coderCommon "cosy/chat/agents/coder/common"
	masterCommon "cosy/chat/agents/quest/common"
	masterSupport "cosy/chat/agents/quest/support"
	"cosy/chat/agents/support"
	toolCommon "cosy/chat/agents/tool/common"
	"cosy/chat/chains/common"
	"cosy/chat/service"
	chatUtil "cosy/chat/util"
	"cosy/config"
	"cosy/definition"
	"cosy/log"
	"cosy/longruntask"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/memory"

	agentDefinition "code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"code.alibaba-inc.com/cosy/lingma-agent-manager/executor"
)

var _ executor.DataProcessor = &AgentProcessor{}

type AgentProcessor struct {
}

type EditFiles struct {
	Files []string
}

func (p AgentProcessor) ConvertInitInput(ctx context.Context, request interface{}) (graph.State, error) {
	inputs := request.(map[string]interface{})
	rawInputParams := inputs[common.KeyChatAskParams].(*definition.AskParams)
	//运行时附加上下文
	rawInputParams.AttachedInputs = inputs
	maxInputTokens := 96_000
	modelName := "qwen3"
	modelConfig := chatUtil.PrepareModelConfig(*rawInputParams)
	if modelConfig != nil {
		maxInputTokens = GetMaxTokens(modelConfig)
		modelName = modelConfig.Model
	}
	return &masterCommon.MasterAgentState{
		Inputs:          inputs,
		ShortTermMemory: memory.NewTokenLimitedMemory(maxInputTokens, modelName),
		CtxForClient:    ctx,
		Extra: map[string]any{
			"agent_state_in_chan": inputs["agent_state_in_chan"],
		},
	}, nil
}

func GetMaxTokens(modelConfig *definition.ModelConfig) int {
	if modelConfig.MaxInputTokens > 0 {
		return modelConfig.MaxInputTokens
	}
	return config.GlobalTruncateConfig.GetChunkLimit(config.TruncateKeyCommonAgent)

}

func (p AgentProcessor) ConvertResumeInput(request interface{}, state graph.State) (graph.State, error) {
	param, ok := request.(*definition.ToolConfirmRequest)
	if !ok {
		return state, nil
	}
	coderState := state.(*masterCommon.MasterAgentState)
	coderState.Approval = param.Approval
	log.Debugf("[common_dev_agent] resume, param=%+v", param)
	return coderState, nil
}

func (p AgentProcessor) ResponsibleFor(request interface{}, state graph.State) (bool, error) {
	return true, nil
}

func (p AgentProcessor) HandleGraphEvent(ctx context.Context, result graph.Event) {
	if result.Type == graph.StatusChanged {
		payload := result.PayLoad.(graph.StatusChangedPayload)
		state := payload.State.(*masterCommon.MasterAgentState)
		if isGraphFinalStatus(payload.Status) {
			masterSupport.FinishChat(ctx, payload, state)
		} else if payload.Status == graph.StatusInterruptBefore {

		}
	}
}

func (p AgentProcessor) HandleNodeStart(ctx context.Context, input graph.State, node map[string]any) {
	nodeName := node["name"].(string)
	if nodeName == InitNodeName {
		taskId, _ := config.GetRemoteModeTaskId()
		if taskId != "" {
			longruntask.UpdateChatTaskStatus(ctx, taskId, definition.ChatTaskStatusRunning, nil)
		}
	} else if nodeName == ToolNodeName {
		// 推送工具running状态
		state := input.(*masterCommon.MasterAgentState)
		historyMessages := state.ShortTermMemory.Messages()
		lastMessage := historyMessages[len(historyMessages)-1]
		rawInputParams := state.Inputs[common.KeyChatAskParams].(*definition.AskParams)
		requestId := rawInputParams.RequestId
		sessionId := rawInputParams.SessionId
		toolCall := lastMessage.ToolCalls[0]
		support.SyncToolParamsToClient(state.CtxForClient, sessionId, requestId, toolCall, coderCommon.ToolCallStatusRunning)
	} else if nodeName == SummaryNodeName {
		taskId, _ := config.GetRemoteModeTaskId()
		if taskId != "" {
			longruntask.UpdateChatTaskStatus(ctx, taskId, definition.ChatTaskStatusSummarizing, nil)
		}
	}
}

func (p AgentProcessor) HandleNodeEnd(ctx context.Context, output graph.State, node map[string]any) {
	nodeName := node["name"].(string)
	if nodeName == LLMNodeName {
		state := output.(*masterCommon.MasterAgentState)
		historyMessages := state.ShortTermMemory.Messages()
		lastMessage := historyMessages[len(historyMessages)-1]
		rawInputParams := state.Inputs[common.KeyChatAskParams].(*definition.AskParams)
		requestId := rawInputParams.RequestId
		sessionId := rawInputParams.SessionId
		// LLM消息只有一条
		message := lastMessage
		//手动终止的时候会补充这个内容
		if message.Content == coderCommon.EmptyMessage {
			return
		}
		startAnchor, endAnchor := buildStartAndEndAnchor(message)
		//更新到chat_record的answer中
		updateChatRecordAnswer(requestId, sessionId, startAnchor+message.Content)

		if message.ToolCalls != nil {
			// toolCall现在只支持1次1个
			toolCall := message.ToolCalls[0]
			//``toolCall::${tool_name}::${tool_call_id}::${tool_call_status}
			//```
			builder := strings.Builder{}
			builder.WriteString(fmt.Sprintf("\n\n```toolCall::%s::%s::%s", support.FormatToolName(toolCall.Function.Name), toolCall.ID, coderCommon.ToolCallStatusInit))
			builder.WriteString("\n```\n\n")
			//更新到chat_record的answer中
			updateChatRecordAnswer(requestId, sessionId, builder.String()+endAnchor)

			// 再更新参数、状态信息
			support.SyncToolParamsToClient(state.CtxForClient, sessionId, requestId, toolCall, coderCommon.ToolCallStatusInit)
		}
	} else if nodeName == ToolNodeName {
		state := output.(*masterCommon.MasterAgentState)
		rawInputParams := state.Inputs[common.KeyChatAskParams].(*definition.AskParams)
		requestId := rawInputParams.RequestId
		sessionId := rawInputParams.SessionId
		historyMessages := state.ShortTermMemory.Messages()
		lastTwoMessages := historyMessages[len(historyMessages)-2:]
		// 推送工具结果
		assistantMessage := lastTwoMessages[0]
		toolMessage := lastTwoMessages[1]
		if assistantMessage.ToolCalls != nil {
			// toolCall现在只支持1次1个
			toolCall := assistantMessage.ToolCalls[0]
			toolCallResult, ok := toolMessage.Extra[coderCommon.KeyToolCallResult].(*coderCommon.ToolCallResult)
			if !ok {
				support.SyncToolParamsToClient(state.CtxForClient, sessionId, requestId, toolCall, coderCommon.ToolCallStatusFinished)
			} else {
				request := support.SyncToolResultToClient(state.CtxForClient, sessionId, requestId, toolCall, toolCallResult)
				// 保存工具结果到到chat_message中
				support.SaveToolCallResult(requestId, request)

				if toolCallResult.Status == coderCommon.ToolCallStatusFinished {
					toolName := toolCall.Function.Name
					if toolName == "add_tasks" || toolName == "update_tasks" || toolName == "write_tasklist" {
						log.Debugf("UploadPlanToServer, toolName: %s", toolName)
						masterSupport.UploadPlanToServer(sessionId)
					}
				}
				masterSupport.SlsToolCall(sessionId, requestId, assistantMessage, toolCall, toolCallResult)
			}
			// 清除extra中的toolCallResult
			toolMessage.Extra[coderCommon.KeyToolCallResult] = nil
		}
	} else if nodeName == SummaryNodeName {
		taskId, _ := config.GetRemoteModeTaskId()
		if taskId != "" {
			longruntask.UpdateChatTaskStatus(ctx, taskId, definition.ChatTaskStatusAwaitingConfirmation, nil)
		}
	}
}

func (p AgentProcessor) HandleNodeError(ctx context.Context, input graph.State, node map[string]any, err error) {
	nodeName := node["name"].(string)
	if nodeName == ToolOverLimitNodeName {
		state := input.(*masterCommon.MasterAgentState)
		rawInputParams := state.Inputs[common.KeyChatAskParams].(*definition.AskParams)
		requestId := rawInputParams.RequestId
		sessionId := rawInputParams.SessionId
		historyMessages := state.ShortTermMemory.Messages()
		lastTwoMessages := historyMessages[len(historyMessages)-2:]
		message := lastTwoMessages[0]
		if message.ToolCalls != nil {
			// toolCall现在只支持1次1个
			toolCall := message.ToolCalls[0]
			masterSupport.SlsToolCallOverLimit(sessionId, requestId, message, toolCall)
		}
	}
}

type ProcessorBuilder struct {
}

func (b *ProcessorBuilder) Build() executor.DataProcessor {
	return &AgentProcessor{}
}

type FormatedMessage struct {
	Role       string     `json:"role"`
	Content    string     `json:"content"`
	ToolCalls  []ToolCall `json:"tool_calls,omitempty"`
	ToolCallID string     `json:"tool_call_id,omitempty"`
}

type ToolCall struct {
	Function agentDefinition.FunctionCall `json:"function"`
	Id       string                       `json:"id"`
	Type     string                       `json:"type"`
}

func isGraphFinalStatus(status graph.Status) bool {
	return status == graph.StatusDone || status == graph.StatusError || status == graph.StatusUserCanceled
}

func updateChatRecordAnswer(requestId, sessionId string, answer string) {
	if answer == "" {
		return
	}
	var updateChatRecord = definition.ChatRecord{
		RequestId:    requestId,
		SessionId:    sessionId,
		Answer:       answer,
		FinishStatus: 0,
	}
	existChatRecord, _ := service.SessionServiceManager.GetChat(updateChatRecord)
	if existChatRecord.RequestId != "" {
		//answer内容追加
		updateChatRecord.Answer = existChatRecord.Answer + updateChatRecord.Answer
		service.SessionServiceManager.UpdateChatContent(updateChatRecord)
	}
}

func getToolExplanation(arguments string) string {
	var args struct {
		Explanation string `json:"explanation"`
	}
	if err := json.Unmarshal([]byte(arguments), &args); err != nil {
		log.Error(fmt.Sprintf("getToolExplanation err, arguments: %s, error: %v", arguments, err))
		return ""
	}
	if args.Explanation != "" {
		//llm偶现直接返回了explanation参数的原文prompt
		if strings.HasPrefix(args.Explanation, toolCommon.ExplanationDefaultDesc) {
			args.Explanation = ""
		}
	}
	return args.Explanation
}

func buildStartAndEndAnchor(message *agentDefinition.Message) (string, string) {
	startAnchor := ""
	endAnchor := ""
	extra := message.Extra
	if extra == nil {
		return "", ""
	}
	if extra["startTime"] != nil {
		startAnchor = fmt.Sprintf("```message::start::%s::%d```\n", extra["callServerRequestId"], extra["startTime"].(time.Time).UnixMilli())
	}
	if extra["endTime"] != nil {
		endAnchor = fmt.Sprintf("\n```message::end::%s::%d```\n", extra["callServerRequestId"], extra["endTime"].(time.Time).UnixMilli())
	}
	return startAnchor, endAnchor
}
