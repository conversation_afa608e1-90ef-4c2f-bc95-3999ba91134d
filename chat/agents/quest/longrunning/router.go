package longrunning

import (
	"context"
	"cosy/chat/agents/quest/common"
	"cosy/chat/agents/quest/support"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
)

// llm调用后的判断逻辑
// 无工具调用 -> CheckNode
// 工具不需要用户确认 -> ToolNode
// 工具调用次数超了 -> ToolOverLimitNode
var afterLlmRouter = graph.NewFunctionPathRouter(
	func(ctx context.Context, input graph.State) ([]string, error) {
		agentState := input.(*common.MasterAgentState)
		messages := agentState.ShortTermMemory.Messages()
		lastMessage := messages[len(messages)-1]

		// 判断是否有tool调用
		ok, _ := hasToolCalls(lastMessage)
		if !ok {
			// 没有选择工具表示agent认为任务完成，进入检查节点
			return []string{"check"}, nil
		} else if support.IsOverLimit(ctx, agentState.ToolCallCount, lastMessage) {
			return []string{"toolOverLimit"}, nil
		}
		return []string{"tool"}, nil
	})

// handleUserRequest后的路由
// 如果退出了，进入finish -> FinishNode
// 如果没有退出，进入llm -> LLMNode
var afterHandleUserRequestRouter = graph.NewFunctionPathRouter(
	func(ctx context.Context, input graph.State) ([]string, error) {
		agentState := input.(*common.MasterAgentState)
		exitForMessage, ok := agentState.Extra[StateExtraExitForMessage].(bool)
		if ok && exitForMessage {
			// 退出接收用户消息
			return []string{"finish"}, nil
		}
		// 继续请求模型
		return []string{"llm"}, nil
	})

// check后的路由
// 如果完成了plan，进入summary -> SummaryNode
// 没完成plan，需要继续处理 -> HandleUserRequestNode
var afterCheckRouter = graph.NewFunctionPathRouter(
	func(ctx context.Context, input graph.State) ([]string, error) {
		// 校验ok，进入summary节点
		agentState := input.(*common.MasterAgentState)
		hasPlanFinish, ok := agentState.Extra[StateExtraHasPlanFinish].(bool)
		if ok && !hasPlanFinish {
			return []string{"continue"}, nil
		}
		return []string{"summary"}, nil
	})

func hasToolCalls(lastMessage *definition.Message) (bool, string) {
	if lastMessage.ToolCalls != nil {
		return true, lastMessage.ToolCalls[0].Function.Name
	}
	return false, ""
}
