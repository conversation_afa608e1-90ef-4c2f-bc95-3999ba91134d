package common

import (
	"context"
	"cosy/log"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/memory"
	"github.com/tiendc/go-deepcopy"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
)

type MasterAgentState struct {
	Inputs       map[string]any  // 原始输入
	CtxForClient context.Context // 保留用于和client通信的ctx

	ShortTermMemory memory.ShortTermMemory // 短期记忆
	Approval        bool                   // 最近一次工具调用是否允许
	ToolCallCount   int                    // 工具调用次数
	Extra map[string]any 				   // 保存extra的参数
	Result any							   // 保存result
}

func (s *MasterAgentState) Clone() graph.State {
	// 只需要深拷贝下面三个
	source := &MasterAgentState{
		Approval:        s.Approval,
		ToolCallCount:   s.ToolCallCount,
	}
	target := &MasterAgentState{}
	err := deepcopy.Copy(target, source)
	if err != nil {
		log.Errorf("clone state error, err=%s", err)
		return s
	}
	target.ShortTermMemory = s.ShortTermMemory
	target.Inputs = s.Inputs
	target.CtxForClient = s.CtxForClient
	target.Extra = s.Extra
	target.Result = s.Result
	return target
}

func (s *MasterAgentState) ToChainInput() map[string]interface{} {
	// example里不需要，先不实现
	return nil
}

func (s *MasterAgentState) FromChainOutput(source map[string]interface{}) graph.State {
	// example里不需要，先不实现
	return nil
}

func (s *MasterAgentState) GetShortTermMemory() memory.ShortTermMemory {
	return s.ShortTermMemory
}

func (s *MasterAgentState) GetCtxForClient() context.Context {
	return s.CtxForClient
}