package coder

import (
	"context"

	coderCommon "cosy/chat/agents/coder/common"
	"cosy/chat/agents/coder/compact"
	agentSupport "cosy/chat/agents/support"
	toolCommon "cosy/chat/agents/tool/common"
	"cosy/chat/agents/tool/mcp"
	"cosy/chat/chains/common"
	chatUtil "cosy/chat/util"
	"cosy/definition"
	cosyDefinition "cosy/definition"
	cosyErrors "cosy/errors"
	"cosy/util"

	"errors"
	"strings"

	"cosy/extension/plan"
	"cosy/log"
	"cosy/memory/stm"

	"github.com/google/uuid"

	agentDefinition "code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
)

// 初始化上下文
var initNode = graph.NewNode(
	InitNodeName,
	graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
		agentState := input.(*coderCommon.CoderAgentState)
		// 读一下历史消息
		rawInputParams := agentState.Inputs[common.KeyChatAskParams].(*definition.AskParams)
		sessionId := rawInputParams.SessionId
		requestId := rawInputParams.RequestId
		var (
			systemPrompt string
			userPrompt   string
			err          error
		)
		//if ask agent, build ask agent prompt
		mode := rawInputParams.Mode
		//ask 模式下的自定义指令
		if mode == definition.SessionModeChat && rawInputParams.TaskDefinitionType == definition.TaskDefinitionTypeCustom {
			systemPrompt, userPrompt, err = buildCustomInstructsPrompt(ctx, rawInputParams)
			if err != nil {
				return nil, cosyErrors.New(cosyErrors.SystemError, "failed to build custom instructs prompt")
			}
		} else {
			systemPrompt, userPrompt = buildPrompt(ctx, rawInputParams, agentState.Inputs)
		}
		if systemPrompt == "" {
			return nil, cosyErrors.New(cosyErrors.SystemError, "failed to build prompt")
		}
		shortTermMemory := agentState.ShortTermMemory
		shortTermMemory.Clear()
		systemMessage := &agentDefinition.Message{Role: agentDefinition.RoleTypeSystem, Content: systemPrompt}
		log.Debugf("[common_dev_agent] mode=%s, message, requestId=%s, message=%+v", mode, requestId, systemMessage)
		shortTermMemory.AddMessage(systemMessage)

		history, hasAskMode, ok := stm.GetMessageHistory(ctx, sessionId)
		if ok {
			// 把历史消息保存到chat_message中，系统指令排除
			if len(history) > 0 {
				shortTermMemory.AppendMessages(history...)
				mode := rawInputParams.Mode
				if mode == definition.SessionModeAgent && hasAskMode {
					//避免切换模式时导致不调用edit_file工具的情况
					userPrompt = userPrompt + "\nYour previous responses may not have had access to tools, but now you do. Please call tools like edit_file, run_in_terminal, get_problems and others as appropriate.\nNEVER output CODE_EDIT_BLOCK code to the me, unless requested."
				}
			}
		}

		// 判断是否有图片
		contextProviderExtras := agentState.Inputs[common.KeyContextProviderExtra].([]definition.CustomContextProviderExtra)
		imageUrls := chatUtil.FindImageUrlsFromContext(contextProviderExtras)
		if len(imageUrls) > 0 {
			imageUrl := imageUrls[0]
			chatPartText := agentDefinition.ChatMessagePart{
				Type: agentDefinition.ChatMessagePartTypeText,
				Text: userPrompt,
			}
			chatPartImage := agentDefinition.ChatMessagePart{
				Type: agentDefinition.ChatMessagePartTypeImageURL,
				ImageURL: &agentDefinition.ChatMessageImageURL{
					URL:      imageUrl,
					MIMEType: "image/png",
				},
			}
			multiContent := []agentDefinition.ChatMessagePart{chatPartText, chatPartImage}
			userMessage := &agentDefinition.Message{Role: agentDefinition.RoleTypeUser, Content: userPrompt, MultiContent: multiContent}
			shortTermMemory.AddMessage(userMessage)
			saveMessageHistory(agentState, userMessage)
		} else {
			userMessage := &agentDefinition.Message{Role: agentDefinition.RoleTypeUser, Content: userPrompt}
			shortTermMemory.AddMessage(userMessage)
			saveMessageHistory(agentState, userMessage)
		}
		return agentState, nil
	}))

// 调用大模型
var llmNode = graph.NewNode(
	LLMNodeName,
	graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
		agentState := input.(*coderCommon.CoderAgentState)
		rawInputParams := agentState.Inputs[common.KeyChatAskParams].(*definition.AskParams)
		agentContext, ok := ctx.Value(common.KeyCoderAgentContext).(*coderCommon.AgentContext)
		if !ok {
			return nil, errors.New("AgentContext not found")
		}

		toolParams := agentSupport.ConvertToModelToolParam(ctx, agentContext.Tools)
		//agent下配置mcp tools
		mode := rawInputParams.Mode
		if mode == definition.SessionModeAgent {
			toolParams = append(toolParams, mcp.ListMcpTools()...)
		}
		toolsToken, _ := agentSupport.GetToolsToken(toolParams)
		//发起llm调用前做上下文长度进行处理
		//messages, _ := TruncateMessages(ctx, agentState.ShortTermMemory, rawInputParams.PluginPayloadConfig.IsEnableProjectRule, rawInputParams.RequestId)
		messages, _ := compact.GlobalModelCompactor.Compact(ctx, agentState.ShortTermMemory, rawInputParams.PluginPayloadConfig.IsEnableProjectRule, rawInputParams.RequestId, toolsToken)


		var response *agentDefinition.Message
		var err error
		var callServerRequestId = uuid.NewString()

		requestId := rawInputParams.RequestId
		sessionId := rawInputParams.SessionId
		sessionType := rawInputParams.SessionType
		remoteAsk, _ := agentSupport.BuildRemoteChatMessageParam(ctx, rawInputParams, messages, toolParams, mode)
		modelConfig, found := agentSupport.PrepareModelConfig(rawInputParams, agentState.ToolCallCount)
		if found {
			remoteAsk.ModelConfig = modelConfig
		}
		sseCtx, cancelFunc := context.WithCancel(ctx)
		responseHandler := agentSupport.LLMResponseHandler{
			SessionType:   sessionType,
			SessionId:     sessionId,
			RequestId:     requestId,
			ToolCallCount: agentState.ToolCallCount,
			CtxForClient:  agentState.CtxForClient,
			CancelFunc:    cancelFunc,
		}
		remoteAsk.RequestId = callServerRequestId
		response, err = agentSupport.SubscribeCommonAgentChat(sseCtx, *rawInputParams, remoteAsk, responseHandler.OnDeltaContent, responseHandler.OnToolParseEvent)
		if err != nil {
			log.Infof("[common_dev_agent] finish chat with error, chatRecordId=%s, requestId=%s", requestId, callServerRequestId)
		} else {
			responseHandler.PostSyncToolCall(sseCtx, response)
			log.Infof("[common_dev_agent] finish chat, chatRecordId=%s, requestId=%s, responseMeta=%+v", requestId, callServerRequestId, response.ResponseMeta)
			if response.ResponseMeta.FinishReason == definition.FinishReasonErrorFinish {
				if response.ToolCalls != nil && util.Contains(toolCommon.EditFileTools, response.ToolCalls[0].Function.Name) {
					response.ResponseMeta.FinishReason = definition.FinishReasonLength
				} else {
					err = cosyErrors.New(cosyErrors.SystemError, response.Content)
				}
			} else if response.Content == "" && response.ToolCallID == "" &&
				len(response.ToolCalls) == 0 && response.ReasoningContent == "" {
				select {
				case <-ctx.Done():
					response.Content = coderCommon.EmptyMessage
				default:
					//如果lastMessage值为空，则直接退出
					log.Error("assistant response is empty.")
					err = cosyErrors.New(cosyErrors.SystemError, "assistant response is empty")
				}
			}
		}
		if err != nil {
			responseHandler.PostSyncToolCallOnError(sseCtx, response)
		}

		if err != nil {
			if modelQueueStatus, isQueued := chatUtil.GetQueueError(err); isQueued {
				agentState.Inputs[common.KeyModelQueueStatus] = modelQueueStatus
			}
			// 调用llm失败，转换一下错误，通过err退出
			return agentState, err
		}
		lastMessage := response
		if lastMessage.ReasoningContentSignature == "" && lastMessage.ReasoningContent != "" {
			thinkStr := "<think>\n" + lastMessage.ReasoningContent + "\n</think>\n"
			lastMessage.Content = thinkStr + lastMessage.Content
		}
		saveMessageHistory(agentState, lastMessage)
		agentState.ShortTermMemory.AddMessage(lastMessage)
		return agentState, nil
	}))

// 工具使用到达上限制
var toolOverLimitNode = graph.NewNode(
	ToolOverLimitNodeName,
	graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
		agentState := input.(*coderCommon.CoderAgentState)
		historyMessages := agentState.ShortTermMemory.Messages()
		lastMessage := historyMessages[len(historyMessages)-1]
		// 取消工具执行，返回工具取消执行的消息
		messages := make([]*agentDefinition.Message, 0, len(lastMessage.ToolCalls))
		toolCallResult := "The tool invocation was canceled because it reached the limit. If the user continues, you can re-initiate the tool call."
		// 收集所有工具调用的ID和名称

		for _, t := range lastMessage.ToolCalls {
			callResponse := &agentDefinition.Message{
				Role:       agentDefinition.RoleTypeTool,
				ToolCallID: t.ID,
				Name:       t.Function.Name,
				Content:    toolCallResult,
			}
			messages = append(messages, callResponse)
		}
		//emptyAssistantMessage := &agentDefinition.Message{
		//	Role:    agentDefinition.RoleTypeAssistant,
		//	Content: "",
		//}
		for _, message := range messages {
			saveMessageHistory(agentState, message)
		}
		agentState.ShortTermMemory.AppendMessages(messages...)
		return agentState, cosyErrors.New(cosyErrors.ToolCallOverLimit, "tool call over limit")
	}))

// Deprecated 工具执行待确认
var toolConfirmNode = graph.NewNode(
	ToolConfirmNodeName,
	graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
		// 用户确认的信息在之前的interrupt事件的时候发出
		agentState := input.(*coderCommon.CoderAgentState)
		if agentState.Approval {
			// 如果通过就返回
			return agentState, nil
		}
		// 如果没通过需要补上拒绝的信息
		historyMessages := agentState.ShortTermMemory.Messages()
		lastMessage := historyMessages[len(historyMessages)-1]
		// 取消工具执行，返回工具取消执行的消息
		messages := make([]*agentDefinition.Message, 0, 2)
		toolCallResult := "The user has canceled this tool invocation. Rethink or ask the user what he expects to do."
		callResponse := &agentDefinition.Message{
			Role:    agentDefinition.RoleTypeTool,
			Content: toolCallResult,
		}
		// 收集所有工具调用的ID和名称
		var toolCallIDs []string
		var toolNames []string
		for _, t := range lastMessage.ToolCalls {
			toolCallIDs = append(toolCallIDs, t.ID)
			toolNames = append(toolNames, t.Function.Name)
		}
		callResponse.ToolCallID = strings.Join(toolCallIDs, ",")
		callResponse.Name = strings.Join(toolNames, ",")
		//emptyAssistantMessage := &agentDefinition.Message{
		//	Role:    agentDefinition.RoleTypeAssistant,
		//	Content: "",
		//}
		messages = append(messages, callResponse)
		for _, message := range messages {
			saveMessageHistory(agentState, message)
		}
		agentState.ShortTermMemory.AppendMessages(messages...)
		return agentState, nil
	}))

// 执行工具调用
var toolNode = graph.NewNode(
	ToolNodeName,
	graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
		agentState := input.(*coderCommon.CoderAgentState)
		agentContext, ok := ctx.Value(common.KeyCoderAgentContext).(*coderCommon.AgentContext)
		if !ok {
			return nil, errors.New("AgentContext not found")
		}
		inputs := agentState.ToChainInput()
		rawInputParams := inputs[common.KeyChatAskParams].(*cosyDefinition.AskParams)
		requestId := rawInputParams.RequestId
		sessionId := rawInputParams.SessionId
		availableTools := agentContext.Tools
		callback := &agentSupport.DefaultToolCallbackHandler{
			CtxForClient: agentState.GetCtxForClient(),
			SessionId: sessionId,
			RequestId: requestId,
		}
		toolMessages := agentSupport.ExecuteTool(ctx, availableTools, agentState, agentSupport.DefaultAgentToolParamSupplier, callback)
		// 遍历 toolMessages，对每个消息调用 addMessageHistory
		for _, message := range toolMessages {
			saveMessageHistory(agentState, message)
		}
		// 每次执行完置为false，调用次数+1
		agentState.Approval = false
		agentState.ToolCallCount++
		agentState.ShortTermMemory.AppendMessages(toolMessages...)
		return agentState, nil
	}))

var finishToolNode = graph.NewNode(
	FinishToolNodeName,
	graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
		agentState := input.(*coderCommon.CoderAgentState)
		//finishMessage := &agentDefinition.Message{Role: agentDefinition.RoleTypeAssistant, Content: "任务已经完成。如果有其他需求，请告诉我。"}
		//agentState.ShortTermMemory.AddMessage(finishMessage)
		return agentState, nil
	}))

var checkNode = graph.NewNode(
	CheckNodeName,
	graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
		agentState := input.(*coderCommon.CoderAgentState)
		rawInputParams := agentState.Inputs[common.KeyChatAskParams].(*definition.AskParams)
		sessionId := rawInputParams.SessionId
		hasFinishPlan := plan.HasAllTasksFinished(sessionId)
		isPause := plan.IsPause(sessionId)
		if hasFinishPlan || isPause {
			agentState.HasPlanFinish = true
		} else {
			agentState.HasPlanFinish = false
			builder := strings.Builder{}
			builder.WriteString("The tasks have not been fully completed, please check the task list and continue with the execution.")
			message := &agentDefinition.Message{
				Role:    agentDefinition.RoleTypeUser,
				Content: builder.String(),
			}
			agentState.ShortTermMemory.AppendMessages(message)
		}
		return agentState, nil
	}))
