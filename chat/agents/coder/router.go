package coder

import (
	"context"
	"cosy/chat/agents/coder/common"
	"cosy/chat/agents/support"
	toolCommon "cosy/chat/agents/tool/common"
	cosyDefinition "cosy/definition"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
)

// llm调用后的判断逻辑
// 无工具调用 -> FinishNode
// 工具需要用户确认 -> ToolConfirmNode
// 工具不需要用户确认 -> ToolNode
var afterLlmRouter = graph.NewFunctionPathRouter(
	func(ctx context.Context, input graph.State) ([]string, error) {
		agentState := input.(*common.CoderAgentState)
		messages := agentState.ShortTermMemory.Messages()
		lastMessage := messages[len(messages)-1]

		// 判断是否有tool调用
		exist := hasToolCalls(lastMessage)
		if !exist {
			return []string{"finish"}, nil
		} else if support.IsOverLimit(ctx, agentState.ToolCallCount, lastMessage) {
			return []string{"toolOverLimit"}, nil
		} else if needUserConfirm(ctx, lastMessage) {
			//return []string{"toolConfirm"}, nil
			// 直接进入tool节点，消息tool节点开始的时候进行推送，确认、取消的逻辑也在tool节点消费
			return []string{"tool"}, nil
		}
		return []string{"tool"}, nil
	})

// 工具确认后的router
// 如果用户确认执行 -> ToolNode
// 如果用户取消执行 -> ToolCancelledNode
var afterToolConfirmRouter = graph.NewFunctionPathRouter(
	func(ctx context.Context, input graph.State) ([]string, error) {
		agentState := input.(*common.CoderAgentState)
		if agentState.Approval {
			return []string{"tool"}, nil
		}
		return []string{"llm"}, nil
	})

// check后的路由
// 如果完成了plan，进入summary -> SummaryNode
// 没完成plan，需要继续处理 -> HandleUserRequestNode
var afterCheckRouter = graph.NewFunctionPathRouter(
	func(ctx context.Context, input graph.State) ([]string, error) {
		// 校验ok，进入summary节点
		agentState := input.(*common.CoderAgentState)
		if !agentState.HasPlanFinish {
			return []string{"continue"}, nil
		}
		return []string{"finish"}, nil
	})

func hasToolCalls(lastMessage *definition.Message) bool {
	if lastMessage.ToolCalls != nil {
		return true
	}
	return false
}

// needUserConfirm 判断工具的执行是否需要用户确认
func needUserConfirm(ctx context.Context, lastMessage *definition.Message) bool {
	if lastMessage.ToolCalls == nil {
		return false
	}
	needConfirm := false
	for i, _ := range lastMessage.ToolCalls {
		if support.NeedUserConfirm(lastMessage.ToolCalls[i]) {
			// ToolCallExtraNeedUserConfirm 设置这个参数，后面推送消息有用
			if lastMessage.ToolCalls[i].Extra != nil {
				lastMessage.ToolCalls[i].Extra[common.ToolCallExtraNeedUserConfirm] = true
			} else {
				lastMessage.ToolCalls[i].Extra = map[string]any{
					common.ToolCallExtraNeedUserConfirm: true,
				}
			}
			confirmChan := make(chan *cosyDefinition.ToolConfirmRequest, 2)
			toolCommon.SetToolConfirmChan(lastMessage.ToolCalls[i].ID, confirmChan)
			needConfirm = true
		}
	}
	return needConfirm
}
