package compact

import (
	agentDefinition "code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"context"
	"cosy/log"
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"sync"
)

const (
	DEFAULT_MAX_MERGE_GAP = 50
)

// LineRange 行范围信息
type LineRange struct {
	StartLine int
	EndLine   int
}

// FileOperation 文件操作信息
type FileOperation struct {
	FilePath      string
	OpType        string // "read_file", "edit_file", "create_file", "search_replace"
	LineRange     LineRange
	Priority      int
	SearchContent []string // 该字段不为空表明无法直接开始和结束行，需要进行匹配
}

// FileContent 文件内容信息
type FileContent struct {
	FilePath    string
	Content     string
	TokenCount  int
	LineRange   LineRange
	TotalLines  int
	Priority    int
	ReadSuccess bool
}

// OptimizedFileReader 优化的文件读取器
type OptimizedFileReader struct {
	maxTotalTokens int
	maxFiles       int
	workspacePath  string
}

// NewOptimizedFileReader 创建新的优化文件读取器
func NewOptimizedFileReader(workspacePath string) *OptimizedFileReader {
	return &OptimizedFileReader{
		maxTotalTokens: 50000, // 默认最大token数
		maxFiles:       5,     // 默认最多5个文件
		workspacePath:  workspacePath,
	}
}

// OptimizedReadRecentFiles 优化读取最近访问的文件
func (r *OptimizedFileReader) OptimizedReadRecentFiles(ctx context.Context, messages []*agentDefinition.Message) []*agentDefinition.Message {
	// STEP 1：从历史消息中提取文件相关操作
	fileOpMap := r.extractFileOperations(messages)
	if len(fileOpMap) == 0 {
		return nil
	}

	// STEP 2：并行文件读取
	var filePaths []string
	for k := range fileOpMap {
		filePaths = append(filePaths, k)
	}
	fileContents := r.readFilesInParallel(ctx, fileOpMap)
	if len(fileContents) == 0 {
		return nil
	}

	// STEP 3：读取范围合并与优化
	optimizedContents := r.optimizeFileContents(fileContents, fileOpMap)

	// STEP 4：结果组装与返回
	return r.assembleResults(optimizedContents)
}

// extractFileOperations 提取文件操作信息
func (r *OptimizedFileReader) extractFileOperations(messages []*agentDefinition.Message) map[string][]FileOperation {
	fileOpMap := make(map[string][]FileOperation)

	for i, msg := range messages {
		if msg.Role != agentDefinition.RoleTypeAssistant || len(msg.ToolCalls) == 0 {
			continue
		}

		// 为每个工具调用寻找对应的tool result消息
		toolResultIndex := i + 1
		for _, toolCall := range msg.ToolCalls {
			functionName := toolCall.Function.Name

			// 检查下一个位置是否是tool result消息
			var toolResult *agentDefinition.Message
			if toolResultIndex < len(messages) && messages[toolResultIndex].Role == agentDefinition.RoleTypeTool {
				toolResult = messages[toolResultIndex]
				toolResultIndex++ // 移动到下一个位置
			} else {
				break
			}

			// 提取文件路径
			fileOps := r.extractFileOp(functionName, toolResult)
			if fileOps == nil || len(fileOps) == 0 {
				continue
			}

			// 添加到结果中
			for _, fileOp := range fileOps {
				filePath := fileOp.FilePath
				fileOp.Priority = i
				fileOpMap[filePath] = append(fileOpMap[filePath], fileOp)
			}
		}
	}

	return fileOpMap
}

// isFileOperation 判断是否为文件操作
func (r *OptimizedFileReader) isFileOperation(funcName string) bool {
	fileOps := []string{"read_file", "edit_file", "create_file", "search_replace"}
	for _, op := range fileOps {
		if funcName == op {
			return true
		}
	}
	return false
}

// isOperationSuccess 判断操作是否成功
func (r *OptimizedFileReader) isOperationSuccess(toolResult *agentDefinition.Message) bool {
	// 返回错误示例 error: code = 30404 message = failed to read file xxx
	if toolResult == nil {
		return false
	}
	errorRegex := regexp.MustCompile(`error:\s*code\s*=\s*\d+\s*message\s*=`)
	content := strings.ToLower(toolResult.Content)
	if errorRegex.MatchString(content) {
		return false
	}
	return true
}

// extractFileOp 从工具调用中提取文件路径（数组类型返回值兼容可能的多文件）
func (r *OptimizedFileReader) extractFileOp(functionName string, toolResult *agentDefinition.Message) []FileOperation {
	var result []FileOperation

	// 检查操作是否成功
	isSuccess := r.isOperationSuccess(toolResult)
	if !isSuccess {
		return result
	}

	switch functionName {
	case "read_file":
		fileOp := r.extractFileOpFromReadFileResult(toolResult.Content)
		if fileOp != nil {
			result = append(result, *fileOp)
		}
	case "edit_file":
		fileOp := r.extractFileOpFromEditFileResult(toolResult.Content)
		if fileOp != nil {
			result = append(result, *fileOp)
		}
	case "create_file":
		fileOp := r.extractFileOpFromCreateFileResult(toolResult.Content)
		if fileOp != nil {
			result = append(result, *fileOp)
		}
	case "search_replace":
		// 暂时忽略
	}
	return result
}

// extractFileOpFromReadFileResult 从 read_file 返回第一行提取文件路径和行号信息
func (r *OptimizedFileReader) extractFileOpFromReadFileResult(content string) *FileOperation {
	if content == "" {
		return nil
	}

	// 取第一行
	lines := strings.Split(content, "\n")
	if len(lines) == 0 {
		return nil
	}

	firstLine := lines[0]

	// 正则表达式匹配：Contents of <path>, from line <start>-<end> (total <total> lines)
	regex := regexp.MustCompile(`Contents of (.+?), from line (\d+)-(\d+) \(total (\d+) lines\)`)
	matches := regex.FindStringSubmatch(firstLine)

	if len(matches) != 5 {
		return nil
	}

	filePath := matches[1]
	startLine, _ := strconv.Atoi(matches[2])
	endLine, _ := strconv.Atoi(matches[3])

	return &FileOperation{
		FilePath: filePath,
		LineRange: LineRange{
			StartLine: startLine,
			EndLine:   endLine,
		},
		OpType: "read_file",
	}
}

// extractFileOpFromEditFileResult 从 edit_file 消息中提取文件路径
func (r *OptimizedFileReader) extractFileOpFromEditFileResult(content string) *FileOperation {
	if content == "" {
		return nil
	}

	// 正则表达式匹配：create file success, file path: <path>
	regex := regexp.MustCompile(`edit file success, file path: (.+?)(?:\n|$)`)
	matches := regex.FindStringSubmatch(content)

	if len(matches) != 2 {
		return nil
	}

	filePath := strings.TrimSpace(matches[1])
	return &FileOperation{
		FilePath: filePath,
		LineRange: LineRange{
			StartLine: 0,
			EndLine:   -1,
		},
		OpType: "edit_file",
	}
}

// extractFileOpFromCreateFileResult 从 create_file 消息中提取文件路径
func (r *OptimizedFileReader) extractFileOpFromCreateFileResult(content string) *FileOperation {
	if content == "" {
		return nil
	}

	// 正则表达式匹配：create file success, file path: <path>
	regex := regexp.MustCompile(`create file success, file path: (.+?)(?:\n|$)`)
	matches := regex.FindStringSubmatch(content)

	if len(matches) != 2 {
		return nil
	}

	filePath := strings.TrimSpace(matches[1])
	return &FileOperation{
		FilePath: filePath,
		LineRange: LineRange{
			StartLine: 0,
			EndLine:   -1,
		},
		OpType: "create_file",
	}
}

// readFilesInParallel 并行读取文件
func (r *OptimizedFileReader) readFilesInParallel(ctx context.Context, fileOpMap map[string][]FileOperation) []FileContent {
	var wg sync.WaitGroup
	resultChan := make(chan []FileContent, len(fileOpMap))

	for filePath, fileOps := range fileOpMap {
		wg.Add(1)
		go func(filePath string, fileOps []FileOperation) {
			defer wg.Done()
			content := r.readSingleFile(ctx, filePath, fileOps)
			resultChan <- content
		}(filePath, fileOps)
	}

	// 等待所有读取完成
	go func() {
		wg.Wait()
		close(resultChan)
	}()

	// 收集结果
	var results []FileContent
	for contents := range resultChan {
		for _, content := range contents {
			if content.ReadSuccess {
				results = append(results, content)
			}
		}
	}
	return results
}

// readSingleFile 读取单个文件的指定行范围
func (r *OptimizedFileReader) readSingleFile(ctx context.Context, filePath string, fileOps []FileOperation) []FileContent {
	// 1.读取文件内容
	fullPath := filePath
	if !filepath.IsAbs(filePath) && r.workspacePath != "" {
		fullPath = filepath.Join(r.workspacePath, filePath)
	}
	content, err := os.ReadFile(fullPath)
	if err != nil {
		// 返回错误结果
		errorResult := FileContent{
			FilePath:    filePath,
			ReadSuccess: false,
		}
		return []FileContent{errorResult}
	}

	// 2.根据行范围提取内容
	contentStr := string(content)
	lines := strings.Split(contentStr, "\n")
	totalLines := len(lines)

	// 2.1.整合行号范围
	priority := 0
	var results []FileContent
	var lineRanges []LineRange
	for _, op := range fileOps {
		if op.LineRange.EndLine == -1 {
			// -1 表示读取整个文件
			lineRanges = []LineRange{{StartLine: 0, EndLine: totalLines - 1}}
			break
		} else {
			lineRanges = append(lineRanges, op.LineRange)
		}
		if op.Priority > priority {
			priority = op.Priority
		}
	}
	if len(lineRanges) == 0 {
		// 如果没有有效范围，默认读取整个文件
		lineRanges = []LineRange{{StartLine: 0, EndLine: totalLines - 1}}
	}

	// 2.2.合并重叠和相近的行范围
	mergedRanges := r.mergeLineRanges(lineRanges)

	// 2.3.为每个合并后的范围创建FileContent
	for _, lineRange := range mergedRanges {
		fileContent := r.extractContentByRange(filePath, lines, totalLines, lineRange)
		fileContent.Priority = priority
		results = append(results, fileContent)
	}

	// 3.返回结果
	if len(results) == 0 {
		errorResult := FileContent{
			FilePath:    filePath,
			ReadSuccess: false,
		}
		return []FileContent{errorResult}
	}
	return results
}

// mergeLineRanges 合并行范围，相近的范围会被合并
func (r *OptimizedFileReader) mergeLineRanges(ranges []LineRange) []LineRange {
	if len(ranges) <= 1 {
		return ranges
	}

	// 按起始行号排序
	sort.Slice(ranges, func(i, j int) bool {
		return ranges[i].StartLine < ranges[j].StartLine
	})

	// 遍历每个范围，如果下一个范围与当前范围重叠或间隔小于等于DEFAULT_MAX_MERGE_GAP，则合并
	var merged []LineRange
	current := ranges[0]
	for i := 1; i < len(ranges); i++ {
		next := ranges[i]
		if next.StartLine <= current.EndLine+DEFAULT_MAX_MERGE_GAP {
			if next.EndLine > current.EndLine {
				current.EndLine = next.EndLine
			}
		} else {
			merged = append(merged, current)
			current = next
		}
	}
	merged = append(merged, current)
	return merged
}

// extractContentByRange 根据行范围提取内容
func (r *OptimizedFileReader) extractContentByRange(filePath string, lines []string, totalLines int, lineRange LineRange) FileContent {
	result := FileContent{
		FilePath:    filePath,
		TotalLines:  totalLines,
		ReadSuccess: false,
	}

	startLine := lineRange.StartLine
	endLine := lineRange.EndLine
	if endLine == -1 {
		endLine = totalLines - 1
	}
	if startLine < 0 {
		startLine = 0
	}
	if endLine >= totalLines {
		endLine = totalLines - 1
	}
	if startLine > endLine {
		return result
	}

	// 提取指定范围的行
	selectedLines := lines[startLine : endLine+1]
	contentStr := strings.Join(selectedLines, "\n")

	result.Content = contentStr
	result.LineRange = LineRange{
		StartLine: startLine,
		EndLine:   endLine,
	}
	result.TokenCount = len(contentStr) / 4 // 估算token数量
	result.ReadSuccess = true

	log.Debugf("[Compact] Extracted content from %s: lines %d-%d (%d tokens)", filePath, startLine, endLine, result.TokenCount)
	return result
}

// optimizeFileContents 优化文件内容
func (r *OptimizedFileReader) optimizeFileContents(contents []FileContent, fileOpMap map[string][]FileOperation) []FileContent {
	// 按照优先级进行排序
	sort.Slice(contents, func(i, j int) bool {
		return contents[i].Priority > contents[j].Priority
	})

	var optimized []FileContent
	perFileTokens := r.maxTotalTokens / r.maxFiles
	availableTokens := 0

	for i, content := range contents {
		if i >= r.maxFiles {
			break
		}
		if content.TokenCount <= perFileTokens+availableTokens {
			// 完整保留文件
			optimized = append(optimized, content)
			availableTokens += perFileTokens - content.TokenCount
		} else {
			// 截断文件内容
			truncatedContent := r.truncateContent(content, perFileTokens+availableTokens)
			if truncatedContent.TokenCount > 0 {
				optimized = append(optimized, truncatedContent)
				availableTokens += perFileTokens - truncatedContent.TokenCount
			}
		}
	}

	return optimized
}

// truncateContent 截断文件内容到指定token数
func (r *OptimizedFileReader) truncateContent(content FileContent, maxTokens int) FileContent {
	if content.TokenCount <= maxTokens {
		return content
	}

	// 按比例取前面部分
	ratio := float64(maxTokens) / float64(content.TokenCount)
	targetLength := int(float64(len(content.Content)) * ratio)

	truncated := content
	truncated.Content = content.Content[:targetLength]
	truncated.TokenCount = maxTokens

	// 重新计算行数和结束行号
	truncatedLineCount := strings.Count(truncated.Content, "\n") + 1
	truncated.LineRange.EndLine = truncated.LineRange.StartLine + truncatedLineCount - 1

	return truncated
}

// assembleResults 组装结果
func (r *OptimizedFileReader) assembleResults(contents []FileContent) []*agentDefinition.Message {
	if len(contents) == 0 {
		return nil
	}

	// 构建一个包含所有文件内容的大消息
	var contentBuilder strings.Builder
	contentBuilder.WriteString("I'm aware that a summary has occurred, so I'm manually providing some previously mentioned files for context. These files may not necessarily be used in the subsequent conversation:")

	// 添加所有文件内容到一个消息中
	for _, content := range contents {
		contentBuilder.WriteString(fmt.Sprintf("\n\nContents of %s, from line %d-%d (total %d lines):\n\n%s",
			content.FilePath, content.LineRange.StartLine, content.LineRange.EndLine, content.TotalLines, content.Content))
	}

	message := &agentDefinition.Message{
		Role:    agentDefinition.RoleTypeUser,
		Content: contentBuilder.String(),
	}

	log.Debugf("[Compact] Assembled 1 message containing %d file contents", len(contents))
	return []*agentDefinition.Message{message}
}
