package compact

import (
	agentDefinition "code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/memory"
	"context"
	"cosy/chat/chains/common"
	"cosy/chat/service"
	"cosy/definition"
	"cosy/extension/plan"
	"cosy/log"
	"cosy/tokenizer"
	"cosy/util"
	"encoding/json"
	"fmt"
)

func (s *SummarizeCompactor) Compact(ctx context.Context, shortTermMemory memory.ShortTermMemory, isEnableProjectRule bool, requestId string, toolsToken int) ([]*agentDefinition.Message, error) {
	return s.BaseCompactor.compactTemplate(ctx, shortTermMemory, isEnableProjectRule, requestId, toolsToken, s)
}

func (s *SummarizeCompactor) buildCompactMessages(ctx context.Context, preProcessedMessages []*agentDefinition.Message) ([]*agentDefinition.Message, error) {
	var messagesToCompact []*agentDefinition.Message
	for _, msg := range preProcessedMessages[1:] {
		messagesToCompact = append(messagesToCompact, msg)
	}
	return messagesToCompact, nil
}

func (s *SummarizeCompactor) mergeMessages(ctx context.Context, originalMessages []*agentDefinition.Message, compactedMessages []*agentDefinition.Message) ([]*agentDefinition.Message, error) {
	// 构建总结后的消息
	var messagesToReturn []*agentDefinition.Message

	// 保留system消息
	messagesToReturn = append(messagesToReturn, originalMessages[0])

	// 添加总结后的消息
	messagesToReturn = append(messagesToReturn, compactedMessages...)

	// 获取最近文件内容
	//recentFiles := getRecentFilesFromMassages(ctx, originalMessages)
	workspace, ok := ctx.Value(definition.ContextKeyWorkspace).(definition.WorkspaceInfo)
	var workspacePath string
	if ok {
		workspacePath, _ = workspace.GetWorkspaceFolder()
	}

	fileReader := NewOptimizedFileReader(workspacePath)
	recentFiles := fileReader.OptimizedReadRecentFiles(ctx, originalMessages)
	if recentFiles != nil && len(recentFiles) > 0 {
		messagesToReturn = append(messagesToReturn, recentFiles...)
	}

	// 保留最后一条用户消息
	var lastUserMessage *agentDefinition.Message
	for i := len(originalMessages) - 1; i >= 0; i-- {
		msg := originalMessages[i]
		if msg.Role == agentDefinition.RoleTypeUser {
			lastUserMessage = msg
			lastUserMessage.Content = util.RemoveContextContent(lastUserMessage.Content)
			// 如果当前会话有plan并且plan尚未完成，则添加plan
			if sessionId, ok := ctx.Value(common.KeySessionId).(string); ok && sessionId != "" && !plan.HasAllTasksFinished(sessionId) {
				if detailPlan, ok := plan.GenerateDetailPlan(sessionId); ok {
					lastUserMessage.Content = fmt.Sprintf(
						"%s\n\nThe current plan progress is\n%s",
						lastUserMessage.Content, detailPlan.MarkdownContent)
				}
			}
			break
		}
	}
	if lastUserMessage != nil {
		messagesToReturn = append(messagesToReturn, lastUserMessage)
	}
	return messagesToReturn, nil
}

func (s *SummarizeCompactor) postProcess(ctx context.Context, mergedMessages []*agentDefinition.Message, requestId string) error {
	messageData, err := json.Marshal(mergedMessages)
	if err != nil {
		log.Errorf("Error marshaling summary messages: %v", err)
		return fmt.Errorf("failed to marshal summary messages: %w", err)
	}
	chatMessages, err := service.SessionServiceManager.GetChatMessageByRequest(requestId)
	if err != nil {
		log.Errorf("Error getting chat messages from database: %v", err)
		return err
	}
	if len(chatMessages) == 0 {
		log.Errorf("Error getting chat messages from database: empty chat messages")
		return nil
	}
	chatMessage := chatMessages[len(chatMessages)-1]
	return service.SessionServiceManager.UpdateChatMessageSummary(chatMessage.Id, string(messageData))
}

func truncateMessageBeforeSummary(messages []*agentDefinition.Message, maxTokenLimit int) ([]*agentDefinition.Message, error) {
	// 从前往后遍历，保留user开头的对话片段
	for i := 1; i < len(messages)-1; i++ {
		msg := messages[i]
		if msg.Role == agentDefinition.RoleTypeUser && i != 0 {
			tempMessages := append([]*agentDefinition.Message{}, messages[0]) // 重新创建，避免数据混乱
			tempMessages = append(tempMessages, messages[i:]...)
			serializedMessages, err := util.SerializeMessages(tempMessages)
			if err != nil {
				return messages, err
			}
			msgTokens, _ := tokenizer.CalQwenTokenCount(serializedMessages)
			//小于上限的时候返回
			if msgTokens < maxTokenLimit {
				return tempMessages, nil
			}
		}
	}
	//如果一个user message就超过，则从后面的message开始移除
	for i := len(messages) - 1; i > 1; i-- {
		msg := messages[i]
		if msg.Role == agentDefinition.RoleTypeAssistant {
			tempMessages := messages[:i-1]
			serializedMessages, err := util.SerializeMessages(tempMessages)
			if err != nil {
				return messages, err
			}
			msgTokens, _ := tokenizer.CalQwenTokenCount(serializedMessages)
			//小于上限的时候返回
			if msgTokens < maxTokenLimit {
				return tempMessages, nil
			}
		}
	}
	return messages, nil
}

// getRecentFilesFromMassages 从消息列表中获取至多5组最近涉及文件操作的消息
func getRecentFilesFromMassages(ctx context.Context, messages []*agentDefinition.Message) []*agentDefinition.Message {
	// 从消息中提取最近涉及文件操作的消息
	recentFileMessages := extractRecentFileMessagesFromMessages(ctx, messages)
	if len(recentFileMessages) == 0 {
		log.Debugf("No recent file operation messages found")
		return nil
	}

	log.Debugf("Found %d recent file operation messages", len(recentFileMessages))
	return recentFileMessages
}

// extractRecentFileMessagesFromMessages 从消息列表中提取最近涉及文件操作的assistant消息和对应的tool result消息
func extractRecentFileMessagesFromMessages(ctx context.Context, messages []*agentDefinition.Message) []*agentDefinition.Message {
	var result []*agentDefinition.Message
	groupCount := 0
	maxGroups := 5

	// 从最新消息开始遍历，最多获取5组消息（每组包含assistant消息和tool result消息）
	for i := len(messages) - 1; i >= 0 && groupCount < maxGroups; i-- {
		message := messages[i]

		// 查找assistant消息中包含文件操作的工具调用
		if message.Role == agentDefinition.RoleTypeAssistant && len(message.ToolCalls) > 0 {
			hasFileOperation := false
			for _, toolCall := range message.ToolCalls {
				function := toolCall.Function
				switch function.Name {
				case "create_file", "edit_file", "delete_file", "search_replace", "read_file":
					hasFileOperation = true
					break
				}
			}

			if hasFileOperation {
				// 查找对应的tool result消息（通常是下一条消息）
				if i+1 < len(messages) && messages[i+1].Role == agentDefinition.RoleTypeTool {
					result = append([]*agentDefinition.Message{messages[i+1]}, result...)
				}

				// 添加assistant消息
				result = append([]*agentDefinition.Message{message}, result...)

				groupCount++
			}
		}
	}

	return result
}
