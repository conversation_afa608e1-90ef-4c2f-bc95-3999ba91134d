package compact

import (
	agentDefinition "code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/memory"
	"context"
	agentSupport "cosy/chat/agents/support"
	"cosy/chat/chains/common"
	"cosy/definition"
	"cosy/log"
	"cosy/prompt"
	"cosy/remote"
	"cosy/tokenizer"
	"cosy/user"
	"cosy/util"
	"errors"
	"fmt"
	"time"
)

const (
	// SUMMARY_TRIGGER_THRESHOLD 设置触发总结阈值
	SUMMARY_TRIGGER_THRESHOLD = 0.9
)

// GlobalModelCompactor 总结压缩器
var GlobalModelCompactor = NewSummarizeCompactor()

// Compactor 消息压缩策略接口
type Compactor interface {
	// Compact 暴露对外的接口，调用该方法即可
	Compact(ctx context.Context, shortTermMemory memory.ShortTermMemory, isEnableProjectRule bool, requestId string, toolsToken int) ([]*agentDefinition.Message, error)

	// shouldCompact 判断是否需要压缩
	shouldCompact(ctx context.Context, originalMessages []*agentDefinition.Message) (bool, error)

	// preProcess 压缩之前的预处理和特殊情况判断
	preProcess(ctx context.Context, originalMessages []*agentDefinition.Message) ([]*agentDefinition.Message, error)

	// buildCompactMessages 构造需要压缩的消息
	buildCompactMessages(ctx context.Context, preProcessedMessages []*agentDefinition.Message) ([]*agentDefinition.Message, error)

	// executeCompact 执行压缩
	executeCompact(ctx context.Context, messagesToCompact []*agentDefinition.Message, requestId string) ([]*agentDefinition.Message, error)

	// mergeMessages 拼接压缩后的消息
	mergeMessages(ctx context.Context, originalMessages []*agentDefinition.Message, compactedMessages []*agentDefinition.Message) ([]*agentDefinition.Message, error)

	// postProcess 压缩之后的处理
	postProcess(ctx context.Context, mergedMessages []*agentDefinition.Message, requestId string) error
}

// BaseCompactor 基础压缩器
type BaseCompactor struct {
	maxAllowedToken int
	tokenCount      int
}

func NewBaseCompactor() *BaseCompactor {
	return &BaseCompactor{}
}

// compactTemplate 模板方法
func (b *BaseCompactor) compactTemplate(ctx context.Context, shortTermMemory memory.ShortTermMemory, isEnableProjectRule bool, requestId string, toolsToken int, compactor Compactor) ([]*agentDefinition.Message, error) {
	originalMessages := shortTermMemory.Messages()
	util.RemoveLastMessageExtra(originalMessages)
	tokenLimitedMemory, ok := shortTermMemory.(*memory.TokenLimitedMemory)
	if !ok {
		return originalMessages, fmt.Errorf("shortTermMemory is not a TokenLimitedMemory")
	}

	// 初始化上下文信息
	b.maxAllowedToken = tokenLimitedMemory.MaxAllowedSize - toolsToken

	// 1. 判断是否需要压缩
	shouldCompact, err := compactor.shouldCompact(ctx, originalMessages)
	if !shouldCompact || err != nil {
		return originalMessages, err
	}
	log.Info("[Compact] The summary is triggered, the current token quantity is ", b.tokenCount)

	if len(originalMessages) == 2 {
		// 一个user message就超token的情况
		return util.ProcessOneTurnAssemblyMessages(originalMessages), nil
	}

	// 2. 压缩之前的预处理和特殊情况判断
	preProcessedMessages, err := compactor.preProcess(ctx, originalMessages)
	if err != nil {
		return originalMessages, err
	}

	// 3. 构造需要压缩的消息
	messagesToCompact, err := compactor.buildCompactMessages(ctx, preProcessedMessages)
	if err != nil {
		return originalMessages, err
	}

	// 4. 执行压缩
	compactedMessages, err := compactor.executeCompact(ctx, messagesToCompact, requestId)
	if err != nil {
		return originalMessages, err
	}

	// 5. 拼接压缩后的消息
	mergedMessages, err := compactor.mergeMessages(ctx, originalMessages, compactedMessages)
	if err != nil {
		return originalMessages, err
	}
	shortTermMemory.SetMessages(mergedMessages)

	// 6. 压缩之后的处理
	err = compactor.postProcess(ctx, mergedMessages, requestId)
	if err != nil {
		return originalMessages, err
	}

	return mergedMessages, nil
}

// shouldCompact 根据 token 上限判断是否需要压缩
func (b *BaseCompactor) shouldCompact(ctx context.Context, originalMessages []*agentDefinition.Message) (bool, error) {
	if originalMessages == nil || len(originalMessages) == 0 {
		return false, nil
	}

	serializedMessages, err := util.SerializeMessages(originalMessages)
	if err != nil {
		return false, err
	}

	// 如果小于 SUMMARY_TRIGGER_THRESHOLD * maxAllowedSize * 2，一个 token 对应 2 个字符(已经非常保守)，则直接返回，避免无谓的 token 计算
	summaryTriggerToken := int(float64(b.maxAllowedToken) * SUMMARY_TRIGGER_THRESHOLD)
	if len(serializedMessages) < summaryTriggerToken*2 {
		return false, nil
	}

	tokenCount, err := tokenizer.CalQwenTokenCount(serializedMessages)
	if err != nil {
		return false, fmt.Errorf("error calculating token count: %w", err)
	}

	if tokenCount < summaryTriggerToken {
		return false, nil
	}

	b.tokenCount = tokenCount
	return true, nil
}

// preProcess 压缩之前的预处理和特殊情况判断
func (b *BaseCompactor) preProcess(ctx context.Context, originalMessages []*agentDefinition.Message) ([]*agentDefinition.Message, error) {
	// 总 token 超过最大限制，则进行截断后再总结
	if b.tokenCount > b.maxAllowedToken {
		return truncateMessageBeforeSummary(originalMessages, b.maxAllowedToken)
	}
	return originalMessages, nil
}

// executeCompact 执行压缩
func (b *BaseCompactor) executeCompact(ctx context.Context, messagesToCompact []*agentDefinition.Message, requestId string) ([]*agentDefinition.Message, error) {
	// 获取总结提示词
	systemPromptForSummary, err := prompt.Engine.RenderLLMMemoryCondenserSummaryPrompt(prompt.LLMMemoryCondenserSummaryInput{})
	if err != nil {
		return nil, fmt.Errorf("failed to get summary prompt: %w", err)
	}

	// 构建用于总结的消息序列
	var messagesToSend []*agentDefinition.Message

	// 添加用于总结的system消息
	sysMsgForSummary := &agentDefinition.Message{
		Role:    agentDefinition.RoleTypeSystem,
		Content: "You are a helpful AI assistant tasked with summarizing conversations.",
	}
	messagesToSend = append(messagesToSend, sysMsgForSummary)

	// 添加需要总结的历史消息
	for _, msg := range messagesToCompact {
		messagesToSend = append(messagesToSend, msg)
	}

	// 添加总结提示词作为最后的用户消息
	summaryUserMessage := &agentDefinition.Message{
		Role:    agentDefinition.RoleTypeUser,
		Content: systemPromptForSummary,
	}
	messagesToSend = append(messagesToSend, summaryUserMessage)

	params := ctx.Value(common.KeyChatAskParams).(*definition.AskParams)

	// 构建模型请求
	remoteAsk := definition.RemoteChatAsk{
		RequestId: params.RequestId,
		//AI Developer本期与requestId一致
		RequestSetId:            params.RequestId,
		ChatRecordId:            params.RequestId,
		ChatTask:                params.ChatTask,
		ChatContext:             params.ChatContext,
		IsReply:                 params.IsReply,
		SessionId:               params.SessionId,
		CodeLanguage:            params.CodeLanguage,
		Source:                  params.Source,
		Stream:                  params.Stream,
		Version:                 "3",
		CustomSystemRoleContent: params.CustomSystemRoleContent,
		Parameters:              params.Parameters,
		UserType:                user.GetUserType(),
		TaskDefinitionType:      params.TaskDefinitionType,
		SessionType:             params.SessionType,
		AgentId:                 definition.AgentCommonAgentId,
		TaskId:                  "common",
		Passkey:                 params.Passkey,
	}
	remoteAsk.Messages = messagesToSend
	remoteAsk.Tools = nil

	modelConfig, found := agentSupport.PrepareModelConfig(params, 0)
	if found {
		remoteAsk.ModelConfig = modelConfig
	}

	requestParams := remote.CommonAgentRequestParams{
		ServiceName:         definition.AgentCommonAgentService,
		FetchKey:            "llm_model_result",
		ModelRequest:        remoteAsk,
		Timeout:             120 * time.Second,
		RequestId:           requestId,
		AgentId:             definition.AgentCommonAgentId,
		OutputFormatVersion: "2",
	}

	// 调用模型进行总结
	resp, err := remote.ExecuteStreamedAgentRequestWithModelConfig(context.Background(), requestParams, &remoteAsk.ModelConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to execute LLM summarization: %w", err)
	}

	// 构建总结消息
	summaryPrefix := "This session is being continued from a previous conversation that ran out of context. The conversation is summarized below:\n"
	summarizedMessage := &agentDefinition.Message{
		Role:    agentDefinition.RoleTypeUser,
		Content: summaryPrefix + resp.Text,
	}
	return []*agentDefinition.Message{
		summarizedMessage,
	}, nil
}

// Compact 需要子类重写
func (b *BaseCompactor) Compact(ctx context.Context, shortTermMemory memory.ShortTermMemory, isEnableProjectRule bool, requestId string) ([]*agentDefinition.Message, error) {
	return nil, errors.New("compact not implemented")
}

// buildCompactMessages 需要子类重写
func (b *BaseCompactor) buildCompactMessages(ctx context.Context, preProcessedMessages []*agentDefinition.Message) ([]*agentDefinition.Message, error) {
	return nil, errors.New("BuildCompactMessages not implemented")
}

// mergeMessages 需要子类重写
func (b *BaseCompactor) mergeMessages(ctx context.Context, originalMessages []*agentDefinition.Message, compactedMessages []*agentDefinition.Message) ([]*agentDefinition.Message, error) {
	return nil, errors.New("MergeMessages not implemented")
}

// postProcess 需要子类重写
func (b *BaseCompactor) postProcess(ctx context.Context, mergedMessages []*agentDefinition.Message, requestId string) error {
	return errors.New("PostProcess not implemented")
}

// SummarizeCompactor 总结压缩策略
type SummarizeCompactor struct {
	*BaseCompactor
}

func NewSummarizeCompactor() *SummarizeCompactor {
	return &SummarizeCompactor{
		BaseCompactor: NewBaseCompactor(),
	}
}
