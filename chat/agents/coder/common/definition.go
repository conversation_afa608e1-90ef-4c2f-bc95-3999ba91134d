package common

import (
	"code.alibaba-inc.com/cosy/lingma-agent-graph/llms"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/tool"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/util"
	cosyDefinition "cosy/definition"
	"cosy/experiment"
)

var BuilderIdentifier = "common-dev-agent-builder"

type ToolCallStatus string

const (
	ToolCallStatusInit                ToolCallStatus = "INIT"
	ToolCallStatusPending             ToolCallStatus = "PENDING"
	ToolCallStatusRunning             ToolCallStatus = "RUNNING"
	ToolCallStatusFinished            ToolCallStatus = "FINISHED"              // 终态
	ToolCallStatusError               ToolCallStatus = "ERROR"                 // 终态
	ToolCallStatusCancelled           ToolCallStatus = "CANCELLED"             // 终态
	ToolCallStatusRunningInBackground ToolCallStatus = "RUNNING_IN_BACKGROUND" // 终态，状态脱离agent维护，等价于FINISHED
)

// ToolCallSyncRequest 和插件同步工具参数及状态
type ToolCallSyncRequest struct {
	SessionId      string                 `json:"sessionId,omitempty"`
	RequestId      string                 `json:"requestId,omitempty"`
	ProjectPath    string                 `json:"projectPath,omitempty"`    // 工作空间目录
	ToolCallId     string                 `json:"toolCallId,omitempty"`     // 工具调用id
	ToolCallStatus ToolCallStatus         `json:"toolCallStatus,omitempty"` // 工具状态
	Parameters     map[string]interface{} `json:"parameters,omitempty"`     // 工具调用参数
	Results        interface{}            `json:"results,omitempty"`        // 工具调用结果
	ErrorCode      int                    `json:"errorCode,omitempty"`      // 工具调用错误码
	ErrorMsg       string                 `json:"errorMsg,omitempty"`       // 工具调用错误信息
}

type ToolCallResult struct {
	Content   string      `json:"content,omitempty"`
	RawData   interface{} `json:"rawData,omitempty"`
	Status    ToolCallStatus
	ErrorCode int    `json:"errorCode,omitempty"`
	ErrorMsg  string `json:"errorMsg,omitempty"` // 工具调用错误信息
	Cost      int    `json:"cost,omitempty"`     // 工具调用耗时
}

type ToolFileResult struct {
	FileName  string `json:"fileName,omitempty"`
	Path      string `json:"path,omitempty"`
	StartLine uint32 `json:"startLine,omitempty"`
	EndLine   uint32 `json:"endLine,omitempty"`
	FileCount uint32 `json:"fileCount,omitempty"`
	FileSize  uint32 `json:"fileSize,omitempty"`
	Type      string `json:"type,omitempty"`
	ItemName  string `json:"itemName,omitempty"`
}

type ToolGetProblemsResult struct {
	FileName string                      `json:"fileName,omitempty"`
	Path     string                      `json:"path,omitempty"`
	Severity string                      `json:"severity,omitempty"`
	Message  string                      `json:"message,omitempty"`
	Range    cosyDefinition.ProblemRange `json:"range"`
}

type ToolRunInTerminalResult struct {
	TerminalId string `json:"terminalId,omitempty"`
	Content    string `json:"content,omitempty"`
	ExitCode   int    `json:"exitCode,omitempty"`
}

type ToolEditFileResult struct {
	Language     string                  `json:"language,omitempty"`
	Path         string                  `json:"path,omitempty"`
	FileId       string                  `json:"fileId,omitempty"`
	FileStatus   string                  `json:"fileStatus,omitempty"`
	Version      string                  `json:"version,omitempty"`
	VersionCount string                  `json:"versionCount,omitempty"`
	DiffInfo     cosyDefinition.DiffInfo `json:"diffInfo"`
}

type ToolMemoryResult struct {
	ID string `json:"id,omitempty"`
}

type AgentContext struct {
	LLMConfig *util.LLMConfig // 模型配置
	LLMClient llms.Model      // 调用模型的client
	Tools     []tool.BaseTool // 可用内置工具列表
}

const (
	ReferenceTypeUnittestClass                  = "class"
	ReferenceTypeUnittestFunction               = "function"
	ReferenceTypeUnittestExternalFunction       = "external_function"
	ReferenceTypeUnittestInternalFunction       = "internal_function"
	ReferenceTypeUnittestReturnType             = "return_type"
	ReferenceTypeUnittestExternalStaticFunction = "external_static_function"
	ReferenceTypeUnittestExistTestCode          = "exist_test_code"
	ReferenceTypeUnittestParameter              = "parameter_type"
)

const ToolCallCountLimit = 100 // 单轮最大工具使用次数

type AgentFinishStatus struct {
	FinishCode int    `json:"finishCode"` // chat_finish的错误码
	Reason     string `json:"reason"`     // chat_finish的原因
}

const LLMLingmaServer = "lingma-server"
const KeyToolCallResult = "toolCallResult"
const KeyToolCallRequest = "keyToolCallRequest"

func GetToolCallLimit() int {
	defaultLimit := ToolCallCountLimit
	key := cosyDefinition.ExperimentKeyAgentToolCallLimit
	limit := experiment.ConfigService.GetIntConfigValue(key, experiment.ConfigScopeClient, defaultLimit)
	return limit
}

const (
	MessageExtraClientAnswer = "client_answer"
)

const (
	ToolCallExtraFileId        = "edit_file_file_id"
	ToolCallExtraFilePathValid = "edit_file_path_valid"
	ToolCallExtraNeedUserConfirm = "need_user_confirm"
)

const (
	ToolCallArgumentNameExplanation  = "explanation"
	ToolCallArgumentNameFilePath     = "file_path"
	ToolCallArgumentNameRandomString = "random_string"
)

const (
	EmptyMessage = "empty message"
)
