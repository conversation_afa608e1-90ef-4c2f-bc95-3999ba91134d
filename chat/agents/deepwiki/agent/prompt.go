package agent

import (
	agentDefinition "code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/memory"
	"context"
	chainsCommon "cosy/chat/chains/common"
	"cosy/deepwiki/common"
	"cosy/deepwiki/support"
	"cosy/definition"
	"cosy/indexing/chat_indexing/workspace_tree_indexing"
	"cosy/prompt"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
)

func buildReadmeGeneratePrompt(ctx context.Context, inputs map[string]any) (string, error) {
	var workspacePath string
	workspace, ok := ctx.Value(definition.ContextKeyWorkspace).(definition.WorkspaceInfo)
	if ok {
		workspacePath, _ = workspace.GetWorkspaceFolder()
	}

	referenceCatalogItemsString := ""
	workspaceTreeIndexer, ok := workspace_tree_indexing.WorkspaceTreeFileIndexers.GetFileIndexer(workspacePath)
	if ok {
		referenceCatalogItemsString = workspaceTreeIndexer.WorkspaceTree.GetTree(WorkspaceTreeTokenLimit)
	}

	if referenceCatalogItemsString == "" {
		return "", errors.New("workspace index tree is nil")
	}
	if referenceCatalogItemsString == "." {
		referenceCatalogItemsString = ""
	}

	input := prompt.WikiReadmeGeneratePromptInput{
		Catalogue:     referenceCatalogItemsString,
		WorkspacePath: workspacePath,
	}

	promptStr, err := prompt.Engine.RenderWikiReadmeGeneratePrompt(input)
	if err != nil {
		return "", err
	}
	return promptStr, nil
}

func buildOverviewGeneratePrompt(ctx context.Context, inputs map[string]any) (string, error) {
	projectReadme, ok := inputs[chainsCommon.KeyReadmeContent]
	if !ok {
		return "", errors.New("project readme is nil")
	}

	var workspacePath string
	workspace, ok := ctx.Value(definition.ContextKeyWorkspace).(definition.WorkspaceInfo)
	if ok {
		workspacePath, _ = workspace.GetWorkspaceFolder()
	}

	referenceCatalogItemsString := ""
	workspaceTreeIndexer, ok := workspace_tree_indexing.WorkspaceTreeFileIndexers.GetFileIndexer(workspacePath)
	if ok {
		referenceCatalogItemsString = workspaceTreeIndexer.WorkspaceTree.GetTree(WorkspaceTreeTokenLimit)
	}

	if referenceCatalogItemsString == "" {
		return "", errors.New("workspace index tree is nil")
	}
	if referenceCatalogItemsString == "." {
		referenceCatalogItemsString = ""
	}

	input := prompt.WikiOverviewGeneratePromptInput{
		Catalogue:     referenceCatalogItemsString,
		WorkspacePath: workspacePath,
		ReadmeContent: projectReadme.(string),
	}

	promptStr, err := prompt.Engine.RenderWikiOverviewGeneratePrompt(input)
	if err != nil {
		return "", err
	}
	return promptStr, nil
}

func buildCatalogueThinkPrompt(ctx context.Context, inputs map[string]any) (string, error) {
	codeFiles, ok := inputs[chainsCommon.KeyOptimizedCatalogue].(string)
	if !ok {
		return "", fmt.Errorf("invalid optimized catalogue")
	}
	repoInfo, ok := inputs[chainsCommon.KeyRepoInfo].(definition.RepositoryInfo)
	if !ok {
		return "", fmt.Errorf("invalid repo info")
	}
	repositoryName := repoInfo.Name

	// 获取偏好语言
	preferredLanguage, ok := ctx.Value(chainsCommon.KeyPreferredLanguage).(string)
	if !ok || preferredLanguage == "" {
		preferredLanguage = support.DefaultPreferredLanguage // 默认中文
	}

	// 获取 overview content
	overviewContent := ""
	if content, exists := inputs[chainsCommon.KeyOverviewContent]; exists && content != nil {
		overviewContent = content.(string)
	}

	input := prompt.WikiCataloguePromptInput{
		CodeFiles:         codeFiles,
		WorkspacePath:     repoInfo.WorkspacePath,
		RepositoryName:    repositoryName,
		PreferredLanguage: preferredLanguage,
		OverviewContent:   overviewContent,
	}
	promptStr, err := prompt.Engine.RenderWikiCatalogueThinkPrompt(input)
	if err != nil {
		return "", err
	}
	return promptStr, nil
}

func buildCatalogueGeneratePrompt(ctx context.Context, inputs map[string]any) (string, error) {
	think, ok := inputs[chainsCommon.KeyCatalogueThink].(string)
	if !ok {
		return "", fmt.Errorf("invalid plan response from agent")
	}
	repoInfo, ok := inputs[chainsCommon.KeyRepoInfo].(definition.RepositoryInfo)
	if !ok {
		return "", fmt.Errorf("invalid repo info")
	}
	var catalogue string
	workspaceTreeIndexer, ok := workspace_tree_indexing.WorkspaceTreeFileIndexers.GetFileIndexer(repoInfo.WorkspacePath)
	if ok {
		catalogue = workspaceTreeIndexer.WorkspaceTree.GetTree(GenerateCatalogueWorkspaceTreeTokenLimit)
	} else {
		catalogue, _ = inputs[chainsCommon.KeyOptimizedCatalogue].(string)
	}
	if catalogue == "" {
		return "", errors.New("invalid catalogue")
	}

	// 获取偏好语言
	preferredLanguage, ok := ctx.Value(chainsCommon.KeyPreferredLanguage).(string)
	if !ok || preferredLanguage == "" {
		preferredLanguage = support.DefaultPreferredLanguage // 默认中文
	}

	input := prompt.WikiCataloguePromptInput{
		CodeFiles:         catalogue,
		RepositoryName:    repoInfo.Name,
		WorkspacePath:     repoInfo.WorkspacePath,
		PreferredLanguage: preferredLanguage,
		Think:             think,
	}
	promptStr, err := prompt.Engine.RenderWikiCatalogueGeneratePrompt(input)
	if err != nil {
		return "", err
	}
	return promptStr, nil
}

// buildCatalogueGenerateMessage 生成文档目录生成提示词
func buildCatalogueGenerateMessage(ctx context.Context, inputs map[string]any, agentMemory memory.ShortTermMemory) error {
	rootUserPrompt, err := buildCatalogueGeneratePrompt(ctx, inputs)
	if err != nil {
		return err
	}
	// 获取偏好语言
	preferredLanguage, ok := ctx.Value(chainsCommon.KeyPreferredLanguage).(string)
	if !ok || preferredLanguage == "" {
		preferredLanguage = support.DefaultPreferredLanguage // 默认中文
	}
	agentMemory.AddMessage(&agentDefinition.Message{
		Role:    agentDefinition.RoleTypeUser,
		Content: rootUserPrompt,
	})
	if parentNode, ok := inputs[chainsCommon.KeyWikiParentCatalogNode].(*definition.DocumentationSection); ok {
		parentJson, err := json.MarshalIndent(*parentNode, "", "  ")
		if err != nil {
			return err
		}
		agentMemory.AddMessage(&agentDefinition.Message{
			Role: agentDefinition.RoleTypeUser,
			Content: fmt.Sprintf(`Output only the next level document structure of the following document:
%s

**IMPORTANT:**
- Output fields must contain title, name, dependent_file, prompt, children_plan, and has_children.
- You MUST start your response with <documentation_structure> (opening tag) and end with </documentation_structure> (closing tag)
- You SHOULD respond in %s if possible.`, string(parentJson), preferredLanguage),
		})
	} else {
		agentMemory.AddMessage(&agentDefinition.Message{
			Role: agentDefinition.RoleTypeUser,
			Content: fmt.Sprintf(`Output only the first level document structure.

**IMPORTANT: **
- Output fields must contain title, name, dependent_file, prompt, children_plan, and has_children.
- You MUST start your response with <documentation_structure> (opening tag) and end with </documentation_structure> (closing tag)
- You SHOULD respond in %s if possible.`, preferredLanguage),
		})
	}
	return nil
}

func buildWikiGeneratePrompt(ctx context.Context, inputs map[string]any) (string, error) {
	document, ok := inputs[chainsCommon.KeyCurrentCatalogue].(definition.DocumentCatalog)
	if !ok {
		return "", fmt.Errorf("invalid current ")
	}
	codeFiles, ok := inputs[chainsCommon.KeyOptimizedCatalogue].(string)
	if !ok {
		return "", fmt.Errorf("invalid optimized catalogue")
	}

	repoInfo, ok := inputs[chainsCommon.KeyRepoInfo].(definition.RepositoryInfo)
	if !ok {
		return "", fmt.Errorf("invalid repo info")
	}
	workspacePath := repoInfo.WorkspacePath

	// 获取偏好语言
	preferredLanguage, ok := ctx.Value(chainsCommon.KeyPreferredLanguage).(string)
	if !ok || preferredLanguage == "" {
		preferredLanguage = support.DefaultPreferredLanguage // 默认中文
	}

	// 使用catalogue result的信息构建提示词
	input := prompt.WikiContentPromptInput{
		Catalogue:         codeFiles,
		Title:             document.Name,
		WorkspacePath:     workspacePath,
		Prompt:            document.Prompt,
		Branch:            "main", //暂时为默认值
		PreferredLanguage: preferredLanguage,
	}

	promptStr, err := prompt.Engine.RenderWikiContentGeneratePrompt(input)
	if err != nil {
		return "", err
	}

	return promptStr, nil
}

// BuildCommitDiffAnalysisPrompt 构建commit diff分析的提示词
func BuildCommitDiffAnalysisPrompt(ctx context.Context, inputs map[string]any) (string, error) {
	// 格式化现有目录结构为JSON字符串
	request, ok := inputs[chainsCommon.KeyWikiCommitDiffAnalysisRequest].(common.CommitDiffAnalysisRequest)
	if !ok {
		return "", errors.New("missing CommitDiffAnalysisRequest in inputs")
	}
	existingCataloguesJSON, err := json.MarshalIndent(request.ExistingDocCatalogues, "", "  ")
	if err != nil {
		return "", fmt.Errorf("failed to marshal existing catalogues: %w", err)
	}

	// 不再使用简化模式，直接使用正常的prompt构建逻辑
	// 如果commits被截断，会在日志中显示相关信息，但使用相同的prompt模板

	// 构建commit信息 - 显示完整的文件变更信息
	var commitPrompt string
	if request.CommitInfo != nil && len(request.CommitInfo.Commits) > 0 {
		var commitBuilder strings.Builder

		// 检查是否为截断版本
		originalTotalCommits := request.CommitInfo.TotalCommits
		actualCommitCount := len(request.CommitInfo.Commits)
		if originalTotalCommits > actualCommitCount {
			commitBuilder.WriteString(fmt.Sprintf("TRUNCATED MODE: Showing %d out of %d total commits (truncated to fit token limit).\n", actualCommitCount, originalTotalCommits))
			commitBuilder.WriteString("You can directly analyze the provided file changes for the most recent commits.\n\n")
		} else {
			commitBuilder.WriteString("NORMAL MODE: Complete commit diff with all file changes provided.\n")
			commitBuilder.WriteString("You can directly analyze the provided file changes without needing additional tool calls.\n\n")
		}

		// 统计信息
		totalFileChanges := 0
		for _, commit := range request.CommitInfo.Commits {
			totalFileChanges += len(commit.FileChanges)
		}
		commitBuilder.WriteString(fmt.Sprintf("Total file changes across all commits: %d\n\n", totalFileChanges))

		// 显示详细的commit信息
		for _, commit := range request.CommitInfo.Commits {
			commitBuilder.WriteString(fmt.Sprintf("<commit>\nHash: %s\nMessage: %s\nDate: %s\n",
				commit.Hash, commit.Message, commit.Date.Format("2006-01-02 15:04:05")))

			// 添加文件变更信息
			for _, change := range commit.FileChanges {
				if change.Status == "Renamed" && change.OldPath != "" {
					commitBuilder.WriteString(fmt.Sprintf(" - %s: %s -> %s\n", change.Status, change.OldPath, change.Path))
				} else {
					commitBuilder.WriteString(fmt.Sprintf(" - %s: %s\n", change.Status, change.Path))
				}
			}
			commitBuilder.WriteString("</commit>\n")
		}
		commitPrompt = commitBuilder.String()
	}

	// 构建输入参数
	input := prompt.CodeChangeWikiCatalogueUpdatePromptInput{
		Catalogue:         request.CurrentCatalogue,
		CodeChange:        commitPrompt,
		WorkspacePath:     request.WorkspacePath,
		DocumentCatalogue: string(existingCataloguesJSON),
		PreferredLanguage: request.Language,
	}

	// 使用prompt引擎渲染
	promptStr, err := prompt.Engine.RenderCodeChangeUpdateCataloguePrompt(input)
	if err != nil {
		return "", fmt.Errorf("failed to render wiki catalogue update prompt: %w", err)
	}

	return promptStr, nil
}

// BuildCommitDiffAnalysisSimplifiedPrompt 构建simplified模式的commit diff分析提示词
func BuildCommitDiffAnalysisSimplifiedPrompt(ctx context.Context, inputs map[string]any) (string, error) {
	// 格式化现有目录结构为JSON字符串
	request, ok := inputs[chainsCommon.KeyWikiCommitDiffAnalysisRequest].(common.CommitDiffAnalysisRequest)
	if !ok {
		return "", errors.New("missing CommitDiffAnalysisRequest in inputs")
	}
	existingCataloguesJSON, err := json.MarshalIndent(request.ExistingDocCatalogues, "", "  ")
	if err != nil {
		return "", fmt.Errorf("failed to marshal existing catalogues: %w", err)
	}

	// 构建simplified模式的commit信息
	var commitPrompt string
	if request.CommitInfo != nil && len(request.CommitInfo.Commits) > 0 {
		var commitBuilder strings.Builder

		commitBuilder.WriteString("✅ SIMPLIFIED MODE: Only commit hashes provided to save tokens.\n")
		commitBuilder.WriteString("Use get_commit_diff tool with commit hash to get detailed information (message, author, date, file changes).\n\n")

		totalCommits := len(request.CommitInfo.Commits)
		commitBuilder.WriteString(fmt.Sprintf("Total commits: %d\n\n", totalCommits))

		// 只显示commit hash列表，大幅减少token使用
		commitBuilder.WriteString("Available commit hashes:\n")
		for i, commit := range request.CommitInfo.Commits {
			// 限制显示数量，避免token过多
			if i >= 100 {
				commitBuilder.WriteString(fmt.Sprintf("... and %d more commits (use get_commit_diff tool to analyze any commit by hash)\n", totalCommits-100))
				break
			}
			commitBuilder.WriteString(fmt.Sprintf("- %s\n", commit.Hash))
		}

		commitBuilder.WriteString("\nTo analyze any commit, use: get_commit_diff(commit_hash=\"<hash>\")\n")
		commitPrompt = commitBuilder.String()
	}

	// 构建输入参数
	input := prompt.CodeChangeWikiCatalogueUpdatePromptInput{
		Catalogue:         request.CurrentCatalogue,
		CodeChange:        commitPrompt,
		WorkspacePath:     request.WorkspacePath,
		DocumentCatalogue: string(existingCataloguesJSON),
		PreferredLanguage: request.Language,
	}

	// 使用simplified模式的prompt引擎渲染
	promptStr, err := prompt.Engine.RenderCodeChangeUpdateCatalogueSimplifiedPrompt(input)
	if err != nil {
		return "", fmt.Errorf("failed to render simplified wiki catalogue update prompt: %w", err)
	}

	return promptStr, nil
}

func BuildCodeChunkDiffAnalysisPrompt(ctx context.Context, inputs map[string]any) (string, error) {
	// 格式化现有目录结构为JSON字符串
	request, ok := inputs[chainsCommon.KeyWikiCodeChunksAnalysisRequest].(common.CodeChunkDiffAnalysisRequest)
	if !ok {
		return "", errors.New("missing CommitDiffAnalysisRequest in inputs")
	}
	existingCataloguesJSON, err := json.MarshalIndent(request.ExistingDocCatalogues, "", "  ")
	if err != nil {
		return "", fmt.Errorf("failed to marshal existing catalogues: %w", err)
	}

	// 构建输入参数
	input := prompt.CodeChunksWikiCatalogueUpdatePromptInput{
		Catalogue:         request.CurrentCatalogue,
		CodeChunks:        request.CodeChunks,
		WorkspacePath:     request.WorkspacePath,
		DocumentCatalogue: string(existingCataloguesJSON),
		PreferredLanguage: request.Language,
	}

	// 使用prompt引擎渲染
	promptStr, err := prompt.Engine.RenderCodeChunksUpdateCataloguePrompt(input)
	if err != nil {
		return "", fmt.Errorf("failed to render wiki catalogue update prompt: %w", err)
	}

	return promptStr, nil
}

// buildWikiUpdatePrompt 构建wiki更新的提示词
func buildWikiUpdatePrompt(ctx context.Context, inputs map[string]any) (string, error) {
	document, ok := inputs[chainsCommon.KeyCurrentCatalogue].(definition.DocumentCatalog)
	if !ok {
		return "", fmt.Errorf("invalid current catalogue")
	}

	codeFiles, ok := inputs[chainsCommon.KeyOptimizedCatalogue].(string)
	if !ok {
		return "", fmt.Errorf("invalid optimized catalogue")
	}

	repoInfo, ok := inputs[chainsCommon.KeyRepoInfo].(definition.RepositoryInfo)
	if !ok {
		return "", fmt.Errorf("invalid repo info")
	}

	existingWikiContent, ok := inputs[chainsCommon.KeyOldWikiContent].(string)
	if !ok {
		existingWikiContent = "" // 如果没有现有内容，设为空字符串
	}

	workspacePath := repoInfo.WorkspacePath

	// 获取偏好语言
	preferredLanguage, ok := ctx.Value(chainsCommon.KeyPreferredLanguage).(string)
	if !ok || preferredLanguage == "" {
		preferredLanguage = support.DefaultPreferredLanguage // 默认中文
	}

	// 构建详细的commits信息作为单独字段
	var relatedCommitsContent string
	if relatedCommits, ok := inputs[chainsCommon.KeyRelatedCommits]; ok {
		relatedCommitsContent = support.BuildRelatedCommitsContent(relatedCommits)
	}

	// 构建输入参数
	input := prompt.WikiUpdatePromptInput{
		Prompt:            document.Prompt, // 保持原始的文档目标
		Title:             document.Name,
		WorkspacePath:     workspacePath,
		Catalogue:         codeFiles,
		WikiContent:       existingWikiContent,
		PreferredLanguage: preferredLanguage,
		RelatedCommits:    relatedCommitsContent, // 单独的commits字段
	}

	promptStr, err := prompt.Engine.RenderWikiUpdatePrompt(input)
	if err != nil {
		return "", err
	}

	return promptStr, nil
}
