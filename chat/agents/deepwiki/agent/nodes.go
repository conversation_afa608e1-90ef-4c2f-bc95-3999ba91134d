package agent

import (
	"code.alibaba-inc.com/cosy/lingma-agent-graph/memory"
	"context"
	common2 "cosy/chat/chains/common"
	common3 "cosy/deepwiki/common"
	wikiAgent "cosy/deepwiki/support"
	"cosy/prompt"
	"cosy/sse"
	"net/http"
	"strings"
	"time"

	agentLlms "code.alibaba-inc.com/cosy/lingma-agent-graph/llms"

	agentSupport "cosy/chat/agents/support"
	toolCommon "cosy/chat/agents/tool/common"
	"cosy/chat/chains/common"
	"cosy/definition"
	cosyErrors "cosy/errors"
	"cosy/log"
	"cosy/util"
	"errors"

	agentDefinition "code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
)

func newReadmeGenerateInitNode(nodeName string) graph.Node {
	return newInitNodeWithPromptBuilder(nodeName, buildReadmeGeneratePrompt)
}

func newOverviewGenerateInitNode(nodeName string) graph.Node {
	return newInitNodeWithPromptBuilder(nodeName, buildOverviewGeneratePrompt)
}

func newCatalogueGenerateInitNode(nodeName string) graph.Node {
	return newInitNodeWithMessageBuilder(nodeName, buildCatalogueGenerateMessage)
}

func newCatalogueThinkInitNode(nodeName string) graph.Node {
	return newInitNodeWithOnlyUserPrompt(nodeName, buildCatalogueThinkPrompt)
}

func newWikiGenerateInitNode(nodeName string) graph.Node {
	return newInitNodeWithOnlyUserPrompt(nodeName, buildWikiGeneratePrompt)
}

func newCommitDiffInitNode(nodeName string) graph.Node {
	return newInitNodeWithPromptBuilder(nodeName, BuildCommitDiffAnalysisPrompt)
}

func newCodeChunkUpdateInitNode(nodeName string) graph.Node {
	return newInitNodeWithPromptBuilder(nodeName, BuildCodeChunkDiffAnalysisPrompt)
}

func newWikiUpdateInitNode(nodeName string) graph.Node {
	return newInitNodeWithOnlyUserPrompt(nodeName, buildWikiUpdatePrompt)
}

func newInitNodeWithPromptBuilder(nodeName string, promptBuilder func(ctx context.Context, inputs map[string]any) (string, error)) graph.Node {
	return graph.NewNode(
		nodeName,
		graph.NewFunctionNodeRunnable(func(ctx context.Context, state graph.State) (graph.State, error) {

			inputs := state.ToChainInput()

			preferredLanguage := inputs[common.KeyPreferredLanguage].(string)
			agentState := state.(*common3.DeepWikiGenerateState)
			agentContext, ok := ctx.Value(common.KeyDeepwikiAgentContext).(*common3.AgentContext)
			if ok {
				agentContext.State = agentState
			}

			userPrompt, err := promptBuilder(ctx, agentState.CopyInputs())
			if err != nil {
				return nil, err
			}

			shortTermMemory := agentState.ShortTermMemory
			shortTermMemory.Clear()

			systemPrompt, err := prompt.Engine.RenderWikiGeneralSystemPrompt(prompt.WikiGeneralSystemPromptInput{
				PreferredLanguage: preferredLanguage,
			})
			if err != nil {
				return nil, err
			}

			systemMessage := &agentDefinition.Message{Role: agentDefinition.RoleTypeSystem, Content: systemPrompt}
			shortTermMemory.AddMessage(systemMessage)

			userMessage := &agentDefinition.Message{Role: agentDefinition.RoleTypeUser, Content: userPrompt}
			shortTermMemory.AddMessage(userMessage)

			return agentState, nil
		}))
}

// newInitNodeWithOnlyUserPrompt 创建只包含用户提示的初始化节点（用于wiki generate和update）
func newInitNodeWithOnlyUserPrompt(nodeName string, promptBuilder func(ctx context.Context, inputs map[string]any) (string, error)) graph.Node {
	return graph.NewNode(
		nodeName,
		graph.NewFunctionNodeRunnable(func(ctx context.Context, state graph.State) (graph.State, error) {

			agentState := state.(*common3.DeepWikiGenerateState)
			agentContext, ok := ctx.Value(common.KeyDeepwikiAgentContext).(*common3.AgentContext)
			if ok {
				agentContext.State = agentState
			}

			userPrompt, err := promptBuilder(ctx, agentState.CopyInputs())
			if err != nil {
				return nil, err
			}

			shortTermMemory := agentState.ShortTermMemory
			shortTermMemory.Clear()

			// 只添加用户消息，不添加系统消息
			userMessage := &agentDefinition.Message{Role: agentDefinition.RoleTypeUser, Content: userPrompt}
			shortTermMemory.AddMessage(userMessage)

			return agentState, nil
		}))
}

// newInitNodeWithMessageBuilder 自由追加message，允许添加多个message
func newInitNodeWithMessageBuilder(nodeName string, messageBuilder func(ctx context.Context, inputs map[string]any, agentMemory memory.ShortTermMemory) error) graph.Node {
	return graph.NewNode(
		nodeName,
		graph.NewFunctionNodeRunnable(func(ctx context.Context, state graph.State) (graph.State, error) {

			agentState := state.(*common3.DeepWikiGenerateState)
			agentContext, ok := ctx.Value(common.KeyDeepwikiAgentContext).(*common3.AgentContext)
			if ok {
				agentContext.State = agentState
			}

			shortTermMemory := agentState.ShortTermMemory
			shortTermMemory.Clear()

			err := messageBuilder(ctx, agentState.CopyInputs(), agentState.ShortTermMemory)
			if err != nil {
				return nil, err
			}

			return agentState, nil
		}))
}

var initCatalogueGenerateNode = newCatalogueGenerateInitNode(InitCatalogueGenerateNodeName)
var initCatalogueThinkNode = newCatalogueThinkInitNode(InitCatalogueThinkNodeName)
var initWikiGenerateNode = newWikiGenerateInitNode(InitWikiGenerateNodeName)
var initCommitDiffNode = newCommitDiffInitNode(InitCommitDiffNodeName)
var initCodeChunkUpdateCatalogueNode = newCodeChunkUpdateInitNode(InitUpdateWikiWithCodeNodeName)
var initWikiUpdateNode = newWikiUpdateInitNode(InitWikiUpdateNodeName)

// 调用大模型
var llmNode = createLLmNode(definition.AgentTaskDeepWikiGenerate)

func createLLmNode(taskId string) graph.Node {
	return graph.NewNode(
		LLMNodeName,
		graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
			agentState := input.(*common3.DeepWikiGenerateState)

			graphStatValue, _ := agentState.GetInput(common.KeyWikiGenerateStatCurrentGraph)
			graphStat := graphStatValue.(*definition.WikiGenerateGraphStat)

			llmCallStat := definition.LlmCallStat{
				StartCall: time.Now(),
			}

			agentContext, ok := ctx.Value(common.KeyDeepwikiAgentContext).(*common3.AgentContext)
			if !ok {
				return nil, errors.New("AgentContext not found")
			}

			toolParams := wikiAgent.ConvertToLlmTool(ctx, agentContext.Tools)

			// 获取输入消息用于日志记录
			inputMessages, _, _ := agentState.ShortTermMemory.MessagesWithCondenser()

			llmMessage, llmRequestId, err := invokeLlmWithTask(ctx, agentState, toolParams, taskId)

			if err != nil {
				// 调用llm失败，转换一下错误，通过err退出
				return agentState, err
			}

			llmCallStat.EndCall = time.Now()
			llmCallStat.Rt = llmCallStat.EndCall.Sub(llmCallStat.StartCall).Milliseconds()
			llmCallStat.RequestId = llmRequestId
			llmCallStat.InputTokens = llmMessage.ResponseMeta.Usage.PromptTokens
			llmCallStat.OutputTokens = llmMessage.ResponseMeta.Usage.CompletionTokens
			llmCallStat.CachedTokens = llmMessage.ResponseMeta.Usage.PromptTokensDetails.CachedTokens

			// 当输出 tokens < 700 时记录 response
			if llmCallStat.OutputTokens < 700 {
				llmCallStat.Response = llmMessage.Content
			}

			// 使用 log info 打印该次 llm call 的 input 和 output message
			inputMsgCount := len(inputMessages)
			inputTokens := llmCallStat.InputTokens
			outputTokens := llmCallStat.OutputTokens
			cachedTokens := llmCallStat.CachedTokens
			responseLength := len(llmMessage.Content)

			log.Debugf("[LLM Call] Input: %d messages, %d tokens (cached: %d) | Output: %d tokens, %d chars | RT: %dms",
				inputMsgCount, inputTokens, cachedTokens, outputTokens, responseLength, llmCallStat.Rt)

			if graphStat != nil {
				graphStat.LlmCalls = append(graphStat.LlmCalls, llmCallStat)
			}

			lastMessage := llmMessage

			agentState.ShortTermMemory.AddMessage(lastMessage)
			return agentState, nil
		}))
}

func createLongLLmNode(verifier func(message *agentDefinition.Message) bool, messageBuilder func(agentState *common3.DeepWikiGenerateState), taskId string) graph.Node {
	return graph.NewNode(
		LLMNodeName,
		graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
			agentState := input.(*common3.DeepWikiGenerateState)

			graphStatValue, _ := agentState.GetInput(common.KeyWikiGenerateStatCurrentGraph)
			graphStat := graphStatValue.(*definition.WikiGenerateGraphStat)

			agentContext, ok := ctx.Value(common.KeyDeepwikiAgentContext).(*common3.AgentContext)
			if !ok {
				return nil, errors.New("AgentContext not found")
			}

			toolParams := wikiAgent.ConvertToLlmTool(ctx, agentContext.Tools)

			for i := 0; i < 2; i++ {
				llmCallStat := definition.LlmCallStat{
					StartCall: time.Now(),
				}
				inputMessages, _, _ := agentState.ShortTermMemory.MessagesWithCondenser()

				llmMessage, llmRequestId, err := invokeLlmWithTask(ctx, agentState, toolParams, taskId)
				log.Debugf("[Long LLM Call] llmRequestId=%s times=%d", llmRequestId, i)
				if err != nil {
					// 调用llm失败，转换一下错误，通过err退出
					return agentState, err
				}

				llmCallStat.EndCall = time.Now()
				llmCallStat.Rt = llmCallStat.EndCall.Sub(llmCallStat.StartCall).Milliseconds()
				llmCallStat.RequestId = llmRequestId
				llmCallStat.InputTokens = llmMessage.ResponseMeta.Usage.PromptTokens
				llmCallStat.OutputTokens = llmMessage.ResponseMeta.Usage.CompletionTokens
				llmCallStat.CachedTokens = llmMessage.ResponseMeta.Usage.PromptTokensDetails.CachedTokens

				// 当输出 tokens < 700 时记录 response
				if llmCallStat.OutputTokens < 700 {
					llmCallStat.Response = llmMessage.Content
				}

				// 使用 log info 打印该次 llm call 的 input 和 output message
				inputMsgCount := len(inputMessages)
				inputTokens := llmCallStat.InputTokens
				outputTokens := llmCallStat.OutputTokens
				cachedTokens := llmCallStat.CachedTokens
				responseLength := len(llmMessage.Content)

				log.Debugf("[LLM Call] Input: %d messages, %d tokens (cached: %d) | Output: %d tokens, %d chars | RT: %dms",
					inputMsgCount, inputTokens, cachedTokens, outputTokens, responseLength, llmCallStat.Rt)

				if graphStat != nil {
					graphStat.LlmCalls = append(graphStat.LlmCalls, llmCallStat)
				}
				if llmMessage != nil {
					agentState.ShortTermMemory.AddMessage(llmMessage)
					if !verifier(llmMessage) {
						messageBuilder(agentState)
					} else {
						break
					}
				}
			}
			return agentState, nil
		}))
}

// createToolOverLimitNode 创建专门为wiki generate和update使用的工具调用超限处理节点，完全过滤system prompt
func createToolOverLimitNode(nodeName string, outputKey string, logPrefix string, userPrompt string) graph.Node {
	return graph.NewNode(
		nodeName,
		graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
			agentState := input.(*common3.DeepWikiGenerateState)

			graphStatValue, _ := agentState.GetInput(common.KeyWikiGenerateStatCurrentGraph)
			graphStat := graphStatValue.(*definition.WikiGenerateGraphStat)

			llmCallStat := definition.LlmCallStat{
				StartCall: time.Now(),
			}

			//不指定工具，强制llm输出
			requestIdValue, _ := agentState.GetInput(common.KeyRequestId)
			requestId, _ := requestIdValue.(string)
			sessionIdValue, _ := agentState.GetInput(common.KeySessionId)
			sessionId, _ := sessionIdValue.(string)
			wikiRequest, _ := agentState.GetInput(common2.KeyCreateDeepwikiRequest)
			createDeepWikiRequest, _ := wikiRequest.(definition.CreateDeepwikiRequest)
			if userPrompt == "" {
				userPrompt = "Now, based on the known context information, output the result directly as required, strictly following the output format requirements without any additional output. Do not call any tool"
			}
			// 在assistant消息后添加一个user消息，要求继续生成
			userMessage := &agentDefinition.Message{
				Role:    agentDefinition.RoleTypeUser,
				Content: userPrompt,
			}
			agentState.ShortTermMemory.AddMessage(userMessage)
			messages, _, _ := agentState.ShortTermMemory.MessagesWithCondenser()

			// 使用专门的过滤函数，完全移除system消息
			messages = clearToolCallMessageWhenOverLimit(messages, false)
			llmMessage, llmRequestId, err := invokeLlmWithMessages(ctx, requestId, sessionId, messages, nil, createDeepWikiRequest)

			if err != nil {
				// 调用llm失败，转换一下错误，通过err退出
				log.Errorf("%s tool over limit error: %v", logPrefix, err)
				return agentState, err
			}

			// 记录 LLM 调用统计信息
			llmCallStat.EndCall = time.Now()
			llmCallStat.Rt = llmCallStat.EndCall.Sub(llmCallStat.StartCall).Milliseconds()
			llmCallStat.RequestId = llmRequestId
			llmCallStat.InputTokens = llmMessage.ResponseMeta.Usage.PromptTokens
			llmCallStat.OutputTokens = llmMessage.ResponseMeta.Usage.CompletionTokens
			llmCallStat.CachedTokens = llmMessage.ResponseMeta.Usage.PromptTokensDetails.CachedTokens

			// 当输出 tokens < 700 时记录 response
			if llmCallStat.OutputTokens < 700 {
				llmCallStat.Response = llmMessage.Content
			}

			// 使用 log info 打印该次 llm call 的 input 和 output message
			inputMsgCount := len(messages)
			inputTokens := llmCallStat.InputTokens
			outputTokens := llmCallStat.OutputTokens
			cachedTokens := llmCallStat.CachedTokens
			responseLength := len(llmMessage.Content)

			log.Debugf("[LLM Call - %s Tool Over Limit No System] Input: %d messages, %d tokens (cached: %d) | Output: %d tokens, %d chars | RT: %dms",
				logPrefix, inputMsgCount, inputTokens, cachedTokens, outputTokens, responseLength, llmCallStat.Rt)

			if graphStat != nil {
				graphStat.LlmCalls = append(graphStat.LlmCalls, llmCallStat)
			}

			lastMessage := llmMessage

			agentState.ShortTermMemory.AddMessage(lastMessage)

			// 将结果写入到对应的key中
			if outputKey != "" && lastMessage.Content != "" {
				agentState.SetInput(outputKey, lastMessage.Content)
				log.Debugf("%s tool over limit completed, content length: %d", logPrefix, len(lastMessage.Content))
			}

			// 确保agentContext中的State是最新的
			if agentContext, ok := ctx.Value(common.KeyDeepwikiAgentContext).(*common3.AgentContext); ok {
				agentContext.State = agentState
				log.Debugf("[%s] Updated agentContext.State with latest agentState in tool over limit", logPrefix)
			}

			return agentState, nil
		}))
}

// 各种场景专用的toolOverLimit node
var readmeToolOverLimitNode = createToolOverLimitNode(
	ReadmeGenerateToolOverLimitNodeName,
	common.KeyReadmeContent,
	"readme",
	"")

var overviewToolOverLimitNode = createToolOverLimitNode(
	OverviewGenerateToolOverLimitNodeName,
	common.KeyOverviewContent,
	"overview",
	"")

var commitDiffToolOverLimitNode = createToolOverLimitNode(
	CommitDiffToolOverLimitNodeName,
	common.KeyWikiCommitDiffResponse,
	"commit diff",
	"",
)

// 执行工具调用
var toolNode = graph.NewNode(
	ToolNodeName,
	graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
		agentState := input.(*common3.DeepWikiGenerateState)
		agentContext, ok := ctx.Value(common.KeyDeepwikiAgentContext).(*common3.AgentContext)
		if !ok {
			return nil, errors.New("AgentContext not found")
		}
		graphStatValue, _ := agentState.GetInput(common.KeyWikiGenerateStatCurrentGraph)
		graphStat := graphStatValue.(*definition.WikiGenerateGraphStat)

		toolCallStat := definition.ToolCallStat{
			StartCall: time.Now(),
		}

		availableTools := agentContext.Tools
		toolMessages := agentSupport.ExecuteTool(ctx, availableTools, agentState, nil, nil)

		toolCalls := agentSupport.GetToolCall(agentState)
		if len(toolCalls) > 0 {
			toolCallStat.EndCall = time.Now()
			toolCallStat.ToolName = toolCalls[0].Function.Name
			toolCallStat.Args = toolCalls[0].Function.Arguments
			toolCallStat.Rt = toolCallStat.EndCall.Sub(toolCallStat.StartCall).Milliseconds()

			// 从最后一条assistant消息的Extra字段中获取LLM调用的RequestId
			messages := agentState.ShortTermMemory.Messages()
			var llmRequestId string

			// 从后往前查找最近的assistant消息，获取其callServerRequestId
			for i := len(messages) - 1; i >= 0; i-- {
				if messages[i].Role == agentDefinition.RoleTypeAssistant && messages[i].Extra != nil {
					if callServerRequestId, ok := messages[i].Extra["callServerRequestId"].(string); ok {
						llmRequestId = callServerRequestId
						break
					}
				}
			}

			if llmRequestId != "" {
				toolCallStat.RequestId = llmRequestId
				log.Debugf("[ToolCallStat] Recording tool call with LLM RequestId: %s", llmRequestId)
			} else {
				// 如果无法获取LLM RequestId，回退到session RequestId
				if requestIdValue, ok := agentState.GetInput(common.KeyRequestId); ok {
					if requestId, ok := requestIdValue.(string); ok {
						toolCallStat.RequestId = requestId
						log.Debugf("[ToolCallStat] Fallback: Recording tool call with Session RequestId: %s", requestId)
					}
				}
			}
		}

		// 先将工具消息添加到短期记忆
		agentState.ToolCallCount++
		agentState.ShortTermMemory.AppendMessages(toolMessages...)

		if graphStat != nil {
			graphStat.ToolCalls = append(graphStat.ToolCalls, toolCallStat)
		}

		return agentState, nil
	}))

// createFinishNode 创建专门的finish node，用于存储特定agent的输出结果
func createFinishNode(nodeName string, outputKey string, logPrefix string) graph.Node {
	return graph.NewNode(
		nodeName,
		graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
			agentState := input.(*common3.DeepWikiGenerateState)
			messages := agentState.ShortTermMemory.Messages()
			if messages == nil || len(messages) <= 0 {
				log.Errorf("%s generate error, response is empty.", logPrefix)
				return agentState, nil
			}
			lastMessage := messages[len(messages)-1]
			agentState.SetInput(outputKey, lastMessage.Content)

			// 确保agentContext中的State是最新的
			if agentContext, ok := ctx.Value(common.KeyDeepwikiAgentContext).(*common3.AgentContext); ok {
				agentContext.State = agentState
				log.Debugf("[%s] Updated agentContext.State with latest agentState", logPrefix)
			}

			log.Debugf("%s generate completed, content length: %d", logPrefix, len(lastMessage.Content))

			return agentState, nil
		}))
}

// 各种agent专用的finish node
var readmeFinishNode = createFinishNode(
	ReadmeGenerateFinishNodeName,
	common.KeyReadmeContent,
	"readme")

var overviewFinishNode = createFinishNode(
	OverviewGenerateFinishNodeName,
	common.KeyOverviewContent,
	"overview")

var catalogueThinkFinishNode = createFinishNode(
	FinishToolNodeName,
	common.KeyCatalogueThink,
	"catalogue think")

var catalogueGenerateFinishNode = createFinishNode(
	FinishToolNodeName,
	common.KeyCatalogueRawOutput,
	"catalogue generate")

var wikiGenerateFinishNode = createFinishNode(
	FinishToolNodeName,
	common.KeyWikiContent,
	"wiki content")

var commitDiffFinishNode = createFinishNode(
	CommitDiffFinishNodeName,
	common.KeyWikiCommitDiffResponse,
	"commit diff",
)

func invokeLlm(ctx context.Context, input graph.State, toolParams []agentLlms.Tool) (*agentDefinition.Message, string, error) {
	return invokeLlmWithTask(ctx, input, toolParams, definition.AgentTaskDeepWikiGenerate)
}

func invokeLlmWithTask(ctx context.Context, input graph.State, toolParams []agentLlms.Tool, taskId string) (*agentDefinition.Message, string, error) {
	agentState := input.(*common3.DeepWikiGenerateState)

	//发起llm调用前做上下文长度进行处理
	messages, _, _ := agentState.ShortTermMemory.MessagesWithCondenser()

	requestIdValue, _ := agentState.GetInput(common.KeyRequestId)
	requestId, _ := requestIdValue.(string)
	sessionIdValue, _ := agentState.GetInput(common.KeySessionId)
	sessionId, _ := sessionIdValue.(string)
	wikiRequest, _ := agentState.GetInput(common2.KeyCreateDeepwikiRequest)
	createDeepWikiRequest, _ := wikiRequest.(definition.CreateDeepwikiRequest)

	return invokeLlmWithMessagesWithTask(ctx, requestId, sessionId, messages, toolParams, createDeepWikiRequest, taskId)
}

func invokeLlmWithMessages(ctx context.Context, requestId, sessionId string, messages []*agentDefinition.Message, toolParams []agentLlms.Tool, wikiRequest definition.CreateDeepwikiRequest) (*agentDefinition.Message, string, error) {
	return invokeLlmWithMessagesWithTask(ctx, requestId, sessionId, messages, toolParams, wikiRequest, definition.AgentTaskDeepWikiGenerate)
}

func invokeLlmWithMessagesWithTask(ctx context.Context, requestId, sessionId string, messages []*agentDefinition.Message, toolParams []agentLlms.Tool, wikiRequest definition.CreateDeepwikiRequest, taskId string) (*agentDefinition.Message, string, error) {
	req, llmRequestId, err := wikiAgent.BuildAgentRequest(ctx, requestId, sessionId, messages, toolParams, wikiRequest, taskId)
	if err != nil {
		log.Debugf("[wiki_agent] build agent request error: %v", err)
		return nil, "", err
	}
	if req == nil {
		return nil, "", errors.New("build agent request error. requestId:" + requestId)
	}
	sseWikiClient := sse.NewSseWikiClient(map[string]string{})
	llmChatResponse, err := agentSupport.ParseStreamingChatResponse(ctx, sseWikiClient, requestId, sessionId, nil, req, func(req *http.Request, rsp *http.Response) {
		log.Debugf("[wiki_agent] parse streaming content error: %v", err)
	}, nil, nil)
	if err != nil {
		log.Debugf("[wiki_agent] parse streaming content error: %v", err)
		return nil, "", err
	}
	llMessage := wikiAgent.ConvertToMessage(llmChatResponse)

	if err != nil {
		log.Debugf("[wiki_agent] finish chat with error, chatRecordId=%s, requestId=%s", requestId, requestId)
	} else {
		if llMessage.ResponseMeta.FinishReason != "" {
			log.Debugf("[wiki_agent] finish chat response, chatRecordId=%s, requestId=%s modelRequestId=%s reason=%s", requestId, requestId, llmChatResponse.ID, llMessage.ResponseMeta.FinishReason)
		}
		if llMessage.ResponseMeta.FinishReason == definition.FinishReasonErrorFinish {
			if llMessage.ToolCalls != nil && util.Contains(toolCommon.EditFileTools, llMessage.ToolCalls[0].Function.Name) {
				llMessage.ResponseMeta.FinishReason = definition.FinishReasonLength
			} else {
				err = cosyErrors.New(cosyErrors.SystemError, llMessage.Content)
			}
		} else if llMessage.Content == "" && llMessage.ToolCallID == "" &&
			len(llMessage.ToolCalls) == 0 && llMessage.ReasoningContent == "" {
			select {
			case <-ctx.Done():
				llMessage.Content = EmptyMessage
			default:
				//如果lastMessage值为空，则直接退出
				log.Debug("assistant response is empty.")
				err = cosyErrors.New(cosyErrors.SystemError, "assistant response is empty")
			}
		}
	}
	return llMessage, llmRequestId, err
}

// clearToolCallMessageWhenOverLimit 专门为wiki generate和update使用，完全过滤掉system消息
func clearToolCallMessageWhenOverLimit(messages []*agentDefinition.Message, requireSystem bool) []*agentDefinition.Message {
	if messages == nil || len(messages) <= 0 {
		return messages
	}
	var newMessages []*agentDefinition.Message
	lastMessageContent := make([]string, 0, 12)
	for _, m := range messages {
		// 跳过system消息，确保wiki generate和update不会有system prompt残余
		if m.Role == agentDefinition.RoleTypeSystem {
			if requireSystem {
				newMessages = append(newMessages, m)
			} else {
				continue // 直接跳过，不添加到newMessages中
			}
		} else if m.Role == agentDefinition.RoleTypeUser {
			if len(lastMessageContent) > 0 {
				newMessages = appendContextOverLimitMessage(newMessages, lastMessageContent)
				lastMessageContent = make([]string, 0, 12)
			}
			newMessages = append(newMessages, m)
		} else if m.Role == agentDefinition.RoleTypeAssistant {
			if len(m.ToolCalls) >= 0 {
				toolCall0 := m.ToolCalls[0]
				explanation := agentSupport.GetToolExplanation(toolCall0.Function.Arguments)
				explanation = strings.TrimSpace(explanation)
				if explanation != "" {
					lastMessageContent = append(lastMessageContent, explanation)
				}
			}
			// 清理trailing whitespace
			content := strings.TrimSpace(m.Content)
			if content != "" {
				lastMessageContent = append(lastMessageContent, content)
			}
		} else if m.Role == agentDefinition.RoleTypeTool {
			// 将Tool消息转换为Assistant消息，并清理trailing whitespace
			content := strings.TrimSpace(m.Content)
			if content != "" {
				lastMessageContent = append(lastMessageContent, content)
			}
		}
	}
	if len(lastMessageContent) > 0 {
		// 如果最后一条消息不是user，需要将收集的上下文flush
		newMessages = appendContextOverLimitMessage(newMessages, lastMessageContent)
	}
	return newMessages
}

// appendContextOverLimitMessage 添加一条user消息，内容为收集的上下文
func appendContextOverLimitMessage(messages []*agentDefinition.Message, lastMessageContent []string) []*agentDefinition.Message {
	newMessage := &agentDefinition.Message{
		Role:    agentDefinition.RoleTypeUser,
		Content: "The following is the relevant context information:\n" + strings.Join(lastMessageContent, "\n"),
	}
	return append(messages, newMessage)
}

// wiki update专用的toolOverLimit和finish节点
var wikiUpdateToolOverLimitNode = createToolOverLimitNode(
	WikiUpdateToolOverLimitNodeName,
	common.KeyWikiContent,
	"wiki update",
	"\"Now, based on the known context information, output the result directly as required, strictly following the output format requirements without any additional output. Output must start with <docs> and end with </docs>. Do not call any tool\"")

var wikiUpdateFinishNode = createFinishNode(
	WikiUpdateFinishNodeName,
	common.KeyWikiContent,
	"wiki update")

// update wiki with code专用的toolOverLimit和finish节点
var updateWikiWithCodeToolOverLimitNode = createToolOverLimitNode(
	UpdateWikiWithCodeToolOverLimitNodeName,
	common.KeyWikiCommitDiffResponse,
	"update wiki with code",
	"")

var updateWikiWithCodeFinishNode = createFinishNode(
	UpdateWikiWithCodeFinishNodeName,
	common.KeyWikiCommitDiffResponse,
	"update wiki with code")

// catalogue generate专用的toolOverLimit和finish节点 (使用新的常量名)
var catalogueGenerateToolOverLimitNodeNew = createToolOverLimitNode(
	CatalogueGenerateToolOverLimitNodeName,
	common.KeyCatalogueRawOutput,
	"catalogue generate",
	"")

var catalogueGenerateFinishNodeNew = createFinishNode(
	CatalogueGenerateFinishNodeName,
	common.KeyCatalogueRawOutput,
	"catalogue generate")

// catalogue think专用的toolOverLimit和finish节点 (使用新的常量名)
var catalogueThinkToolOverLimitNodeNew = createToolOverLimitNode(
	CatalogueThinkToolOverLimitNodeName,
	common.KeyCatalogueThink,
	"catalogue think",
	"Now, based on the known context information, output the result directly as required, strictly following the output format requirements without any additional output. Output must start with <analysis> and end with </analysis>. Do not call any tool")

var catalogueThinkFinishNodeNew = createFinishNode(
	CatalogueThinkFinishNodeName,
	common.KeyCatalogueThink,
	"catalogue think")

// wiki generate专用的toolOverLimit和finish节点 (使用新的常量名)
var wikiGenerateToolOverLimitNodeNew = createToolOverLimitNode(
	WikiGenerateToolOverLimitNodeName,
	common.KeyWikiContent,
	"wiki content",
	"Now, based on the known context information, output the result directly as required, strictly following the output format requirements without any additional output. Output must start with <docs> and end with </docs>. Do not call any tool")

var wikiGenerateFinishNodeNew = createFinishNode(
	WikiGenerateFinishNodeName,
	common.KeyWikiContent,
	"wiki content")
