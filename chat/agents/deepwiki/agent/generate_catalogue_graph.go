package agent

import (
	agentDefinition "code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"context"
	chainsCommon "cosy/chat/chains/common"
	wikiCommon "cosy/deepwiki/common"
	"cosy/definition"
	"cosy/indexing"
	"cosy/log"
	"fmt"
	"strings"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/graph"
	"code.alibaba-inc.com/cosy/lingma-agent-manager/executor"
)

const (
	InitCatalogueGenerateNodeName          = "InitCatalogueGenerateNode"
	CatalogueGenerateToolOverLimitNodeName = "CatalogueGenerateToolOverLimitNode"
	CatalogueGenerateFinishNodeName        = "CatalogueGenerateFinishNode"
)

// var catalogueGenerateLlmNode = createLongLLmNode(catalogueGenerateVerifier, catalogueGenerateBuilder, definition.AgentTaskDeepWikiCatalogGenerate)
var catalogueGenerateLlmNode = createLLmNode(definition.AgentTaskDeepWikiCatalogGenerate)

var catalogueGenerateFinishLongNodeNew = createCatalogueGenerateFinishNode(
	CatalogueGenerateFinishNodeName,
	chainsCommon.KeyCatalogueRawOutput,
	"catalogue generate")

func catalogueGenerateGraphBuilder() *graph.Graph {
	graphBuilder := graph.NewGraph()

	graphBuilder.AddNode(initCatalogueGenerateNode)
	graphBuilder.AddNode(catalogueGenerateLlmNode)
	graphBuilder.AddNode(toolNode)
	graphBuilder.AddNode(catalogueGenerateToolOverLimitNodeNew)
	graphBuilder.AddNode(catalogueGenerateFinishLongNodeNew)

	graphBuilder.AddEdge("START", InitCatalogueGenerateNodeName)
	graphBuilder.AddEdge(InitCatalogueGenerateNodeName, LLMNodeName)
	graphBuilder.AddConditionalEdges(LLMNodeName, afterLlmRouter, map[string]string{
		"finishTool":    CatalogueGenerateFinishNodeName,
		"tool":          ToolNodeName,
		"toolOverLimit": CatalogueGenerateToolOverLimitNodeName,
		"finish":        CatalogueGenerateFinishNodeName,
	})
	graphBuilder.AddEdge(ToolNodeName, LLMNodeName)
	graphBuilder.AddEdge(CatalogueGenerateToolOverLimitNodeName, "END")
	graphBuilder.AddEdge(CatalogueGenerateFinishNodeName, "END")
	return graphBuilder
}

func InitCatalogueGenerateExecutorBuilder() (*executor.SingleGraphExecutorBuilder, error) {
	graphConfig := &executor.GraphConfig{
		Name:             "catalogue_generate",
		GraphBuilder:     catalogueGenerateGraphBuilder(),
		ProcessorBuilder: &ProcessorBuilder{},
	}
	executorBuilder := executor.NewSingleGraphExecutorBuilder(graphConfig)
	return executorBuilder, nil
}

// InitCatalogueAgentContext 初始化agent的context，放入llm配置、llm client、tool等数据
func InitCatalogueAgentContext(ctx context.Context) (context.Context, *wikiCommon.AgentContext, error) {
	//Initialize working directory to project root
	workspace, ok := ctx.Value(definition.ContextKeyWorkspace).(definition.WorkspaceInfo)
	if !ok {
		return nil, nil, fmt.Errorf("workspace info not found in context")
	}
	projectPath, _ := workspace.GetWorkspaceFolder()
	fileIndexer, _ := ctx.Value(definition.ContextKeyFileIndexer).(*indexing.ProjectFileIndex)

	preferredLanguage, ok := ctx.Value(chainsCommon.KeyPreferredLanguage).(string)
	if !ok || preferredLanguage == "" {
		preferredLanguage = definition.LocaleZh
	}

	availableTools := ListAgentAvailableTools(projectPath, preferredLanguage, fileIndexer)

	agentContext := wikiCommon.AgentContext{
		Tools: availableTools,
	}
	ctx = context.WithValue(ctx, chainsCommon.KeyDeepwikiAgentContext, &agentContext)
	return ctx, &agentContext, nil
}

func catalogueGenerateVerifier(message *agentDefinition.Message) bool {
	if strings.Contains(message.Content, "<documentation_structure>") && strings.Contains(message.Content, "</documentation_structure>") {
		return true
	} else if strings.Contains(message.Content, "<documentation_structure>") && !strings.Contains(message.Content, "</documentation_structure>") {
		return false
	}
	return true
}

func catalogueGenerateBuilder(agentState *wikiCommon.DeepWikiGenerateState) {
	messages := agentState.ShortTermMemory.Messages()
	agentState.ShortTermMemory.Clear()
	lastMessage := messages[len(messages)-1]
	if lastMessage.Role == agentDefinition.RoleTypeAssistant {
		content := lastMessage.Content
		// 移除最后一行，避免最后一行是不完整的
		content = strings.TrimSuffix(content, "\n")
		lines := strings.Split(content, "\n")
		if len(lines) > 0 {
			lastLine := lines[len(lines)-1]
			if content != "{" && lastLine != "}," {
				content = strings.Join(lines[:len(lines)-1], "\n")
			}
		}
		content += "\n"
		lastMessage.Content = content
	}
	agentState.ShortTermMemory.AppendMessages(messages...)
	userPrompt := `Continue to complete the task. Start directly from the unfinished position, not from scratch, and no additional output.
**NOTE:**
You must start with unfinished characters, not the <documentation_structure> label or root { character.`
	userMessage := &agentDefinition.Message{Role: agentDefinition.RoleTypeUser, Content: userPrompt}
	agentState.ShortTermMemory.AddMessage(userMessage)
}

// createFinishNode 创建专门的finish node，用于存储特定agent的输出结果
func createCatalogueGenerateFinishNode(nodeName string, outputKey string, logPrefix string) graph.Node {
	return graph.NewNode(
		nodeName,
		graph.NewFunctionNodeRunnable(func(ctx context.Context, input graph.State) (graph.State, error) {
			agentState := input.(*wikiCommon.DeepWikiGenerateState)
			messages := agentState.ShortTermMemory.Messages()
			if messages == nil || len(messages) <= 0 {
				log.Errorf("%s generate error, response is empty.", logPrefix)
				return agentState, nil
			}
			outputStringBuilder := strings.Builder{}
			validStart := false
			for _, message := range messages {
				if message.Role == agentDefinition.RoleTypeAssistant && message.Content != "" {
					if !validStart {
						if strings.Contains(message.Content, "<documentation_structure>") {
							validStart = true
						}
					}
					if validStart {
						outputStringBuilder.WriteString(message.Content)
					}
				}
			}
			outputContent := outputStringBuilder.String()
			agentState.SetInput(outputKey, outputContent)

			// 确保agentContext中的State是最新的
			if agentContext, ok := ctx.Value(chainsCommon.KeyDeepwikiAgentContext).(*wikiCommon.AgentContext); ok {
				agentContext.State = agentState
				log.Infof("[%s] Updated agentContext.State with latest agentState", logPrefix)
			}

			log.Infof("%s generate completed, content length: %d", logPrefix, len(outputContent))

			return agentState, nil
		}))
}
