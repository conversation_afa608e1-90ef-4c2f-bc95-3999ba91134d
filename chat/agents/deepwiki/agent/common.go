package agent

import (
	"cosy/chat/agents/tool/codebase"
	toolCommon "cosy/chat/agents/tool/common"
	"cosy/chat/agents/tool/wiki"
	"cosy/components"
	"cosy/indexing"
	"cosy/indexing/chat_indexing/workspace_tree_indexing"
	"cosy/indexing/completion_indexing"
	"cosy/log"

	"code.alibaba-inc.com/cosy/lingma-agent-graph/tool"
	"code.alibaba-inc.com/cosy/lingma-agent-graph/tool/file"
)

const (

	//默认工程目录树节点数限制
	WorkspaceTreeTokenLimit = 15000

	//生成目录树的工程目录树节点数限制
	GenerateCatalogueWorkspaceTreeTokenLimit = 50000

	// LLM上下文token限制
	LlmContextTokenLimit = 150_000
)

const (
	EmptyMessage = "empty message"
)

func ListAgentAvailableTools(projectPath string, preferredLanguage string, fileIndexer *indexing.ProjectFileIndex) []tool.BaseTool {
	toolExplanationDesc := toolCommon.ExplanationDefaultDesc

	embedder := components.NewLingmaEmbedder()
	searchCodebaseTool, err := codebase.NewSearchCodebaseTool(&codebase.SearchCodebaseConfig{
		Embedder:        embedder,
		FileIndexer:     fileIndexer,
		ExplanationDesc: toolExplanationDesc,
	})
	if err != nil {
		log.Warnf("Failed to initialize searchCodebase Tool: %v", err)
	}
	listDirTool, err := wiki.NewListDirTool(&wiki.ListDirConfig{
		WorkspacePath:   projectPath,
		ExplanationDesc: toolExplanationDesc,
	})
	if err != nil {
		log.Warnf("Failed to initialize listDir Tool: %v", err)
	}

	readFileTool, err := codebase.NewReadFileTool(&codebase.ReadFileConfig{
		WorkspacePath:   projectPath,
		MaxLineCount:    800,
		FileIndexer:     fileIndexer,
		ExplanationDesc: toolExplanationDesc,
	})
	if err != nil {
		log.Warnf("Failed to initialize readFile Tool: %v", err)
	}

	grepCodeTool, err := file.NewGrepCodeTool(&file.GrepCodeConfig{
		WorkspacePath:   projectPath,
		MaxResultCount:  25,
		ExplanationDesc: toolExplanationDesc,
	})
	if err != nil {
		log.Warnf("Failed to initialize grepCode Tool: %v", err)
	}

	searchFileTool, err := file.NewSearchFileTool(&file.SearchFileConfig{
		WorkspacePath:   projectPath,
		MaxResultCount:  20,
		ExplanationDesc: toolExplanationDesc,
	})
	if err != nil {
		log.Warnf("Failed to initialize searchFile Tool: %v", err)
	}

	metaFileIndexer, ok := completion_indexing.MetaFileIndexers.GetFileIndexer(projectPath)
	if !ok {
		log.Warnf("Failed to initialize metaFileIndexer")
	}
	workspaceTreeIndexer, ok := workspace_tree_indexing.WorkspaceTreeFileIndexers.GetFileIndexer(projectPath)
	if !ok {
		log.Warnf("Failed to initialize workspaceTreeIndexer")
	}

	recommendFileTool, err := codebase.NewRecommendFileTool(&codebase.RecommendFileConfig{
		WorkspacePath:            projectPath,
		WorkspaceTreeFileIndexer: workspaceTreeIndexer,
		MetaFileIndexer:          metaFileIndexer, // 也设为 nil 避免其他错误
	})
	if err != nil {
		log.Warnf("Failed to initialize recommendFile Tool: %v", err)
	}
	availableTools := []tool.BaseTool{
		searchCodebaseTool,
		listDirTool,
		readFileTool,
		grepCodeTool,
		recommendFileTool,
		searchFileTool,
	}

	return availableTools
}
