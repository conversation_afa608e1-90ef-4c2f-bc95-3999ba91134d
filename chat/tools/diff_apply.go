package tools

import (
	"context"
	"cosy/chat/service"
	"cosy/definition"
	"cosy/experiment"
	"cosy/indexing/chat_indexing/workspace_tree_indexing"
	"cosy/log"
	"cosy/prompt"
	"cosy/remote"
	"cosy/sls"
	"cosy/sse"
	"cosy/stable"
	"cosy/tokenizer"
	"cosy/user"
	"cosy/util"
	ragUtil "cosy/util/rag"
	"cosy/websocket"
	"encoding/json"
	"errors"
	"fmt"
	"math/rand"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/google/uuid"
)

const (
	ExistCodeComment = "... existing code ..."

	RemoveComment = "删除:"
)

var DiffApplyErrorMap sync.Map

/**
 * ctx 用于 websocket 通信的上下文
 * params:
 *  NeedRecord 是否需要埋点
 *  NeedSyncWorkingSpaceFile 是否需要同步快照/工作区
 *  NeedWebSocketMethod 是否需要通过 websocket 返回
 *  FinishMethod NeedWebSocketMethod=true时传入，表示apply结束的回调方法
 *  AnswerMethod NeedWebSocketMethod=true时传入，表示回答的回调方法
 *  NeedSave 应用的代码是否需要直接写入本地
 *  ChatRecordId 本地对话id
 *  Stream 是否流式返回
 *  Modification 解决方案代码
 *  SessionId 会话Id，NeedSyncWorkingSpaceFile=true时传入
 *  WorkingSpaceFile 工作区文件信息，json结构
 *    FileId 文件路径
 *    Language 文件使用的语言
 */
func DiffApply(ctx context.Context, params definition.DiffApplyParams) definition.DiffApplyResult {
	// 填充请求信息
	if params.RequestId == "" {
		params.RequestId = uuid.NewString()
		params.RequestSetId = params.ChatRecordId
	}

	cachedUser := user.GetCachedUserInfo()
	if cachedUser == nil || cachedUser.Uid == "" {
		return definition.DiffApplyResult{
			RequestId: params.RequestId,
			ErrorMsg:  "user not login",
			IsSuccess: false,
		}
	}

	// 未传入工作区文件时，判断是否需要同步工作区文件，需要则补充快照/工作区文件信息，否则创建临时工作区文件用于 apply
	if params.NeedSyncWorkingSpaceFile {
		// 从 Agent 调用时，文件路径已修正，无需重新修正
		/*
			// 修正文件路径
			afterPath, err := CheckPath(params.WorkingSpaceFile.FileId, ctx, params.RequestId, params.SessionId)
			if err != nil {
				return definition.DiffApplyResult{
					RequestId: params.RequestId,
					ErrorMsg:  err.Error(),
					IsSuccess: false,
				}
			}
			params.WorkingSpaceFile.FileId = afterPath
		*/

		errorCode, errorMsg := "", ""
		if params.WorkingSpaceFile.Id == "" || params.WorkingSpaceFile.Status == "" {
			// 创建工作区文件
			currentSnapshot, exists := service.CurrentSnapshotMap[params.SessionId]
			if !exists {
				if snapshots, err := service.WorkingSpaceServiceManager.ListSnapshot(params.SessionId); err == nil && len(snapshots) > 0 {
					currentSnapshot = snapshots[0]
				}
			}
			if currentSnapshot.ChatRecordId != params.ChatRecordId {
				// 如果未创建新的快照
				if service.WorkingSpaceServiceManager != nil {
					service.WorkingSpaceServiceManager.CreateSnapshot(ctx, params.SessionId, params.ChatRecordId, definition.SessionModeAgent)
					// 间隔 100 ms，由于目前 websocket 消息不能保证消息顺序，避免工作区文件消息先于快照信息到达
					time.Sleep(100 * time.Millisecond)
				} else {
					log.Error("WorkingSpaceServiceManager not exist")
				}
			}
			id, err := service.WorkingSpaceServiceManager.CreateWorkingSpaceFile(ctx, params.SessionId, params.WorkingSpaceFile.FileId, params.WorkingSpaceFile.Language, "", service.GENERATING, params.WorkingSpaceFile.Id, definition.SessionModeAgent)
			params.WorkingSpaceFile.Id = id
			if err != nil {
				errorMsg = fmt.Sprintf("Failed to create workingSpaceFile for sessionId: %s, path: %s, err: %s", params.SessionId, params.WorkingSpaceFile.FileId, err.Error())
				log.Error(errorMsg)
				return definition.DiffApplyResult{
					RequestId: params.RequestId,
					ErrorMsg:  errorMsg,
					IsSuccess: false,
				}
			}
			service.WorkingSpaceFileGeneratingContentMap[id] = &definition.GenerateStreamContent{
				FullContent: "",
			}
		} else {
			id := params.WorkingSpaceFile.Id
			service.WorkingSpaceFileGeneratingContentMap[id] = &definition.GenerateStreamContent{
				FullContent: params.Modification,
			}
			errorCode, errorMsg = service.WorkingSpaceServiceManager.OperateWorkingSpaceFile(ctx, definition.WorkingSpaceFileOperateParams{
				Id:     id,
				OpType: service.APPLY.String(),
				Params: map[string]interface{}{
					service.NO_RECORD: !params.NeedRecord,
					"diffApplyParams": params,
				},
			})
		}
		return definition.DiffApplyResult{
			WorkingSpaceFileId: params.WorkingSpaceFile.Id,
			RequestId:          params.RequestId,
			ErrorMsg:           errorMsg,
			IsSuccess:          errorCode == "",
		}
	} else if params.WorkingSpaceFile.Id == "" {
		params.WorkingSpaceFile.Id = uuid.NewString()
		content := ""
		code, err := util.GetFileContent(params.WorkingSpaceFile.FileId)
		if err == nil {
			content = string(code)
		}
		params.OriginalCode = content
		service.WorkingSpaceFileGeneratingContentMap[params.WorkingSpaceFile.Id] = &definition.GenerateStreamContent{
			FullContent: params.Modification,
		}
		service.WorkingSpaceFileApplyingContentMap[params.WorkingSpaceFile.Id] = &definition.ApplyStreamContent{
			OriginalContent: content,
			AppliedContent:  "",
			FullContent:     "",
			LastContent:     "",
			StreamContent:   []string{},
		}
		service.WorkingSpaceFileIngMap.Store(params.WorkingSpaceFile.Id, service.APPLYING.String())
	}

	// 如果原始代码为空，或者解决方案不包含 existing code，认为是修改全文则直接返回
	if strings.TrimSpace(params.OriginalCode) == "" ||
		strings.Index(params.Modification, ExistCodeComment) == -1 ||
		strings.TrimSpace(params.Modification) == "" {
		applyStreamContent := service.WorkingSpaceFileApplyingContentMap[params.WorkingSpaceFile.Id]
		applyStreamContent.FullContent = params.Modification
		diffApplyMsgFinish := definition.DiffApplyMsgFinish{
			RequestId:        params.RequestId,
			SessionId:        params.SessionId,
			WorkingSpaceFile: params.WorkingSpaceFile,
			Reason:           "success",
			StatusCode:       200,
		}
		diffApplyFinish(ctx, diffApplyMsgFinish, params)
		return definition.DiffApplyResult{
			RequestId: params.RequestId,
			ErrorMsg:  "",
			IsSuccess: true,
			IsFinish:  true,
		}
	}

	remoteReq, err, errorCode := buildRemoteDiffApplyQuest(ctx, params)
	if err != nil {
		return definition.DiffApplyResult{
			RequestId: params.RequestId,
			ErrorMsg:  err.Error(),
			ErrorCode: errorCode,
			IsSuccess: false,
		}
	}
	params.TaskId = remoteReq.TaskId
	params.RequestId = remoteReq.RequestId

	svcRequest := remote.BigModelSvcRequest{
		ServiceName: definition.AgentChatAskService,
		FetchKey:    "llm_model_result",
		Async:       params.Stream,
		RequestBody: remoteReq,
		RequestID:   remoteReq.RequestId,
		AgentID:     remoteReq.AgentId,
	}
	req, err := remote.BuildBigModelSvcRequestWithConfig(svcRequest, nil, nil)
	if params.NeedRecord {
		go sls.Report(sls.EventTypeChatAgentRequest, remoteReq.RequestId, map[string]string{
			"agent_id":       remoteReq.AgentId,
			"task_id":        remoteReq.TaskId,
			"request_id":     remoteReq.RequestId,
			"request_set_id": params.RequestSetId,
			"chat_record_id": params.ChatRecordId,
		})
	}
	if err != nil {
		var tokenExpiredError *definition.TokenExpiredError
		if errors.As(err, &tokenExpiredError) {
			return definition.DiffApplyResult{
				RequestId: params.RequestId,
				ErrorMsg:  "login token expired",
				IsSuccess: false,
			}
		}
		log.Error("Build new request to diff apply failed")
		return definition.DiffApplyResult{
			RequestId: params.RequestId,
			ErrorMsg:  err.Error(),
			IsSuccess: false,
		}
	}
	if params.Stream {
		stable.GoSafe(ctx, func() {
			_ = doPostDiffApply(ctx, req, params)
		}, stable.SceneEditFile)
	} else {
		//TODO 暂不支持
		return definition.DiffApplyResult{
			RequestId: params.RequestId,
			ErrorMsg:  "none stream is not unsupported",
			IsSuccess: false,
		}
	}
	return definition.DiffApplyResult{
		WorkingSpaceFileId: params.WorkingSpaceFile.Id,
		RequestId:          params.RequestId,
		ErrorMsg:           "",
		IsSuccess:          true,
	}
}

func doPostDiffApply(ctx context.Context, req *http.Request, params definition.DiffApplyParams) error {
	lastDiffApplyData := ""

	sseClient := sse.NewSseChatClient(map[string]string{})

	log.Debugf("Async diff apply, request id: %s", params.RequestId)

	startTime := time.Now()

	err := sseClient.Subscribe(req, 115*time.Second, func(msg *sse.Event) {
		var response definition.ChatResponse
		if string(msg.Event) == "error" {
			log.Warnf("Answer finish error, reason=%s", msg.Data)

			diffApplyMsgFinish := definition.DiffApplyMsgFinish{
				RequestId:        params.RequestId,
				SessionId:        params.SessionId,
				TaskId:           params.TaskId,
				WorkingSpaceFile: params.WorkingSpaceFile,
				Reason:           "SSE Event Error: " + string(msg.Data),
				StatusCode:       500,
			}
			diffApplyFinish(ctx, diffApplyMsgFinish, params)

			return
		}
		if string(msg.Event) == "finish" {

			log.Infof("diff apply finish. requestId=%s cost: %s", params.RequestId, time.Since(startTime))

			diffApplyMsgFinish := definition.DiffApplyMsgFinish{
				RequestId:        params.RequestId,
				SessionId:        params.SessionId,
				TaskId:           params.TaskId,
				WorkingSpaceFile: params.WorkingSpaceFile,
				Reason:           "success",
				StatusCode:       200,
			}
			diffApplyFinish(ctx, diffApplyMsgFinish, params)

			return
		}
		err := json.Unmarshal(msg.Data, &response)
		if err != nil {
			log.Error("Unmarshal sse msg data error: ", err)
			return
		}
		if response.StatusCodeValue != 200 {
			message := response.Body
			diffApplyMsgFinish := definition.DiffApplyMsgFinish{
				RequestId:        params.RequestId,
				SessionId:        params.SessionId,
				TaskId:           params.TaskId,
				WorkingSpaceFile: params.WorkingSpaceFile,
				Reason:           "SSE Status Error: " + message,
				StatusCode:       response.StatusCodeValue,
			}
			diffApplyFinish(ctx, diffApplyMsgFinish, params)
			log.Debug("Answer finished, reason: " + message)
			return
		}
		body, err := definition.NewChatBody(response.Body)
		if err != nil {
			log.Error("Unmarshal commitMsg body error: ", err)
			return
		}
		text := body.GetOutputText()
		appendText := strings.TrimPrefix(text, lastDiffApplyData)
		lastDiffApplyData = text
		diffApplyMsgAnswer := definition.DiffApplyMsgAnswer{
			RequestId:        params.RequestId,
			SessionId:        params.SessionId,
			TaskId:           params.TaskId,
			WorkingSpaceFile: params.WorkingSpaceFile,
			Text:             appendText,
			FullText:         text,
			Timestamp:        time.Now().UnixMicro(),
		}

		diffApplyAnswer(ctx, diffApplyMsgAnswer, params)

	}, func() {
		log.Error("Diff Apply timeout")
		// 超时，结束对话
		diffApplyMsgFinish := definition.DiffApplyMsgFinish{
			RequestId:        params.RequestId,
			SessionId:        params.SessionId,
			TaskId:           params.TaskId,
			WorkingSpaceFile: params.WorkingSpaceFile,
			Reason:           "Timeout Error",
			StatusCode:       408,
		}
		diffApplyFinish(ctx, diffApplyMsgFinish, params)
	})
	if err != nil {
		log.Error("Diff Apply error", err)
		// 超时，结束对话
		diffApplyMsgFinish := definition.DiffApplyMsgFinish{
			RequestId:        params.RequestId,
			SessionId:        params.SessionId,
			TaskId:           params.TaskId,
			WorkingSpaceFile: params.WorkingSpaceFile,
			Reason:           "SSE Unknown Error: " + err.Error(),
			StatusCode:       500,
		}
		diffApplyFinish(ctx, diffApplyMsgFinish, params)
	}
	return err
}

func buildRemoteDiffApplyQuest(ctx context.Context, params definition.DiffApplyParams) (definition.DiffApplyRequest, error, string) {
	promptInput := prompt.AIDevelopDiffApplyPromptInput{
		BaseInput: prompt.BaseInput{
			RequestId: params.RequestId,
			SessionId: params.SessionId,
		},
		OriginalCode: params.OriginalCode,
		Modification: params.Modification,
	}

	// 统一将 \r\n 转换成 \n，保证 原代码和解决方案的换行一致性，避免因为换行符不同导致的速度问题
	if strings.Count(promptInput.OriginalCode, "\r\n") > 0 && strings.Count(promptInput.OriginalCode, "\r\n") == strings.Count(promptInput.OriginalCode, "\n") {
		promptInput.OriginalCode = strings.ReplaceAll(promptInput.OriginalCode, "\r\n", "\n")
	}
	if strings.Count(promptInput.Modification, "\r\n") > 0 && strings.Count(promptInput.Modification, "\r\n") == strings.Count(promptInput.Modification, "\n") {
		promptInput.Modification = strings.ReplaceAll(promptInput.Modification, "\r\n", "\n")
	}

	taskId := definition.AgentTaskDiffApply
	// 截断长度
	newPromptInput, _, codeTokens := truncatePromptLength(promptInput, taskId)

	defaultMinTokens := 9 * 1000
	//defaultMinTokens := 0
	minTokens := experiment.ConfigService.GetIntConfigValue(definition.ExperimentKeyDiffApplyByChunkMinOriginalCodeTokenLimit, experiment.ConfigScopeClient, defaultMinTokens)
	defaultMaxChunks := 2
	//defaultMaxChunks := 100
	maxChunks := experiment.ConfigService.GetIntConfigValue(definition.ExperimentKeyDiffApplyByChunkMaxModificationChunkLimit, experiment.ConfigScopeClient, defaultMaxChunks)
	rng := rand.New(rand.NewSource(time.Now().UnixNano()))
	randNumber := rng.Intn(100)
	defaultRate := 50
	//defaultRate := 100
	chunkRate := experiment.ConfigService.GetIntConfigValue(definition.ExperimentKeyDiffApplyByChunkRate, experiment.ConfigScopeClient, defaultRate)
	// 源代码超过9k，解决方案片段不超过2段，则使用片段模型
	if params.TaskId == "" && randNumber <= chunkRate && codeTokens > minTokens && strings.Count(promptInput.Modification, ExistCodeComment) <= maxChunks+1 {
		taskId = definition.AgentTaskDiffApplyWithChunk
		// 增加行号
		code := promptInput.OriginalCode
		lines := strings.Split(code, "\n")
		code = ""
		for i, line := range lines {
			code += strconv.Itoa(i) + " " + line + "\n"
		}
		code = code[:len(code)-1]
		promptInput.OriginalCode = code
		newPromptInput, _, codeTokens = truncatePromptLength(promptInput, taskId)
	}
	// 发生了截断，目前就意味着会出错，直接返回失败
	if newPromptInput.OriginalCode != promptInput.OriginalCode || newPromptInput.Modification != promptInput.Modification {
		fileLength := util.GetFileLines(promptInput.OriginalCode)
		extraInfo := map[string]any{
			"fileLength": fileLength,
		}
		go stable.ReportCommonError(ctx, definition.MonitorErrorKeyApplyTooLong, params.RequestId, nil, extraInfo)

		return definition.DiffApplyRequest{}, errors.New("ModelTokenLimit: Unsupported Long File"), definition.ApplyUnSupportedErrorCode
	}

	promptText := ""
	var err error
	if taskId == definition.AgentTaskDiffApply {
		promptText, err = prompt.Engine.RenderAiDevelopDiffApplyPrompt(promptInput)
	} else {
		promptText, err = prompt.Engine.RenderAiDevelopDiffApplyWithChunkPrompt(promptInput)
	}
	if err != nil {
		log.Errorf("build prompt failed, err: %s", err)
		return definition.DiffApplyRequest{}, err, ""
	}
	log.Debugf("build diff apply prompt: %s", promptText)

	r := definition.DiffApplyRequest{
		ChatPrompt: promptText,
		Stream:     params.Stream,
		RequestId:  params.RequestId,
		// TODO: 确认参数设置
		Parameters: map[string]any{
			"top_p":       0.9,
			"temperature": 0.2,
			"seed":        1234,
		},
		AgentId: definition.AgentAIDeveloper,
		TaskId:  taskId,
	}
	return r, nil, ""
}

func truncatePromptLength(input prompt.AIDevelopDiffApplyPromptInput, taskId string) (prompt.AIDevelopDiffApplyPromptInput, int, int) {
	if qwenTokenizer, err := tokenizer.NewQwenTokenizer(false); err == nil {
		defaultMaxTokens := 20*1000 - 200

		key := definition.ExperimentKeyDiffApplyMaxOriginalCodeTokenLimit
		if taskId == definition.AgentTaskDiffApplyWithChunk {
			key = definition.ExperimentKeyDiffApplyByChunkMaxOriginalCodeTokenLimit
		}
		maxTokens := experiment.ConfigService.GetIntConfigValue(key, experiment.ConfigScopeClient, defaultMaxTokens)
		modificationLimit := maxTokens / 2

		modification, modificationTokens := ragUtil.GetContentByLimitTokens(input.Modification, qwenTokenizer, modificationLimit)
		originCode, codeTokens := ragUtil.GetContentByLimitTokens(input.OriginalCode, qwenTokenizer, maxTokens-modificationTokens)
		input.Modification = modification
		input.OriginalCode = originCode
		return input, modificationTokens, codeTokens
	}
	return input, -1, -1
}

/**
 * 接收到 DiffApply 的返回信号
 */
func diffApplyAnswer(ctx context.Context, params definition.DiffApplyMsgAnswer, originParams definition.DiffApplyParams) {
	version := "1"
	if params.TaskId == definition.AgentTaskDiffApplyWithChunk {
		version = "0"
	}
	// 如果正在对应的工作区文件处理中
	if _, exists := service.WorkingSpaceFileIngMap.Load(params.WorkingSpaceFile.Id); exists {
		// 更新工作区文件处理中内容表
		applyStreamContent := service.WorkingSpaceFileApplyingContentMap[params.WorkingSpaceFile.Id]
		modificationCode := ""
		if service.WorkingSpaceFileGeneratingContentMap[params.WorkingSpaceFile.Id] != nil {
			modificationCode = service.WorkingSpaceFileGeneratingContentMap[params.WorkingSpaceFile.Id].FullContent
		}
		applyStreamContent.FullContent = params.FullText
		appendText := strings.TrimPrefix(applyStreamContent.FullContent, applyStreamContent.LastContent)
		// 新增了修改片段触发适配
		if version != "0" {
			if appliedContent, _, err, _ := service.GetApplyFullContent(applyStreamContent.OriginalContent, applyStreamContent.FullContent, modificationCode, version, params.RequestId); err == nil {
				applyStreamContent.LastContent = applyStreamContent.FullContent
				oldAppliedContent := applyStreamContent.AppliedContent
				applyStreamContent.AppliedContent = appliedContent
				appendText = strings.TrimPrefix(appliedContent, oldAppliedContent)
				if appendText != "" {
					if originParams.NeedWebSocketMethod {
						websocket.SendRequestWithTimeout(ctx, originParams.AnswerMethod,
							definition.DiffApplyGenerateAnswer{
								RequestId: originParams.RequestId,
								Text:      appendText,
								Timestamp: time.Now().UnixMicro(),
							}, nil, 3*time.Second)
					}
					if originParams.AnswerFunc != nil {
						originParams.AnswerFunc(definition.DiffApplyGenerateAnswer{
							RequestId: originParams.RequestId,
							Text:      appendText,
							Timestamp: time.Now().UnixMicro(),
						})
					}
				}
			}
		} else if strings.Index(appendText, "</修改片段>") > -1 {
			if appliedContent, parts, err, _ := service.GetApplyFullContent(applyStreamContent.OriginalContent, applyStreamContent.FullContent, modificationCode, version, params.RequestId); err == nil {
				applyStreamContent.LastContent = applyStreamContent.FullContent
				applyStreamContent.AppliedContent = appliedContent
				// 找到上次流式返回的代码块，返回后续的代码块
				findLastPart := false
				lastPart := ""
				if len(applyStreamContent.StreamContent) == 0 {
					findLastPart = true
				} else {
					lastPart = applyStreamContent.StreamContent[len(applyStreamContent.StreamContent)-1]
				}
				applyStreamContent.StreamContent = parts
				for _, part := range parts {
					if findLastPart {
						if originParams.NeedWebSocketMethod {
							websocket.SendRequestWithTimeout(ctx, originParams.AnswerMethod,
								definition.DiffApplyGenerateAnswer{
									RequestId: originParams.RequestId,
									Text:      part,
									Timestamp: time.Now().UnixMicro(),
								}, nil, 3*time.Second)
						}
						if originParams.AnswerFunc != nil {
							originParams.AnswerFunc(definition.DiffApplyGenerateAnswer{
								RequestId: originParams.RequestId,
								Text:      part,
								Timestamp: time.Now().UnixMicro(),
							})
						}
					}
					if part == lastPart {
						findLastPart = true
					}
				}
			}
		}
	}
}

/**
 * 接收到 DiffApply 的结束信号
 */
func diffApplyFinish(ctx context.Context, params definition.DiffApplyMsgFinish, originParams definition.DiffApplyParams) {
	version := "1"
	if params.TaskId == definition.AgentTaskDiffApplyWithChunk {
		version = "0"
	}

	if params.StatusCode != 200 && originParams.NeedRecord {
		diffApplyError := fmt.Errorf("diff apply error. code=%d, message: %s", params.StatusCode, params.Reason)
		errorType := params.Reason
		i := strings.Index(errorType, ":")
		if i > -1 {
			errorType = params.Reason[:i]
		}
		extraMap := map[string]any{
			"code":   params.StatusCode,
			"reason": params.Reason,
			"type":   errorType,
		}
		go stable.ReportCommonError(ctx, definition.MonitorErrorKeyDiffApply, params.RequestId, diffApplyError, extraMap)
	}

	// 如果正在对应的工作区文件处理中
	if _, exists := service.WorkingSpaceFileIngMap.Load(params.WorkingSpaceFile.Id); exists {
		op := service.COMPLETE_APPLY
		content := ""
		if params.StatusCode != 200 {
			// 采纳失败，认为是取消
			op = service.CANCEL
			errorNotification := definition.NotificationError{
				Code:    definition.ApplyUnknownErrorCode,
				Message: "System Error, please try later",
			}
			if params.StatusCode == 408 {
				errorNotification.Code = definition.ApplyTimeoutErrorCode
				errorNotification.Message = "Apply timeout, please try later"
			}
			go func() {
				e := websocket.SendRequestWithTimeout(ctx,
					"error/notificationError", errorNotification, nil, 3*time.Second)
				if e != nil {
					log.Error("Send request error/notificationError error:", e)
				}
			}()
		} else {
			applyContent := ""
			if service.WorkingSpaceFileApplyingContentMap[params.WorkingSpaceFile.Id] != nil {
				applyContent = service.WorkingSpaceFileApplyingContentMap[params.WorkingSpaceFile.Id].FullContent
			}
			modification := ""
			if service.WorkingSpaceFileGeneratingContentMap[params.WorkingSpaceFile.Id] != nil {
				modification = service.WorkingSpaceFileGeneratingContentMap[params.WorkingSpaceFile.Id].FullContent
			}
			fullContent, _, err, _ := service.GetApplyFullContent(service.WorkingSpaceFileApplyingContentMap[params.WorkingSpaceFile.Id].OriginalContent,
				applyContent, modification, version, params.RequestId)
			// 考虑 chunk 方案的异常兜底
			if params.TaskId == definition.AgentTaskDiffApplyWithChunk && ((fullContent == "" &&
				service.WorkingSpaceFileApplyingContentMap[params.WorkingSpaceFile.Id].OriginalContent != "") || err != nil) {
				if panicError, ok := DiffApplyErrorMap.Load(params.RequestId); ok || err != nil {
					if ok {
						log.Warn("Diff Apply With Chunk Failed, try full", panicError)
						DiffApplyErrorMap.Delete(params.RequestId)
					} else {
						log.Warn("Diff Apply With Chunk Failed, try full", err)
					}
					// 重试全文方案
					applyStreamContent := service.WorkingSpaceFileApplyingContentMap[params.WorkingSpaceFile.Id]
					applyStreamContent.FullContent = ""
					applyStreamContent.AppliedContent = ""
					applyStreamContent.LastContent = ""
					applyStreamContent.StreamContent = []string{}
					originParams.TaskId = definition.AgentTaskDiffApply
					DiffApply(ctx, originParams)
					return
				}
			}
			if err == nil {
				// 校正修复多余注释
				fullContent = adjustApplyContent(service.WorkingSpaceFileApplyingContentMap[params.WorkingSpaceFile.Id].OriginalContent, modification, fullContent)
				content = checkValidContent(fullContent, service.WorkingSpaceFileApplyingContentMap[params.WorkingSpaceFile.Id].OriginalContent,
					applyContent, modification)
				// 恢复 \n -> \r\n
				if strings.Count(originParams.OriginalCode, "\r\n") > 0 && strings.Count(content, "\r\n") == 0 {
					content = strings.ReplaceAll(content, "\n", "\r\n")
				}
				// 如果之前未流式返回，则一次性返回
				if service.WorkingSpaceFileApplyingContentMap[params.WorkingSpaceFile.Id].AppliedContent == "" && content != "" {
					if originParams.NeedWebSocketMethod {
						websocket.SendRequestWithTimeout(ctx, originParams.AnswerMethod,
							definition.DiffApplyGenerateAnswer{
								RequestId: originParams.RequestId,
								Text:      content,
								Timestamp: time.Now().UnixMicro(),
							}, nil, 3*time.Second)
					}
					if originParams.AnswerFunc != nil {
						originParams.AnswerFunc(definition.DiffApplyGenerateAnswer{
							RequestId: originParams.RequestId,
							Text:      content,
							Timestamp: time.Now().UnixMicro(),
						})
					}
				}
			} else {
				log.Error("getApplyFullContent error: ", err)
				op = service.CANCEL
			}
			log.Infof("Diff Apply Finish, content: %s finalContent: %s", service.WorkingSpaceFileApplyingContentMap[params.WorkingSpaceFile.Id].FullContent, content)
		}
		// 空文件
		if strings.TrimSpace(content) == "" {
			content = service.CONTENT_USE_EMPTY_FILE
		}
		statusCode := 200
		if op != service.COMPLETE_APPLY {
			statusCode = 500
		}
		if originParams.NeedWebSocketMethod {
			websocket.SendRequestWithTimeout(ctx, originParams.FinishMethod,
				definition.DiffApplyGenerateFinish{
					RequestId:  params.RequestId,
					Reason:     "",
					StatusCode: statusCode,
				}, nil, 3*time.Second)
		}
		if content == service.CONTENT_USE_EMPTY_FILE {
			content = ""
		}
		if originParams.FinishFunc != nil {
			diffText, _ := util.GetDiff(originParams.OriginalCode, content)
			originParams.FinishFunc(definition.DiffApplyGenerateFinish{
				RequestId:   params.RequestId,
				Reason:      "",
				StatusCode:  statusCode,
				DiffText:    diffText,
				FullContent: content,
			})
		}
		// 更新状态表
		service.WorkingSpaceFileGeneratingContentMap[params.WorkingSpaceFile.Id] = nil
		delete(service.WorkingSpaceFileGeneratingContentMap, params.WorkingSpaceFile.Id)
		service.WorkingSpaceFileApplyingContentMap[params.WorkingSpaceFile.Id] = nil
		delete(service.WorkingSpaceFileApplyingContentMap, params.WorkingSpaceFile.Id)
		service.WorkingSpaceFileIngMap.Delete(params.WorkingSpaceFile.Id)
		// 判断是否需要写入本地
		if originParams.NeedSave && op == service.COMPLETE_APPLY {
			util.NewFile(params.WorkingSpaceFile.FileId, content)
		}
	}
}

func checkValidContent(afterContent string, originalContent string, applyContent string, modificationCode string) string {
	// 空文本影响较大，需要确认
	if strings.TrimSpace(afterContent) == "" {
		if strings.TrimSpace(modificationCode) == "" {
			return originalContent
		}
	}
	return afterContent
}

/**
 * 移除 RemoveComment
 */
func removeCommentLineBlock(content string) string {
	if strings.Index(content, RemoveComment) == -1 {
		return content
	}
	lines := strings.Split(content, "\n")
	result := ""
	// 简单兼容处理 // 删除: /* */的场景
	specialRemove := false
	// 过滤删除语句
	for _, line := range lines {
		stripLine := strings.TrimSpace(line)
		// 如果形如 注释符号 + "删除:" + {{ code }} 则删除
		idx := strings.Index(stripLine, RemoveComment)
		comment := false
		if idx > -1 {
			if idx-3 >= 0 && stripLine[idx-3:idx] == "// " {
				pureLine := strings.TrimSpace(stripLine[idx+len(RemoveComment):])
				if strings.HasPrefix(pureLine, "/*") {
					specialRemove = true
				} else if strings.HasPrefix(pureLine, "*/") {
					specialRemove = false
				}
				idx -= 3
				if idx == 0 {
					comment = true
				}
			} else if idx-2 >= 0 && stripLine[idx-2:idx] == "# " {
				idx -= 2
				if idx == 0 {
					comment = true
				}
			}
			// 删除场景
			if comment {
				continue
			}
		}
		if specialRemove && strings.HasPrefix(stripLine, "//") {
			pureLine := strings.TrimPrefix(stripLine, "//")
			if strings.HasPrefix(strings.TrimSpace(pureLine), "*/") {
				specialRemove = false
			}
			continue
		}
		specialRemove = false
		result += line + "\n"
	}
	return result
}

/**
 * 修正 applyContent，删除多余的注释
 * 删除 ExistCodeComment 和 RemoveComment
 */
func adjustApplyContent(originalContent string, modificationCode string, targetCode string) string {
	lines := strings.Split(targetCode, "\n")
	targetCode = ""
	for _, line := range lines {
		// 忽略 existing code 注释
		idx := strings.Index(line, ExistCodeComment)
		comment := false
		if idx > -1 {
			if idx-3 >= 0 && line[idx-3:idx] == "// " {
				idx -= 3
				comment = true
			} else if idx-2 >= 0 && line[idx-2:idx] == "# " {
				idx -= 2
				comment = true
			}
			if comment {
				if strings.TrimSpace(line[:idx]) == "" {
					continue
				}
			}
		}
		targetCode += line + "\n"
	}
	targetCode = removeCommentLineBlock(targetCode)
	targetCode = strings.TrimSuffix(targetCode, "\n")
	return targetCode
}

/**
 * 检查并修正路径
 * 返回值: 修正后的路径，路径是否异常
 */
func CheckPath(path string, ctx context.Context, requestId string, sessionId string) (string, error) {
	// 填充项目路径
	workspaceInfo, _ := ctx.Value(definition.ContextKeyWorkspace).(definition.WorkspaceInfo)
	projectUris := []string{}
	for _, folder := range workspaceInfo.WorkspaceFolders {
		projectUris = append(projectUris, folder.URI)
	}
	originalPath := path
	afterPath := fixPath(path, projectUris, []string{}, requestId, sessionId)
	// 如果路径无法创建，则返回异常
	canNewFile := util.CanNewFile(afterPath)
	if !canNewFile || afterPath != originalPath {
		// 如果路径发生了修正，说明模型生成路径的指令遵循效果不好，埋点记录
		eventData := map[string]string{
			"session_id":    sessionId,
			"request_id":    requestId,
			"original_path": originalPath,
			"path":          path,
			"canNewFile":    strconv.FormatBool(canNewFile),
			"chat_mode":     definition.SessionModeAgent,
		}
		go sls.Report(sls.EventTypeChatAiDeveloperFixPath, requestId, eventData)
		if !canNewFile {
			return afterPath, errors.New("can not create path")
		}
	}
	return afterPath, nil
}

/**
 * 修正路径，保障路径为当前工程下的正确路径
 */
func fixPath(path string, projectUris []string, contextFilePaths []string, requestId string, sessionId string) string {
	log.Debugf("try to fix path: %v", path)
	path = strings.TrimSpace(path)

	// 处理 {{ path }} 的情况
	if strings.HasPrefix(path, "{{") {
		path = strings.TrimPrefix(path, "{{")
	}
	if strings.HasSuffix(path, "}}") {
		path = strings.TrimSuffix(path, "}}")
	}
	path = strings.TrimSpace(path)

	// 处理 :path 的情况
	if strings.HasPrefix(path, ":") {
		path = strings.TrimPrefix(path, ":")
	}

	// 移除示例根目录
	if strings.HasPrefix(path, "/Users/<USER>/workspace/xxx/") {
		path = strings.TrimPrefix(path, "/Users/<USER>/workspace/xxx/")
	} else if strings.HasPrefix(path, "/Users/<USER>/workspace/") {
		path = strings.TrimPrefix(path, "/Users/<USER>/workspace/")
	} else if strings.HasPrefix(path, "/path/to/your/project/") {
		path = strings.TrimPrefix(path, "/path/to/your/project/")
	} else if strings.HasPrefix(path, "/path/to/your/workspace/") {
		path = strings.TrimPrefix(path, "/path/to/your/workspace/")
	}

	// 修复 filex name="xxx" 的场景
	idx := strings.Index(path, " name=\"")
	if idx > -1 {
		path = path[idx+len(" name=\""):]
		path = strings.TrimSuffix(path, "\"")
	}

	// 将相对路径填充成绝对路径
	if len(path) > 0 && len(projectUris) > 0 {
		relative := true
		for _, projectUri := range projectUris {
			log.Debugf("try to fixPath by projectUri: %v", projectUri)
			// 处理 windows 下 /{绝对路径} 的问题，将 /{绝对路径} 替换成 {绝对路径}
			if !(strings.HasPrefix(projectUri, string(os.PathSeparator)) || strings.HasPrefix(projectUri, "/")) &&
				(strings.HasPrefix(path, string(os.PathSeparator)) || strings.HasPrefix(path, "/")) &&
				util.IsWindowsPath(projectUri) {
				path = path[1:]
			}
			// 处理漏了 / 的情况
			if strings.HasPrefix("/"+path, projectUri) {
				path = "/" + path
			}
			// windows 系统下/\转换问题
			if projectUri != "" && strings.Count(projectUri, string(os.PathSeparator)) > 0 &&
				strings.Count(path, string(os.PathSeparator)) == 0 {
				path = strings.ReplaceAll(path, "/", string(os.PathSeparator))
			}
			// 处理 windows 下大小写不匹配的问题
			idx = strings.Index(path, ":")
			if idx >= 0 && idx <= 5 {
				idx2 := strings.Index(projectUri, ":")
				if idx2 >= 0 && strings.ToUpper(path[:idx]) == strings.ToUpper(projectUri[:idx2]) {
					path = projectUri[:idx2] + path[idx:]
				}
			}
			// 处理 windows 下大小写不匹配且丢失了:的情况
			if strings.Index(projectUri, ":") >= 0 &&
				len(path) > 1 && len(projectUri) > 1 &&
				strings.ToUpper(path[:1]) == strings.ToUpper(projectUri[:1]) {
				_path := strings.Replace(path[1:], ":", "", 1)
				_projectUri := strings.Replace(projectUri[1:], ":", "", 1)
				if strings.HasPrefix(_path, _projectUri) {
					idx = strings.Index(path, _projectUri)
					if idx >= 0 {
						path = projectUri + path[idx+len(_projectUri):]
					}
				}
			}
			log.Debugf("try to judge prefix: path: %v, projectUri: %v", path, projectUri)
			if strings.HasPrefix(path, projectUri) {
				relative = false
				break
			}
		}
		if relative && filepath.IsAbs(path) {
			relative = false
		}
		if relative {
			originFileName := util.GetFileName(path)
			targetProjectUri := projectUris[0]
			for _, projectUri := range projectUris {
				rootPath := filepath.Join(projectUri, path)
				// 先尝试匹配是否与存在的路径匹配
				workspaceTreeIndexer, ok := workspace_tree_indexing.WorkspaceTreeFileIndexers.GetFileIndexer(rootPath)
				if !util.FileExists(rootPath) && originFileName != path && ok &&
					workspaceTreeIndexer != nil && workspaceTreeIndexer.WorkspaceTree != nil {
					pathWithoutName := strings.TrimSuffix(path, originFileName)
					fileList := workspaceTreeIndexer.WorkspaceTree.TreeToList()
					flag := false
					for _, filePath := range fileList {
						fileName := util.GetFileName(filePath)
						if fileName == filePath {
							continue
						}
						filePathWithoutName := strings.TrimSuffix(filePath, fileName)
						if strings.HasSuffix(filePathWithoutName, pathWithoutName) {
							path = filepath.Join(filePathWithoutName, originFileName)
							flag = true
							break
						}
					}
					if flag {
						targetProjectUri = projectUri
						break
					}
				} else if util.FileExists(rootPath) {
					targetProjectUri = projectUri
					break
				}
			}
			// 兜底拼上工程根目录
			path = filepath.Join(targetProjectUri, path)
		}
	}

	// 尝试判断路径是否出现基本的出错
	if !util.FileExists(path) {
		fileName := util.GetFileName(path)
		if len(fileName) > 0 {
			for _, filePath := range contextFilePaths {
				if len(path) == len(filePath) {
					oldFileName := util.GetFileName(filePath)
					if fileName == oldFileName {
						same := true
						for i := len(path) - 1; i >= 0; i-- {
							if path[i] == filePath[i] {
								continue
							}
							if (path[i] == '.' || path[i] == '/' || path[i] == '\\') && (filePath[i] == '.' || filePath[i] == '/' || filePath[i] == '\\') {
								continue
							}
							same = false
							break
						}
						if same {
							path = filePath
							break
						}
					}
				}
			}
		}
	}

	log.Debugf("try to fix path finish: %v", path)

	return path
}
