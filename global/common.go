package global

import (
	"cosy/definition"
	"os"
	"strings"
	"sync" // 引入 sync 包以支持并发安全的 Map
)

// Cosy version number

var CosyVersion = "dev"

var ModelVersion = "0.0.1"

// 推理模式：自动模式/速度优先/长度优先
var LocalInferenceMode = definition.InferenceModeAuto

// IDE客户端信息，改为并发安全的 sync.Map
var IdeInfo sync.Map

// 推理配置，此处为默认配置，如果用户本地有自定义设置，则在初始化的时候(config/local.go)被覆盖
var LocalBeamConfig = definition.BeamConfig{
	BeamSize:       3,
	TopK:           3,
	TokenThreshold: 0.1,
	MaxLength:      10,
}

// 是否是debug模式
var DebugMode = false

// Completion item identifier for cosy
const CosyCompletionItemIdentifier = "✨ "

// IDE平台主要类型
type IdeSeriesType string

const (
	IDE_SERIES_VSCODE    IdeSeriesType = "VSCode"
	IDE_SERIES_JETBRAINS IdeSeriesType = "JetBrains"
)

var BuildOption definition.BuildOption

// IDE平台次要类型
type IdePlatformType string

var DefaultLocalLanguage string

// HttpsProxy 系统变量的proxy
var HttpsProxy string

// SocketPort 监听的本地服务ws端口
var SocketPort int

// HttpPort 测试http服务端口
var HttpPort int

// ProfileWebsocketPort 监听的本地个人设置页服务profile端口
var ProfileWebsocketPort int

// MoreInfo 是否显示更多信息
var MoreInfo bool

var CosyConfig definition.CosyConfig

// ExtensionConfigPullInterval 企业级扩展配置拉取定时任务拉取间隔(单位:分钟)
var ExtensionConfigPullInterval = 5

// CustomizedEndpoint 服务端地址
var CustomizedEndpoint string

// TransportType 进程通信方式
var TransportType string

// WorkDir client工作目录
// 设置后，子进程启动/数据都会会存在该目录下
var WorkDir string

var (
	envValueChatCodeUpdateWiki = os.Getenv("LINGMA_CHAT_CODE_UPDATE_WIKI")
	envValueEvaluationMode     = os.Getenv("LINGMA_CLIENT_EVALUATION_MODE")
	envValueYOLOMode           = os.Getenv("EVALUATION_YOLO_MODE")
	envValueSubAgentMode       = os.Getenv("EVALUATION_SUB_AGENT_MODE")
)

func ConfigBuildOption(options definition.BuildOption) {
	BuildOption = options
	BuildFormType = options.BuildFormType
}

// IsEvaluationMode 是否为评估模式
func IsEvaluationMode() bool {
	if !DebugMode {
		return false
	}
	return envValueEvaluationMode == "true"
}

func IsChatCodeUpdateWikiEnable() bool {
	if !DebugMode {
		return false
	}
	return envValueChatCodeUpdateWiki == "true"
}

func IsYOLOMode() bool {
	if !DebugMode {
		return false
	}
	return envValueYOLOMode == "true"
}

func IsSubAgentMode() bool {
	if !DebugMode {
		return false
	}
	return envValueSubAgentMode == "true"
}

// 判断是否是正式版
// 3位版本号认为是正式版
func IsReleaseVersion() bool {
	version := CosyVersion
	parts := strings.Split(version, ".")
	return len(parts) == 3
}
