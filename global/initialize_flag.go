package global

import (
	"sync"
	"time"
)

type InitializeRecord struct {
	Mutex        sync.Mutex
	IndexerFlags map[string]bool
}

var (
	initializeRecord = &InitializeRecord{
		Mutex:        sync.Mutex{},
		IndexerFlags: make(map[string]bool),
	}
)

func GetIndexerFlag(workspacePath string) bool {
	initializeRecord.Mutex.Lock()
	defer initializeRecord.Mutex.Unlock()
	return initializeRecord.IndexerFlags[workspacePath]
}

func SetIndexerFlag(workspacePath string, flag bool) {
	initializeRecord.Mutex.Lock()
	defer initializeRecord.Mutex.Unlock()
	initializeRecord.IndexerFlags[workspacePath] = flag
}

func WaitForIndexerInit(workspacePath string) bool {
	// 检查indexer，直到indexer已经初始化
	retryCount := 0
	for !GetIndexerFlag(workspacePath) {
		time.Sleep(1 * time.Second)
		retryCount++
		if retryCount > 60 {
			break
		}
	}

	return GetIndexerFlag(workspacePath)
}
