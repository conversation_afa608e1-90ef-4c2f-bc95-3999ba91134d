package storage

import (
	"cosy/util"
	"crypto/sha256"
	"fmt"
	"path/filepath"
)

// GetGraphDbStoragePath
// 获取graphdb文件的存储路径
func GetGraphDbStoragePath(workspaceDir string) string {
	return filepath.Join(getGraphDbStorageUri(workspaceDir), GraphDbName)
}

// GetGraphDbStoragePath
// 获取graphdb文件的存储uri
func getGraphDbStorageUri(workspaceDir string) string {
	sha256 := fmt.Sprintf("%x", sha256.Sum256([]byte(workspaceDir)))
	graphStoreDir := filepath.Join(util.GetCosyHomePath(), "index", "graph", "v1")
	return filepath.Join(graphStoreDir, filepath.Base(workspaceDir)+"_"+sha256)
}
