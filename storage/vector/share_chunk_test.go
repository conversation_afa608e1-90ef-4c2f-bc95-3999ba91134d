package vector

import (
	"context"
	"cosy/client"
	"cosy/components"
	"cosy/config"
	"cosy/definition"
	"fmt"
	"math"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
)

func BuildShareChunks() []definition.ShareChunkEntity {
	entities := make([]definition.ShareChunkEntity, 0)
	for i := 0; i < 100; i++ {
		contentId := definition.GetFileId([]byte(uuid.NewString()))
		chunkId := "98b683549885328adfb83c49eeb2e692"
		repoPath := globalTestWorkspacePath
		entity := definition.ShareChunkEntity{
			ContentChunkId: contentId,
			SqliteChunkId:  chunkId,
			RepoPath:       repoPath,
			HitCnt:         0,
		}
		entities = append(entities, entity)
	}
	return entities
}

func TestInsertShareChunk(t *testing.T) {
	client.InitClients()
	config.InitConfig()
	entities := BuildShareChunks()
	for _, entity := range entities {
		err := InsertShareChunk(entity)
		if err != nil {
			t.Fatalf("Failed to insert share chunk: %v", err.Error())
		}
	}
}

func TestQueryShareChunk(t *testing.T) {
	client.InitClients()
	config.InitConfig()
	contentChunkId := "83f706d4331a3c663b4a3b41aff3c77dfd15a01f128a249051c7b83052111cd4"
	chunk, err := QueryShareChunk(contentChunkId)
	assert.Nil(t, err)
	assert.NotNil(t, chunk)
	fmt.Println(chunk.Embedding)
}

func TestCorrection(t *testing.T) {
	// 1.按照规定数据插入sqlite一条chunk数据
	// 2.插入一条共享chunk数据
	// 3.查出chunk数据，查看回查的embedding数据和插入的embedding数据是否一致
	client.InitClients()
	config.InitConfig()

	vecClient, err := NewSqliteVecClient(globalTestWorkspacePath, globalTestVersion, globalTestMode, -1)
	assert.Nil(t, err)
	assert.NotNil(t, vecClient)

	chunks := make([]definition.StorageChunk, 0)

	embedder := components.NewLingmaEmbedder()
	content := "this is test chunk content"

	embeddings, err := embedder.CreateEmbedding(context.Background(), []string{content}, components.TextTypeDocument)
	assert.Nil(t, err)
	assert.NotNil(t, embeddings)
	assert.Equal(t, len(embeddings), 1)
	sqliteChunkId := "sqlite_chunk_id"
	chunks = append(chunks, definition.StorageChunk{
		ChunkId:     sqliteChunkId,
		Content:     content,
		FileName:    "share_chunk_test.go",
		FilePath:    "./share_chunk_test.go",
		StartLine:   123,
		EndLine:     456,
		StartOffset: 123,
		EndOffset:   456,
		Embedding:   embeddings[0],
	})
	virtualFile := definition.NewVirtualFile("./share_chunk_test.go")

	err = vecClient.BatchInsert(chunks, virtualFile)
	assert.Nil(t, err)

	contentChunkId := definition.GetFileId([]byte(content))
	shareChunkEntity := definition.ShareChunkEntity{
		ContentChunkId: contentChunkId,
		SqliteChunkId:  sqliteChunkId,
		RepoPath:       globalTestWorkspacePath,
		HitCnt:         0,
	}
	err = InsertShareChunk(shareChunkEntity)
	assert.Nil(t, err)

	chunk, err := QueryShareChunk(contentChunkId)
	assert.Nil(t, err)
	queryEmbedding := chunk.Embedding
	embedderEmbedding := embeddings[0]
	assert.Equal(t, len(queryEmbedding), len(embedderEmbedding))
	for i := 0; i < len(queryEmbedding); i++ {
		assert.True(t, math.Abs(float64(queryEmbedding[i]-embedderEmbedding[i])) <= 1e-5)
	}

}
