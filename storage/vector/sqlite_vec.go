package vector

import (
	"bytes"
	"context"
	"cosy/components"
	"cosy/deepwiki/storage"
	"cosy/definition"
	"cosy/log"
	"cosy/sls"
	"cosy/user"
	"cosy/util"
	"cosy/util/rag"
	"crypto/md5"
	"database/sql"
	"encoding/binary"
	"errors"
	"fmt"
	"math"
	"os"
	"path/filepath"
	"runtime/debug"
	"sort"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/google/uuid"

	sqlitevec "github.com/asg017/sqlite-vec-go-bindings/cgo"
	"github.com/mattn/go-sqlite3"
)

var SqliteClientMap *sync.Map
var createMutex sync.Mutex
var DatabaseRecoveringError error

const (
	// RecoverErrorFileIsNotADatabase
	// 删库重建，一般是触发了panic才会用这个来构造error
	RecoverErrorFileIsNotADatabase = "file is not a database"

	PanicMsgSep = "@~@~@"
)

func GetSqliteClientMapKey(workspacePath string, version string, mode string) string {
	return workspacePath + version + mode
}

func init() {
	if SqliteClientMap == nil {
		SqliteClientMap = &sync.Map{}
	}
	createMutex = sync.Mutex{}
	DatabaseRecoveringError = errors.New("database is recovering")
	sqlitevec.Auto()
}

const (
	ChatDatabaseFileName       = "chat.db"
	CompletionDatabaseFileName = "completion.db"
	WikiDatabaseFileName       = "wiki.db"
	// DatabaseModeChat 问答模式
	DatabaseModeChat = "chat"
	// DatabaseModeCompletion 补全模式
	DatabaseModeCompletion = "completion"
	// DatabaseModeWiki wiki模式
	DatabaseModeWiki = "wiki"

	// EmbeddingDataTableMainName embedding数据表名，表名是动态的，必须包含向量维度，这里是主要的名字
	EmbeddingDataTableMainName = "embedding_table"
	// ChunkDataTableMainName chunk数据表名，表名是动态的，必须包含向量维度，这里是主要的名字
	ChunkDataTableMainName = "chunk_table"
	// ChunkDataTableIndexMainName chunk数据表的file_path索引表名，建索引是为了更快根据file_path检索，表名是动态的，必须包含向量维度，这里是主要的名字
	ChunkDataTableIndexMainName = "chunk_file_path_index"
	// IndexRecordTableMainName 索引记录表名，表名是动态的，必须包含向量维度，这里是主要的名字
	IndexRecordTableMainName = "index_record"

	AutoDeleteFileNumber   = 50  // 触发自动删除时的文件数量，删除粒度最小为文件，一次删除50个文件
	DefaultMaxRecoverTimes = 20  // 默认的最大恢复次数，超过这个次数后删除当前数据库文件并重新创建
	DefaultMaxQueryNumber  = 100 // 默认的最大QueryChunk的数量

	// MaxRecordLastTime 默认的最大记录时间，超过这个时间，则认为当前记录已经失效，需要重新建立索引
	MaxRecordLastTime = 15 * 24 * time.Hour
)

type SqliteVecClient struct {
	WorkspacePath             string       // WorkspacePath
	DatabasePath              string       // 数据库路径
	Conn                      *sql.DB      // 数据库连接池
	Dim                       int          // embedding向量维度
	Version                   string       // 版本（专有云/公有云）
	Mode                      string       // 问答/补全
	RecoverMtx                sync.RWMutex // 数据库恢复锁
	Recovering                atomic.Bool  // 是否正在数据库恢复中
	RecoverTimes              atomic.Int32 // 记录恢复次数
	Closed                    atomic.Bool  // 是否已经关闭
	FlushEmptyRecordTimeStamp time.Time    // 上次全量清理空记录的时间戳
}

// SqliteVecEngine 接口，用来和外界交互的全部函数
// 向量引擎接收到数据库层面的error时，需要等待数据库恢复完毕后，再次执行
type SqliteVecEngine interface {
	// Query 根据嵌入向量查询代码块
	Query(query definition.QueryCondition) (definition.SqliteVecRetrieveResult, error)

	// BatchInsert 批量插入代码块
	// 以文件为粒度，chunks必须是同一个文件的
	BatchInsert(chunks []definition.StorageChunk, file definition.VirtualFile) error

	// BatchUpdate 批量更新代码块
	// 以文件为粒度，chunks必须是同一个文件的
	BatchUpdate(chunks []definition.StorageChunk, file definition.VirtualFile) error

	// BatchDelete 批量删除代码块
	// 传入的文件路径用来模糊匹配
	// 端侧删除目录时，只能传入删除的目录名，而不是具体的文件
	// 端侧删除具体文件时，传入的是具体文件名，模糊匹配也可以查到
	BatchDelete(filePathPatterns []string) error

	// Close 关闭数据库连接，并从SqliteClientMap中删除缓存
	// 当用户关闭Ide时，可以调用close来关闭数据库连接，目前没有调用
	// 在评测时，需要减少资源消耗，也可以考虑调用这个
	Close() error

	// GetStorageChunkNum 获取当前库中的代码块数量
	GetStorageChunkNum() int

	// GetStorageFileChunks 获取当前库中某个文件的全部代码块
	GetStorageFileChunks(filePath string) ([]definition.StorageChunk, error)

	// GetStorageFileNum 根据当前库中已建立索引的文件数量
	GetStorageFileNum() int

	// CheckFileChanged 检查某个文件是否已建立索引，或是索引是否已变更
	CheckFileChanged(filePath string, fileContent string) (bool, bool)

	// AutoDelete 自动删除工作
	// 淘汰策略为完全随机
	AutoDelete() error

	// available
	// 返回当前数据库是否可用
	available() bool

	// recoverDatabase 数据库修复工作
	recoverDatabase(failedError error)

	// checkConnection 检查数据库连接是否正常
	checkConnection() error

	// QueryEmbedding 用于共享chunk获取当前存储的embedding数据
	QueryEmbedding(chunkId string) (*definition.StorageChunk, error)

	// QueryAllStorageRecords 用于获取全部索引记录
	QueryAllStorageRecords() ([]definition.VectorIndexRecord, error)
}

func GetSqliteVecDbPath(workspacePath string, version string, mode string) string {
	basePath := filepath.Join(util.GetCosyHomePath(), "index", "vector")

	// 确保索引目录存在
	err := os.MkdirAll(basePath, 0755)
	if err != nil {
		log.Errorf("[codebase]-[storage] failed to create index directory: %s, error: %v", basePath, err)
	}

	// 这部分应该是端侧的工作
	//// 使用 filepath.ToSlash 将路径统一转换为斜杠格式
	//// 避免 Windows 下反斜杠的问题
	//workspacePath = filepath.ToSlash(workspacePath)
	//
	//// 去除末尾的斜杠（如果有）
	//workspacePath = strings.TrimRight(workspacePath, "/")

	workspaceId := definition.GetWorkspaceId(workspacePath)
	repoName := filepath.Base(workspacePath)
	databaseDir := strings.Join([]string{repoName, workspaceId}, "_")

	// 如果是wiki模式，在databaseDir后面添加_wiki后缀
	if mode == DatabaseModeWiki {
		databaseDir = databaseDir + "_wiki"
	}

	// 确保数据库目录存在
	dbDir := filepath.Join(basePath, version, databaseDir)
	err = os.MkdirAll(dbDir, 0755)
	if err != nil {
		log.Errorf("[codebase]-[storage] failed to create database directory: %s, error: %v", dbDir, err)
	}

	// 默认的database是问答数据库名
	databaseName := ChatDatabaseFileName
	if mode == DatabaseModeCompletion {
		// 目前不存在补全的数据库使用，未来有可能使用
		databaseName = CompletionDatabaseFileName
	} else if mode == DatabaseModeWiki {
		databaseName = WikiDatabaseFileName
	}
	dbPath := filepath.Join(basePath, version, databaseDir, databaseName)
	log.Debugf("[codebase]-[storage] database path: %s", dbPath)
	return dbPath
}

// GetEmbeddingDim 返回-1代表失败
// 用来动态获取当前embedding的维度，数据库建表时需要写死embedding维度
func GetEmbeddingDim() int {
	embedder := components.NewLingmaEmbedder()
	embedding, err := embedder.CreateEmbedding(context.Background(), []string{"test"}, components.TextTypeQuery)

	if err != nil {
		log.Errorf("[codebase]-[storage] calculate embedding dim error: %v", err)
		return -1
	}

	if len(embedding) != 1 {
		log.Errorf("[codebase]-[storage] the obtained embedding array size is 0")
		return -1
	}
	log.Debugf("[codebase]-[storage] the obtained embedding dimension is %d", len(embedding[0]))

	if len(embedding[0]) <= 0 {
		log.Errorf("[codebase]-[storage] the obtained embedding dimension is %d", len(embedding[0]))
		return -1
	}

	return len(embedding[0])
}

func NewSqliteVecClient(workspacePath string, version string, mode string, dim int) (*SqliteVecClient, error) {
	if SqliteClientMap == nil {
		SqliteClientMap = &sync.Map{}
	}

	// 检查工作区路径
	if workspacePath == "" {
		return nil, errors.New("[codebase]-[storage] workspace path cannot be empty")
	}

	key := GetSqliteClientMapKey(workspacePath, version, mode)

	if c, ok := SqliteClientMap.Load(key); ok && c != nil {
		//log.Debugf("[codebase]-[storage] load sqlite client from cache")
		return c.(*SqliteVecClient), nil
	}

	// 加锁，确保单例创建
	createMutex.Lock()
	defer createMutex.Unlock()

	// double check
	if c, ok := SqliteClientMap.Load(key); ok && c != nil {
		//log.Debugf("[codebase]-[storage] load sqlite client from cache")
		return c.(*SqliteVecClient), nil
	}

	databasePath := GetSqliteVecDbPath(workspacePath, version, mode)

	// 传递过来为-1时，获取embedding维度
	if dim == -1 {
		dim = GetEmbeddingDim()
	}
	if dim == -1 {
		return nil, errors.New("[codebase]-[storage] get embedding dim error")
	}

	newClient := &SqliteVecClient{
		WorkspacePath: workspacePath,
		DatabasePath:  databasePath,
		Conn:          nil,
		Dim:           dim,
		Version:       version,
		Mode:          mode,
		RecoverMtx:    sync.RWMutex{},
		Recovering:    atomic.Bool{},
		RecoverTimes:  atomic.Int32{},
	}
	newClient.Recovering.Store(false)
	newClient.RecoverTimes.Store(0)
	newClient.FlushEmptyRecordTimeStamp = time.Date(2006, 06, 01, 0, 0, 0, 0, time.UTC)
	// 创建数据库文件
	err := newClient.createDatabase(false)
	if err != nil {
		return nil, err
	}

	SqliteClientMap.Store(key, newClient)

	// 定时减少恢复次数
	go func() {
		for {
			time.Sleep(time.Hour * 6)
			if newClient.RecoverTimes.Add(-1) < 0 {
				newClient.RecoverTimes.Store(0)
			}
		}
	}()
	log.Debugf("[codebase]-[storage] create sqlite client success")
	return newClient, nil
}

// getFullTableName 由于embedding表的维度是动态获取的，索引表名也是动态的
// 传入参数是表名的mainName
// 凡是用到表的地方，都需要调用这个来获取真正的表名
func (c *SqliteVecClient) getFullTableName(tableMainName string) string {
	// c.Dim 能根据向量维度来自动变更表名
	if tableMainName == EmbeddingDataTableMainName {
		return fmt.Sprintf("%s_%d", EmbeddingDataTableMainName, c.Dim)
	} else if tableMainName == ChunkDataTableMainName {
		return fmt.Sprintf("%s_%d", ChunkDataTableMainName, c.Dim)
	} else if tableMainName == IndexRecordTableMainName {
		return fmt.Sprintf("%s_%d", IndexRecordTableMainName, c.Dim)
	} else if tableMainName == ChunkDataTableIndexMainName {
		return fmt.Sprintf("%s_%d", ChunkDataTableIndexMainName, c.Dim)
	}

	// 默认是数据表
	return fmt.Sprintf("%s_%d", EmbeddingDataTableMainName, c.Dim)
}

// checkTableExists
// 检查表是否存在
func (c *SqliteVecClient) checkTableExists(tableMainName string) (bool, error) {
	tableFullName := c.getFullTableName(tableMainName)
	query := fmt.Sprintf("SELECT name FROM sqlite_master WHERE type='table' AND name=?;")
	var name string
	err := c.Conn.QueryRow(query, tableFullName).Scan(&name)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return false, nil // 表不存在
		}
		log.Errorf("[codebase]-[storage] check %s table exists error: %v", tableFullName, err)
		go func() {
			c.recoverDatabase(err)
		}()
		return false, err // 其他错误
	}
	return true, nil // 表存在
}

// ensureDatabaseDirExists 确保数据库文件的父目录存在
func ensureDatabaseDirExists(dbPath string) error {
	dbDir := filepath.Dir(dbPath)
	err := os.MkdirAll(dbDir, 0755)
	if err != nil {
		log.Errorf("[codebase]-[storage] failed to create database directory: %s, error: %v", dbDir, err)
		return err
	}
	return nil
}

// createDatabase 创建数据库文件
func (c *SqliteVecClient) createDatabase(overwrite bool) error {
	// 确保数据库目录存在
	if err := ensureDatabaseDirExists(c.DatabasePath); err != nil {
		return err
	}

	if c.Conn != nil {
		_ = c.Conn.Close()
		c.Conn = nil
	}

	if overwrite {
		dbDir := filepath.Dir(c.DatabasePath)
		if err := os.RemoveAll(dbDir); err != nil {
			log.Errorf("[codebase]-[storage] delete database file error: %v", err)
			return err
		}

		err := os.MkdirAll(dbDir, 0755)
		if err != nil {
			log.Errorf("[codebase]-[storage] create database directory error: %v", err)
			return err
		}
		log.Debugf("[codebase]-[storage] overwrite database file success, database path: %s", c.DatabasePath)
	}

	//// 检查数据库文件是否存在
	//if util.PathExists(c.DatabasePath) && !overwrite {
	//	log.Debugf("[codebase]-[storage] database file already exists: %s", c.DatabasePath)
	//	return nil
	//}

	// 以读/写模式打开数据库文件，如果不存在则创建
	conn, err := sql.Open("sqlite3", c.DatabasePath)
	if err != nil {
		log.Errorf("[codebase]-[storage] create database file error: %v", err)
		return err
	}
	c.Conn = conn

	// 检查是否可以连接到数据库
	err = c.Conn.Ping()
	if err != nil {
		log.Errorf("[codebase]-[storage] ping database error: %v", err)
		return err
	}

	c.Conn.SetMaxOpenConns(3)
	c.Conn.SetMaxIdleConns(3)

	// 启用 WAL 模式
	_, err = c.Conn.Exec("PRAGMA journal_mode=WAL;")
	//_, err = conn.Exec("PRAGMA journal_mode=OFF;")
	//_, err = conn.Exec("PRAGMA journal_mode=MEMORY;")
	if err != nil {
		return err
	}

	//  -- 设置页面缓存大小为 1 MB
	//_, err = db.Exec("PRAGMA cache_size = -10240;")
	_, err = c.Conn.Exec("PRAGMA cache_size = -5120;")
	if err != nil {
		return err
	}

	// 检查连接
	err = c.checkConnection()
	if err != nil {
		log.Errorf("[codebase]-[storage] [recover database] check connection error: %v", err)
		return err
	}

	// 创建数据库表
	err = c.createAllTable()
	if err != nil {
		log.Errorf("[codebase]-[storage] [recover database] create all table error: %v", err)
		return err
	}

	c.Closed.Store(false)
	log.Debugf("[codebase]-[storage] create database file success: %s", c.DatabasePath)
	return nil
}

// flushEmptyRecord
// 索引记录表中可能存在chunk为空的文件，冲刷掉这些无用数据
func (c *SqliteVecClient) flushEmptyIndexRecord() error {
	if available := c.available(); !available {
		log.Errorf("[codebase]-[storage] [flush] is ignored, database is recovering, database path: %s", c.DatabasePath)
		return DatabaseRecoveringError
	}

	defer func() {
		panicMsg := make([]string, 0)
		if r := recover(); r != nil {
			log.Errorf("[codebase]-[storage] [flush] panic, database path: %s, panic: %v", c.DatabasePath, r)
			panicMsg = append(panicMsg, fmt.Sprintf("%s", string(debug.Stack())))
		}

		// 捕获RUnlock的panic
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("[codebase]-[storage] [flush] unlock panic, database path: %s, panic: %v", c.DatabasePath, r)
				panicMsg = append(panicMsg, fmt.Sprintf("%s", string(debug.Stack())))
			}

			if len(panicMsg) > 0 {
				go func() {
					// 存储触发panic，上报记录
					data := make(map[string]string)
					data["full_workspace_path"] = c.WorkspacePath
					data["dim"] = strconv.Itoa(c.Dim)
					data["vector_version"] = definition.DatabaseVersion
					data["panic_msg"] = strings.Join(panicMsg, PanicMsgSep)
					data["function"] = "flushEmptyIndexRecord"
					userType := user.GetUserType()
					data["user_type"] = userType
					sls.Report(sls.EventTypeChatCodebaseVectorStoragePanic, uuid.NewString(), data)
				}()
				go func() {
					c.recoverDatabase(errors.New(RecoverErrorFileIsNotADatabase))
				}()
			}
		}()

		// 释放读锁
		c.RecoverMtx.RUnlock()
	}()

	if exist, err := c.checkTableExists(IndexRecordTableMainName); !exist || err != nil {
		return errors.New("[codebase]-[storage] index record table not exists")
	}

	tableFullName := c.getFullTableName(IndexRecordTableMainName)
	querySql := fmt.Sprintf("SELECT file_path,identifier,record_time FROM %s", tableFullName)
	rows, err := c.Conn.Query(querySql)
	if err != nil {
		return err
	}
	defer func() {
		err = rows.Close()
		if err != nil {
			log.Errorf("[codebase]-[storage] [flush get file num] close error: %v", err)
			go func() {
				c.recoverDatabase(err)
			}()
		}
	}()

	for rows.Next() {
		var filePath string
		var identifier string
		var recordTime int

		err = rows.Scan(
			&filePath,
			&identifier,
			&recordTime,
		)
		if err != nil {
			log.Errorf("[codebase]-[storage] [flush record query] scan error: %v", err)
			return err
		}

		query := definition.QueryCondition{
			ExactFilePath: filePath,
		}
		chunks, err := c.queryChunkTable(query)
		if err != nil {
			log.Errorf("[codebase]-[storage] [flush chunk query] query error: %v", err)
			return err
		}

		if len(chunks) == 0 {
			tx, err := c.Conn.Begin()
			if err != nil {
				log.Errorf("[codebase]-[storage] [flush record delete] begin transaction error: %v", err)
				return err
			}
			err = c.deleteIndexRecord(tx, definition.VectorIndexRecord{
				FilePath: filePath,
			})
			if err != nil {
				log.Errorf("[codebase]-[storage] [flush record delete] delete index record error: %v", err)
				return err
			}
			err = tx.Commit()
			if err != nil {
				log.Errorf("[codebase]-[storage] [flush record delete] commit transaction error: %v", err)
				rollbackErr := tx.Rollback()
				if rollbackErr != nil {
					log.Errorf("[codebase]-[storage] [flush record delete] rollback transaction error: %v", rollbackErr)
				}
				// 执行数据库recover
				go func() {
					c.recoverDatabase(rollbackErr)
				}()
				return err
			}
		}

	}

	return nil
}

// createAllTable
// 这里不能检查Available，因为数据库恢复时会调用这个
func (c *SqliteVecClient) createAllTable() error {
	err := c.createEmbeddingDataTable()
	if err != nil {
		return err
	}

	err = c.createChunkDataTable()
	if err != nil {
		return err
	}

	err = c.createChunkIndexTable()
	if err != nil {
		return err
	}

	err = c.createIndexRecordTable()
	if err != nil {
		return err
	}

	go func() {
		nowTime := time.Now()
		if nowTime.Sub(c.FlushEmptyRecordTimeStamp) >= time.Hour*24 {
			c.FlushEmptyRecordTimeStamp = time.Now()
			err = c.flushEmptyIndexRecord()
			if err != nil {
				log.Errorf("[codebase]-[storage] flush empty record error: %v", err)
				c.recoverDatabase(err)
			}
		}
	}()
	return nil
}

// createChunkDataTable 创建ChunkData表
func (c *SqliteVecClient) createChunkDataTable() error {
	if exist, err := c.checkTableExists(ChunkDataTableMainName); exist {
		// 表已存在
		//log.Debugf("[codebase]-[storage] chunk data table already exists")
		return nil
	} else if err != nil {
		return err
	}

	tableFullName := c.getFullTableName(ChunkDataTableMainName)
	createTableSql := fmt.Sprintf(
		`
		CREATE TABLE IF NOT EXISTS %s (
		    chunk_id TEXT PRIMARY KEY,
			file_path TEXT,
			file_name TEXT,
			start_line INTEGER,
			end_line INTEGER,
			start_offset INTEGER,
			end_offset INTEGER
		);	 
	`, tableFullName)

	_, err := c.Conn.Exec(createTableSql)
	if err != nil {
		log.Errorf("[codebase]-[storage] create chunk data table error: %v", err)
		return err
	}
	return nil
}

func (c *SqliteVecClient) createChunkIndexTable() error {
	if exist, err := c.checkTableExists(ChunkDataTableIndexMainName); exist {
		// 表已存在
		//log.Debugf("[codebase]-[storage] chunk data table already exists")
		return nil
	} else if err != nil {
		return err
	}

	filePathIndexFullName := c.getFullTableName(ChunkDataTableIndexMainName)
	tableFullName := c.getFullTableName(ChunkDataTableMainName)

	createIndexSql := fmt.Sprintf(
		`
		CREATE INDEX IF NOT EXISTS %s ON %s (file_path);
	`, filePathIndexFullName, tableFullName)
	_, err := c.Conn.Exec(createIndexSql)
	if err != nil {
		log.Errorf("[codebase]-[storage] create chunk data index error: %v", err)
		return err
	}
	return nil
}

// createEmbeddingDataTable 创建EmbeddingData表
func (c *SqliteVecClient) createEmbeddingDataTable() error {
	if exist, err := c.checkTableExists(EmbeddingDataTableMainName); exist {
		// 表已存在
		//log.Debugf("[codebase]-[storage] embedding data table already exists")
		return nil
	} else if err != nil {
		return err
	}

	tableFullName := c.getFullTableName(EmbeddingDataTableMainName)
	createTableSql := fmt.Sprintf(
		`
		CREATE VIRTUAL TABLE %s USING vec0(
			chunk_id TEXT PRIMARY KEY,
			embedding float[%d]
		);
	`, tableFullName, c.Dim)

	_, err := c.Conn.Exec(createTableSql)
	if err != nil {
		log.Errorf("[codebase]-[storage] create embedding data table error: %v", err)
		return err
	}
	return nil
}

// createIndexRecordTable 创建IndexRecord表
func (c *SqliteVecClient) createIndexRecordTable() error {
	if exist, err := c.checkTableExists(IndexRecordTableMainName); exist {
		// 表已存在
		//log.Debugf("[codebase]-[storage] index record table already exists")
		return nil
	} else if err != nil {
		return err
	}

	tableFullName := c.getFullTableName(IndexRecordTableMainName)
	createTableSql := fmt.Sprintf(`
		CREATE TABLE IF NOT EXISTS %s (
		    file_path TEXT PRIMARY KEY,
		    identifier TEXT,
		    record_time INTEGER
		);	    
	`, tableFullName)

	_, err := c.Conn.Exec(createTableSql)
	if err != nil {
		log.Errorf("[codebase]-[storage] create index record table error: %v", err)
		return err
	}
	return nil
}

// Query 根据查询条件查询数据
func (c *SqliteVecClient) Query(query definition.QueryCondition) (definition.SqliteVecRetrieveResult, error) {
	if available := c.available(); !available {
		log.Errorf("[codebase]-[storage] [query] is ignored, database is recovering, database path: %s", c.DatabasePath)
		return definition.SqliteVecRetrieveResult{}, DatabaseRecoveringError
	}

	defer func() {
		panicMsg := make([]string, 0)
		if r := recover(); r != nil {
			log.Errorf("[codebase]-[storage] [query] panic, database path: %s, panic: %v", c.DatabasePath, r)
			panicMsg = append(panicMsg, fmt.Sprintf("%s", string(debug.Stack())))
		}

		// 捕获RUnlock的panic
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("[codebase]-[storage] [query] unlock panic, database path: %s, panic: %v", c.DatabasePath, r)
				panicMsg = append(panicMsg, fmt.Sprintf("%s", string(debug.Stack())))
			}

			if len(panicMsg) > 0 {
				go func() {
					// 存储触发panic，上报记录
					data := make(map[string]string)
					data["full_workspace_path"] = c.WorkspacePath
					data["dim"] = strconv.Itoa(c.Dim)
					data["vector_version"] = definition.DatabaseVersion
					data["panic_msg"] = strings.Join(panicMsg, PanicMsgSep)
					data["function"] = "Query"
					userType := user.GetUserType()
					data["user_type"] = userType
					sls.Report(sls.EventTypeChatCodebaseVectorStoragePanic, uuid.NewString(), data)
				}()
				go func() {
					c.recoverDatabase(errors.New(RecoverErrorFileIsNotADatabase))
				}()
			}
		}()

		// 释放读锁
		c.RecoverMtx.RUnlock()
	}()

	if len(query.QueryEmbedding) != c.Dim {
		log.Errorf("[codebase]-[storage] vector dimension mismatch, query dim: %d, database dim: %d, database path: %s", len(query.QueryEmbedding), c.Dim, c.DatabasePath)
		return definition.SqliteVecRetrieveResult{}, errors.New("vector dimension mismatch")
	}

	// 1. 如果存在非embedding条件的逻辑，则需要先查chunk_table，来确定embedding查询的范围
	// 1.1 查询chunk_table，创建临时表
	tempTableName, err := c.queryChunkIntoTempTable(query)
	if err != nil {
		log.Errorf("[codebase]-[storage] [query chunk] failed, database path: %s, err: %v", c.DatabasePath, err)
		// 查询出错，执行recover
		go func() {
			c.recoverDatabase(err)
		}()
		return definition.SqliteVecRetrieveResult{}, err
	}
	if tempTableName != "" {
		defer func() {
			_, err = c.Conn.Exec(fmt.Sprintf("DROP TABLE IF EXISTS %s", tempTableName))
			if err != nil {
				log.Errorf("[codebase]-[storage] [query chunk] drop temp table failed, database path: %s, err: %v", c.DatabasePath, err)
				go func(recoverErr error) {
					c.recoverDatabase(recoverErr)
				}(err)
			}
		}()
	}

	if query.TopK <= 0 || query.TopK > DefaultMaxQueryNumber {
		query.TopK = DefaultMaxQueryNumber
	}

	// 2. 如果仅有embedding条件，则直接查embedding_table
	embeddingRetrieveResult, err := c.queryEmbeddingTable(query, tempTableName)
	if err != nil {
		log.Errorf("[codebase]-[storage] [query embedding] failed, database path: %s, err: %v", c.DatabasePath, err)

		// 查询出错，执行recover
		go func() {
			c.recoverDatabase(err)
		}()
		return definition.SqliteVecRetrieveResult{}, err
	}

	// 3.将得到的embedding查询结果读取文件内容
	// 使用embedding的结果反查chunk_table
	// embedding的结果由于有topK的限制，数量不会超过200，不会超过sqlite的参数限制
	var tmpResult definition.SqliteVecRetrieveResult
	var chunkIds []string
	for _, chunk := range embeddingRetrieveResult.Chunks {
		chunkIds = append(chunkIds, chunk.ChunkId)
	}
	chunks, err := c.queryChunkTableByChunkIds(chunkIds)
	if err != nil {
		log.Errorf("[codebase]-[storage] [query chunk back] failed, database path: %s, err: %v", c.DatabasePath, err)

		// 查询出错，执行recover
		go func() {
			c.recoverDatabase(err)
		}()
		return definition.SqliteVecRetrieveResult{}, err
	}
	// 将embeddingChunk中的embedding数据和得分 赋给反查到的chunks
	for _, embeddingChunk := range embeddingRetrieveResult.Chunks {
		for _, chunk := range chunks {
			if chunk.ChunkId == embeddingChunk.ChunkId {
				//chunk.Embedding = embeddingChunk.Embedding
				tmpResult.Chunks = append(tmpResult.Chunks, definition.SqliteVecRetrieveChunk{
					StorageChunk: chunk,
					Score:        embeddingChunk.Score,
				})
				break
			}
		}
	}

	// 4.按照得分排序，得分从高到低
	sort.Slice(tmpResult.Chunks, func(i, j int) bool {
		return tmpResult.Chunks[i].Score > tmpResult.Chunks[j].Score
	})

	var result definition.SqliteVecRetrieveResult
	invalidFilePaths := make(map[string]bool)

	// 5.以topK数量过滤，同时读取文件内容
	for _, chunk := range tmpResult.Chunks {
		// 按照行号读取文件内容
		chunk.Content, err = readContent(chunk.FilePath, chunk.StartLine, chunk.EndLine, c.Mode)
		if err != nil || chunk.Content == "" {
			// 删除这个文件相关的所有索引
			if chunk.FilePath != "" {
				invalidFilePaths[chunk.FilePath] = true
			}

			if err != nil {
				log.Errorf("[codebase]-[storage] query read file error: %v", err)
			} else {
				log.Errorf("[codebase]-[storage] [content empty] filepath: %s, fetch file content is empty", chunk.FilePath)
			}

			// 读取文件出错误，舍弃这个块
			continue
		}

		if len(result.Chunks) < query.TopK {
			result.Chunks = append(result.Chunks, chunk)
		} else {
			break
		}
	}

	if len(invalidFilePaths) > 0 {
		go func() {
			// 删除无效的Chunk
			var todoDeleteFilePaths []string
			for invalidFilePath := range invalidFilePaths {
				todoDeleteFilePaths = append(todoDeleteFilePaths, invalidFilePath)
			}
			err = c.BatchDelete(todoDeleteFilePaths)

			if err != nil {
				// 删除出错，执行recover
				c.recoverDatabase(err)
			}
		}()
	}

	return result, nil
}

// BatchInsert 以文件为粒度，整体插入数据和索引记录
func (c *SqliteVecClient) BatchInsert(chunks []definition.StorageChunk, file definition.VirtualFile) error {
	if len(chunks) == 0 {
		log.Errorf("[codebase]-[storage] [insert] chunks is empty, database path: %s", c.DatabasePath)
		return errors.New("chunks is empty")
	}

	// 参数校验
	for _, chunk := range chunks {
		if len(chunk.Embedding) != c.Dim {
			log.Errorf("[codebase]-[storage] [insert] embedding is nil or dimension mismatch, database path: %s, chunk embedding dim: %d, expected dim: %d", c.DatabasePath, len(chunk.Embedding), c.Dim)
			return errors.New("embedding dimension mismatch")
		}
	}

	if available := c.available(); !available {
		log.Errorf("[codebase]-[storage] [insert] is ignored, database is recovering, database path: %s", c.DatabasePath)
		return DatabaseRecoveringError
	}

	defer func() {
		panicMsg := make([]string, 0)
		if r := recover(); r != nil {
			log.Errorf("[codebase]-[storage] [insert] panic, database path: %s, panic: %v", c.DatabasePath, r)
			panicMsg = append(panicMsg, fmt.Sprintf("%s", string(debug.Stack())))
		}

		// 捕获RUnlock的panic
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("[codebase]-[storage] [insert] unlock panic, database path: %s, panic: %v", c.DatabasePath, r)
				panicMsg = append(panicMsg, fmt.Sprintf("%s", string(debug.Stack())))
			}

			if len(panicMsg) > 0 {
				go func() {
					// 存储触发panic，上报记录
					data := make(map[string]string)
					data["full_workspace_path"] = c.WorkspacePath
					data["dim"] = strconv.Itoa(c.Dim)
					data["vector_version"] = definition.DatabaseVersion
					data["panic_msg"] = strings.Join(panicMsg, PanicMsgSep)
					data["function"] = "BatchInsert"
					userType := user.GetUserType()
					data["user_type"] = userType
					sls.Report(sls.EventTypeChatCodebaseVectorStoragePanic, uuid.NewString(), data)
				}()
				go func() {
					c.recoverDatabase(errors.New(RecoverErrorFileIsNotADatabase))
				}()
			}
		}()

		// 释放读锁
		c.RecoverMtx.RUnlock()
	}()

	tx, err := c.Conn.Begin()
	if err != nil {
		return err
	}

	for _, chunk := range chunks {
		// 添加Embedding维度拦截，防止数据库数据损坏
		if len(chunk.Embedding) != c.Dim {
			log.Errorf("[codebase]-[storage] [insert] embedding dimension mismatch, database path: %s, chunk embedding dim: %d, expected dim: %d", c.DatabasePath, len(chunk.Embedding), c.Dim)
			// 更新embedding出错，rollback
			rollBackErr := tx.Rollback()
			if rollBackErr != nil {
				log.Errorf("[codebase]-[storage] [insert] rollback failed, database path: %s, err: %v", c.DatabasePath, rollBackErr)
			}
			return errors.New("embedding dimension mismatch")
		}
		// 1. 插入embedding数据
		err = c.insertEmbedding(tx, chunk)
		if err != nil {
			if strings.Contains(err.Error(), "UNIQUE constraint failed") {
				err = c.updateEmbedding(tx, chunk)
				if err != nil {
					log.Errorf("[codebase]-[storage] [insert] update embedding failed, database path: %s, err: %v", c.DatabasePath, err)
					// 更新embedding出错，rollback
					rollBackErr := tx.Rollback()
					if rollBackErr != nil {
						log.Errorf("[codebase]-[storage] [insert] rollback failed, database path: %s, err: %v", c.DatabasePath, rollBackErr)
					}
					// 执行数据库recover
					go func() {
						c.recoverDatabase(err)
					}()
					return err
				}
			} else {
				log.Errorf("[codebase]-[storage] [insert] insert embedding failed, database path: %s, err: %v", c.DatabasePath, err)
				// 插入embedding出错，rollback
				rollBackErr := tx.Rollback()
				if rollBackErr != nil {
					log.Errorf("[codebase]-[storage] [insert] rollback failed, database path: %s, err: %v", c.DatabasePath, rollBackErr)
				}
				// 执行数据库recover
				go func() {
					c.recoverDatabase(err)
				}()
				return err
			}
		}

		// 2. 插入chunk数据
		err = c.insertChunk(tx, chunk)
		if err != nil {
			log.Errorf("[codebase]-[storage] [insert] insert chunk failed, database path: %s, err: %v", c.DatabasePath, err)
			// 插入Chunk出错，rollback
			rollBackErr := tx.Rollback()
			if rollBackErr != nil {
				log.Errorf("[codebase]-[storage] [insert] rollback failed, database path: %s, err: %v", c.DatabasePath, rollBackErr)
			}
			// 执行数据库恢复
			go func() {
				c.recoverDatabase(err)
			}()
			return err
		}
	}

	// 3. 插入索引记录数据
	fileContent, err := file.GetFileContent()
	if err != nil {
		rollBackErr := tx.Rollback()
		if rollBackErr != nil {
			log.Errorf("[codebase]-[storage] [insert] rollback failed, database path: %s, err: %v", c.DatabasePath, rollBackErr)
		}
		return err
	}

	newIdentifier := fmt.Sprintf("%x", md5.Sum(fileContent))
	record := definition.VectorIndexRecord{
		FilePath:   file.GetFilePath(),
		Identifier: newIdentifier,
		RecordTime: time.Now().Unix(),
	}

	err = c.insertIndexRecord(tx, record)
	if err != nil {
		log.Errorf("[codebase]-[storage] [insert] insert index record failed, database path: %s, err: %v", c.DatabasePath, err)
		rollBackErr := tx.Rollback()
		if rollBackErr != nil {
			log.Errorf("[codebase]-[storage] [insert] rollback failed, database path: %s, err: %v", c.DatabasePath, rollBackErr)
		}

		// 插入索引记录出错，执行recover
		go func() {
			c.recoverDatabase(err)
		}()
		return err
	}

	err = tx.Commit()
	if err != nil {
		log.Errorf("[codebase]-[storage] [insert] commit failed, database path: %s, err: %v", c.DatabasePath, err)
		rollBackErr := tx.Rollback()
		if rollBackErr != nil {
			log.Errorf("[codebase]-[storage] [insert] rollback failed, database path: %s, err: %v", c.DatabasePath, rollBackErr)
		}
		// commit出错，执行recover
		go func() {
			c.recoverDatabase(err)
		}()
		return err
	}

	//log.Debugf("[codebase]-[storage] [batch insert] success, database path: %s, file path: %s", c.DatabasePath, file.GetFilePath())
	return nil
}

// BatchUpdate 以文件为粒度，整体更新数据和索引记录
func (c *SqliteVecClient) BatchUpdate(chunks []definition.StorageChunk, file definition.VirtualFile) error {
	err := c.BatchDelete([]string{file.GetFilePath()})
	if err != nil {
		return err
	}

	err = c.BatchInsert(chunks, file)
	if err != nil {
		return err
	}

	return nil
}

// BatchDelete 批量删除数据
// 传入的参数是模糊匹配的文件路径
// 1. 查询当前文件的全部chunk
// 2. 将全部的chunk_id从embedding和chunk 表中删除
// 3. 删除索引记录
func (c *SqliteVecClient) BatchDelete(filePathPatterns []string) error {
	// 参数校验
	if len(filePathPatterns) == 0 {
		return errors.New("filePaths can not be empty")
	}

	if available := c.available(); !available {
		log.Errorf("[codebase]-[storage] [delete] is ignored, database is recovering, database path: %s", c.DatabasePath)
		return DatabaseRecoveringError
	}

	defer func() {
		panicMsg := make([]string, 0)
		if r := recover(); r != nil {
			log.Errorf("[codebase]-[storage] [delete] panic, database path: %s, panic: %v", c.DatabasePath, r)
			panicMsg = append(panicMsg, fmt.Sprintf("%s", string(debug.Stack())))
		}

		// 捕获RUnlock的panic
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("[codebase]-[storage] [delete] unlock panic, database path: %s, panic: %v", c.DatabasePath, r)
				panicMsg = append(panicMsg, fmt.Sprintf("%s", string(debug.Stack())))
			}

			if len(panicMsg) > 0 {
				go func() {
					// 存储触发panic，上报记录
					data := make(map[string]string)
					data["full_workspace_path"] = c.WorkspacePath
					data["dim"] = strconv.Itoa(c.Dim)
					data["vector_version"] = definition.DatabaseVersion
					data["panic_msg"] = strings.Join(panicMsg, PanicMsgSep)
					data["function"] = "BatchDelete"
					userType := user.GetUserType()
					data["user_type"] = userType
					sls.Report(sls.EventTypeChatCodebaseVectorStoragePanic, uuid.NewString(), data)
				}()
				go func() {
					c.recoverDatabase(errors.New(RecoverErrorFileIsNotADatabase))
				}()
			}
		}()

		// 释放读锁
		c.RecoverMtx.RUnlock()
	}()

	for _, filePathPattern := range filePathPatterns {
		if filePathPattern == "" {
			continue
		}

		// 删除某个目录时，端侧只会传过来目录，而不是具体文件
		// 因此这里使用模糊匹配，直接找到这个目录全部切块
		query := definition.QueryCondition{
			FilePathPattern: filePathPattern,
		}
		chunks, err := c.queryChunkTable(query)
		if err != nil {
			log.Errorf("[codebase]-[storage] [delete] query chunk table failed, database path: %s, err: %v", c.DatabasePath, err)
			go func() {
				c.recoverDatabase(err)
			}()
			return err
		}

		tx, err := c.Conn.Begin()
		if err != nil {
			log.Errorf("[codebase]-[storage] [delete] begin tx failed, database path: %s, err: %v", c.DatabasePath, err)
			return err
		}

		// 实际的文件路径，做一下去重
		todoDeleteFilePaths := make(map[string]struct{})

		for _, chunk := range chunks {
			todoDeleteFilePaths[chunk.FilePath] = struct{}{}
			err = c.deleteEmbedding(tx, chunk)
			if err != nil {
				log.Errorf("[codebase]-[storage] [delete] delete embedding failed, database path: %s, err: %v", c.DatabasePath, err)
				rollBackErr := tx.Rollback()
				if rollBackErr != nil {
					log.Errorf("[codebase]-[storage] [delete] embedding rollback failed, database path: %s, err: %v", c.DatabasePath, rollBackErr)
				}
				go func() {
					c.recoverDatabase(err)
				}()
				return err
			}

			err = c.deleteChunk(tx, chunk)
			if err != nil {
				log.Errorf("[codebase]-[storage] [delete] delete chunk failed, database path: %s, err: %v", c.DatabasePath, err)
				rollBackErr := tx.Rollback()
				if rollBackErr != nil {
					log.Errorf("[codebase]-[storage] [delete] chunk rollback failed, database path: %s, err: %v", c.DatabasePath, rollBackErr)
				}
				go func() {
					c.recoverDatabase(err)
				}()
				return err
			}
		}

		// 因为这里的filePath
		for chunkFilePath := range todoDeleteFilePaths {
			record := definition.VectorIndexRecord{
				FilePath: chunkFilePath,
			}
			err = c.deleteIndexRecord(tx, record)
			if err != nil {
				log.Errorf("[codebase]-[storage] [delete] delete index record failed, database path: %s, err: %v", c.DatabasePath, err)
				rollBackErr := tx.Rollback()
				if rollBackErr != nil {
					log.Errorf("[codebase]-[storage] [delete] record rollback failed, database path: %s, err: %v", c.DatabasePath, rollBackErr)
				}
				go func() {
					c.recoverDatabase(err)
				}()
				return err
			}
		}

		err = tx.Commit()
		if err != nil {
			log.Errorf("[codebase]-[storage] [delete] commit failed, database path: %s, err: %v", c.DatabasePath, err)
			rollBackErr := tx.Rollback()
			if rollBackErr != nil {
				log.Errorf("[codebase]-[storage] [insert] rollback failed, database path: %s, err: %v", c.DatabasePath, rollBackErr)
			}
			go func() {
				c.recoverDatabase(err)
			}()
			return err
		}
	}
	log.Debugf("[codebase]-[storage] [batch delete] success, database path: %s, filepath pattern: %v", c.DatabasePath, filePathPatterns)
	return nil
}

func readContent(filePath string, startLine uint32, endLine uint32, mode string) (string, error) {
	if mode == DatabaseModeWiki {
		return readMemoryByLine(filePath, startLine, endLine)
	} else {
		return rag.GetFileContentByLine(filePath, startLine, endLine)
	}
}

// readMemoryByLine
// 给记忆模块用的
func readMemoryByLine(filePath string, startLine uint32, endLine uint32) (string, error) {
	wikiItem, err := storage.GlobalStorageService.GetWikiItemByID(strings.TrimSuffix(filePath, ".md"))
	if err != nil {
		return "", fmt.Errorf("failed to get wiki item by id %s: %v", filePath, err)
	}
	if wikiItem == nil {
		return "", fmt.Errorf("wiki item not found with id %s", filePath)
	}

	// Convert wiki content to lines
	lines := strings.Split(wikiItem.Content, "\n")

	// Validate line numbers
	if int(startLine) >= len(lines) {
		return "", fmt.Errorf("start line %d exceeds total lines %d", startLine, len(lines))
	}
	if int(endLine) >= len(lines) {
		endLine = uint32(len(lines) - 1)
	}

	// Join the requested lines
	return strings.Join(lines[startLine:endLine+1], "\n"), nil
}

// queryChunkTableByChunkIds
// 根据chunkId查询chunk
func (c *SqliteVecClient) queryChunkTableByChunkIds(chunkIds []string) ([]definition.StorageChunk, error) {
	if available := c.available(); !available {
		log.Errorf("[codebase]-[storage] [query chunk] is ignored, database is recovering, database path: %s", c.DatabasePath)
		return nil, DatabaseRecoveringError
	}

	defer func() {
		panicMsg := make([]string, 0)
		if r := recover(); r != nil {
			log.Errorf("[codebase]-[storage] [query chunk] panic, database path: %s, panic: %v", c.DatabasePath, r)
			panicMsg = append(panicMsg, fmt.Sprintf("%s", string(debug.Stack())))
		}

		// 捕获RUnlock的panic
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("[codebase]-[storage] [query chunk] unlock panic, database path: %s, panic: %v", c.DatabasePath, r)
				panicMsg = append(panicMsg, fmt.Sprintf("%s", string(debug.Stack())))
			}

			if len(panicMsg) > 0 {
				go func() {
					// 存储触发panic，上报记录
					data := make(map[string]string)
					data["full_workspace_path"] = c.WorkspacePath
					data["dim"] = strconv.Itoa(c.Dim)
					data["vector_version"] = definition.DatabaseVersion
					data["panic_msg"] = strings.Join(panicMsg, PanicMsgSep)
					data["function"] = "queryChunkTableByChunkIds"
					userType := user.GetUserType()
					data["user_type"] = userType
					sls.Report(sls.EventTypeChatCodebaseVectorStoragePanic, uuid.NewString(), data)
				}()
				go func() {
					c.recoverDatabase(errors.New(RecoverErrorFileIsNotADatabase))
				}()
			}
		}()

		// 释放读锁
		c.RecoverMtx.RUnlock()
	}()

	// 查询sql
	querySqlTmpl := `
		SELECT 
		    chunk_id,
		    file_path,
		    file_name,
		    start_line,
		    end_line,
		    start_offset,
		    end_offset
		FROM %s
		%s
	`

	condition := ""
	if len(chunkIds) > 0 {
		existValidChunkId := false
		chunkIdJoinStr := ""
		for idx, chunkId := range chunkIds {
			if len(chunkId) == 0 {
				continue
			}
			existValidChunkId = true
			if idx != len(chunkIds)-1 {
				chunkIdJoinStr += fmt.Sprintf("'%s',", chunkId)
			} else {
				chunkIdJoinStr += fmt.Sprintf("'%s'", chunkId)
			}
		}
		condition = fmt.Sprintf("WHERE chunk_id in ( %s );", chunkIdJoinStr)
		if !existValidChunkId {
			condition = ";"
		}
	}

	tableFullName := c.getFullTableName(ChunkDataTableMainName)
	querySql := fmt.Sprintf(querySqlTmpl, tableFullName, condition)

	rows, err := c.Conn.Query(querySql)
	if err != nil {
		log.Errorf("[codebase]-[storage] [query chunk] error: %v", err)
		return nil, err
	}
	defer func() {
		err = rows.Close()
		if err != nil {
			log.Errorf("[codebase]-[storage] [query chunk] by ids rows close error: %v", err)
			go func() {
				c.recoverDatabase(err)
			}()
		}
	}()

	var result []definition.StorageChunk
	for rows.Next() {
		var chunkId string
		var filePath string
		var fileName string
		var startLine uint32
		var endLine uint32
		var startOffset uint32
		var endOffset uint32
		err = rows.Scan(
			&chunkId,
			&filePath, &fileName,
			&startLine, &endLine, &startOffset, &endOffset,
		)

		result = append(result, definition.StorageChunk{
			ChunkId:     chunkId,
			FilePath:    filePath,
			FileName:    fileName,
			StartLine:   startLine,
			EndLine:     endLine,
			StartOffset: startOffset,
			EndOffset:   endOffset,
		})
	}

	return result, nil
}

// buildChunkQueryIntoTempTableSql
// 根据精确匹配/模糊匹配构建查询sql
func (c *SqliteVecClient) buildChunkQueryIntoTempTableSql(query definition.QueryCondition, tempTableName string) string {
	// 查询sql
	querySqlTmpl := `
		CREATE TEMPORARY TABLE %s AS
		SELECT 
		    chunk_id
		FROM %s
	`
	if query.ExactFilePath != "" {
		querySqlTmpl += " WHERE file_path = ?;"
	} else if query.FilePathPattern != "" {
		querySqlTmpl += " WHERE file_path LIKE ?;"
	}

	tableFullName := c.getFullTableName(ChunkDataTableMainName)
	return fmt.Sprintf(querySqlTmpl, tempTableName, tableFullName)
}

// queryChunkIntoTempTable 根据查询条件查询chunk数据，将数据存入临时表中
// 必须满足以下之一的query
// 1. ExactFilePath不为空
// 2. LikeFilePath不为空
// 二者都不为空时，使用ExactFilePath精确匹配
// 返回临时表的表名，临时表的删除由调用者负责
func (c *SqliteVecClient) queryChunkIntoTempTable(query definition.QueryCondition) (string, error) {
	if available := c.available(); !available {
		log.Errorf("[codebase]-[storage] [query chunk temp table] is ignored, database is recovering, database path: %s", c.DatabasePath)
		return "", DatabaseRecoveringError
	}

	defer func() {
		panicMsg := make([]string, 0)
		if r := recover(); r != nil {
			log.Errorf("[codebase]-[storage] [query chunk temp table] panic, database path: %s, panic: %v", c.DatabasePath, r)
			panicMsg = append(panicMsg, fmt.Sprintf("%s", string(debug.Stack())))
		}

		// 捕获RUnlock的panic
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("[codebase]-[storage] [query chunk temp table] unlock panic, database path: %s, panic: %v", c.DatabasePath, r)
				panicMsg = append(panicMsg, fmt.Sprintf("%s", string(debug.Stack())))
			}

			if len(panicMsg) > 0 {
				go func() {
					// 存储触发panic，上报记录
					data := make(map[string]string)
					data["full_workspace_path"] = c.WorkspacePath
					data["dim"] = strconv.Itoa(c.Dim)
					data["vector_version"] = definition.DatabaseVersion
					data["panic_msg"] = strings.Join(panicMsg, PanicMsgSep)
					data["function"] = "queryChunkIntoTempTable"
					userType := user.GetUserType()
					data["user_type"] = userType
					sls.Report(sls.EventTypeChatCodebaseVectorStoragePanic, uuid.NewString(), data)
				}()
				go func() {
					c.recoverDatabase(errors.New(RecoverErrorFileIsNotADatabase))
				}()
			}
		}()

		// 释放读锁
		c.RecoverMtx.RUnlock()
	}()

	// 有效条件判定
	var param string
	if query.ExactFilePath != "" {
		param = strings.Trim(query.ExactFilePath, " ")
	} else if query.FilePathPattern != "" {
		param = "%" + strings.Trim(query.FilePathPattern, " ") + "%"
	} else {
		return "", nil
	}
	tempTableName := fmt.Sprintf("%s_%010d", "temp_query_", time.Now().UnixNano()%1e10)
	querySql := c.buildChunkQueryIntoTempTableSql(query, tempTableName)
	_, err := c.Conn.Exec(querySql, param)
	if err != nil {
		log.Errorf("[codebase]-[storage] [query chunk temp table] error: %v", err)
		return "", err
	}
	return tempTableName, nil
}

// buildChunkQuerySql
// 根据精确匹配/模糊匹配构建查询sql
func (c *SqliteVecClient) buildChunkQuerySql(query definition.QueryCondition) string {
	// 查询sql
	querySqlTmpl := `
		SELECT
			chunk_id,
			file_path,
			file_name,
			start_line,
			end_line,
			start_offset,
			end_offset
		FROM %s
	`
	if query.ExactFilePath != "" {
		querySqlTmpl += " WHERE file_path = ?;"
	} else if query.FilePathPattern != "" {
		querySqlTmpl += " WHERE file_path LIKE ?;"
	}

	tableFullName := c.getFullTableName(ChunkDataTableMainName)
	return fmt.Sprintf(querySqlTmpl, tableFullName)
}

// queryChunkTable 根据查询条件查询chunk数据，将数据存入临时表中
// 必须满足以下之一的query
// 1. ExactFilePath不为空
// 2. LikeFilePath不为空
// 二者都不为空时，使用ExactFilePath精确匹配
// 返回临时表的表名，临时表的删除由调用者负责
func (c *SqliteVecClient) queryChunkTable(query definition.QueryCondition) ([]definition.StorageChunk, error) {
	if available := c.available(); !available {
		log.Errorf("[codebase]-[storage] [query chunk] is ignored, database is recovering, database path: %s", c.DatabasePath)
		return nil, DatabaseRecoveringError
	}

	defer func() {
		panicMsg := make([]string, 0)
		if r := recover(); r != nil {
			log.Errorf("[codebase]-[storage] [query chunk] panic, database path: %s, panic: %v", c.DatabasePath, r)
			panicMsg = append(panicMsg, fmt.Sprintf("%s", string(debug.Stack())))
		}

		// 捕获RUnlock的panic
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("[codebase]-[storage] [query chunk] unlock panic, database path: %s, panic: %v", c.DatabasePath, r)
				panicMsg = append(panicMsg, fmt.Sprintf("%s", string(debug.Stack())))
			}

			if len(panicMsg) > 0 {
				go func() {
					// 存储触发panic，上报记录
					data := make(map[string]string)
					data["full_workspace_path"] = c.WorkspacePath
					data["dim"] = strconv.Itoa(c.Dim)
					data["vector_version"] = definition.DatabaseVersion
					data["panic_msg"] = strings.Join(panicMsg, PanicMsgSep)
					data["function"] = "queryChunkTable"
					userType := user.GetUserType()
					data["user_type"] = userType
					sls.Report(sls.EventTypeChatCodebaseVectorStoragePanic, uuid.NewString(), data)
				}()
				go func() {
					c.recoverDatabase(errors.New(RecoverErrorFileIsNotADatabase))
				}()
			}
		}()

		// 释放读锁
		c.RecoverMtx.RUnlock()
	}()

	// 有效条件判定
	var param string
	if query.ExactFilePath != "" {
		param = strings.Trim(query.ExactFilePath, " ")
	} else if query.FilePathPattern != "" {
		param = "%" + strings.Trim(query.FilePathPattern, " ") + "%"
	} else {
		return nil, nil
	}
	querySql := c.buildChunkQuerySql(query)
	rows, err := c.Conn.Query(querySql, param)
	if err != nil {
		log.Errorf("[codebase]-[storage] [query chunk] error: %v", err)
		return nil, err
	}
	defer func() {
		err = rows.Close()
		if err != nil {
			log.Errorf("[codebase]-[storage] [query chunk] rows close error: %v", err)
			go func() {
				c.recoverDatabase(err)
			}()
		}
	}()

	var result []definition.StorageChunk
	for rows.Next() {
		var chunkId string
		var filePath string
		var fileName string
		var startLine uint32
		var endLine uint32
		var startOffset uint32
		var endOffset uint32
		err = rows.Scan(
			&chunkId,
			&filePath, &fileName,
			&startLine, &endLine, &startOffset, &endOffset,
		)

		result = append(result, definition.StorageChunk{
			ChunkId:     chunkId,
			FilePath:    filePath,
			FileName:    fileName,
			StartLine:   startLine,
			EndLine:     endLine,
			StartOffset: startOffset,
			EndOffset:   endOffset,
		})
	}

	return result, nil
}

// buildEmbeddingQuerySql
// 构建查询embedding库的sql
func (c *SqliteVecClient) buildEmbeddingQuerySql(query definition.QueryCondition, tempTableName string) string {
	// 查询sql
	querySqlTmpl := `
		SELECT 
		    chunk_id,
		    embedding,
		    distance
		FROM %s
		WHERE embedding MATCH ?
		%s
		AND k = %d
		ORDER BY distance;
	`

	condition := ""
	if tempTableName != "" {
		condition = fmt.Sprintf("AND chunk_id in (SELECT chunk_id FROM %s)", tempTableName)
	}

	topK := query.TopK
	if topK <= 0 || topK > DefaultMaxQueryNumber {
		topK = DefaultMaxQueryNumber
	}
	// 这里参数设置成topK * 2原因是，不让数据库返回过多
	// 由于返回的chunk是本地读取文件，有可能有些chunk已经无效
	// 过滤掉无效chunk有一些损失，因此需要让数据库返回的多些，在程序内部控制至多返回topK个即可
	topK = topK * 2

	tableFullName := c.getFullTableName(EmbeddingDataTableMainName)
	return fmt.Sprintf(querySqlTmpl, tableFullName, condition, topK)
}

// queryEmbeddingTable 根据查询条件查询embedding数据
// 这里的QueryEmbedding不能为空，否则返回空
// 这里返回的chunk仅有chunkId有效，返回后需要反查chunkId对应的chunk信息
func (c *SqliteVecClient) queryEmbeddingTable(query definition.QueryCondition, tempTableName string) (definition.SqliteVecRetrieveResult, error) {
	if available := c.available(); !available {
		log.Errorf("[codebase]-[storage] [query embedding] is ignored, database is recovering, database path: %s", c.DatabasePath)
		return definition.SqliteVecRetrieveResult{}, DatabaseRecoveringError
	}

	defer func() {
		panicMsg := make([]string, 0)
		if r := recover(); r != nil {
			log.Errorf("[codebase]-[storage] [query embedding] panic, database path: %s, panic: %v", c.DatabasePath, r)
			panicMsg = append(panicMsg, fmt.Sprintf("%s", string(debug.Stack())))
		}

		// 捕获RUnlock的panic
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("[codebase]-[storage] [query embedding] unlock panic, database path: %s, panic: %v", c.DatabasePath, r)
				panicMsg = append(panicMsg, fmt.Sprintf("%s", string(debug.Stack())))
			}

			if len(panicMsg) > 0 {
				go func() {
					// 存储触发panic，上报记录
					data := make(map[string]string)
					data["full_workspace_path"] = c.WorkspacePath
					data["dim"] = strconv.Itoa(c.Dim)
					data["vector_version"] = definition.DatabaseVersion
					data["panic_msg"] = strings.Join(panicMsg, PanicMsgSep)
					data["function"] = "queryEmbeddingTable"
					userType := user.GetUserType()
					data["user_type"] = userType
					sls.Report(sls.EventTypeChatCodebaseVectorStoragePanic, uuid.NewString(), data)
				}()
				go func() {
					c.recoverDatabase(errors.New(RecoverErrorFileIsNotADatabase))
				}()
			}
		}()

		// 释放读锁
		c.RecoverMtx.RUnlock()
	}()

	querySql := c.buildEmbeddingQuerySql(query, tempTableName)
	vec, err := sqlitevec.SerializeFloat32(query.QueryEmbedding)
	if err != nil {
		// 这个操作一般不会出错误，出错时记录一下就好，与数据库无关
		log.Errorf("[codebase]-[storage] serialize query embedding error: %v", err)
		return definition.SqliteVecRetrieveResult{}, err
	}
	rows, err := c.Conn.Query(querySql, vec)
	if err != nil {
		log.Errorf("[codebase]-[storage] [query embedding] error: %v", err)
		return definition.SqliteVecRetrieveResult{}, err
	}
	defer func() {
		err = rows.Close()
		if err != nil {
			log.Errorf("[codebase]-[storage] [query embedding] rows close error: %v", err)
			go func() {
				c.recoverDatabase(err)
			}()
		}
	}()

	minDistance := math.MaxFloat64
	maxDistance := 0.0
	var tmpResult definition.SqliteVecRetrieveResult
	for rows.Next() {
		var chunkId string
		var retrieveEmbeddingByte []byte
		// 这个distance可用于后续排序的标准
		var distance float64
		err = rows.Scan(
			&chunkId,
			&retrieveEmbeddingByte,
			&distance,
		)

		// 召回的embedding向量数据
		//retrieveEmbedding := make([]float32, len(retrieveEmbeddingByte)/4)
		//buf := bytes.NewReader(retrieveEmbeddingByte)
		//for i := range retrieveEmbedding {
		//	err = binary.Read(buf, binary.LittleEndian, &retrieveEmbedding[i])
		//	if err != nil {
		//		return definition.SqliteVecRetrieveResult{}, err
		//	}
		//}

		if maxDistance < distance {
			maxDistance = distance
		}
		if minDistance > distance {
			minDistance = distance
		}

		tmpResult.Chunks = append(tmpResult.Chunks, definition.SqliteVecRetrieveChunk{
			StorageChunk: definition.StorageChunk{
				ChunkId: chunkId,
			},
			Score: distance, // 这个distance是临时记录，后续用于重新计算得分
		})
	}

	// 存在数据，并且最大最小距离不相同时
	if maxDistance-minDistance > 1e-5 && len(tmpResult.Chunks) > 0 {
		// 将得分归一化至 [0,1]，用于threshold过滤
		distanceRange := maxDistance - minDistance
		for idx := range tmpResult.Chunks {
			score := 1 - (tmpResult.Chunks[idx].Score-minDistance)/distanceRange
			tmpResult.Chunks[idx].Score = score
		}
	} else {
		// 特殊情况，得分全部相同，获得最高分
		for idx := range tmpResult.Chunks {
			tmpResult.Chunks[idx].Score = 1.0
		}
	}
	var retValue definition.SqliteVecRetrieveResult
	if query.ScoreThreshold > 1e-5 {
		// 阈值不为0，进行过滤
		for idx := range tmpResult.Chunks {
			if tmpResult.Chunks[idx].Score >= query.ScoreThreshold {
				retValue.Chunks = append(retValue.Chunks, tmpResult.Chunks[idx])
			}
		}
	} else {
		retValue.Chunks = tmpResult.Chunks
	}

	return retValue, nil
}

// insertEmbeddingTable
// 不做recover，小写函数不执行recover
func (c *SqliteVecClient) insertEmbedding(tx *sql.Tx, chunk definition.StorageChunk) error {
	if tx == nil {
		return errors.New("tx is nil")
	}
	// 插入embedding_table的sql
	insertEmbeddingSqlTmpl := `
		INSERT OR REPLACE INTO %s 
		    (
			chunk_id,
			embedding
			)
		VALUES (?, ?);
	`

	vec, err := sqlitevec.SerializeFloat32(chunk.Embedding)
	if err != nil {
		// 这里一般不会出现错误，出现了打个日志就行
		log.Errorf("[codebase]-[storage] insert serialize embedding error: %v", err)
		return err
	}

	embeddingTableName := c.getFullTableName(EmbeddingDataTableMainName)
	insertEmbeddingSql := fmt.Sprintf(insertEmbeddingSqlTmpl, embeddingTableName)

	_, err = tx.Exec(
		insertEmbeddingSql,
		chunk.ChunkId, vec,
	)

	return err
}

func (c *SqliteVecClient) deleteEmbedding(tx *sql.Tx, chunk definition.StorageChunk) error {
	if tx == nil {
		return errors.New("tx is nil")
	}

	if len(chunk.ChunkId) == 0 {
		return nil
	}

	deleteSqlTmpl := `
		DELETE FROM %s 
		WHERE chunk_id = ?;
	`
	embeddingTableName := c.getFullTableName(EmbeddingDataTableMainName)
	deleteSql := fmt.Sprintf(deleteSqlTmpl, embeddingTableName)
	_, err := tx.Exec(deleteSql, chunk.ChunkId)
	return err
}

func (c *SqliteVecClient) updateEmbedding(tx *sql.Tx, chunk definition.StorageChunk) error {
	if tx == nil {
		return errors.New("tx is nil")
	}

	err := c.deleteEmbedding(tx, chunk)
	if err != nil {
		return err
	}

	return c.insertEmbedding(tx, chunk)
}

func (c *SqliteVecClient) insertChunk(tx *sql.Tx, chunk definition.StorageChunk) error {
	if tx == nil {
		return errors.New("tx is nil")
	}
	// 插入chunk_table的sql
	insertChunkSqlTmpl := `
		INSERT OR REPLACE INTO %s 
		    (
			chunk_id,
		    file_path,
		    file_name,
		    start_line,
		    end_line,
		    start_offset,
		    end_offset
			)
		VALUES (?, ?, ?, ?, ?, ?, ?);
	`
	chunkTableName := c.getFullTableName(ChunkDataTableMainName)
	insertChunkSql := fmt.Sprintf(insertChunkSqlTmpl, chunkTableName)

	_, err := tx.Exec(
		insertChunkSql,
		chunk.ChunkId, chunk.FilePath, chunk.FileName,
		chunk.StartLine, chunk.EndLine, chunk.StartOffset, chunk.EndOffset,
	)

	return err
}

func (c *SqliteVecClient) deleteChunk(tx *sql.Tx, chunk definition.StorageChunk) error {
	if tx == nil {
		return errors.New("tx is nil")
	}

	if len(chunk.ChunkId) == 0 {
		return nil
	}

	deleteSqlTmpl := `
		DELETE FROM %s 
		WHERE chunk_id = ?;
	`
	chunkTableName := c.getFullTableName(ChunkDataTableMainName)
	deleteSql := fmt.Sprintf(deleteSqlTmpl, chunkTableName)
	_, err := tx.Exec(deleteSql, chunk.ChunkId)
	return err
}

func (c *SqliteVecClient) updateChunk(tx *sql.Tx, chunk definition.StorageChunk) error {
	if tx == nil {
		return errors.New("tx is nil")
	}
	err := c.deleteChunk(tx, chunk)
	if err != nil {
		return err
	}
	return c.insertChunk(tx, chunk)
}

func (c *SqliteVecClient) insertIndexRecord(tx *sql.Tx, record definition.VectorIndexRecord) error {
	if tx == nil {
		return errors.New("tx is nil")
	}

	// 插入索引记录
	insertRecordSqlTmpl := `
		INSERT OR REPLACE INTO %s 
		    (
			file_path,
			identifier,
			record_time
			)
		VALUES (?, ?, ?);
	`
	recordTableName := c.getFullTableName(IndexRecordTableMainName)
	insertRecordSql := fmt.Sprintf(insertRecordSqlTmpl, recordTableName)

	_, err := tx.Exec(
		insertRecordSql,
		record.FilePath, record.Identifier, record.RecordTime,
	)

	return err
}

func (c *SqliteVecClient) deleteIndexRecord(tx *sql.Tx, record definition.VectorIndexRecord) error {
	if tx == nil {
		return errors.New("tx is nil")
	}

	if len(record.FilePath) == 0 {
		return nil
	}

	deleteRecordSqlTmpl := `
		DELETE FROM %s
		WHERE file_path = ?;
	`

	recordTableName := c.getFullTableName(IndexRecordTableMainName)
	deleteRecordSql := fmt.Sprintf(deleteRecordSqlTmpl, recordTableName)
	_, err := tx.Exec(deleteRecordSql, record.FilePath)
	return err
}

func (c *SqliteVecClient) Close() error {
	if c.Closed.Load() {
		return nil
	}

	key := GetSqliteClientMapKey(c.WorkspacePath, c.Version, c.Mode)
	SqliteClientMap.Delete(key)
	c.Closed.Store(true)

	defer func() {
		c.Conn = nil
		// 处理关闭数据库连接时，仍然有任务在执行造成的panic
		if r := recover(); r != nil {
			// 日志记录
			log.Errorf("[codebase]-[storage] [close] recover from panic: %v", r)
		}
	}()

	if c.Conn != nil {
		err := c.Conn.Close()
		if err != nil {
			log.Errorf("[codebase]-[storage] [close] close error: %v", err)
			return err
		}
	}
	return nil
}

// Available
// 返回当前数据库是否可用
// WARN 不要随便用，记得释放锁
func (c *SqliteVecClient) available() bool {
	// 先检查Recovering，如果正在恢复中，则返回false
	// 如果被Close，或者Conn为空，返回不可用
	if c.Recovering.Load() || c.Conn == nil || c.Closed.Load() {
		if c.Closed.Load() {
			go func() {
				c.recoverDatabase(errors.New("database is closed"))
			}()
		}
		return false
	}
	// 当获取到读锁时，代表未在恢复
	// 当无法获取到读锁时，代表正在恢复
	return c.RecoverMtx.TryRLock()
}

// GetStorageFileNum
// 获取切块文件的数量
func (c *SqliteVecClient) GetStorageFileNum() int {
	if available := c.available(); !available {
		log.Errorf("[codebase]-[storage] [get file num] is ignored, database is recovering, database path: %s", c.DatabasePath)
		return 0
	}

	defer func() {
		panicMsg := make([]string, 0)
		if r := recover(); r != nil {
			log.Errorf("[codebase]-[storage] [get file num] panic, database path: %s, panic: %v", c.DatabasePath, r)
			panicMsg = append(panicMsg, fmt.Sprintf("%s", string(debug.Stack())))
		}

		// 捕获RUnlock的panic
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("[codebase]-[storage] [get file num] unlock panic, database path: %s, panic: %v", c.DatabasePath, r)
				panicMsg = append(panicMsg, fmt.Sprintf("%s", string(debug.Stack())))
			}

			if len(panicMsg) > 0 {
				go func() {
					// 存储触发panic，上报记录
					data := make(map[string]string)
					data["full_workspace_path"] = c.WorkspacePath
					data["dim"] = strconv.Itoa(c.Dim)
					data["vector_version"] = definition.DatabaseVersion
					data["panic_msg"] = strings.Join(panicMsg, PanicMsgSep)
					data["function"] = "GetStorageFileNum"
					userType := user.GetUserType()
					data["user_type"] = userType
					sls.Report(sls.EventTypeChatCodebaseVectorStoragePanic, uuid.NewString(), data)
				}()
				go func() {
					c.recoverDatabase(errors.New(RecoverErrorFileIsNotADatabase))
				}()
			}
		}()

		// 释放读锁
		c.RecoverMtx.RUnlock()
	}()

	if exist, err := c.checkTableExists(IndexRecordTableMainName); !exist || err != nil {
		return 0
	}

	tableFullName := c.getFullTableName(IndexRecordTableMainName)
	querySql := fmt.Sprintf("SELECT COUNT(*) FROM %s", tableFullName)
	rows, err := c.Conn.Query(querySql)
	if err != nil {
		// 查询出错，执行recover
		log.Errorf("[codebase]-[storage] [get file num] index record query error: %v", err)
		go func() {
			c.recoverDatabase(err)
		}()
		return 0
	}
	defer func() {
		err = rows.Close()
		if err != nil {
			log.Errorf("[codebase]-[storage] [get file num] close error: %v", err)
			go func() {
				c.recoverDatabase(err)
			}()
		}
	}()

	var count int
	for rows.Next() {
		// 使用 Scan 方法将查询结果映射到变量
		if err := rows.Scan(&count); err != nil {
			log.Errorf("[codebase]-[storage] [get file num] scan error: %v", err)
			return 0
		}
	}

	return count
}

// GetStorageFileChunks 获取当前库中某个文件的全部代码块
// 不包含embedding数据
func (c *SqliteVecClient) GetStorageFileChunks(filePath string) ([]definition.StorageChunk, error) {
	if available := c.available(); !available {
		log.Errorf("[codebase]-[storage] [get file chunks] is ignored, database is recovering, database path: %s", c.DatabasePath)
		return nil, DatabaseRecoveringError
	}

	defer func() {
		panicMsg := make([]string, 0)
		if r := recover(); r != nil {
			log.Errorf("[codebase]-[storage] [get file chunks] panic, database path: %s, panic: %v", c.DatabasePath, r)
			panicMsg = append(panicMsg, fmt.Sprintf("%s", string(debug.Stack())))
		}

		// 捕获RUnlock的panic
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("[codebase]-[storage] [get file chunks] unlock panic, database path: %s, panic: %v", c.DatabasePath, r)
				panicMsg = append(panicMsg, fmt.Sprintf("%s", string(debug.Stack())))
			}

			if len(panicMsg) > 0 {
				go func() {
					// 存储触发panic，上报记录
					data := make(map[string]string)
					data["full_workspace_path"] = c.WorkspacePath
					data["dim"] = strconv.Itoa(c.Dim)
					data["vector_version"] = definition.DatabaseVersion
					data["panic_msg"] = strings.Join(panicMsg, PanicMsgSep)
					data["function"] = "GetStorageFileChunks"
					userType := user.GetUserType()
					data["user_type"] = userType
					sls.Report(sls.EventTypeChatCodebaseVectorStoragePanic, uuid.NewString(), data)
				}()
				go func() {
					c.recoverDatabase(errors.New(RecoverErrorFileIsNotADatabase))
				}()
			}
		}()

		// 释放读锁
		c.RecoverMtx.RUnlock()
	}()

	if exist, err := c.checkTableExists(EmbeddingDataTableMainName); !exist || err != nil {
		if err != nil {
			return nil, err
		}
		return nil, errors.New("embedding table not exists")
	}

	if exist, err := c.checkTableExists(ChunkDataTableMainName); !exist || err != nil {
		if err != nil {
			return nil, err
		}
		return nil, errors.New("chunk table not exists")
	}

	query := definition.QueryCondition{
		ExactFilePath: filePath,
	}
	chunks, err := c.queryChunkTable(query)
	if err != nil {
		log.Errorf("[codebase]-[storage] [query] query error: %v", err)
		go func() {
			c.recoverDatabase(err)
		}()
		return nil, err
	}

	var result []definition.StorageChunk
	invalidFilePaths := make(map[string]bool)

	// 同时读取文件内容
	for _, chunk := range chunks {
		// 按照行号读取文件内容
		chunk.Content, err = readContent(chunk.FilePath, chunk.StartLine, chunk.EndLine, c.Mode)
		if err != nil || chunk.Content == "" {
			// 删除这个文件相关的所有索引
			if chunk.FilePath != "" {
				invalidFilePaths[chunk.FilePath] = true
			}

			if err != nil {
				log.Errorf("[codebase]-[storage] query read file error: %v", err)
			} else {
				log.Errorf("[codebase]-[storage] [content empty] filepath: %s, fetch file content is empty", chunk.FilePath)
			}

			// 读取文件出错误，舍弃这个块
			continue
		}

		result = append(result, chunk)
	}

	if len(invalidFilePaths) > 0 {
		go func() {
			// 删除无效的Chunk
			var todoDeleteFilePaths []string
			for invalidFilePath := range invalidFilePaths {
				todoDeleteFilePaths = append(todoDeleteFilePaths, invalidFilePath)
			}
			err = c.BatchDelete(todoDeleteFilePaths)
			if err != nil {
				// 删除出错，执行recover
				c.recoverDatabase(err)
			}
		}()
	}

	return result, nil
}

// GetStorageChunkNum
// 正确获取切块行数
func (c *SqliteVecClient) GetStorageChunkNum() int {
	if available := c.available(); !available {
		log.Errorf("[codebase]-[storage] [get chunk num] is ignored, database is recovering, database path: %s", c.DatabasePath)
		return 0
	}

	defer func() {
		panicMsg := make([]string, 0)
		if r := recover(); r != nil {
			log.Errorf("[codebase]-[storage] [get chunk num] panic, database path: %s, panic: %v", c.DatabasePath, r)
			panicMsg = append(panicMsg, fmt.Sprintf("%s", string(debug.Stack())))
		}

		// 捕获RUnlock的panic
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("[codebase]-[storage] [get chunk num] unlock panic, database path: %s, panic: %v", c.DatabasePath, r)
				panicMsg = append(panicMsg, fmt.Sprintf("%s", string(debug.Stack())))
			}

			if len(panicMsg) > 0 {
				go func() {
					// 存储触发panic，上报记录
					data := make(map[string]string)
					data["full_workspace_path"] = c.WorkspacePath
					data["dim"] = strconv.Itoa(c.Dim)
					data["vector_version"] = definition.DatabaseVersion
					data["panic_msg"] = strings.Join(panicMsg, PanicMsgSep)
					data["function"] = "GetStorageChunkNum"
					userType := user.GetUserType()
					data["user_type"] = userType
					sls.Report(sls.EventTypeChatCodebaseVectorStoragePanic, uuid.NewString(), data)
				}()
				go func() {
					c.recoverDatabase(errors.New(RecoverErrorFileIsNotADatabase))
				}()
			}
		}()

		// 释放读锁
		c.RecoverMtx.RUnlock()
	}()

	if exist, err := c.checkTableExists(ChunkDataTableMainName); !exist || err != nil {
		return 0
	}
	tableFullName := c.getFullTableName(ChunkDataTableMainName)
	querySql := fmt.Sprintf("SELECT COUNT(*) FROM %s", tableFullName)
	rows, err := c.Conn.Query(querySql)
	if err != nil {
		// 查询出错，执行recover
		log.Errorf("[codebase]-[storage] [record query] query error: %v", err)
		go func() {
			c.recoverDatabase(err)
		}()
		return 0
	}
	defer func() {
		err = rows.Close()
		if err != nil {
			log.Errorf("[codebase]-[storage] [get chunk num] close error: %v", err)
			go func() {
				c.recoverDatabase(err)
			}()
		}
	}()

	var count int
	for rows.Next() {
		// 使用 Scan 方法将查询结果映射到变量
		if err := rows.Scan(&count); err != nil {
			log.Errorf("[codebase]-[storage] [get chunk num] scan error: %v", err)
			return 0
		}
	}

	return count
}

// CheckFileChanged 检查文件是否被修改
// 存在性, 是否文件内容发生变动
func (c *SqliteVecClient) CheckFileChanged(filePath string, fileContent string) (bool, bool) {
	if available := c.available(); !available {
		log.Errorf("[codebase]-[storage] [record query] is ignored, database is recovering, database path: %s", c.DatabasePath)
		return false, false
	}

	defer func() {
		panicMsg := make([]string, 0)
		if r := recover(); r != nil {
			log.Errorf("[codebase]-[storage] [record query] panic, database path: %s, panic: %v", c.DatabasePath, r)
			panicMsg = append(panicMsg, fmt.Sprintf("%s", string(debug.Stack())))
		}

		// 捕获RUnlock的panic
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("[codebase]-[storage] [record query] unlock panic, database path: %s, panic: %v", c.DatabasePath, r)
				panicMsg = append(panicMsg, fmt.Sprintf("%s", string(debug.Stack())))
			}

			if len(panicMsg) > 0 {
				go func() {
					// 存储触发panic，上报记录
					data := make(map[string]string)
					data["full_workspace_path"] = c.WorkspacePath
					data["dim"] = strconv.Itoa(c.Dim)
					data["vector_version"] = definition.DatabaseVersion
					data["panic_msg"] = strings.Join(panicMsg, PanicMsgSep)
					data["function"] = "CheckFileChanged"
					userType := user.GetUserType()
					data["user_type"] = userType
					sls.Report(sls.EventTypeChatCodebaseVectorStoragePanic, uuid.NewString(), data)
				}()
				go func() {
					c.recoverDatabase(errors.New(RecoverErrorFileIsNotADatabase))
				}()
			}
		}()

		// 释放读锁
		c.RecoverMtx.RUnlock()
	}()

	tableFullName := c.getFullTableName(IndexRecordTableMainName)
	// 使用参数化查询，而不是直接拼接 SQL 字符串
	querySql := fmt.Sprintf("SELECT file_path,identifier,record_time FROM %s WHERE file_path = ?", tableFullName)
	rows, err := c.Conn.Query(querySql, filePath)
	if err != nil {
		// 查询出错，执行recover
		log.Errorf("[codebase]-[storage] [record query] query error: %v, querySQL: %s", err, querySql)
		go func() {
			c.recoverDatabase(err)
		}()
		return false, false
	}
	defer func() {
		err = rows.Close()
		if err != nil {
			log.Errorf("[codebase]-[storage] [record query] rows close error: %v", err)
			go func() {
				c.recoverDatabase(err)
			}()
		}
	}()

	for rows.Next() {
		var oldFilePath string
		var oldIdentifier string
		var recordTime int

		err = rows.Scan(
			&oldFilePath,
			&oldIdentifier,
			&recordTime,
		)
		if err != nil {
			log.Errorf("[codebase]-[storage] [record query] scan error: %v", err)
			// 不存在
			return false, false
		}

		timeInterval := time.Now().Unix() - int64(recordTime)
		if timeInterval >= int64(MaxRecordLastTime) {
			// 记录索引距今时间过长，重建索引
			// 存在，发生改变
			return true, true
		}

		newIdentifier := definition.GetIdentifier(fileContent)
		if newIdentifier != oldIdentifier {
			// 存在，发生改变
			return true, true
		} else {
			// 存在，未改变
			return true, false
		}
	}

	return false, false
}

// AutoDelete 自动清理数据
// 在建立索引过程中调用
func (c *SqliteVecClient) AutoDelete() error {
	if available := c.available(); !available {
		log.Errorf("[codebase]-[storage] [auto delete] is ignored, database is recovering, database path: %s", c.DatabasePath)
		return DatabaseRecoveringError
	}

	defer func() {
		panicMsg := make([]string, 0)
		if r := recover(); r != nil {
			log.Errorf("[codebase]-[storage] [auto delete] panic, database path: %s, panic: %v", c.DatabasePath, r)
			panicMsg = append(panicMsg, fmt.Sprintf("%s", string(debug.Stack())))
		}

		// 捕获RUnlock的panic
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("[codebase]-[storage] [auto delete] unlock panic, database path: %s, panic: %v", c.DatabasePath, r)
				panicMsg = append(panicMsg, fmt.Sprintf("%s", string(debug.Stack())))
			}

			if len(panicMsg) > 0 {
				go func() {
					// 存储触发panic，上报记录
					data := make(map[string]string)
					data["full_workspace_path"] = c.WorkspacePath
					data["dim"] = strconv.Itoa(c.Dim)
					data["vector_version"] = definition.DatabaseVersion
					data["panic_msg"] = strings.Join(panicMsg, PanicMsgSep)
					data["function"] = "AutoDelete"
					userType := user.GetUserType()
					data["user_type"] = userType
					sls.Report(sls.EventTypeChatCodebaseVectorStoragePanic, uuid.NewString(), data)
				}()
				go func() {
					c.recoverDatabase(errors.New(RecoverErrorFileIsNotADatabase))
				}()
			}
		}()

		// 释放读锁
		c.RecoverMtx.RUnlock()
	}()

	// 随机获取当前库的一些索引记录
	querySqlTmpl := `
		SELECT 
		    file_path
		FROM %s
		ORDER BY RANDOM()
		LIMIT %d
	`

	tableFullName := c.getFullTableName(IndexRecordTableMainName)
	querySql := fmt.Sprintf(querySqlTmpl, tableFullName, AutoDeleteFileNumber)

	rows, err := c.Conn.Query(querySql)
	if err != nil {
		log.Errorf("[codebase]-[storage] [auto delete] query error: %v", err)
		go func() {
			c.recoverDatabase(err)
		}()
		return err
	}
	defer func() {
		err = rows.Close()
		if err != nil {
			log.Errorf("[codebase]-[storage] [auto delete] close rows error: %v", err)
			go func() {
				c.recoverDatabase(err)
			}()
		}
	}()

	// 这个就是准备删除的所有文件
	var filePaths []string
	for rows.Next() {
		var filePath string
		err = rows.Scan(
			&filePath,
		)
		if err != nil {
			log.Errorf("[codebase]-[storage] [auto delete] scan error: %v", err)
			go func() {
				c.recoverDatabase(err)
			}()
			return err
		}

		filePaths = append(filePaths, filePath)
	}

	// 清理数据
	err = c.BatchDelete(filePaths)
	if err != nil {
		go func() {
			c.recoverDatabase(err)
		}()
		return err
	}

	return nil
}

// checkConnection 检查连接是否可用
func (c *SqliteVecClient) checkConnection() error {
	var version string
	//err := c.Conn.QueryRow("select vec_version()").Scan(&vecVersion)
	err := c.Conn.QueryRow("select sqlite_version()").Scan(&version)
	if err != nil {
		log.Errorf("[codebase]-[storage] check connection error: %v", err)
		return err
	}
	return nil
}

// QueryEmbedding 根据查询条件查询embedding数据
// 这里的QueryEmbedding不能为空，否则返回空
// 这里返回的chunk仅有chunkId有效，返回后需要反查chunkId对应的chunk信息
func (c *SqliteVecClient) QueryEmbedding(chunkId string) (*definition.StorageChunk, error) {
	// 参数校验
	if chunkId == "" {
		log.Errorf("[codebase]-[storage] [query embedding chunk] chunkId is empty")
		return nil, errors.New("chunkId is empty")
	}

	if available := c.available(); !available {
		log.Errorf("[codebase]-[storage] [query embedding chunk] is ignored, database is recovering, database path: %s", c.DatabasePath)
		return nil, DatabaseRecoveringError
	}

	defer func() {
		panicMsg := make([]string, 0)
		if r := recover(); r != nil {
			log.Errorf("[codebase]-[storage] [query embedding chunk] panic, database path: %s, panic: %v", c.DatabasePath, r)
			panicMsg = append(panicMsg, fmt.Sprintf("%s", string(debug.Stack())))
		}

		// 捕获RUnlock的panic
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("[codebase]-[storage] [query embedding chunk] unlock panic, database path: %s, panic: %v", c.DatabasePath, r)
				panicMsg = append(panicMsg, fmt.Sprintf("%s", string(debug.Stack())))
			}

			if len(panicMsg) > 0 {
				go func() {
					// 存储触发panic，上报记录
					data := make(map[string]string)
					data["full_workspace_path"] = c.WorkspacePath
					data["dim"] = strconv.Itoa(c.Dim)
					data["vector_version"] = definition.DatabaseVersion
					data["panic_msg"] = strings.Join(panicMsg, PanicMsgSep)
					data["function"] = "QueryEmbedding"
					userType := user.GetUserType()
					data["user_type"] = userType
					sls.Report(sls.EventTypeChatCodebaseVectorStoragePanic, uuid.NewString(), data)
				}()
				go func() {
					c.recoverDatabase(errors.New(RecoverErrorFileIsNotADatabase))
				}()
			}
		}()

		// 释放读锁
		c.RecoverMtx.RUnlock()
	}()

	// 查询sql
	querySqlTmpl := `
		SELECT 
		    chunk_id,
		    embedding
		FROM %s
		WHERE chunk_id = ?;
	`

	tableFullName := c.getFullTableName(EmbeddingDataTableMainName)

	querySql := fmt.Sprintf(querySqlTmpl, tableFullName)
	rows, err := c.Conn.Query(querySql, chunkId)
	if err != nil {
		log.Errorf("[codebase]-[storage] [query embedding chunk] error: %v", err)
		return nil, err
	}
	defer func() {
		err = rows.Close()
		if err != nil {
			log.Errorf("[codebase]-[storage] [query embedding chunk] rows close error: %v", err)
			go func() {
				c.recoverDatabase(err)
			}()
		}
	}()

	result := definition.StorageChunk{
		ChunkId: "",
	}
	for rows.Next() {
		var queryChunkId string
		var retrieveEmbeddingByte []byte
		err = rows.Scan(
			&queryChunkId,
			&retrieveEmbeddingByte,
		)

		// 召回的embedding向量数据
		retrieveEmbedding := make([]float32, len(retrieveEmbeddingByte)/4)
		buf := bytes.NewReader(retrieveEmbeddingByte)
		for i := range retrieveEmbedding {
			// 由于存储时使用的是小端存储，因此读取时也是小端读取
			err = binary.Read(buf, binary.LittleEndian, &retrieveEmbedding[i])
			if err != nil {
				return nil, err
			}
		}

		result.ChunkId = queryChunkId
		result.Embedding = retrieveEmbedding
		break
	}
	if result.ChunkId == "" {
		// 没有查询到chunk
		return nil, nil
	}

	return &result, nil
}

func (c *SqliteVecClient) QueryAllStorageRecords() ([]definition.VectorIndexRecord, error) {
	if available := c.available(); !available {
		log.Errorf("[codebase]-[storage] [query all record] is ignored, database is recovering, database path: %s", c.DatabasePath)
		return nil, DatabaseRecoveringError
	}

	defer func() {
		panicMsg := make([]string, 0)
		if r := recover(); r != nil {
			log.Errorf("[codebase]-[storage] [query all record] panic, database path: %s, panic: %v", c.DatabasePath, r)
			panicMsg = append(panicMsg, fmt.Sprintf("%s", string(debug.Stack())))
		}

		// 捕获RUnlock的panic
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("[codebase]-[storage] [query all record] unlock panic, database path: %s, panic: %v", c.DatabasePath, r)
				panicMsg = append(panicMsg, fmt.Sprintf("%s", string(debug.Stack())))
			}

			if len(panicMsg) > 0 {
				go func() {
					// 存储触发panic，上报记录
					data := make(map[string]string)
					data["full_workspace_path"] = c.WorkspacePath
					data["dim"] = strconv.Itoa(c.Dim)
					data["vector_version"] = definition.DatabaseVersion
					data["panic_msg"] = strings.Join(panicMsg, PanicMsgSep)
					data["function"] = "QueryAllStorageRecords"
					userType := user.GetUserType()
					data["user_type"] = userType
					sls.Report(sls.EventTypeChatCodebaseVectorStoragePanic, uuid.NewString(), data)
				}()
				go func() {
					c.recoverDatabase(errors.New(RecoverErrorFileIsNotADatabase))
				}()
			}
		}()

		// 释放读锁
		c.RecoverMtx.RUnlock()
	}()

	tableFullName := c.getFullTableName(IndexRecordTableMainName)
	// 使用参数化查询，而不是直接拼接 SQL 字符串
	querySql := fmt.Sprintf("SELECT file_path,identifier,record_time FROM %s;", tableFullName)
	rows, err := c.Conn.Query(querySql)
	if err != nil {
		// 查询出错，执行recover
		log.Errorf("[codebase]-[storage] [query all record] query error: %v, querySQL: %s", err, querySql)
		go func() {
			c.recoverDatabase(err)
		}()
		return nil, err
	}
	defer func() {
		err = rows.Close()
		if err != nil {
			log.Errorf("[codebase]-[storage] [query all record] rows close error: %v", err)
			go func() {
				c.recoverDatabase(err)
			}()
		}
	}()

	var records []definition.VectorIndexRecord
	for rows.Next() {
		var filePath string
		var identifier string
		var recordTime int

		err = rows.Scan(
			&filePath,
			&identifier,
			&recordTime,
		)
		if err != nil {
			log.Errorf("[codebase]-[storage] [query all record] scan error: %v", err)
			// 不存在
			return nil, err
		}

		records = append(records, definition.VectorIndexRecord{
			FilePath:   filePath,
			Identifier: identifier,
			RecordTime: int64(recordTime),
		})
	}

	return records, nil
}

// recoverDatabase 数据库恢复工作
// 如果数据库的触发失败是由于扩展造成的，如果不是主键重复错误，则直接删除当前库，大概率是数据库表被破坏了
// 1.标记修复状态
// 2.修复次数过多时，删除当前数据库文件，并重新创建一个空的数据库文件
// 3.确定连接可用后，获取embedding维度后，重新建表
func (c *SqliteVecClient) recoverDatabase(failedError error) {
	if failedError == nil {
		log.Debugf("[codebase]-[storage] [recover database] skip, failed error is nil, database path: %s", c.DatabasePath)
		return
	}

	if errors.Is(failedError, DatabaseRecoveringError) {
		// 数据库正在恢复的错误，不需要特殊处理，直接返回即可
		return
	}

	// 先检查恢复标记，防止重入
	if !c.Recovering.CompareAndSwap(false, true) {
		log.Debugf("[codebase]-[storage] [recover database] skip, database is recovering, database path: %s", c.DatabasePath)
		return
	}

	// 数据库恢复，获取写锁
	c.RecoverMtx.Lock()

	defer func() {
		panicMsg := make([]string, 0)
		// 兜底recover
		if r := recover(); r != nil {
			log.Errorf("[codebase]-[storage] [recover database] panic, database path: %s, panic: %v", c.DatabasePath, r)
			panicMsg = append(panicMsg, fmt.Sprintf("%s", string(debug.Stack())))
		}

		defer func() {
			// 捕获Unlock的panic
			if r := recover(); r != nil {
				log.Errorf("[codebase]-[storage] [recover database] unlock panic, database path: %s, panic: %v", c.DatabasePath, r)
				panicMsg = append(panicMsg, fmt.Sprintf("%s", string(debug.Stack())))
			}

			if len(panicMsg) > 0 {
				go func() {
					// 存储触发panic，上报记录
					data := make(map[string]string)
					data["full_workspace_path"] = c.WorkspacePath
					data["dim"] = strconv.Itoa(c.Dim)
					data["vector_version"] = definition.DatabaseVersion
					data["panic_msg"] = strings.Join(panicMsg, PanicMsgSep)
					data["function"] = "recoverDatabase"
					userType := user.GetUserType()
					data["user_type"] = userType
					sls.Report(sls.EventTypeChatCodebaseVectorStoragePanic, uuid.NewString(), data)
				}()
				go func() {
					c.recoverDatabase(errors.New(RecoverErrorFileIsNotADatabase))
				}()
			}
			c.Recovering.Store(false)
		}()

		// 释放写锁
		c.RecoverMtx.Unlock()
	}()

	log.Errorf("[codebase]-[storage] [recover database] start, database path: %s, occur error: %s", c.DatabasePath, failedError.Error())

	overwrite := false
	var sqliteErr sqlite3.Error
	if errors.As(failedError, &sqliteErr) {
		if sqliteErr.ExtendedCode == 1 &&
			!strings.Contains(failedError.Error(), "UNIQUE constraint failed") {
			// 代表是由sqlite-vec导致的，直接删除当前库的全部数据
			overwrite = true
		}
		if strings.Contains(failedError.Error(), RecoverErrorFileIsNotADatabase) {
			// 代表是由sqlite-vec导致的，直接删除当前库的全部数据
			overwrite = true
		}
	}

	if errors.Is(failedError, sqlite3.ErrBusy) {
		// 繁忙，等待500ms秒重试
		time.Sleep(time.Duration(500) * time.Millisecond)
	} else {
		// 非繁忙错误时，恢复次数加1
		c.RecoverTimes.Add(1)
	}

	// 这里考虑删除损坏的数据库文件，因为恢复次数太多，数据库文件可能已经彻底损坏
	if c.RecoverTimes.Load() >= DefaultMaxRecoverTimes {
		c.RecoverTimes.Store(0)
		overwrite = true
		log.Errorf("[codebase]-[storage] [recover database] recover too many times, database path: %s, overwrite: %v", c.DatabasePath, overwrite)
	}

	// 创建数据库文件
	// 创建新的连接
	err := c.createDatabase(overwrite)
	if err != nil {
		log.Errorf("[codebase]-[storage] [recover database] create database file error: %v", err)
		return
	}

	log.Infof("[codebase]-[storage] [recover database] success, database path: %s", c.DatabasePath)
}
