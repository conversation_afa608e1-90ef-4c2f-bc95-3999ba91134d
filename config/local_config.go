package config

import (
	"cosy/definition"
	"net/url"
	"runtime"
	"strings"
)

const (
	UrlPublicBigModelEndpoint = "https://lingma-api.tongyi.aliyun.com/algo"
	UrlPublicAuthLoginUrl     = "https://account.aliyun.com/login/login.htm"
	UrlPublicAuthLogoutUrl    = "https://account.aliyun.com/logout/logout.htm"
	UrlPublicLoginUrl         = "https://devops.aliyun.com/lingma/login"
)

const (
	//TruncateKeyChat is the key of chat prompt，8k场景下默认5800，32k场景下20k
	TruncateKeyChat = "chat.common"
	// TruncateKeyChatTestCase 单测的截断长度，默认都是32k
	TruncateKeyChatTestCase = "chat.testcase"
	// TruncateKeyExplainCode 8k场景下默认5800，32k场景下20k
	TruncateKeyExplainCode = "chat.explain_code"
	// TruncateKeCommitMsg is the key of commit msg prompt，8k场景下默认5800，32k场景下20k
	TruncateKeCommitMsg = "commit.msg"
	// TruncateKeyCodeProblem is the key of code problem prompt，8k场景下默认5800，32k场景下20k
	TruncateKeyCodeProblem = "code.problem"
	// TruncateKeyWorkspaceRag 库内rag prompt，8k场景下默认5800，32k场景下20k
	TruncateKeyWorkspaceRag = "workspace.rag"
	// TruncateKeyWorkspaceRequirementAnalysis 库内rag问题分析 prompt，8k场景下默认5800，32k场景下25k
	TruncateKeyWorkspaceRequirementAnalysis = "workspace.requirement_analysis"
	// TruncateKeyKnowledgeRag 企业知识库rag prompt，8k场景下默认5800，32k场景下20k
	TruncateKeyKnowledgeRag = "knowledge.rag"
	// TruncateKeyFreeInput 自由问答场景总体上下文大小限制，公有云默认50k，专有云默认5800
	TruncateKeyFreeInput = "free.input.truncate"
	// TruncateKeyAiDevelop AiDevelop场景总体上下文大小限制，公有云默认60k，专有云默认5800
	TruncateKeyAiDevelop = "ai.develop.truncate"
	// TruncateKeyMultimodal 多模态场景上下文大小限制，公有云默认25k，专有云5800
	TruncateKeyMultimodal = "multimodal.truncate"
	// TruncateKeyChatMemory 问答记忆提取上下文大小限制
	TruncateKeyChatMemory = "chat.memory"
	// TruncateKeyChatFetchMemory 问答记忆获取上下文大小限制
	TruncateKeyChatFetchMemory = "chat.fetch.memory"
	// TruncateKeyCommonAgent 通用研发agnet上下文限制，公有云默认96K，专有云默认5800
	TruncateKeyCommonAgent = "common.agent.truncate"
)

// ViperBeamConfig is used to read config from local config viper
// It should be identical with definition.BmeamConfig, which is tagged json
type ViperBeamConfig struct {
	BeamSize       int     `mapstructure:"beam_size"`
	TopK           int     `mapstructure:"topk"` // The number of returned results
	TokenThreshold float32 `mapstructure:"token_threshold"`
	MaxLength      int     `mapstructure:"max_length"` // Max beam search inference steps
}

// RemoteConfig 服务端连接配置
type RemoteConfig struct {
	//代码搜索域名
	ForceAiEndpoint string `mapstructure:"force_ai_endpoint" json:"force_ai_endpoint"`
	//lingma服务端域名
	BigModelEndpoint string `mapstructure:"big_model_endpoint" json:"big_model_endpoint"`
	BigModelCookie   string `mapstructure:"big_model_cookie" json:"big_model_cookie"`
	//lingma host header，已废弃
	BigModelHost string `mapstructure:"big_model_host" json:"big_model_host"`
	//插件跳登录URL
	LoginUrl string `mapstructure:"login_url" json:"login_url"`
	//账号登录回调URL，aliyun/yunxiao login
	AuthLoginUrl string `mapstructure:"auth_login_url" json:"auth_login_url"`
	//账号登出URL，aliyun/yunxiao logout
	AuthLogoutUrl string `mapstructure:"auth_logout_url" json:"auth_logout_url"`
	//默认网络代理
	ServerProxy string `mapstructure:"server_proxy" json:"server_proxy"`
	OssBucket   string `mapstructure:"oss_bucket" json:"oss_bucket"`
	OssBinRoot  string `mapstructure:"oss_bin_root" json:"oss_bin_root"`
	//插件路径
	OssPluginRoot string `mapstructure:"oss_plugin_root" json:"oss_plugin_root"`
	//本地模型
	OssModelRoot string `mapstructure:"oss_model_root" json:"oss_model_root"`
	//本地模型运行环境
	OssModelEnvRoot string `mapstructure:"oss_model_env_root" json:"oss_model_env_root"`
	HeartbeatTag    string `mapstructure:"heartbeat_tag" json:"heartbeat_tag"`
	// 消息加密模式，0：不加密，1：使用CustomBase64
	MessageEncode string `mapstructure:"message_encode" json:"message_encode"`
	// 登录回调参数加密模式，1：不加密，2：使用CustomBase64
	LoginEncode string `mapstructure:"login_encode" json:"login_encode"`
}

// region HA配置
type RegionHAConfig struct {
	CenterNodes []string            `json:"centerNodes" mapstructure:"centerNodes"`
	InferNodes  []string            `json:"inferNodes" mapstructure:"inferNodes"`
	DataNodes   map[string][]string `json:"dataNodes" mapstructure:"dataNodes"`
}

// region config配置
type RegionConfig struct {
	PreferredCenterNode    *EndpointConfig            `json:"preferredCenterNode" mapstructure:"preferredCenterNode"`
	PreferredDataNodeMap   map[string]*EndpointConfig `json:"preferredDataNodeMap" mapstructure:"preferredDataNodeMap"`
	PreferredInferenceNode *EndpointConfig            `json:"preferredInferenceNode" mapstructure:"preferredInferenceNode"`
}

func (r *RegionConfig) IsCodebaseEndpointAvailable() bool {
	return r.GetCodebaseEndpoint() != ""
}

func (r *RegionConfig) GetCodebaseEndpoint() string {
	if r.PreferredDataNodeMap == nil || len(r.PreferredDataNodeMap) <= 0 {
		return ""
	}
	for regionType, endpoint := range r.PreferredDataNodeMap {
		if regionType != "" && strings.EqualFold(regionType, definition.DataRegionTypeCodebase) {
			if endpoint != nil {
				return endpoint.Endpoint
			}
		}
	}
	return ""
}

func (r *RegionConfig) GetCodebaseRegion() *EndpointConfig {
	if r.PreferredDataNodeMap == nil || len(r.PreferredDataNodeMap) <= 0 {
		return nil
	}
	for regionType, endpoint := range r.PreferredDataNodeMap {
		if regionType != "" && strings.EqualFold(regionType, definition.DataRegionTypeCodebase) {
			if endpoint != nil {
				return endpoint
			}
		}
	}
	return nil
}

func (r *RegionConfig) IsRemoteAgentEndpointAvailable() bool {
	if r.PreferredDataNodeMap == nil || len(r.PreferredDataNodeMap) <= 0 {
		return false
	}
	for bizType, endpoint := range r.PreferredDataNodeMap {
		if bizType != "" && strings.EqualFold(bizType, definition.DataRegionTypeRemoteAgent) {
			if endpoint != nil {
				return endpoint.Endpoint != ""
			}
		}
	}
	return false
}

// 缓存的region select endpoint
type EndpointConfig struct {
	Region   string `json:"region,omitempty" mapstructure:"region"`
	Endpoint string `json:"endpoint" mapstructure:"endpoint"` //连接的URL
	Latency  int    `json:"latency" mapstructure:"latency"`   //最后的延迟，单位毫秒
}

// 目前只设置cookie
func (r *RemoteConfig) withExtra(config *RemoteConfig) {
	if config == nil {
		return
	}

	if config.BigModelEndpoint != "" {
		r.BigModelEndpoint = config.BigModelEndpoint
		parsedHost := parseBigModelHost(config.BigModelEndpoint)
		if parsedHost != "" {
			r.BigModelHost = parsedHost
		}
	}
	if config.BigModelCookie != "" {
		r.BigModelCookie = config.BigModelCookie
	}
}

func parseBigModelHost(endPoint string) string {
	parsedUri, _ := url.Parse(endPoint)
	if parsedUri != nil {
		return parsedUri.Host
	}
	return ""
}

type TruncateConfig struct {
	ChunkLimits map[string]int `mapstructure:"chunk_limits" json:"chunk_limits"`
}

func (t TruncateConfig) GetChunkLimit(key string) int {
	limit, ok := t.ChunkLimits[key]
	if !ok {
		return -1
	}
	return limit
}

// 公有云是
// 专有云链接格式：/api/v1/model/download/${version}/${language}
func (r *RemoteConfig) BuildModeFileDownloadUrl(modelVersion, language string) string {
	if OnPremiseMode {
		return r.OssModelRoot + "/" + modelVersion + "/" + language
	}
	modelName := language + ".model"
	return r.OssModelRoot + "/" + modelVersion + "/" + "model/" + modelName
}

func (r *RemoteConfig) BuildModeEnvDownloadUrl() string {
	if OnPremiseMode {
		return r.OssModelEnvRoot + "/" + runtime.GOOS + "/" + runtime.GOARCH
	}
	platformAndArch := runtime.GOOS + "-" + runtime.GOARCH
	return r.OssModelEnvRoot + "/env/" + platformAndArch
}

// ModelConfig go服务全局配置
type ModelConfig struct {

	//代理模式，system/manual
	ProxyMode string `mapstructure:"proxy_mode"`

	// 插件配置的proxy
	HttpProxy string `mapstructure:"http_proxy"`

	// 专属版连接host，影响模型连接配置
	// 格式，不带algo后缀
	Endpoint string `mapstructure:"endpoint"`

	CommandAllowList string `mapstructure:"command_allow_list"`

	CommandDenyList string `mapstructure:"command_deny_list"`

	// MCP自动执行, enable-启用
	McpAutoRun string `mapstructure:"mcp_auto_run"`

	// 命令行工具执行模式
	TerminalRunMode string `mapstructure:"terminal_run_mode"`

	// Web Tools 配置，包括是否有fetch content，search web等Web Tools，也包括Web Tools是否自动执行
	WebToolsExecutionMode string `mapstructure:"web_tools_execution_mode"`

	//
	AskModeUseTools string `mapstructure:"ask_mode_use_tools"`
}

type AuthExtConfig struct {
	Enabled bool `json:"enabled"`
}

// UrlConfig url公专全局配置
type UrlConfig struct {
	Help           string `json:"help"`
	Homepage       string `json:"homepage"`
	Feedback       string `json:"feedback"`
	Join           string `json:"join"`
	SurveyFeedback string `json:"survey_feedback"`
}

type EnvConfig struct {
	OnPremise            string               `json:"on_premise"`
	Remote               RemoteConfig         `json:"remote_config"`
	Urls                 UrlConfig            `json:"url_config"`
	Truncate             TruncateConfig       `json:"truncate_config"`
	TestAgentTrackConfig TestAgentTrackConfig `json:"test_agent_track_config"`
}

type TestAgentTrackConfig struct {
	DoTestAgentTrack     string `json:"do_test_agent_track"`
	EvalOutPutLogFileDir string `json:"output_dir"`
}
