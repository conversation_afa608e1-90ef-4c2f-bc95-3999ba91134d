package config

import (
	"encoding/json"
	"os"
)

const (
	AgentModeRemoteAgent = "REMOTE_AGENT"
	AgentModeLocalAgent  = "LOCAL_AGENT"
)

const (
	defaultRemoteAgentUserInfoPath  = "/data/.task/user_info.json"
	defaultTaskInfoPath             = "/data/.task/task.json"
	DefaultDesignMDPath             = "/data/.task/design.md"
	defaultRemoteAgentWorkspacePath = "/data/workspace"
)

var agentMode string
var taskId string
var lspAddr string

// IsRemoteAgentMode 判断是否RemoteAgent模式
func IsRemoteAgentMode() bool {
	return agentMode == AgentModeRemoteAgent
}

// GetRemoteAgentUserInfoPath RemoteAgent模式下，user-info路径
func GetRemoteAgentUserInfoPath() string {
	return defaultRemoteAgentUserInfoPath
}

// SetAgentMode 设置Agent运行模式
func SetAgentMode(mode string) {
	agentMode = mode
}

// GetRemoteModeTaskId 获取remote模式下的任务id
func GetRemoteModeTaskId() (string, error) {
	if taskId != "" {
		return taskId, nil
	}
	bytes, err := os.ReadFile(defaultTaskInfoPath)
	if err != nil {
		return "", err
	}
	var taskInfo struct {
		TaskId string `json:"taskId"`
	}
	err = json.Unmarshal(bytes, &taskInfo)
	if err != nil {
		return "", err
	}
	taskId = taskInfo.TaskId
	return taskId, nil
}

func GetRemoteAgentWorkspacePath() string {
	// 目前RemoteAgent工作目录是固定的
	return defaultRemoteAgentWorkspacePath
}

func GetLspAddr() string {
	if lspAddr != "" {
		return lspAddr
	}
	lspAddr = "localhost:8008"
	return lspAddr
}
