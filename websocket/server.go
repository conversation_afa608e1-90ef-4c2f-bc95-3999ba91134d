package websocket

import (
	"context"
	"cosy/definition"
	"cosy/log"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"sync"
	"sync/atomic"
	"time"
)

type contextKey string

const (
	ClientCtxKey contextKey = "client"
	ServerCtxKey contextKey = "server"

	// ClientTimeout IDE本地连接消息回复通常<1s，超过3s未回复可认为连接已失效
	ClientTimeout        = 3 * time.Second
	IdleTimeout          = 10 * time.Minute
	IdleUnlimitedTimeout = 7 * 24 * time.Hour
)

// ClientMessage contains a message comes from client and the client's reference
type ClientMessage struct {
	client  *Client
	message []byte
}

// Server runs as a local service which accepts websocket connections from multiple clients(IDEs)
// It parses and forwards messages from clients to handler, as well as sends messages to clients
type Server struct {
	// Register receives a new client
	register chan *Client
	// unregister receives a client to be unregistered
	unregister chan *Client
	// clients is a set of all registered clients
	clients sync.Map
	// forward is a channel receiving incoming messages from clients
	forward chan ClientMessage
	// handler handles incoming messages
	handler Handler
	// pendingMu protects the pending map
	pendingMu sync.Mutex
	// pending maps IDs of active requests to the channels receiving coressponding responses
	// It's used to dispatch IDE's response back to the caller on the server side
	pending map[ID]chan *WireResponse
	// seq is the atomic jsonrpc id used when call from server to IDE client
	// seq must only be accessed using atomic operations
	seq int64
	// Number of active client
	activeClient int
}

var WsInst CommServer

func InitWebsocketServer(handler Handler, timer *time.Timer) {
	WsInst = &Server{
		register:     make(chan *Client),
		unregister:   make(chan *Client),
		clients:      sync.Map{},
		forward:      make(chan ClientMessage),
		handler:      handler,
		pendingMu:    sync.Mutex{},
		pending:      make(map[ID]chan *WireResponse),
		seq:          0,
		activeClient: 0,
	}
	go WsInst.Run(timer)
}

// Combined has all the fields of both Request and Response.
// We can decode this and then work out which it is.
type Combined struct {
	VersionTag VersionTag       `json:"jsonrpc"`
	ID         *ID              `json:"id,omitempty"`
	Method     string           `json:"method"`
	Params     *json.RawMessage `json:"params,omitempty"`
	Result     *json.RawMessage `json:"result,omitempty"`
	Error      *Error           `json:"error,omitempty"`
}

// Run starts websocket server, this function blocks until the main process quits
func (s *Server) Run(timer *time.Timer) {
	// Root context
	// The context is used for controlling requests and responses' goroutines
	// Each request has a context with inherits root context
	ctx := context.Background()
	for {
		select {
		case client := <-s.register:
			// Register a new client
			s.clients.Store(client, true)
			// s.clients[client] = true
			s.activeClient += 1
			timer.Stop()
			log.Infof("Client %p is registered", client)
		case client := <-s.unregister:
			// Unregister an existing client
			_, ok := s.clients.Load(client)
			if ok {
				s.handler.UnregisterClient(client)
				s.clients.Delete(client)
				client.Close()
				log.Infof("Client %p is unregistered", client)
				s.activeClient -= 1
				if s.activeClient == 0 {
					// All clients are unregistered, quit after cmd.idleTimeout
					timer.Reset(IdleTimeout)
				}
			}
		case clientMessage := <-s.forward:
			// An incoming clientMessage needs to be parsed and forwarded to handler
			_, ok := s.clients.Load(clientMessage.client)
			if ok {
				// Parse raw jsonrpc messages
				rawMessage := string(clientMessage.message)
				if !strings.HasPrefix(rawMessage, "Content-Length") {
					log.Warn("Unknown message header")
					continue
				}
				// Read header
				lines := strings.Split(rawMessage, "\n")
				if len(lines) <= 1 {
					log.Warn("Invalid incoming message")
					continue
				}
				data := strings.TrimSpace(strings.Join(lines[1:], "\n"))

				// read a Combined message
				msg := &Combined{}
				if err := json.Unmarshal([]byte(data), msg); err != nil {
					log.Info("Unmarshal failed: ", err, ":\n", data)
					continue
				}

				// Work out whether this is a request or response.
				switch {
				case msg.Method != "":
					// If method is set, it must be a request.
					// concurrently process all requests, don't block here
					// move reply to another go routine
					req := &WireRequest{
						VersionTag: msg.VersionTag,
						Method:     msg.Method,
						Params:     msg.Params,
						ID:         msg.ID,
					}

					// Add client of current request and websocket server to ctx
					// This ctx is used to control goroutines for processing requests
					reqCtx := context.WithValue(ctx, ClientCtxKey, clientMessage.client)

					// Deliver requests
					go s.handler.Deliver(reqCtx, req)

				case msg.ID != nil:
					// If method is not set, this should be a response, in which case we must
					// have an id to send the response back to the caller.
					s.pendingMu.Lock()
					// rchan is the channel which receives response
					rchan, ok := s.pending[*msg.ID]
					s.pendingMu.Unlock()
					if ok {
						// Send response to the caller
						response := &WireResponse{
							Result: msg.Result,
							Error:  msg.Error,
							ID:     msg.ID,
						}
						rchan <- response
					}
				default:
					log.Error("The message is neither a request or a response, ignore it")
					continue
				}
			} else {
				log.Warn("Message comes from an unregistered client, discard it")
			}
		}
	}
}

func (s *Server) Unregister(c *Client) {
	s.unregister <- c
}

func (s *Server) Register(c *Client) {
	s.register <- c
}

func (s *Server) Forward(message ClientMessage) {
	s.forward <- message
}

func (s *Server) GetClientList() []*Client {
	var clients []*Client
	s.clients.Range(func(key, _ interface{}) bool {
		client := key.(*Client)
		clients = append(clients, client)
		return true
	})
	return clients
}

func (s *Server) GetHandler() Handler {
	return s.handler
}

func (s *Server) RemoveClients(clients []*Client) {
	for c := range clients {
		s.clients.Delete(c)
	}
}

// NotifyClient is called to send a notification request over the connection.
// It will return as soon as the notification has been sent, as no response is
// possible.
func (s *Server) NotifyClient(ctx context.Context, method string, params interface{}) (err error) {
	// Get requested client from ctx, if no client, return error
	client, ok := ctx.Value(ClientCtxKey).(*Client)
	if !ok {
		return errors.New("no client found in ctx")
	}
	return s.NotifyToClient(ctx, client, method, params)
}

func (s *Server) NotifyToClient(ctx context.Context, client *Client, method string, params interface{}) (err error) {
	_, ok := s.clients.Load(client)
	if !ok {
		return errors.New("client closes the connection without sending close message")
	}
	if client.IsClosed() {
		log.Warnf("Client %p is closed", client)
		return nil
	}

	// Build jsonrpc request
	jsonParams, err := MarshalToRaw(params)
	if err != nil {
		return fmt.Errorf("marshalling call parameters: %v", err)
	}
	request := &WireRequest{
		Method: method,
		Params: jsonParams,
	}
	// Marshal the notification body now it is complete
	data, err := json.Marshal(request)
	if err != nil {
		return fmt.Errorf("marshalling call request: %v", err)
	}

	// Add header
	msg := AddJsonrpcHeader(data)
	defer func() {
		if r := recover(); r != nil {
			log.Error("Notify client panic:", r)
		}
	}()

	// Send data to client
	client.send <- msg

	return nil
}

func (s *Server) BroadcastClient(ctx context.Context, method string, params, result interface{}) {
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("Panic recovered in BroadcastClient not ready: method: %s, params: %+v,  %v", method, params, r)
		}
	}()

	successCount := 0
	wg := sync.WaitGroup{}
	invalidClients := make([]*Client, 0)
	for _, client := range s.GetClientList() {
		wg.Add(1)
		go func(c *Client) {
			defer func() {
				if r := recover(); r != nil {
					log.Error("Recovered from panic:", r)
				}
			}()

			if err := s.RequestToWsClientWithTimeout(ctx, c, method, params, result, ClientTimeout); err != nil {
				invalidClients = append(invalidClients, c)
				log.Debugf("Failed to send request to socket client %p", c)
			} else {
				successCount++
			}
			defer wg.Done()
		}(client)
	}
	wg.Wait()
	if len(invalidClients) > 0 {
		// 移除已经失效的客户端
		s.RemoveClients(invalidClients)
	}
	log.Infof("Broadcast %d clients, %d success", len(s.GetClientList()), successCount)
}

func (s *Server) RequestClient(ctx context.Context, method string, params, result interface{}, timeout time.Duration) (err error) {
	if ctx == nil {
		return errors.New("ctx is nil")
	}
	// Get requested client from ctx, if no client, return error
	client, ok := ctx.Value(ClientCtxKey).(*Client)
	//log.Debugf("Send to client: %p", client)
	if !ok {
		return errors.New("no client found in ctx")
	}

	_, ok = s.clients.Load(client)
	if !ok {
		return errors.New("[RequestClient] client closes the connection without sending close message")
	}

	//log.Debugf("Request ws to Client: %p, method: %s", client, method)

	return s.RequestToWsClientWithTimeout(ctx, client, method, params, result, timeout)
}

// RequestToWsClient sends a request over the connection and then waits for a response.
// If the response is not an error, it will be decoded into result.
// result must be of a type you pass to json.Unmarshal.
func (s *Server) RequestToWsClientWithTimeout(ctx context.Context, client *Client, method string, params, result interface{}, timeout time.Duration) (err error) {
	if client.IsClosed() {
		log.Warnf("Client %p is closed, ignore request", client)
		return nil
	}

	// Jsonrpc request id should be atomic
	id := ID{Number: atomic.AddInt64(&s.seq, 1)}

	defer func() {
		if r := recover(); r != nil {
			log.Error("Recovered from panic:", r)
		}
	}()

	// Build jsonrpc request
	jsonParams, err := MarshalToRaw(params)
	if err != nil {
		return fmt.Errorf("marshalling call parameters: %v", err)
	}
	request := &WireRequest{
		ID:     &id,
		Method: method,
		Params: jsonParams,
	}
	// Marshal the request now it is complete
	data, err := json.Marshal(request)
	if err != nil {
		return fmt.Errorf("marshalling call request: %v", err)
	}

	// Add header
	msg := AddJsonrpcHeader(data)

	// We have to add ourselves to the pending map before we send, otherwise we
	// are racing the response. Also add a buffer to rchan, so that if we get a
	// wire response between the time this call is cancelled and id is deleted
	// from s.pending, send to rchan will not block.
	rchan := make(chan *WireResponse, 1)
	s.pendingMu.Lock()
	s.pending[id] = rchan
	s.pendingMu.Unlock()
	defer func() {
		s.pendingMu.Lock()
		delete(s.pending, id)
		s.pendingMu.Unlock()
	}()

	// Send data to client
	client.send <- msg

	// Wait for the response
	select {
	case response := <-rchan:
		if response.Error != nil {
			// If it's an error response, forward the error
			return response.Error
		}
		if result == nil || response.Result == nil {
			// BuildResult pointer is nil, just ignore the response data
			return nil
		}
		if err = json.Unmarshal(*response.Result, result); err != nil {
			return fmt.Errorf("unmarshal result: %v", err)
		}
		return nil
	case <-ctx.Done():
		// If the request is cancenlled, or timeout
		return ctx.Err()
	case <-time.After(timeout):
		// Hard timeout for requesting client
		// Without this timeout, the function will block forever if the client is offline and ctx doesn't quit correctly
		ctx.Done()
		return fmt.Errorf("no response from client, timeout reached")
	}
}

// Reply sends responses of request to given client
func (s *Server) Reply(ctx context.Context, request *WireRequest, response interface{}, responseErr error) error {
	// Get requested client from ctx, if no client, return error
	client, ok := ctx.Value(ClientCtxKey).(*Client)
	if client == nil || !ok {
		return errors.New("no client found in ctx")
	}
	if client.IsClosed() {
		log.Warnf("Client is closed, skip reply")
		return nil
	}
	_, ok = s.clients.Load(client)
	if !ok {
		return errors.New("[Reply] client closes the connection without sending close message")
	}

	// Build response
	var raw *json.RawMessage
	if responseErr == nil {
		raw, responseErr = MarshalToRaw(response)
	}
	// ID comes from request
	wireResponse := &WireResponse{
		Result: raw,
		ID:     request.ID,
	}
	// If input err is not nil, send error to client
	if responseErr != nil {
		if callErr, ok := responseErr.(*Error); ok {
			wireResponse.Error = callErr
		} else {
			wireResponse.Error = NewErrorf(0, "%s", responseErr)
		}
	}
	content, err := json.Marshal(wireResponse)
	if err != nil {
		return err
	}
	// Add header
	data := AddJsonrpcHeader(content)

	defer func() {
		if r := recover(); r != nil {
			log.Error("Recovered from ws panic:", r)
		}
	}()

	// Send data to client
	select {
	case client.send <- data:
	default:
		log.Warnf("Client %p is closed, skip reply", client)
	}
	return nil
}

func (s *Server) GetIdeInfo(client *Client) (definition.IdeConfig, error) {
	return s.handler.GetIdeInfo(client)
}

// NewErrorf builds an Error struct for the supplied message and code.
// If args is not empty, message and args will be passed to Sprintf.
func NewErrorf(code int64, format string, args ...interface{}) *Error {
	return &Error{
		Code:    code,
		Message: fmt.Sprintf(format, args...),
	}
}

func CopyContext(ctx context.Context) context.Context {
	newCtx := context.Background()
	newCtx = context.WithValue(newCtx, ClientCtxKey, ctx.Value(ClientCtxKey))
	return newCtx
}

func AddJsonrpcHeader(data []byte) (result []byte) {
	result = []byte(fmt.Sprintf("Content-Length: %v\r\n\r\n", len(data)))
	result = append(result, data...)
	return result
}

// NewWebsocketServer 创建一个新的 WebSocket 服务器实例
// now just for unit test
func NewWebsocketServer(handler Handler) *Server {
	return &Server{
		register:   make(chan *Client),
		unregister: make(chan *Client),
		forward:    make(chan ClientMessage),
		handler:    handler,
		pending:    make(map[ID]chan *WireResponse),
	}
}
