package auth

import (
	"cosy/client"
	"cosy/definition"
	"cosy/log"
	"cosy/remote"
	"encoding/json"
	"errors"
	"io"
	"net/http"
)

// cloudType	可选值：
//  ○ cloud（公有云）
//  ○ vpc（专有云）
//  ○ vip(国内专属版)
//  ○ individual_intl（国际个人版）
//  ○ dedicated_intl(国际专属版)

var defaultServerMeta = &definition.ServerMetaInfo{
	CloudType: "cloud",
}

var regionServerMeta *definition.ServerMetaInfo

func UpdateServerMeta() {
	serverMeta, err := fetchRemoteServerMeta()
	if err != nil {
		regionServerMeta = defaultServerMeta

		log.Warnf("Failed to update server meta: %v", err)
		return
	}
	regionServerMeta = &serverMeta
}

func ClearServerMeta() {
	regionServerMeta = nil
}

func GetServerMeta() *definition.ServerMetaInfo {
	if regionServerMeta == nil {
		go UpdateServerMeta()
	}
	return regionServerMeta
}

func fetchRemoteServerMeta() (definition.ServerMetaInfo, error) {
	request, err := remote.BuildBigModelSignRequest(http.MethodGet, definition.UrlPathServerMetaUriPath, nil)
	if err != nil {
		log.Error("Failed to build server meta query request: %v", err)
		return definition.ServerMetaInfo{}, err
	}

	serverMetaInfo := definition.ServerMetaInfo{}
	resp, err := client.GetDefaultClient().Do(request)
	if err != nil {
		return definition.ServerMetaInfo{}, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		log.Warnf("Failed to get server meta response: %v", resp.Status)
		return definition.ServerMetaInfo{}, errors.New("resp is not ok error")
	}

	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return definition.ServerMetaInfo{}, err
	}

	err = json.Unmarshal(bodyBytes, &serverMetaInfo)
	if err != nil {
		return definition.ServerMetaInfo{}, err
	}
	return serverMetaInfo, nil
}
