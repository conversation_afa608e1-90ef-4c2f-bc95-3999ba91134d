"use strict";(self.webpackChunkcosy_client_assets=self.webpackChunkcosy_client_assets||[]).push([[992],{35992:(e,t,r)=>{r.r(t),r.d(t,{default:()=>Wr});var n=r(44891),a=r(59461),i=r(58980),o=r(31948),c=r(3950),l=r(43356),s=r(67165),u=r(27435),m=r(99599),d=r(77368),f=r(126),p=r(80450),v=r(76528),h=r(22713),g=r(37427),y=r(37745),E=r(764),b=r(85055),w=r(14546),k=r(15720),N=r(87694),C=r(17511),S=r(87363),x=r.n(S),_=r(65871),W=r(51768),P=r(2234),O=r(16536),L=r(38883),j=r(20454),M=r(65057),I=r(17206),A=r(75375),T=r(71784),G=r(40620),B=r(74725),Z=r(15296),F=r(39607),z=r(62689),R=r(24361),D=["name"],U,V;function K(e){return K="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},K(e)}function q(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function J(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?q(Object(r),!0).forEach((function(t){H(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):q(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function H(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Y(e,t){if(null==e)return{};var r=X(e,t),n,a;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(a=0;a<i.length;a++)n=i[a],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function X(e,t){if(null==e)return{};var r={},n=Object.keys(e),a,i;for(i=0;i<n.length;i++)a=n[i],t.indexOf(a)>=0||(r[a]=e[a]);return r}function $(e,t){return ne(e)||re(e,t)||ee(e,t)||Q()}function Q(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function ee(e,t){if(e){if("string"==typeof e)return te(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?te(e,t):void 0}}function te(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function re(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n=[],a=!0,i=!1,o,c;try{for(r=r.call(e);!(a=(o=r.next()).done)&&(n.push(o.value),!t||n.length!==t);a=!0);}catch(e){i=!0,c=e}finally{try{a||null==r.return||r.return()}finally{if(i)throw c}}return n}}function ne(e){if(Array.isArray(e))return e}function ae(e){var t={};return e?(e.startsWith("?")&&(e=e.substring(1)),e.split("&").forEach((function(e){var r,n=$(e.split("="),2),a=n[0],i=n[1];a&&(t[a]=i)})),t):t}function ie(){var e=(new Date).getTime(),t;return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(t){var r=(e+16*Math.random())%16|0;return e=Math.floor(e/16),("x"===t?r:3&r|8).toString(16)}))}function oe(e,t){return 0===(null==t?void 0:t.indexOf("zh"))?V[e]:U[e]}function ce(e){function t(e,t,n){Array.isArray(e)&&(r[n][t]=e.reduce((function(e,t){return e[t.key]=t.value,e}),{}))}var r={};return e.forEach((function(e){var n=e.name,a=Y(e,D);r[n]=a,t(a.env,"env",n),t(a.headers,"headers",n)})),r}function le(e){var t=new Map(Object.entries(e)),r=Array.from(t.keys()).map((function(t){var r=e[t],n=J(J({name:t},r),{},{env:[]});return"object"!==K(r.env)||Array.isArray(r.env)||(n.env=Object.keys(r.env).map((function(e){return{key:e,value:r.env[e]}}))),"object"!==K(r.headers)||Array.isArray(r.headers)||(n.headers=Object.keys(r.headers).map((function(e){return{key:e,value:r.headers[e]}}))),n}));return r.sort((function(e,t){return e.createAt&&t.createAt?t.createAt-e.createAt:0}))}function se(e){return e.trim().startsWith("rgb")||e.trim().startsWith("hsl")?decodeURIComponent(e):/^([A-Fa-f0-9]{3}){1,2}$/.test(e)?"#".concat(e):e}function ue(e,t){if(t){var r=se(t);r&&document.documentElement.style.setProperty(e,r)}}!function(e){e.Individual="Individual",e.Business="Business",e["Enterprise Dedicated"]="Enterprise Dedicated",e["Enterprise Private"]="Enterprise Private"}(U||(U={})),function(e){e.Individual="个人版",e.Business="企业标准版",e["Enterprise Dedicated"]="企业专属版",e["Enterprise Private"]="企业私有版"}(V||(V={}));var me=function e(t){if(!t)return"";var r=t.split(""),n=[];return r.forEach((function(e){if(/[\u4e00-\u9fa5]/.test(e)){var t=(0,R.ZP)(e,{style:R.ZP.STYLE_FIRST_LETTER,heteronym:!1});n.push(t[0][0])}else/[a-zA-Z]/.test(e)&&n.push(e)})),n.join("")},de;const fe=function e(t){var r=t.edition,n=t.chat_filter,a=t.post_chat_filter,i=t.completion_filter,o=t.post_completion_filter;if(!n||!i)return null;var c={blocker:(0,W.W)("匹配规则时拦截","Block request when matching rules"),filter:(0,W.W)("匹配规则时替换内容","Replace content when matching rules"),no_ops:(0,W.W)("匹配规则时不处理","Ignore when matching rules")},l=(0,W.W)("已开启","Opened"),s=(0,W.W)("未开启","Closed");return x().createElement(x().Fragment,null,r===U["Enterprise Dedicated"]||r===U.Business||r===U["Enterprise Private"]?x().createElement("div",{className:"page-section security-filter"},x().createElement("div",{className:"page-section-header"},x().createElement("div",{className:"page-section-header-title"},(0,W.W)("安全过滤器","Security Filter")),x().createElement("div",{className:"page-section-header-desc"},(0,W.W)("安全过滤器由企业的通义灵码管理员在控制台配置。","The security filter is configured by the administrator of the  organization on the console of Lingma."))),x().createElement("div",{className:"page-section-body"},x().createElement("div",{className:"security-filter-card"},x().createElement("div",{className:"security-filter-card-left"},x().createElement("div",{className:"security-filter-card-title"},(0,W.W)("智能问答安全前置过滤器","AI Chat Security Pre-filter")),x().createElement("div",{className:"security-filter-card-desc"},n.is_configured?x().createElement(x().Fragment,null,n.is_script?(0,W.W)("自定义脚本","Customized script"):c[n.handle_strategy]||"--"):"--")),x().createElement("div",{className:"security-filter-card-right"},n.enabled?l:s)),a?x().createElement("div",{className:"security-filter-card"},x().createElement("div",{className:"security-filter-card-left"},x().createElement("div",{className:"security-filter-card-title"},(0,W.W)("智能问答安全后置过滤器","AI Chat Security Post-filter")),x().createElement("div",{className:"security-filter-card-desc"},a.is_configured?x().createElement(x().Fragment,null,a.is_script?(0,W.W)("自定义脚本","Customized script"):c[a.handle_strategy]||"--"):"--")),x().createElement("div",{className:"security-filter-card-right"},a.enabled?l:s)):null,x().createElement("div",{className:"security-filter-card"},x().createElement("div",{className:"security-filter-card-left"},x().createElement("div",{className:"security-filter-card-title"},(0,W.W)("行间生成安全前置过滤器","Inline Code Generation Security Pre-filter")),x().createElement("div",{className:"security-filter-card-desc"},i.is_configured?x().createElement(x().Fragment,null,i.is_script?(0,W.W)("自定义脚本","Customized script"):c[i.handle_strategy]||"--"):"--")),x().createElement("div",{className:"security-filter-card-right"},i.enabled?l:s)),o?x().createElement("div",{className:"security-filter-card"},x().createElement("div",{className:"security-filter-card-left"},x().createElement("div",{className:"security-filter-card-title"},(0,W.W)("行间生成安全后置过滤器","Inline Code Generation Security Post-filter")),x().createElement("div",{className:"security-filter-card-desc"},o.is_configured?x().createElement(x().Fragment,null,o.is_script?(0,W.W)("自定义脚本","Customized script"):c[o.handle_strategy]||"--"):"--")),x().createElement("div",{className:"security-filter-card-right"},o.enabled?l:s)):null)):null)};var pe;const ve=function e(t){var r=t.edition,n=t.commands,a=t.sendWSMessage,i=t.officialCommandsEnd,o=t.setOfficialCommandsEnd,c=function e(t){o(t),a("webview/profile/update/command/officialOrderEnd",{agree:t})};return x().createElement(x().Fragment,null,r===U["Enterprise Dedicated"]||r===U.Business||r===U["Enterprise Private"]?x().createElement("div",{className:"page-section customized-commands"},x().createElement("div",{className:"page-section-header"},x().createElement("div",{className:"page-section-header-title"},(0,W.W)("自定义指令","Customized Commands")),x().createElement("div",{className:"page-section-header-desc"},(0,W.W)("自定义指令由企业的通义灵码管理员在控制台配置，此处展示对你可见的指令。","The customized commands are configured by the administrator of the  organization on the console of Lingma. Here are commands for you."))),x().createElement("div",{className:"page-section-body"},x().createElement(_.Checkbox,{checked:i,onChange:c},(0,W.W)("在通义灵码官方指令之前","Before Commands Supported By Lingma")),null!=n&&n.length?n.map((function(e){return x().createElement("div",{className:"customized-commands-name",key:e.identifier},x().createElement("span",{className:"customized-commands-name-left"},e.name_left),x().createElement("span",{className:"customized-commands-name-right"},e.name_right))})):x().createElement(_.Result,{img:x().createElement("img",{className:"no-data-img",src:"/static/yunxiao-fe/cosy-client-assets/0.1.10/downloads/imgextra/i1/O1CN01RihSXp1p578wgJR6B_!!6000000005308-55-tps-240-240.svg"}),title:(0,W.W)("暂无数据","No data"),size:"small"}))):null)};var he=r(40890),ge=r(60409),ye=r(97445),Ee=r(7933),be=r(60826),we=r(69326),ke=r(25851),Ne=r(38248),Ce=r(69801),Se=r(87680),xe=r(79836),_e=r(79791);function We(e){return We="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},We(e)}function Pe(){Pe=function t(){return e};var e={},t=Object.prototype,r=t.hasOwnProperty,n="function"==typeof Symbol?Symbol:{},a=n.iterator||"@@iterator",i=n.asyncIterator||"@@asyncIterator",o=n.toStringTag||"@@toStringTag";function c(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function e(t,r,n){return t[r]=n}}function l(e,t,r,n){var a=t&&t.prototype instanceof m?t:m,i=Object.create(a.prototype),o=new N(n||[]);return i._invoke=function(e,t,r){var n="suspendedStart";return function(a,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===a)throw i;return S()}for(r.method=a,r.arg=i;;){var o=r.delegate;if(o){var c=b(o,r);if(c){if(c===u)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var l=s(e,t,r);if("normal"===l.type){if(n=r.done?"completed":"suspendedYield",l.arg===u)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(n="completed",r.method="throw",r.arg=l.arg)}}}(e,r,o),i}function s(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}e.wrap=l;var u={};function m(){}function d(){}function f(){}var p={};c(p,a,(function(){return this}));var v=Object.getPrototypeOf,h=v&&v(v(C([])));h&&h!==t&&r.call(h,a)&&(p=h);var g=f.prototype=m.prototype=Object.create(p);function y(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function E(e,t){function n(a,i,o,c){var l=s(e[a],e,i);if("throw"!==l.type){var u=l.arg,m=u.value;return m&&"object"==We(m)&&r.call(m,"__await")?t.resolve(m.__await).then((function(e){n("next",e,o,c)}),(function(e){n("throw",e,o,c)})):t.resolve(m).then((function(e){u.value=e,o(u)}),(function(e){return n("throw",e,o,c)}))}c(l.arg)}var a;this._invoke=function(e,r){function i(){return new t((function(t,a){n(e,r,t,a)}))}return a=a?a.then(i,i):i()}}function b(e,t){var r=e.iterator[t.method];if(void 0===r){if(t.delegate=null,"throw"===t.method){if(e.iterator.return&&(t.method="return",t.arg=void 0,b(e,t),"throw"===t.method))return u;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return u}var n=s(r,e.iterator,t.arg);if("throw"===n.type)return t.method="throw",t.arg=n.arg,t.delegate=null,u;var a=n.arg;return a?a.done?(t[e.resultName]=a.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,u):a:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,u)}function w(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function k(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function N(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(w,this),this.reset(!0)}function C(e){if(e){var t=e[a];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,i=function t(){for(;++n<e.length;)if(r.call(e,n))return t.value=e[n],t.done=!1,t;return t.value=void 0,t.done=!0,t};return i.next=i}}return{next:S}}function S(){return{value:void 0,done:!0}}return d.prototype=f,c(g,"constructor",f),c(f,"constructor",d),d.displayName=c(f,o,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===d||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,f):(e.__proto__=f,c(e,o,"GeneratorFunction")),e.prototype=Object.create(g),e},e.awrap=function(e){return{__await:e}},y(E.prototype),c(E.prototype,i,(function(){return this})),e.AsyncIterator=E,e.async=function(t,r,n,a,i){void 0===i&&(i=Promise);var o=new E(l(t,r,n,a),i);return e.isGeneratorFunction(r)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},y(g),c(g,o,"Generator"),c(g,a,(function(){return this})),c(g,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=[];for(var r in e)t.push(r);return t.reverse(),function r(){for(;t.length;){var n=t.pop();if(n in e)return r.value=n,r.done=!1,r}return r.done=!0,r}},e.values=C,N.prototype={constructor:N,reset:function e(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(k),!t)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=void 0)},stop:function e(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function e(t){if(this.done)throw t;var n=this;function a(e,r){return c.type="throw",c.arg=t,n.next=e,r&&(n.method="next",n.arg=void 0),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],c=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var l=r.call(o,"catchLoc"),s=r.call(o,"finallyLoc");if(l&&s){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(l){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function e(t,n){for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=n&&n<=o.finallyLoc&&(o=null);var c=o?o.completion:{};return c.type=t,c.arg=n,o?(this.method="next",this.next=o.finallyLoc,u):this.complete(c)},complete:function e(t,r){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&r&&(this.next=r),u},finish:function e(t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),k(n),u}},catch:function e(t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===t){var a=n.completion;if("throw"===a.type){var i=a.arg;k(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function e(t,r,n){return this.delegate={iterator:C(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=void 0),u}},e}function Oe(e,t,r,n,a,i,o){try{var c=e[i](o),l=c.value}catch(e){return void r(e)}c.done?t(l):Promise.resolve(l).then(n,a)}function Le(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var i=e.apply(t,r);function o(e){Oe(i,n,a,o,c,"next",e)}function c(e){Oe(i,n,a,o,c,"throw",e)}o(void 0)}))}}function je(e,t){return Ge(e)||Te(e,t)||Ie(e,t)||Me()}function Me(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Ie(e,t){if(e){if("string"==typeof e)return Ae(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Ae(e,t):void 0}}function Ae(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Te(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n=[],a=!0,i=!1,o,c;try{for(r=r.call(e);!(a=(o=r.next()).done)&&(n.push(o.value),!t||n.length!==t);a=!0);}catch(e){i=!0,c=e}finally{try{a||null==r.return||r.return()}finally{if(i)throw c}}return n}}function Ge(e){if(Array.isArray(e))return e}var Be=_.Balloon.Tooltip,Ze=function e(t){var r,n=t.sendWSMessageWithRetry,a=t.memoryId,i=window.user_id,o,c=je((0,S.useState)(!1),2),l=c[0],s=c[1],u,m=je((0,S.useState)(window.memory_count),2),d=m[0],f=m[1],p,v=je((0,S.useState)(null),2),h=v[0],g=v[1],y,E=je((0,S.useState)(!0),2),b=E[0],w=E[1],k=(0,S.useRef)(!0),N=(0,S.useCallback)(function(){var e=Le(Pe().mark((function e(t){var r,a;return Pe().wrap((function e(o){for(;;)switch(o.prev=o.next){case 0:return o.next=2,n("webview/memory/list",{UserId:i,page:t,pageSize:20});case 2:r=o.sent,a=r.result,g(a),k.current&&(w(!1),k.current=!1);case 6:case"end":return o.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),[n,i]),C=(0,S.useCallback)(Le(Pe().mark((function e(){var t,r;return Pe().wrap((function e(a){for(;;)switch(a.prev=a.next){case 0:return a.next=2,n("webview/memory/count",{UserId:i});case 2:t=a.sent,r=t.result,f(r.total);case 5:case"end":return a.stop()}}),e)}))),[n,i]),_=(0,S.useCallback)((function(e){N(e)}),[N]),P=(0,S.useCallback)(function(){var e=Le(Pe().mark((function e(t){var r,a;return Pe().wrap((function e(i){for(;;)switch(i.prev=i.next){case 0:return i.next=2,n("webview/memory/delete",{ids:[t]});case 2:r=i.sent,(a=r.result).success&&N((null==h?void 0:h.page)||1);case 5:case"end":return i.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),[N,null==h?void 0:h.page,n]),O=(0,S.useCallback)((function(){if(a){var e=document.getElementById(a);e&&e.scrollIntoView({behavior:"smooth"})}}),[a]),L=function e(){window.parent.postMessage({type:"MSG_BACK_TO_SETTING"},"*")};return(0,S.useEffect)((function(){C()}),[C]),(0,S.useEffect)((function(){l?N(1):C()}),[C,N,O,l]),(0,S.useEffect)((function(){a&&s(!0)}),[a]),(0,S.useEffect)((function(){null!=h&&h.records.length&&a&&O()}),[h,a,O]),x().createElement("div",{className:"page-section memories"},x().createElement("div",{className:"page-section-header"},x().createElement("div",{className:"page-section-header-title"},(0,W.W)("记忆管理","Memories"),x().createElement(we.Z,{bordered:!1,className:"memories-tag"},(0,W.W)("预览","Preview"))),x().createElement("div",{className:"page-section-header-desc"},(0,W.W)("记忆是自动从使用灵码的过程中不断积累的，可以帮助灵码更好地和你互动。它贯穿于你和灵码的对话中，并且随着时间流逝，也能够让灵码越来越懂你。","Memories are context that accumulate automatically to help Lingma respond to you better. They persist across all your conversations with Lingma and allow it to know you better over time."))),x().createElement("div",{className:"memories-add-button",onClick:function e(){s(!0)}},x().createElement("span",null,d," ",(0,W.W)("条记忆","Memories")),x().createElement(he.G_,{type:"arrow-right-2-line"})),l&&x().createElement("div",{className:"memories-detail"},x().createElement("div",{className:"page-header memories-detail-header"},x().createElement("div",null,x().createElement("span",{onClick:function e(){s(!1)},className:"memories-detail-header-back"},x().createElement(he.G_,{type:"arrow-left-2-line"}),(0,W.W)("返回","Back")),x().createElement("span",{className:"memories-detail-header-title"},(0,W.W)("记忆管理","Memories"))),x().createElement(ke.ZP,{type:"link",size:"small",onClick:L},(0,W.W)("前往设置","Go to settings"))),null!=h&&null!==(r=h.records)&&void 0!==r&&r.length?x().createElement(x().Fragment,null,x().createElement(Ne.Z,{className:"memories-list",bordered:!1,accordion:!0,defaultActiveKey:a?[a]:[],ghost:!0,expandIcon:function e(t){var r=t.isActive;return x().createElement(he.G_,{style:{transform:r?"rotate(90deg)":""},type:"arrow-right-2-line"})},items:((null==h?void 0:h.records)||[]).map((function(e){return{key:e.id,label:x().createElement(ze,{handleDelete:P,memory:e}),style:{marginBottom:8,border:"none",borderRadius:8},children:x().createElement(Re,{memory:e})}}))})):x().createElement("div",{className:"result-content"},b?x().createElement(Ce.Z,{size:"large"}):x().createElement(Se.ZP,{icon:x().createElement("img",{className:"no-data-img",src:"/static/yunxiao-fe/cosy-client-assets/0.1.10/downloads/imgextra/i1/O1CN01RihSXp1p578wgJR6B_!!6000000005308-55-tps-240-240.svg"}),title:(0,W.W)("暂无记忆","No memories yet")})),h&&(null==h?void 0:h.total)>h.pageSize&&x().createElement(xe.Z,{align:"center",current:h.page,className:"memories-detail-pagination",onChange:_,pageSize:h.pageSize,showSizeChanger:!1,total:h.total})))};const Fe=Ze;var ze=function e(t){var r,n=t.memory,a=t.handleDelete,i=n.scopeId!==window.workspace_path&&"global"!==(null===(r=n.scopeId)||void 0===r?void 0:r.toLocaleLowerCase());return x().createElement("div",{id:n.id,className:i?"memories-change-color memories-list-item":"memories-list-item"},x().createElement("span",{className:"memories-list-item-title"},n.title||"-"),x().createElement("div",{className:"memories-list-item-setup"},x().createElement(Be,{trigger:x().createElement(he.G_,{type:"delete-line",className:"setup-del-icon",onClick:function e(){return a(n.id)}}),align:"t"},(0,W.W)("删除","Delete"))))},Re=function e(t){var r,n=t.memory,a=n.scopeId!==window.workspace_path&&"global"!==(null===(r=n.scopeId)||void 0===r?void 0:r.toLocaleLowerCase());return x().createElement("div",{className:a?"memories-change-color":""},x().createElement("div",{className:"memories-item-detail-content"},x().createElement("div",{className:"memories-item-detail-header"},(0,W.W)("范围","Scope")),x().createElement("p",{className:"memories-item-detail-inner"},n.scopeId||"-")),x().createElement("div",{className:"memories-item-detail-content"},x().createElement("div",{className:"memories-item-detail-header"},(0,W.W)("关键词","Keywords")),x().createElement("p",{className:"memories-item-detail-inner"},n.keywords||"-")),x().createElement("div",{className:"memories-item-detail-content"},x().createElement("div",{className:"memories-item-detail-header"},(0,W.W)("记忆内容","Content")),x().createElement("p",{className:"memories-item-detail-inner memories-item-detail-markdown"},x().createElement(_e.UG,null,n.content))))},De=r(40398),Ue=r(433),Ve=r(24728),Ke=r(85544),qe=r(20758),Je=r(67553),He=r(49243),Ye=r(80709),Xe=r(79477),$e=r(55768),Qe=r(35028),et=r(34555),tt=r(91526),rt=r(50712),nt=r(99317);function at(e){return at="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},at(e)}var it=["key","name"];function ot(){return ot=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ot.apply(this,arguments)}function ct(e,t){if(null==e)return{};var r=lt(e,t),n,a;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(a=0;a<i.length;a++)n=i[a],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function lt(e,t){if(null==e)return{};var r={},n=Object.keys(e),a,i;for(i=0;i<n.length;i++)a=n[i],t.indexOf(a)>=0||(r[a]=e[a]);return r}function st(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function ut(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?st(Object(r),!0).forEach((function(t){mt(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):st(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function mt(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function dt(e){return vt(e)||pt(e)||wt(e)||ft()}function ft(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function pt(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function vt(e){if(Array.isArray(e))return kt(e)}function ht(){ht=function t(){return e};var e={},t=Object.prototype,r=t.hasOwnProperty,n="function"==typeof Symbol?Symbol:{},a=n.iterator||"@@iterator",i=n.asyncIterator||"@@asyncIterator",o=n.toStringTag||"@@toStringTag";function c(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function e(t,r,n){return t[r]=n}}function l(e,t,r,n){var a=t&&t.prototype instanceof m?t:m,i=Object.create(a.prototype),o=new N(n||[]);return i._invoke=function(e,t,r){var n="suspendedStart";return function(a,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===a)throw i;return S()}for(r.method=a,r.arg=i;;){var o=r.delegate;if(o){var c=b(o,r);if(c){if(c===u)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var l=s(e,t,r);if("normal"===l.type){if(n=r.done?"completed":"suspendedYield",l.arg===u)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(n="completed",r.method="throw",r.arg=l.arg)}}}(e,r,o),i}function s(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}e.wrap=l;var u={};function m(){}function d(){}function f(){}var p={};c(p,a,(function(){return this}));var v=Object.getPrototypeOf,h=v&&v(v(C([])));h&&h!==t&&r.call(h,a)&&(p=h);var g=f.prototype=m.prototype=Object.create(p);function y(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function E(e,t){function n(a,i,o,c){var l=s(e[a],e,i);if("throw"!==l.type){var u=l.arg,m=u.value;return m&&"object"==at(m)&&r.call(m,"__await")?t.resolve(m.__await).then((function(e){n("next",e,o,c)}),(function(e){n("throw",e,o,c)})):t.resolve(m).then((function(e){u.value=e,o(u)}),(function(e){return n("throw",e,o,c)}))}c(l.arg)}var a;this._invoke=function(e,r){function i(){return new t((function(t,a){n(e,r,t,a)}))}return a=a?a.then(i,i):i()}}function b(e,t){var r=e.iterator[t.method];if(void 0===r){if(t.delegate=null,"throw"===t.method){if(e.iterator.return&&(t.method="return",t.arg=void 0,b(e,t),"throw"===t.method))return u;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return u}var n=s(r,e.iterator,t.arg);if("throw"===n.type)return t.method="throw",t.arg=n.arg,t.delegate=null,u;var a=n.arg;return a?a.done?(t[e.resultName]=a.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,u):a:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,u)}function w(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function k(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function N(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(w,this),this.reset(!0)}function C(e){if(e){var t=e[a];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,i=function t(){for(;++n<e.length;)if(r.call(e,n))return t.value=e[n],t.done=!1,t;return t.value=void 0,t.done=!0,t};return i.next=i}}return{next:S}}function S(){return{value:void 0,done:!0}}return d.prototype=f,c(g,"constructor",f),c(f,"constructor",d),d.displayName=c(f,o,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===d||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,f):(e.__proto__=f,c(e,o,"GeneratorFunction")),e.prototype=Object.create(g),e},e.awrap=function(e){return{__await:e}},y(E.prototype),c(E.prototype,i,(function(){return this})),e.AsyncIterator=E,e.async=function(t,r,n,a,i){void 0===i&&(i=Promise);var o=new E(l(t,r,n,a),i);return e.isGeneratorFunction(r)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},y(g),c(g,o,"Generator"),c(g,a,(function(){return this})),c(g,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=[];for(var r in e)t.push(r);return t.reverse(),function r(){for(;t.length;){var n=t.pop();if(n in e)return r.value=n,r.done=!1,r}return r.done=!0,r}},e.values=C,N.prototype={constructor:N,reset:function e(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(k),!t)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=void 0)},stop:function e(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function e(t){if(this.done)throw t;var n=this;function a(e,r){return c.type="throw",c.arg=t,n.next=e,r&&(n.method="next",n.arg=void 0),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],c=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var l=r.call(o,"catchLoc"),s=r.call(o,"finallyLoc");if(l&&s){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(l){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function e(t,n){for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=n&&n<=o.finallyLoc&&(o=null);var c=o?o.completion:{};return c.type=t,c.arg=n,o?(this.method="next",this.next=o.finallyLoc,u):this.complete(c)},complete:function e(t,r){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&r&&(this.next=r),u},finish:function e(t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),k(n),u}},catch:function e(t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===t){var a=n.completion;if("throw"===a.type){var i=a.arg;k(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function e(t,r,n){return this.delegate={iterator:C(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=void 0),u}},e}function gt(e,t,r,n,a,i,o){try{var c=e[i](o),l=c.value}catch(e){return void r(e)}c.done?t(l):Promise.resolve(l).then(n,a)}function yt(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var i=e.apply(t,r);function o(e){gt(i,n,a,o,c,"next",e)}function c(e){gt(i,n,a,o,c,"throw",e)}o(void 0)}))}}function Et(e,t){return Ct(e)||Nt(e,t)||wt(e,t)||bt()}function bt(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function wt(e,t){if(e){if("string"==typeof e)return kt(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?kt(e,t):void 0}}function kt(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Nt(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n=[],a=!0,i=!1,o,c;try{for(r=r.call(e);!(a=(o=r.next()).done)&&(n.push(o.value),!t||n.length!==t);a=!0);}catch(e){i=!0,c=e}finally{try{a||null==r.return||r.return()}finally{if(i)throw c}}return n}}function Ct(e){if(Array.isArray(e))return e}var St=Xe.Z.Search,xt=_.Balloon.Tooltip,_t=10,Wt=["#5FB3A9","#E88C45","#72BCF5","#E593BC","#8080FF"],Pt=(0,S.forwardRef)((function(e,t){var r,n,a,i,o,c,l=e.showMarketplaceFilterTag,s=e.sendWSMessageWithRetry,u=e.jumpMarketplace,m=e.setMcpListViewTab,d=e.setMcpActiveKey,f=e.userConfig,p=[{value:"browser-automation",label:(0,W.W)("浏览器自动化","browser-automation")},{value:"search",label:(0,W.W)("搜索工具","search")},{value:"communication",label:(0,W.W)("交流协作工具","communication")},{value:"developer-tools",label:(0,W.W)("开发者工具","developer-tools")},{value:"entertainment-and-media",label:(0,W.W)("娱乐与多媒体","entertainment-and-media")},{value:"file-systems",label:(0,W.W)("文件系统","file-systems")},{value:"finance",label:(0,W.W)("金融","finance")},{value:"knowledge-and-memory",label:(0,W.W)("知识管理与记忆","knowledge-and-memory")},{value:"location-services",label:(0,W.W)("位置服务","location-services")},{value:"art-and-culture",label:(0,W.W)("文化与艺术","art-and-culture")},{value:"research-and-data",label:(0,W.W)("学术研究","research-and-data")},{value:"calendar-management",label:(0,W.W)("日程管理","calendar-management")},{value:"other",label:(0,W.W)("其他","other")}],v,h=Et(Ke.Z.useModal(),2),g=h[0],y=h[1],E,b=Et((0,S.useState)(!0),2),w=b[0],k=b[1],N,C=Et((0,S.useState)(""),2),_=C[0],P=C[1],O,L=Et((0,S.useState)(!0),2),j=L[0],M=L[1],I,A=Et((0,S.useState)(!0),2),T=A[0],G=A[1],B,Z=Et((0,S.useState)({marketplaceList:[],total:0}),2),F=Z[0],z=Z[1],R,D=Et((0,S.useState)([]),2),U=D[0],V=D[1],K,q=Et((0,S.useState)(null),2),J=q[0],H=q[1],Y,X=Et((0,S.useState)(!1),2),$=X[0],Q=X[1],ee,te=Et((0,S.useState)(!1),2),re=te[0],ne=te[1],ae=(0,S.useRef)(null),ie=(0,S.useRef)(1),oe=(0,S.useRef)(J),ce,le,se=Et(He.Z.useForm(),1)[0];(0,S.useImperativeHandle)(t,(function(){return{filterSelectedTag:J}})),(0,S.useEffect)((function(){ue()}),[]),(0,S.useEffect)((function(){var e;(null===(e=F.marketplaceList)||void 0===e?void 0:e.length)===F.total?G(!1):G(!0)}),[F]);var ue=(0,S.useCallback)(yt(ht().mark((function e(){return ht().wrap((function e(t){for(;;)switch(t.prev=t.next){case 0:k(!0),s("webview/mcpMarket/listRecommended",{}).then((function(e){var t=e.result;k(!1),t.success&&V(t.items)})).catch((function(){ne(!0),k(!1)}));case 2:case"end":return t.stop()}}),e)}))),[s]),de=(0,S.useCallback)((function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;M(!1),1===e&&k(!0),1===e&&z({marketplaceList:[],total:0}),s("webview/mcpMarket/searchMarket",{page:e,pageSize:_t,query:_,categories:oe.current||""}).then((function(t){k(!1);var r=t.result;null!=r&&r.success?z({marketplaceList:1===e?r.items:[].concat(dt(F.marketplaceList),dt(r.items)),total:r.total}):z({marketplaceList:[],total:0})})).catch((function(){ne(!0),k(!1)}))}),[F.marketplaceList,_,s]),fe=function(){var e=yt(ht().mark((function e(){var t,r;return ht().wrap((function e(n){for(;;)switch(n.prev=n.next){case 0:if(T){n.next=2;break}return n.abrupt("return");case 2:return t=document.querySelector(".marketplace-list"),r=(null==t?void 0:t.scrollTop)||0,ie.current++,n.next=7,de(ie.current);case 7:setTimeout((function(){t&&(t.scrollTop=r)}),0);case 8:case"end":return n.stop()}}),e)})));return function t(){return e.apply(this,arguments)}}(),pe=(0,S.useCallback)((function(e){ie.current=1,H(e.target.value),oe.current=e.target.value,de(1)}),[de]),ve=(0,S.useCallback)((function(e){e.preventDefault(),oe.current=null,ie.current=1,H(null),de(1)}),[de]),ge=(0,S.useMemo)((function(){return!w&&U.length>0&&j}),[j,w,U.length]),ye=(0,S.useCallback)((function(){var e=dt(j?U:F.marketplaceList),t=ae.current;void 0!==(null==t?void 0:t.index)&&(e[t.index].loading=!1),j?V(dt(e)):z(ut(ut({},F),{},{marketplaceList:dt(e)}))}),[j,F,U]),Ee=(0,S.useCallback)((function(e){var t=e.title,r=void 0===t?(0,W.W)("自动安装失败","Installation Failed"):t,n=e.content,a=e.isShowDetails;g.confirm({icon:x().createElement(he.G_,{type:"information-line"}),wrapClassName:"error-modal",title:r,content:x().createElement("span",null,n),footer:[x().createElement(ke.ZP,{key:"confirm",type:"primary",onClick:function e(){Ke.Z.destroyAll(),ye()}},(0,W.W)("确认","Confirm")),a?x().createElement(ke.ZP,{key:"details",type:"text",onClick:function e(){var t;null!==(t=ae.current)&&void 0!==t&&t.marketUrl&&(null==u||u(ae.current.marketUrl))},icon:x().createElement(he.G_,{type:"signout-line"})},(0,W.W)("查看详情","View Details")):null]})}),[g,ye]),be=(0,S.useCallback)((function(e){var t;e.success?(null==m||m("MyMCPs"),null==d||d((null===(t=ae.current)||void 0===t?void 0:t.serverId)||null)):e.success||("52501"===String(e.errorCode)?(se.setFieldValue("env",Pe(e.env)),Q(!0)):["52404","52401"].includes(String(e.errorCode))?Ee({content:(0,W.W)("服务配置解析异常，请手动添加该服务","Server config retrieval or parsing failed. Please add it manually."),isShowDetails:!0}):"52502"===String(e.errorCode)?Ee({content:(0,W.W)("该服务已存在，请勿重复安装","Server already exists. Do not reinstall."),isShowDetails:!1}):Ee({content:(0,W.W)("系统出现异常，请稍后再试或前往社区查看服务详情。","A system error occurred. Please try again later or view details for more information."),isShowDetails:!0}))}),[Ee,se,m]),Ne=(0,S.useCallback)(function(){var e=yt(ht().mark((function e(t){var r,n;return ht().wrap((function e(a){for(;;)switch(a.prev=a.next){case 0:return a.next=2,s("webview/mcpMarket/install",{marketType:t.marketType,serverId:t.serverId});case 2:r=a.sent,n=r.result,be(n);case 5:case"end":return a.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),[be,s]),xe=function(){var e=yt(ht().mark((function e(){var t,r,n,a;return ht().wrap((function e(i){for(;;)switch(i.prev=i.next){case 0:return i.next=2,s("webview/mcpMarket/installWithEnv",{marketType:null===(t=ae.current)||void 0===t?void 0:t.marketType,serverId:null===(r=ae.current)||void 0===r?void 0:r.serverId,env:Oe(se.getFieldValue("env"))});case 2:n=i.sent,(a=n.result).success&&Q(!1),be(a);case 6:case"end":return i.stop()}}),e)})));return function t(){return e.apply(this,arguments)}}(),_e=function e(t,r){return x().createElement(ke.ZP,{type:"primary",className:"install ".concat(null!=f&&f.userConfigError?"installed":""),loading:t.loading,onClick:function e(){var n;if(null==f||!f.userConfigError){var a=dt(j?U:F.marketplaceList);null!=a&&null!==(n=a.filter((function(e){return e.loading})))&&void 0!==n&&n.length||(a[r].loading=!0,j?V(a):z(ut(ut({},F),{},{marketplaceList:dt(a)})),ae.current={marketType:t.marketType,serverId:t.serverId,index:r,marketUrl:t.marketUrl,serverName:t.serverName,serverNameEn:t.serverNameEn},Ne(t))}}},(0,W.W)("安装","Install"))},We=(0,S.useCallback)((function(e,t){return x().createElement(tt.Z,{className:"marketplace-list-item",key:"".concat(e.serverId,"-").concat(t)},x().createElement("div",{className:"marketplace-list-item-top"},x().createElement("div",{className:"marketplace-list-item-top-left"},e.logoUrl?x().createElement(rt.Z,{className:"marketplace-list-item-top-left-avatar",src:e.logoUrl,alt:"MasterGo Logo",onError:function e(){var r=document.querySelectorAll(".marketplace-list-item-top-left-avatar");"IMG"===r[t].children[0].tagName&&(r[t].children[0].style.display="none"),r[t].style.background=Wt[t%Wt.length]}},me((0,W.W)(e.serverName,e.serverNameEn)).slice(0,1)):x().createElement("div",{className:"marketplace-list-item-top-left-avatar",style:{background:Wt[t%Wt.length],display:"flex",justifyContent:"center",alignItems:"center",fontSize:18,fontWeight:600,color:"#26282B"}},me((0,W.W)(e.serverName,e.serverNameEn)).slice(0,1)),x().createElement("div",{className:"marketplace-list-item-top-left-info"},x().createElement("div",{className:"marketplace-list-item-top-left-info-title"},x().createElement("span",{className:"title",onClick:function t(){null==u||u(e.marketUrl)}},(0,W.W)(e.serverName,e.serverNameEn)),e.recommend&&x().createElement("img",{className:"recommendTag",src:(0,W.a)()?"/static/yunxiao-fe/cosy-client-assets/0.1.10/downloads/imgextra/i2/O1CN013ygNjA1qokReQUoj2_!!6000000005543-2-tps-136-32.png":"/static/yunxiao-fe/cosy-client-assets/0.1.10/downloads/imgextra/i2/O1CN01waFFzv27EQVFjsHah_!!6000000007765-2-tps-92-32.png"})),x().createElement("div",{className:"marketplace-list-item-top-left-info-desc"},x().createElement("span",{className:"tag"},e.author),x().createElement(he.G_,{type:"visible-line"})," ",e.viewCount))),"uninstalled"===e.installStatus?e.loading?x().createElement(ke.ZP,{type:"primary",className:"install",loading:e.loading},(0,W.W)("安装中","Installing")):null!=f&&f.userConfigError?x().createElement(xt,{trigger:_e(e,t),align:"tr"},(0,W.W)("服务配置文件异常，请修复问题后再试","Please fix the configuration issues first")):_e(e,t):x().createElement(xt,{trigger:x().createElement(ke.ZP,{type:"primary",className:"install installed"},(0,W.W)("已安装","Installed")),align:"tr"},(0,W.W)("该服务已安装","Already installed"))),x().createElement("div",{className:"marketplace-list-item-desc"},(0,W.W)(e.description,e.descriptionEn)))}),[j,U,F,Ne]),Pe=function e(t){return Object.entries(t).map((function(e){var t=Et(e,2),r,n;return{key:t[0],value:t[1]}}))},Oe=function e(t){return t.reduce((function(e,t){var r=t.key,n=t.value;return e[r]=n,e}),{})};return x().createElement("div",{className:"marketplace"},x().createElement(St,{placeholder:(0,W.W)("搜索 MCP 服务","Search MCP Server"),onSearch:function e(){de(1)},className:"marketplace-search",prefix:x().createElement(he.G_,{type:"search-line"}),value:_,onChange:function e(t){ie.current=1,P(t.target.value)}}),l&&x().createElement($e.ZP.Group,{onChange:function e(t){pe(t)},className:"marketplace-tag-list",value:J},p.map((function(e){return x().createElement($e.ZP.Button,{className:"marketplace-tag-item",key:e.value,value:e.value,onClick:function t(){J===e.value&&(ie.current=1,oe.current="",de(1),H(""))}},e.label)}))),J&&!l&&x().createElement("div",{className:"marketplace-filter"},x().createElement("span",{className:"marketplace-filter-text"},(0,W.W)("筛选项为","Filtered by")),x().createElement(we.Z,{closeIcon:!0,onClose:ve},null===(r=p.filter((function(e){return e.value===J})))||void 0===r||null===(n=r[0])||void 0===n?void 0:n.label)),x().createElement("div",{id:"marketplace-scroll-container",className:"marketplace-list ".concat(j?"recommend-list":"")},re?x().createElement(Se.ZP,{icon:x().createElement("img",{className:"no-data-img",src:"/static/yunxiao-fe/cosy-client-assets/0.1.10/downloads/imgextra/i1/O1CN01RihSXp1p578wgJR6B_!!6000000005308-55-tps-240-240.svg"}),title:x().createElement("span",null,(0,W.W)("页面加载异常，请从个人设置页面重新进入 MCP 广场后再试。","Page loading error. Please try re-entering the MCP Square from the Personal Settings page."))}):x().createElement(x().Fragment,null,w&&x().createElement(Ce.Z,{size:"large"}),j&&null!=U&&U.length?x().createElement("div",{style:{overflow:"auto",height:"100%",padding:"0 16px"}},null!=U&&U.length?x().createElement("span",{className:"marketplace-recommended"},(0,W.W)("推荐","Top Pick")," · ",U.length):null,U.map((function(e,t){return We(e,t)})),ge&&x().createElement("div",{className:"footer-more",onClick:function e(){M(!1),de(1)}},x().createElement(he.G_,{type:"arrow-down-2-line"}),x().createElement("span",{className:"footer-more-text"},(0,W.W)("查看更多","View More")),x().createElement("div",{className:"footer-more-img"},["/static/yunxiao-fe/cosy-client-assets/0.1.10/downloads/imgextra/i2/O1CN01P634NY217CYmJsnfI_!!6000000006937-2-tps-225-225.png","/static/yunxiao-fe/cosy-client-assets/0.1.10/downloads/imgextra/i4/O1CN01X3eUBU1qdkwGGSlWS_!!6000000005519-2-tps-1088-1088.png","/static/yunxiao-fe/cosy-client-assets/0.1.10/downloads/imgextra/i3/O1CN01jAeimC1oatE7z2EVZ_!!6000000005242-2-tps-225-225.png"].map((function(e,t){return x().createElement("div",{key:t},x().createElement("img",{src:e}))}))))):(null===(a=F.marketplaceList)||void 0===a?void 0:a.length)>0&&x().createElement(nt.Z,{dataLength:F.marketplaceList.length,next:fe,hasMore:F.marketplaceList.length<F.total,loader:x().createElement(Ce.Z,null),scrollableTarget:"marketplace-scroll-container",style:{overflow:"visible",padding:"0 16px"}},F.marketplaceList.map((function(e,t){return We(e,t)}))),!w&&(j?!U.length:!(null!==(i=F.marketplaceList)&&void 0!==i&&i.length))&&x().createElement(Se.ZP,{icon:x().createElement("img",{className:"no-data-img",src:"/static/yunxiao-fe/cosy-client-assets/0.1.10/downloads/imgextra/i1/O1CN01RihSXp1p578wgJR6B_!!6000000005308-55-tps-240-240.svg"}),title:(0,W.W)("暂无相关内容，请稍后再试","No relevant content available, please try again later")}))),x().createElement("span",{className:"footer"},(0,W.W)("由魔搭社区提供技术支持","Powered by ModelScope")),x().createElement(Ke.Z,{open:$,title:(0,W.W)("".concat(null===(o=ae.current)||void 0===o?void 0:o.serverName," MCP 服务安装"),"".concat(null===(c=ae.current)||void 0===c?void 0:c.serverNameEn," MCP Server Installation")),onOk:xe,cancelText:(0,W.W)("取 消","Cancel"),okText:(0,W.W)("立即添加","Add"),classNames:{content:"install-modal"},getContainer:!1,onCancel:function e(){Q(!1),ye()}},x().createElement("span",{className:"install-modal-tip"},(0,W.W)("请前往对应三方服务平台获取所需信息，再完成以下配置。","Get the required info from the third-party platform, then complete the configuration below."),x().createElement("a",{href:"javascript:;",onClick:function e(){null==u||u(ae.current.marketUrl)}},x().createElement(he.G_,{type:"signout-line",style:{marginRight:4}}),(0,W.W)("查看服务详情","View details"))),x().createElement(He.Z,{form:se,layout:"vertical",colon:!1},x().createElement(He.Z.Item,{name:"env",label:(0,W.W)("环境变量","Environment")},x().createElement(Ye.Z,{style:{display:"flex",marginBottom:8},align:"baseline"},x().createElement("span",{className:"label"},"key"),x().createElement("span",{className:"label"},"value")),x().createElement(He.Z.List,{name:"env"},(function(e){return x().createElement(x().Fragment,null,e.map((function(e){var t=e.key,r=e.name,n=ct(e,it);return x().createElement(Ye.Z,{key:t,style:{display:"flex"},align:"baseline"},x().createElement(He.Z.Item,ot({},n,{name:[r,"key"]}),x().createElement(Xe.Z,{disabled:!0})),x().createElement(He.Z.Item,ot({},n,{name:[r,"value"]}),x().createElement(Xe.Z,null)))})))}))))),y)}));const Ot=Pt;var Lt=function e(){return x().createElement("svg",{className:"icon",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16"},x().createElement("path",{d:"M264.1408 887.2448v-452.096H344.064v412.0576h481.1776V432.6912h-201.216a39.9872 39.9872 0 0 1-40.0384-38.912v-217.088h-68.7616L430.3872 96.6656h186.4192a39.9872 39.9872 0 0 1 28.4672 11.9296l248.4736 251.4432a39.9872 39.9872 0 0 1 11.52 28.16v499.0464c0 22.0672-17.92 39.9872-39.9872 39.9872H304.128a39.9872 39.9872 0 0 1-39.9872-39.9872z m-37.4784-368.384C158.3104 493.2608 122.88 432.64 122.88 359.936c0-46.2336 19.456-84.2752 51.5072-110.7456 32-26.4704 76.4928-41.3696 126.5664-41.3696l4.4032-0.0512h58.3168l-50.688-50.9952 49.664-49.3056 108.4416 109.1584 0.768 0.768a34.9696 34.9696 0 0 1-0.1536 47.9232L363.264 380.1088l-50.8416-48.0768 51.3024-54.272h-58.368c-32.768 0-59.5968 4.8128-80.0768 19.2-20.48 14.336-34.6624 38.2976-34.6624 63.8976q0 48.7424 35.9936 75.0592v82.944z m437.3504-277.504v111.3088h109.9776l-109.9776-111.2576z","p-id":"13846"}))},jt=function e(){return x().createElement("svg",{className:"icon",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16"},x().createElement("path",{d:"M480 160v153.6a32 32 0 1 0 64 0v-153.6a32 32 0 1 0-64 0z m0 550.4v153.6a32 32 0 1 0 64 0v-153.6a32 32 0 1 0-64 0z m384-230.4h-153.6a32 32 0 1 0 0 64h153.6a32 32 0 1 0 0-64z m-550.4 0h-153.6a32 32 0 1 0 0 64h153.6a32 32 0 1 0 0-64z m469.9136 258.2528l-108.5952-108.544a32 32 0 1 0-45.2608 45.2096l108.5952 108.5952a32 32 0 0 0 45.2608-45.2608zM394.3424 349.0816L285.696 240.4864a32 32 0 1 0-45.2608 45.2608L349.0816 394.24a32 32 0 1 0 45.2608-45.2096z m343.9104-108.5952L629.76 349.0816a32 32 0 0 0 45.2096 45.2608l108.5952-108.5952a32 32 0 0 0-45.2608-45.2608z m-389.1712 389.1712L240.4864 738.304a32 32 0 0 0 45.2608 45.2608l108.544-108.5952a32 32 0 0 0-45.2096-45.2608z","p-id":"13846"}))},Mt=function e(){return x().createElement("svg",{className:"icon",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"13987",width:"16",height:"16"},x().createElement("path",{d:"M913.6128 512a401.6128 401.6128 0 1 1-803.2256 0 401.6128 401.6128 0 0 1 803.2256 0z m-80.0256 0A321.5872 321.5872 0 1 0 190.464 512a321.5872 321.5872 0 0 0 643.1744 0z m-336.384 118.6816L692.4288 440.32l-53.6576-54.9376L470.528 549.376 386.9696 467.0464 333.056 521.728l110.3872 108.8a38.4 38.4 0 0 0 53.76 0.1536z","p-id":"13988"}))},It=function e(){return x().createElement("svg",{className:"icon",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"13912",width:"16",height:"16"},x().createElement("path",{d:"M889.17504 418.18112l0.34816-222.06464a40.96 40.96 0 0 0-40.22784-41.01632l-218.624-3.89632a61.44 61.44 0 0 0-43.74528 17.20832l-71.5776 69.04832-237.53728-41.6256a20.48 20.48 0 0 0-18.432 6.10816L159.91808 307.30752a20.48 20.48 0 0 0 0.4096 28.53888L284.5696 460.09344l-58.89024 56.8064a20.48 20.48 0 0 0-0.512 28.96896l255.19104 264.25856a20.48 20.48 0 0 0 28.9536 0.512l64.03072-61.7728 122.5728 122.56768a20.48 20.48 0 0 0 29.13792-0.1792l101.9392-104.48896a20.46976 20.46976 0 0 0 5.72416-16.25088l-22.07744-230.57408 59.7504-57.64096a61.42976 61.42976 0 0 0 18.7904-44.11904zM807.54176 236.288l-0.27136 173.0816-310.64064 299.6736-169.8304-175.86176 310.89664-299.91936 169.84576 3.02592zM343.53664 403.20512L260.46464 320.13312l35.78368-37.90336 146.14016 25.61536L343.53664 403.2z m366.31552 366.32064l-77.5424-77.5424 102.99392-99.36384 13.14816 137.33888-38.59968 39.56736zM174.29504 626.75968l21.71904-21.72416 43.44832 43.44832-21.72416 21.72416-28.96384 28.95872a30.70976 30.70976 0 0 1-43.4432 0 30.72 30.72 0 0 1 0-43.4432l28.96384-28.96384z m83.26656 83.27168l25.344-25.344 50.688 50.688-25.344 25.33888-72.41216 72.40704a35.84 35.84 0 0 1-50.688 0 35.84 35.84 0 0 1 0-50.688l72.41216-72.40192z m90.51136 90.50624l21.71904-21.71904 43.44832 43.4432-21.72416 21.72416-28.96384 28.96384a30.72 30.72 0 0 1-43.4432-43.44832l28.96384-28.96384z","p-id":"13913"}))};function At(e){return At="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},At(e)}var Tt=["onConfigChange","onEdit","currentAction","configHasError","setShowDetail"],Gt=["key","name"];function Bt(e,t){if(null==e)return{};var r=Zt(e,t),n,a;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(a=0;a<i.length;a++)n=i[a],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function Zt(e,t){if(null==e)return{};var r={},n=Object.keys(e),a,i;for(i=0;i<n.length;i++)a=n[i],t.indexOf(a)>=0||(r[a]=e[a]);return r}function Ft(){return Ft=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ft.apply(this,arguments)}function zt(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Rt(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?zt(Object(r),!0).forEach((function(t){Dt(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):zt(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Dt(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Ut(){Ut=function t(){return e};var e={},t=Object.prototype,r=t.hasOwnProperty,n="function"==typeof Symbol?Symbol:{},a=n.iterator||"@@iterator",i=n.asyncIterator||"@@asyncIterator",o=n.toStringTag||"@@toStringTag";function c(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function e(t,r,n){return t[r]=n}}function l(e,t,r,n){var a=t&&t.prototype instanceof m?t:m,i=Object.create(a.prototype),o=new N(n||[]);return i._invoke=function(e,t,r){var n="suspendedStart";return function(a,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===a)throw i;return S()}for(r.method=a,r.arg=i;;){var o=r.delegate;if(o){var c=b(o,r);if(c){if(c===u)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var l=s(e,t,r);if("normal"===l.type){if(n=r.done?"completed":"suspendedYield",l.arg===u)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(n="completed",r.method="throw",r.arg=l.arg)}}}(e,r,o),i}function s(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}e.wrap=l;var u={};function m(){}function d(){}function f(){}var p={};c(p,a,(function(){return this}));var v=Object.getPrototypeOf,h=v&&v(v(C([])));h&&h!==t&&r.call(h,a)&&(p=h);var g=f.prototype=m.prototype=Object.create(p);function y(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function E(e,t){function n(a,i,o,c){var l=s(e[a],e,i);if("throw"!==l.type){var u=l.arg,m=u.value;return m&&"object"==At(m)&&r.call(m,"__await")?t.resolve(m.__await).then((function(e){n("next",e,o,c)}),(function(e){n("throw",e,o,c)})):t.resolve(m).then((function(e){u.value=e,o(u)}),(function(e){return n("throw",e,o,c)}))}c(l.arg)}var a;this._invoke=function(e,r){function i(){return new t((function(t,a){n(e,r,t,a)}))}return a=a?a.then(i,i):i()}}function b(e,t){var r=e.iterator[t.method];if(void 0===r){if(t.delegate=null,"throw"===t.method){if(e.iterator.return&&(t.method="return",t.arg=void 0,b(e,t),"throw"===t.method))return u;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return u}var n=s(r,e.iterator,t.arg);if("throw"===n.type)return t.method="throw",t.arg=n.arg,t.delegate=null,u;var a=n.arg;return a?a.done?(t[e.resultName]=a.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,u):a:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,u)}function w(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function k(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function N(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(w,this),this.reset(!0)}function C(e){if(e){var t=e[a];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,i=function t(){for(;++n<e.length;)if(r.call(e,n))return t.value=e[n],t.done=!1,t;return t.value=void 0,t.done=!0,t};return i.next=i}}return{next:S}}function S(){return{value:void 0,done:!0}}return d.prototype=f,c(g,"constructor",f),c(f,"constructor",d),d.displayName=c(f,o,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===d||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,f):(e.__proto__=f,c(e,o,"GeneratorFunction")),e.prototype=Object.create(g),e},e.awrap=function(e){return{__await:e}},y(E.prototype),c(E.prototype,i,(function(){return this})),e.AsyncIterator=E,e.async=function(t,r,n,a,i){void 0===i&&(i=Promise);var o=new E(l(t,r,n,a),i);return e.isGeneratorFunction(r)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},y(g),c(g,o,"Generator"),c(g,a,(function(){return this})),c(g,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=[];for(var r in e)t.push(r);return t.reverse(),function r(){for(;t.length;){var n=t.pop();if(n in e)return r.value=n,r.done=!1,r}return r.done=!0,r}},e.values=C,N.prototype={constructor:N,reset:function e(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(k),!t)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=void 0)},stop:function e(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function e(t){if(this.done)throw t;var n=this;function a(e,r){return c.type="throw",c.arg=t,n.next=e,r&&(n.method="next",n.arg=void 0),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],c=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var l=r.call(o,"catchLoc"),s=r.call(o,"finallyLoc");if(l&&s){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(l){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function e(t,n){for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=n&&n<=o.finallyLoc&&(o=null);var c=o?o.completion:{};return c.type=t,c.arg=n,o?(this.method="next",this.next=o.finallyLoc,u):this.complete(c)},complete:function e(t,r){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&r&&(this.next=r),u},finish:function e(t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),k(n),u}},catch:function e(t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===t){var a=n.completion;if("throw"===a.type){var i=a.arg;k(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function e(t,r,n){return this.delegate={iterator:C(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=void 0),u}},e}function Vt(e,t,r,n,a,i,o){try{var c=e[i](o),l=c.value}catch(e){return void r(e)}c.done?t(l):Promise.resolve(l).then(n,a)}function Kt(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var i=e.apply(t,r);function o(e){Vt(i,n,a,o,c,"next",e)}function c(e){Vt(i,n,a,o,c,"throw",e)}o(void 0)}))}}function qt(e,t){return $t(e)||Xt(e,t)||Ht(e,t)||Jt()}function Jt(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Ht(e,t){if(e){if("string"==typeof e)return Yt(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Yt(e,t):void 0}}function Yt(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Xt(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n=[],a=!0,i=!1,o,c;try{for(r=r.call(e);!(a=(o=r.next()).done)&&(n.push(o.value),!t||n.length!==t);a=!0);}catch(e){i=!0,c=e}finally{try{a||null==r.return||r.return()}finally{if(i)throw c}}return n}}function $t(e){if(Array.isArray(e))return e}var Qt=Ke.Z.confirm,er=_.Balloon.Tooltip,tr;!function(e){e.Disconnected="disconnected",e.Connecting="connecting",e.Connected="connected",e.Error="error"}(tr||(tr={}));var rr="MyMCPs",nr=(0,S.memo)((function(e){var t=e.sendWSMessageWithRetry,r=e.sendWSMessage,n=e.mcpListView,a=e.jumpMarketplace,i=e.notification,o=JSON.parse(window.mcp_config_overview),c=[{key:"MyMCPs",label:(0,W.W)("我的服务","My Servers")},{key:"Marketplace",label:(0,W.W)("MCP 广场","MCP Square")}],l,s=qt((0,S.useState)(!1),2),u=s[0],m=s[1],d,f=qt((0,S.useState)(void 0),2),p=f[0],v=f[1],h,g=qt((0,S.useState)(!1),2),y=g[0],E=g[1],b,w=qt((0,S.useState)(1),2),k=w[0],N=w[1],C,_=qt((0,S.useState)(!0),2),P=_[0],O=_[1],L,j=qt((0,S.useState)(null),2),M=j[0],I=j[1],A,T=qt((0,S.useState)({mcpServers:[],total:0}),2),G=T[0],B=T[1],Z,F=qt((0,S.useState)({totalMcpSeverCount:o.totalMcpSeverCount,enabledMcpSeverCount:o.enabledMcpSeverCount}),2),z=F[0],R=F[1],D,U=qt((0,S.useState)("MyMCPs"),2),V=U[0],K=U[1],q,J=qt((0,S.useState)(!1),2),H=J[0],Y=J[1],X,$=qt((0,S.useState)({userConfigError:!1,userConfigErrorMsg:"",userConfigFilePath:""}),2),Q=$[0],ee=$[1],te,re=qt((0,S.useState)(null),2),ne=re[0],ae=re[1],ie=(0,S.useRef)(null),oe=(0,S.useRef)(null),se=(0,S.useRef)(!0),ue=[{key:"1",label:Q.userConfigError?x().createElement(er,{popupContainer:function e(t){var r;return document.getElementById("mcp-service")||t},trigger:(0,W.W)("手动添加","Add manually"),align:"l",popupClassName:"mcp-service-add-manually-tips"},(0,W.W)("请修复配置文件问题后再使用","Please fix the configuration issues first")):(0,W.W)("手动添加","Add manually"),disabled:Q.userConfigError},{key:"2",label:(0,W.W)("配置文件添加","Add by JSON")}],me=(0,S.useCallback)(function(){var e=Kt(Ut().mark((function e(r){var n,a;return Ut().wrap((function e(i){for(;;)switch(i.prev=i.next){case 0:return i.next=2,t("webview/mcpSetting/listServers",{page:r||k,pageSize:20});case 2:n=i.sent,a=n.result,B({mcpServers:a.mcpServers?le(a.mcpServers):[],total:a.total}),ee({userConfigError:a.userConfigError,userConfigErrorMsg:a.userConfigErrorMsg,userConfigFilePath:a.userConfigFilePath}),se.current&&(O(!1),se.current=!1);case 7:case"end":return i.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),[k,t,B]),de=(0,S.useMemo)((function(){switch(null==i?void 0:i.id){case yr.mcp_parse_user_json_error:return ee((function(e){return Rt(Rt({},e),{},{userConfigError:!0,userConfigErrorMsg:i.msg||""})})),x().createElement("div",{className:"mcp-service-config-status mcp-service-config-status-hidden"},x().createElement(he.G_,{className:"mcp-service-config-status-error",type:"add2-line"}),(0,W.W)("更新失败","Update failed"));case yr.mcp_parse_user_json_no_error:ee((function(e){return Rt(Rt({},e),{},{userConfigError:!1,userConfigErrorMsg:""})}));break;case yr.mcp_updating_from_user_json:return x().createElement("div",{className:"mcp-service-config-status"},x().createElement(Qe.Z,{component:jt,className:"mcp-service-config-status-loading"}),(0,W.W)("更新中","Updating"));case yr.mcp_updated_from_user_json:return me(),x().createElement("div",{className:"mcp-service-config-status  mcp-service-config-status-hidden"},x().createElement(Qe.Z,{component:Mt,className:"mcp-service-config-status-success"}),(0,W.W)("更新成功","Update succeeded"))}}),[me,null==i?void 0:i.id,i.msg]),fe=(0,S.useCallback)((function(){window.parent.postMessage({type:"MSG_TYPE_OPEN_MCP_CONFIG",message:{filePath:Q.userConfigFilePath}},"*")}),[Q.userConfigFilePath]),pe=(0,S.useCallback)((function(e){var t=e.key;"1"===t?(m(!0),v(void 0)):"2"===t&&fe()}),[fe]),ve=(0,S.useCallback)(function(){var e=Kt(Ut().mark((function e(r,n){var a;return Ut().wrap((function e(i){for(;;)switch(i.prev=i.next){case 0:if(r.env&&(r.env=r.env.filter((function(e){return e.key&&e.value}))),r.headers&&(r.headers=r.headers.filter((function(e){return e.key}))),delete(a=ce([r]))[r.name].type,"string"==typeof a[r.name].args&&(a[r.name].args=a[r.name].args.split(" ").map((function(e){return e.trim()}))),"add"!==n){i.next=12;break}return i.next=8,t("webview/mcpSetting/addServer",{mcpServers:a});case 8:me(1),N(1),i.next=14;break;case 12:return i.next=14,t("webview/mcpSetting/updateServer",{mcpServers:a});case 14:m(!1);case 15:case"end":return i.stop()}}),e)})));return function(t,r){return e.apply(this,arguments)}}(),[me,t]),ge=(0,S.useCallback)(function(){var e=Kt(Ut().mark((function e(r,n){return Ut().wrap((function e(a){for(;;)switch(a.prev=a.next){case 0:if((null==M?void 0:M.identifier)!==n){a.next=2;break}return a.abrupt("return");case 2:return I({action:r,identifier:n}),a.next=5,t("webview/mcpSetting/configServer",{identifier:n,action:r});case 5:I(null),"delete"===r?(me(1),N(1)):me();case 7:case"end":return a.stop()}}),e)})));return function(t,r){return e.apply(this,arguments)}}(),[null==M?void 0:M.identifier,me,t]),ye=(0,S.useCallback)((function(e){var t=((null==G?void 0:G.mcpServers)||[]).find((function(t){return t.identifier===e}));v(t),m(!0)}),[null==G?void 0:G.mcpServers]),Ee=(0,S.useCallback)((function(e){N(e)}),[N]),be=(0,S.useCallback)(function(){var e=Kt(Ut().mark((function e(r){return Ut().wrap((function e(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",t("webview/mcpSetting/verify",{name:r}));case 1:case"end":return n.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),[t]),we=(0,S.useCallback)(Kt(Ut().mark((function e(){var r,n;return Ut().wrap((function e(a){for(;;)switch(a.prev=a.next){case 0:return a.next=2,t("webview/mcpSetting/overview",{});case 2:r=a.sent,n=r.result,R(n);case 5:case"end":return a.stop()}}),e)}))),[t]),_e=(0,S.useCallback)((function(){r("webview/openUrl",{url:"intl"===window.region_env?"https://www.alibabacloud.com/help/en/doc-detail/2877058.html":window.mcp_docs_url})}),[]);(0,S.useEffect)((function(){n&&E(!0)}),[n]),(0,S.useEffect)((function(){return oe.current=setInterval((function(){me()}),1e4),y?me():(we(),clearInterval(oe.current)),function(){return clearInterval(oe.current)}}),[me,we,y]);var We,Pe=qt((0,S.useState)(""),2),Oe=Pe[0],Le=Pe[1];(0,S.useEffect)((function(){var e,t,r;ne&&Le((null==G||null===(e=G.mcpServers)||void 0===e||null===(t=e.filter((function(e){return e.fromId===ne})))||void 0===t||null===(r=t[0])||void 0===r?void 0:r.identifier)||"")}),[ne,G]),(0,S.useEffect)((function(){ne&&me()}),[ne]);var je=function e(){return G&&G.total>0?x().createElement(x().Fragment,null,Q.userConfigError&&x().createElement("div",{className:"mcp-service-error-content"},x().createElement("div",{className:"mcp-service-error-content-header"},x().createElement("span",null,x().createElement(er,{trigger:x().createElement(he.G_,{type:"information-line",className:"error-icon"}),align:"tl",popupContainer:function e(t){var r;return document.getElementById("mcp-service")||t},popupClassName:"mcp-service-error-content-header-tip"},x().createElement("span",null,(0,W.W)("服务配置文件更新失败","MCP Configuration Update Failed"))),(0,W.W)("配置更新失败"," Configuration Update Failed")),x().createElement(ke.ZP,{type:"link",size:"small",onClick:fe},(0,W.W)("修改配置","Go to Edit"))),x().createElement("div",{className:"mcp-service-error-content-detail"},Q.userConfigErrorMsg)),x().createElement(Ne.Z,{className:"mcp-service-list",bordered:!1,accordion:!0,ghost:!0,expandIcon:function e(t){var r=t.isActive;return x().createElement(he.G_,{type:"arrow-right-2-line",style:{transform:r?"rotate(90deg)":""}})},onChange:function e(t){ae(""),Le(t)},activeKey:Oe,items:(G.mcpServers||[]).map((function(e){return{key:e.identifier,label:x().createElement(ir,Ft({onConfigChange:ge,onEdit:ye,currentAction:M,configHasError:Q.userConfigError},e,{setShowDetail:E})),style:{marginBottom:8,border:"none",borderRadius:8},children:x().createElement(or,{sendWSMessage:r,mcpServer:e,setShowDetail:E})}}))}),G.total>20&&x().createElement(xe.Z,{align:"center",className:"mcp-service-detail-pagination",current:k,onChange:Ee,pageSize:20,showSizeChanger:!1,total:G.total})):x().createElement("div",{className:"result-content"},P?x().createElement(Ce.Z,{size:"large"}):x().createElement(Se.ZP,{icon:x().createElement("img",{className:"no-data-img",src:"/static/yunxiao-fe/cosy-client-assets/0.1.10/downloads/imgextra/i1/O1CN01RihSXp1p578wgJR6B_!!6000000005308-55-tps-240-240.svg"}),title:x().createElement(x().Fragment,null,x().createElement("div",{style:{marginBottom:10}},(0,W.W)("暂无 MCP 服务","No MCP server yet")),x().createElement(ke.ZP,{onClick:function e(){null==K||K("Marketplace")},type:"primary"},(0,W.W)("前往添加","Go to MCP Square")))}))},Me,Ie;return x().createElement("div",{className:"page-section mcp-service",id:"mcp-service"},x().createElement("div",{className:"page-section-header"},x().createElement("div",{className:"page-section-header-title"},(0,W.W)("MCP 服务","MCP Servers")),x().createElement("div",{className:"page-section-header-desc"},(0,W.W)("MCP 是通义灵码扩展新工具的一种方式，可以查看","MCP is a way to offer new tools to Lingma Agent. See more in"),x().createElement("a",{href:"#",onClick:_e},(0,W.W)("帮助文档"," Docs")),(0,W.W)("了解更多。",""))),x().createElement("div",{className:"mcp-service-add-button",onClick:function e(){E(!0)}},x().createElement("span",null,z.enabledMcpSeverCount," / ",null==z?void 0:z.totalMcpSeverCount,(0,W.W)(" 服务已连接"," Servers Connected")),x().createElement(he.G_,{type:"arrow-right-2-line"})),y&&x().createElement("div",{className:"mcp-service-detail"},x().createElement("div",{className:"page-header mcp-service-detail-header"},x().createElement("span",null,x().createElement("span",{className:"mcp-service-detail-header-back",onClick:function e(){E(!1)}},x().createElement(he.G_,{type:"arrow-left-2-line"}),(0,W.W)("返回","Back")),x().createElement("span",{className:"mcp-service-detail-header-title"},(0,W.W)("MCP 服务","MCP Servers"))),x().createElement("div",{className:"mcp-service-detail-header-add-content"},x().createElement("div",{className:"mcp-service-detail-header-add-open-config"},x().createElement(er,{popupContainer:function e(t){var r;return document.getElementById("mcp-service")||t},trigger:x().createElement(Qe.Z,{component:Lt,className:"open-config-icon",onClick:fe}),align:"t",popupClassName:"mcp-service-config-tips"},(0,W.W)("查看配置文件","View JSON config"))),x().createElement(qe.Z,{menu:{items:ue,onClick:pe},overlayClassName:"mcp-service-detail-header-add",trigger:["click"],getPopupContainer:function e(t){var r;return document.getElementById("mcp-service")||t}},x().createElement("div",{onClick:function e(t){return t.preventDefault()},className:"mcp-service-detail-header-add-button"},x().createElement(he.G_,{type:"add-line1",className:"mcp-service-icon"}),x().createElement(he.G_,{type:"arrow-down-2-line",className:"mcp-service-icon"}))))),x().createElement("div",{className:"mcp-service-detail-tabs"},c.map((function(e){return x().createElement("span",{onClick:function t(){K(e.key)},className:"tab-item ".concat(V===e.key?"active":"")},e.label)})),x().createElement("div",{className:"tabBar-extra"},V===rr?x().createElement(x().Fragment,null,de):H?x().createElement(he.G_,{type:"shrink-3-line",className:"shrink-line",onClick:function e(){return Y(!H)}}):x().createElement("div",{className:"mcp-service-detail-tabs-extra",onClick:function e(){return Y(!H)}},x().createElement(he.G_,{type:"filter-line"}),(null===(Me=ie.current)||void 0===Me||null===(Ie=Me.filterSelectedTag)||void 0===Ie?void 0:Ie.length)&&x().createElement("span",{className:"badge"})))),V!==rr?x().createElement(Ot,{showMarketplaceFilterTag:H,sendWSMessageWithRetry:t,jumpMarketplace:a,ref:ie,setMcpListViewTab:K,setMcpActiveKey:ae,userConfig:Q}):je(),x().createElement(cr,{value:p,open:u,setOpen:m,onSubmit:ve,onVerifyName:be})))}));const ar=nr;var ir=function e(t){var r=t.onConfigChange,n=t.onEdit,a=t.currentAction,i=t.configHasError,o=t.setShowDetail,c=Bt(t,Tt),l,s=qt((0,S.useState)(!1),2),u=s[0],m=s[1],d=(0,S.useCallback)((function(e,t){e.stopPropagation(),"delete"===t?Qt({getContainer:document.body.querySelector(".mcp-service-list-item"),title:(0,W.W)("删除 MCP 服务","Delete MCP Server"),icon:x().createElement(he.G_,{type:"information-line",className:"mcp-service-del-icon"}),content:(0,W.W)("确定删除该 MCP 服务吗？删除后将无法撤销。","Are you sure you want to delete the MCP server？This action cannot be undone."),className:"mcp-service-del-modal",okText:(0,W.W)("删 除","Delete"),cancelText:(0,W.W)("取 消","Cancel"),onOk:function e(){r(t,c.identifier)}}):r(t,c.identifier)}),[c.identifier,r]),f=(0,S.useCallback)((function(e,t){var r;d(t,e?"enable":"disable")}),[d]),p=(0,S.useCallback)((function(e){e.stopPropagation(),n(c.identifier)}),[c.identifier,n]),v=(0,S.useCallback)((function(e){return i?(0,W.W)("请修复配置文件问题后再使用","Please fix the configuration issues first"):e?(0,W.W)("点击后启用","Click to enable"):(0,W.W)("点击后禁用","Click to disable")}),[i]),h=(0,S.useCallback)((function(){var e=(0,W.W)("请帮我调用 ".concat(c.name," 中的任意工具，完成一个使用示例。"),"Please help me call any tool within the ".concat(c.name," and provide an example."));null==o||o(!1),window.parent.postMessage({type:"MSG_TYPE_EXPERIENCE_MCP_CASE",message:{content:e,isSendChat:!0}},"*")}),[c.name]),g=function e(t){t.stopPropagation(),Qt({getContainer:document.body.querySelector(".mcp-service-list-item"),title:(0,W.W)("确定离开当前页面？","Leave this page?"),icon:x().createElement(he.G_,{type:"information-line",className:"mcp-service-del-icon"}),content:(0,W.W)("点击继续将切换至智能会话的智能体模式，同时自动新建一个会话。此操作将会终止当前编码任务，并且拒绝所有未接受的代码建议。","Clicking Continue will take you to a new chat of the Agent Mode of AI Chat. The current chat will be terminated, and all unaccepted code suggestions will be rejected."),className:"mcp-service-confirm-modal",okText:(0,W.W)("继续","Continue"),cancelText:(0,W.W)("取 消","Cancel"),onOk:h})};return x().createElement("div",{className:"mcp-service-list-item",key:c.identifier},x().createElement("div",{className:"mcp-service-list-item-status"},"disconnected"===c.status&&x().createElement(er,{trigger:x().createElement(he.G_,{type:"unlink-line"}),align:"t"},(0,W.W)("未连接","Disconnected")),"connecting"===c.status&&x().createElement(er,{trigger:x().createElement(he.G_,{type:"loading-fill"}),align:"t"},(0,W.W)("连接中","Connecting")),"connected"===c.status&&x().createElement(er,{trigger:x().createElement(he.G_,{className:"success",type:"link-line"}),align:"t"},(0,W.W)("已连接","Connected")),"error"===c.status&&x().createElement(er,{trigger:x().createElement(he.G_,{className:"mcp-service-list-item-status-error error",type:"add2-line"}),align:"t"},(0,W.W)("异常","Error"))),x().createElement("div",{className:"mcp-service-list-item-name"},c.name),x().createElement("div",{className:"mcp-service-list-item-setup"},x().createElement(x().Fragment,null,"connected"===c.status?x().createElement(er,{trigger:x().createElement(Qe.Z,{component:It,onClick:function e(t){return g(t)},className:"setup-icon"}),align:"t",popupContainer:function e(t){var r;return document.getElementById("mcp-service")||t},popupClassName:"mcp-service-tip"},(0,W.W)("快速体验","Take a trial")):x().createElement(er,{trigger:x().createElement(Qe.Z,{component:It,className:"setup-icon setup-quick-icon-disabled"}),popupContainer:function e(t){var r;return document.getElementById("mcp-service")||t},align:"t",popupClassName:"mcp-service-tip"},(0,W.W)("请先连接服务后再体验","Connect to the MCP Server to enable the trial")),"connecting"===c.status?x().createElement(x().Fragment,null,x().createElement(he.G_,{type:"refresh-line",className:"setup-icon setup-disabled"}),x().createElement(he.G_,{type:"edit-line",className:"setup-icon setup-disabled"}),x().createElement(he.G_,{type:"delete-line",className:"setup-icon setup-disabled"})):x().createElement(x().Fragment,null,c.disabled?x().createElement(he.G_,{type:"refresh-line",className:"setup-icon setup-disabled"}):x().createElement(er,{trigger:x().createElement(he.G_,{type:"refresh-line",onClick:function e(t){m(!0),d(t,"refresh"),setTimeout((function(){m(!1)}),2e3)},className:"setup-icon ".concat(u?"setup-refresh":"")}),align:"t"},(0,W.W)("刷新","Refresh")),x().createElement(er,{trigger:x().createElement(he.G_,{type:"edit-line",onClick:function e(t){return!i&&p(t)},className:i?"setup-icon setup-disabled":"setup-icon"}),align:i?"tl":"t"},i?(0,W.W)("请修复配置文件问题后再使用","Please fix the configuration issues first"):(0,W.W)("修改","Edit")),x().createElement(er,{trigger:x().createElement(he.G_,{type:"delete-line",onClick:function e(t){return!i&&d(t,"delete")},className:i?"setup-icon setup-disabled":"setup-icon setup-del-icon"}),align:i?"tl":"t"},i?(0,W.W)("请修复配置文件问题后再使用","Please fix the configuration issues first"):(0,W.W)("删除","Delete"))))),x().createElement("div",{className:"mcp-service-list-item-switch"},c.canBeEnabled?x().createElement(er,{trigger:x().createElement("div",null,x().createElement(Je.Z,{size:"small",checked:!c.disabled,disabled:i,loading:(null==a?void 0:a.identifier)===c.identifier&&("enable"===(null==a?void 0:a.action)||"disable"===(null==a?void 0:a.action)),onClick:f})),align:"tl"},v(c.disabled)):x().createElement(er,{trigger:x().createElement("div",null,x().createElement(Je.Z,{size:"small",disabled:!0})),align:"tl"},i?(0,W.W)("请修复配置文件问题后再使用","Please fix the configuration issues first"):(0,W.W)("最多启用 10个  ","No more than 10 enabled"))))},or=function e(t){var r,n,a=t.mcpServer,i=t.setShowDetail,o=function e(){Qt({getContainer:document.body.querySelector(".mcp-service-detail"),title:(0,W.W)("确定离开当前页面？","Leave this page?"),icon:x().createElement(he.G_,{type:"information-line",className:"mcp-service-del-icon"}),content:(0,W.W)("点击继续将切换至智能会话的智能体模式，并开始问题修复流程。","Click Continue to proceed to the Agent mode of AI Chat and start troubleshooting."),className:"mcp-service-confirm-modal",okText:(0,W.W)("继续","Continue"),cancelText:(0,W.W)("取 消","Cancel"),onOk:c})},c=(0,S.useCallback)((function(){var e="",t,r,n,o;a.command?e=[a.command||"",Array.isArray(a.args)?a.args.join(" "):a.args||"",(null===(t=a.env)||void 0===t?void 0:t.map((function(e){return"".concat(e.key,"=").concat(e.value)})).join(" "))||""].filter(Boolean).join(" "):e=a.url?"curl -i ".concat(a.url):" ";var c=(0,W.W)("我的 MCP Server ".concat(a.name," 在启动时报错，帮我解决这个问题。\n异常信息：\n```\n").concat(a.errorMsg,"\n```\n在解决问题时，可以参考下面的步骤：\n- 分析异常信息，找到可能的原因\n- 如果异常信息不足，可以执行以下命令以获取详细的异常信息：").concat(e,"\n- 参考常见问题说明：https://help.aliyun.com/document_detail/2922563.html"),"My MCP Server ".concat(a.name," is encountering an error during startup. Please help me resolve this issue.\nError message:\n```\n").concat(a.errorMsg,"\n```\nWhen addressing the problem, you can follow these instructions:\n- Analyze the error message to identify the possible cause\n- If the error message is insufficient, you can run the following command to get more detailed error information: ").concat(e,"\n- Refer to the FAQ for more details: https://help.aliyun.com/document_detail/2922563.html"));null==i||i(!1),window.parent.postMessage({type:"MSG_TYPE_FIX_MCP_ERROR",message:{content:c}},"*")}),[a]);return x().createElement(x().Fragment,null,a.command&&x().createElement("div",{className:"mcp-service-item-content"},x().createElement("div",{className:"mcp-service-item-header"},(0,W.W)("命令","Command")),x().createElement("p",{className:"mcp-service-item-inner"},a.command||"-")),a.url&&x().createElement("div",{className:"mcp-service-item-content"},x().createElement("div",{className:"mcp-service-item-header"},(0,W.W)("服务地址","Server URL")),x().createElement("p",{className:"mcp-service-item-inner"},a.url||"-")),x().createElement("div",{className:"mcp-service-item-content"},x().createElement("div",{className:"mcp-service-item-header"},(0,W.W)("工具","Tools"),"(",(null===(r=a.tools)||void 0===r?void 0:r.length)||0,")"),x().createElement("div",{className:"mcp-service-item-tools"},null!==(n=a.tools)&&void 0!==n&&n.length?x().createElement(x().Fragment,null,a.tools.map((function(e){return x().createElement(er,{trigger:x().createElement("div",{className:"mcp-service-item-tools-list"},x().createElement(he.G_,{type:"setting-2-line",className:"mcp-service-item-tools-list-icon"}),x().createElement("span",{className:"mcp-service-item-tools-list-name"},e.name||"-"),x().createElement("span",{className:"mcp-service-item-tools-list-description"},e.description||"-")),followTrigger:!0,popupContainer:function e(){return document.querySelector(".mcp-service-item-tools")},popupStyle:{maxWidth:"80%",right:"12px"},align:"t"},x().createElement("div",{className:"mcp-service-item-tools-list-description-tips"},e.description||"-",e.parameters.length?x().createElement(x().Fragment,null,x().createElement("div",{className:"params-title"},(0,W.W)("参数","Parameters")),x().createElement("ul",null,e.parameters.map((function(e){return x().createElement("li",{key:e.name,className:"params"},x().createElement("span",{className:e.required?"params-require params-name":"params-name"},e.name),": ",e.description||"-")})))):""))}))):x().createElement("div",null,(0,W.W)("暂未获取到，连接成功后可查看","No tools are available yet, but you will see once the server is connected.")))),a.errorMsg&&x().createElement("div",{className:"mcp-service-item-content"},x().createElement("div",{className:"mcp-service-item-header mcp-service-item-error "},(0,W.W)("错误信息","Error Message")),x().createElement("p",{className:"mcp-service-item-inner"},a.errorMsg||"-"),x().createElement("p",{className:"mcp-service-item-fix"},x().createElement(ke.ZP,{type:"primary",onClick:o},(0,W.W)("一键修复","ask Lingma to fix")))))},cr=(0,S.memo)((function(e){var t=e.setOpen,r=e.open,n=e.onSubmit,a=e.value,i=e.onVerifyName,o,c,l=qt(He.Z.useForm(),1)[0],s=l.resetFields,u=l.setFieldsValue,m=He.Z.useWatch("type",l),d=(0,S.useMemo)((function(){return!!a}),[a]),f={name:"",command:"",args:"",env:[{key:"",value:""}],headers:[{key:"",value:""}],type:"stdio"},p=(0,S.useCallback)((function(e){d&&a?(e.identifier=a.identifier,e.headers||"sse"!==e.type||(e.headers=a.headers),n(e,"edit")):n(e,"add"),v()}),[n,a]),v=function e(){s(),t(!1)},h=(0,S.useCallback)(function(){var e=Kt(Ut().mark((function e(t,r){var n,o,c,l;return Ut().wrap((function e(t){for(;;)switch(t.prev=t.next){case 0:if(!d||r!==(null==a?void 0:a.name)){t.next=2;break}return t.abrupt("return",Promise.resolve());case 2:if(r){t.next=5;break}return n=(0,W.W)("请输入服务名称","Server name is needed"),t.abrupt("return",Promise.reject(new Error(n)));case 5:return t.next=7,i(r);case 7:if(o=t.sent,(c=o.result).verfiyResult){t.next=12;break}return l=(0,W.W)("已存在该名称","The server name already exists"),t.abrupt("return",Promise.reject(new Error(l)));case 12:return t.abrupt("return",Promise.resolve());case 13:case"end":return t.stop()}}),e)})));return function(t,r){return e.apply(this,arguments)}}(),[d,i,a]);function g(e,t,r){return Array.isArray(e)&&t&&r?e.filter((function(e){return""===e[t].trim()&&""!==e[r].trim()})):[];var n}var y=(0,S.useCallback)(function(){var e=Kt(Ut().mark((function e(t,r){var n,a,i,o;return Ut().wrap((function e(t){for(;;)switch(t.prev=t.next){case 0:if(!(n=g(r,"key","value")).length){t.next=3;break}return t.abrupt("return",Promise.reject(new Error((0,W.W)("Key 为空","Key is empty"))));case 3:if("stdio"!==m){t.next=7;break}if(!(a=g(r,"value","key")).length){t.next=7;break}return t.abrupt("return",Promise.reject(new Error((0,W.W)("Value 为空","Value is empty"))));case 7:if(i=r.filter((function(e){return"stdio"===m?""!==e.key&&""!==e.value:""!==e.key})).map((function(e){return e&&e.key})),o=new Set(i),i.length===o.size){t.next=11;break}return t.abrupt("return",Promise.reject(new Error((0,W.W)("Key 存在重复","Key is duplicated"))));case 11:return t.abrupt("return",Promise.resolve());case 12:case"end":return t.stop()}}),e)})));return function(t,r){return e.apply(this,arguments)}}(),[m]);(0,S.useEffect)((function(){var e;a?a.command?u(Rt(Rt({},a),{},{args:Array.isArray(a.args)?a.args.join(" "):a.args,type:"stdio"})):u(Rt(Rt({},a),{},{type:"sse",headers:null!==(e=null==a?void 0:a.headers)&&void 0!==e?e:[{key:"",value:""}]})):s()}),[l,s,u,a,r]);var E=function e(){return x().createElement(He.Z.List,{name:"stdio"===m?"env":"headers",rules:[{validator:y}]},(function(e,t,r){var n=t.add,a=t.remove,i=r.errors;return x().createElement(x().Fragment,null,e.map((function(t){var r=t.key,n=t.name,i=Bt(t,Gt);return x().createElement(Ye.Z,{key:r,className:"mcp-service-add-modal-env",style:{display:"flex"},align:"baseline"},x().createElement(He.Z.Item,Ft({},i,{name:[n,"key"],label:"Key"}),x().createElement(Xe.Z,{placeholder:(0,W.W)("输入 Key"," Key for env")})),x().createElement(He.Z.Item,Ft({},i,{name:[n,"value"],label:"Value"}),x().createElement(Xe.Z,{placeholder:(0,W.W)("输入 Value","Value for env")})),x().createElement(He.Z.Item,Ft({},i,{label:(0,W.W)("操作","Action"),className:"mcp-service-add-modal-del"}),x().createElement(he.G_,{type:"delete-line",className:1===e.length&&"stdio"===m?"mcp-service-add-modal-del-icon not-delete":"mcp-service-add-modal-del-icon",onClick:function t(){"stdio"===m?e.length>1&&a(n):a(n)}})))})),x().createElement("div",{className:"mcp-service-add-modal-error"},x().createElement(He.Z.ErrorList,{errors:i})),x().createElement(ke.ZP,{type:"link",size:"small",className:"mcp-service-add-modal-add",onClick:function e(){return n({key:"",value:""})},icon:x().createElement(he.G_,{type:"add2-line",className:"mcp-service-icon"})},"stdio"===m?(0,W.W)("添加环境变量","Add Variables"):(0,W.W)("添加","Add")))}))};return x().createElement(Ke.Z,{open:r,title:d?(0,W.W)("修改 MCP 服务","Edit MCP Server"):(0,W.W)("添加 MCP 服务","Add MCP Server"),okButtonProps:{autoFocus:!0,htmlType:"submit"},getContainer:!1,okText:d?(0,W.W)("保 存","Save"):(0,W.W)("立即添加","Add"),cancelText:(0,W.W)("取 消","Cancel"),onCancel:v,classNames:{content:"mcp-service-add-modal"},destroyOnClose:!0,maskClosable:!1,centered:!0,forceRender:!0,modalRender:function e(t){return x().createElement(He.Z,{onFinish:p,form:l,clearOnDestroy:!0,initialValues:f,layout:"vertical"},t)}},x().createElement(He.Z.Item,{name:"name",label:(0,W.W)("名称","Name"),validateTrigger:"onBlur",required:!0,rules:[{validator:h}]},x().createElement(Xe.Z,{placeholder:(0,W.W)("输入服务名称","Server name")})),x().createElement(He.Z.Item,{name:"type",label:(0,W.W)("类型","Type")},x().createElement($e.ZP.Group,null,x().createElement($e.ZP,{value:"stdio"},"STDIO"),x().createElement($e.ZP,{value:"sse"},(0,W.W)("SSE 或 Streamable HTTP","SSE or Streamable HTTP")))),"stdio"===m?x().createElement(x().Fragment,null,x().createElement(He.Z.Item,{name:"command",label:(0,W.W)("命令","Command"),rules:[{required:!0,message:(0,W.W)("请输入命令","Command is needed")}]},x().createElement(Xe.Z,{placeholder:(0,W.W)("输入需要运行的命令","Command to run")})),x().createElement(He.Z.Item,{name:"args",label:(0,W.W)("参数","Arguments")},x().createElement(Xe.Z,{placeholder:(0,W.W)("输入命令执行参数（空格隔开）","Arguments for the command (space-separated)")})),x().createElement(He.Z.Item,{label:(0,W.W)("环境变量","Environment Variables")},E())):x().createElement(x().Fragment,null,x().createElement(He.Z.Item,{label:(0,W.W)("服务地址","Server URL"),name:"url",rules:[{required:!0,message:(0,W.W)("请输入服务地址","Server URL is needed")},{pattern:/^https?:\/\/[^\s/$.?#].[^\s]*$/,message:(0,W.W)("URL 不合法","Please input the right URL")}]},x().createElement(Xe.Z,{placeholder:(0,W.W)("输入 SSE endpoint 或 Streamable HTTP 的 URL","Input the URL of an SSE endpoint or a Streamable HTTP.")})),x().createElement(Ne.Z,{defaultActiveKey:[""],ghost:!0,className:"mcp-service-add-modal-sse-headers",items:[{key:"sse",label:(0,W.W)("高级设置","Advanced Settings"),children:x().createElement(He.Z.Item,{label:x().createElement(x().Fragment,null,(0,W.W)("请求头","Headers"),x().createElement(er,{trigger:x().createElement(he.G_,{type:"question-line",className:"mcp-services-sse-tips-icon"}),className:"mcp-services-sse-tips",align:"tl",popupContainer:function e(){return document.querySelector(".mcp-service-add-modal")}},(0,W.W)("HTTP请求的自定义请求头","Custom headers for HTTP requests")))},E())}]})))})),lr=r(62634),sr;function ur(e,t){return vr(e)||pr(e,t)||dr(e,t)||mr()}function mr(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function dr(e,t){if(e){if("string"==typeof e)return fr(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?fr(e,t):void 0}}function fr(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function pr(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n=[],a=!0,i=!1,o,c;try{for(r=r.call(e);!(a=(o=r.next()).done)&&(n.push(o.value),!t||n.length!==t);a=!0);}catch(e){i=!0,c=e}finally{try{a||null==r.return||r.return()}finally{if(i)throw c}}return n}}function vr(e){if(Array.isArray(e))return e}var hr={fontColor:"--font-color",fontColorGray:"--font-color-gray",bg:"--bg",activeBg:"--active-bg",selectBg:"--select-bg",cardBg:"--card-bg",focusBorder:"--focus-border",inputBg:"--input-bg",scrollbarBg:"--scrollbar-bg",scrollbarThumbBg:"--scrollbar-thumb-bg",textLinkColor:"--text-link-color",widgetBorder:"--widget-border",widgetShadow:"--widget-shadow",dividerColor:"--divider-color",themeColor:"--theme-color",buttonBg:"--button-bg",buttonTextColor:"--button-text-color",tabColor:"--tab-color",buttonHoverBg:"--button-hover-bg",editorBackground:"--editor-background"},gr={fontColor:"dfe1e6",fontColorGray:"6e747b",bg:"fafafa",activeBg:"rgba(0, 0, 0, 0.08)",selectBg:"rgba(0, 0, 0, 0.08)",cardBg:"rgba(0,0,0,0.04)",focusBorder:"005fb8",inputBg:"ffffff",scrollbarBg:"181818",scrollbarThumbBg:"rgba(100, 100, 100, 0.4)",textLinkColor:"4daafc",widgetBorder:"313131",widgetShadow:"rgba(0, 0, 0, 0.36)",dividerColor:"rgba(253, 253, 253, 0.12)",themeColor:"0078d4",buttonBg:"0078d4",buttonTextColor:"fff",buttonHoverBg:"026ec1",editorBackground:"1f1f1f"},yr;!function(e){e.mcp_parse_user_json_error="mcp_parse_user_json_error",e.mcp_parse_user_json_no_error="mcp_parse_user_json_no_error",e.mcp_updating_from_user_json="mcp_updating_from_user_json",e.mcp_updated_from_user_json="mcp_updated_from_user_json"}(yr||(yr={}));var Er=function e(t){Object.entries(hr).forEach((function(e){var r,n=ur(e,2),a=n[0],i=n[1],o=null!==(r=t[a])&&void 0!==r?r:gr[a];o&&(ue(i,o),"bg"===a&&t.bg&&(document.body.style.backgroundColor=se(o)),t.bg||(document.body.style.backgroundColor="transparent",document.documentElement.style.setProperty("--bg","transparent")))}))};function br(e,t){var r=document.getElementById("dynamic-scrollbar-style");r||((r=document.createElement("style")).id="dynamic-scrollbar-style",document.head.appendChild(r)),r.innerHTML="\n    ::-webkit-scrollbar {\n      background: ".concat(se(e)," !important;\n    }\n    ::-webkit-scrollbar-thumb {\n      background: ").concat(se(t)," !important;\n    }\n  ")}var wr=ae(window.location.search);window.locale=null!==(sr=wr.locale)&&void 0!==sr?sr:"zh",Er(wr),wr.scrollbarBg,wr.scrollbarThumbBg&&br(wr.scrollbarBg,wr.scrollbarThumbBg);var kr,Nr="intl"===window.region_env?"https://www.alibabacloud.com/help/zh/legal/latest/alibaba-cloud-international-website-product-terms-of-service":"https://help.aliyun.com/document_detail/2590617.html",Cr=function e(){var t,r,n,a,i,o=ur((0,S.useState)(null!==(t=window.policy_agreed)&&void 0!==t&&t),2),c=o[0],l=o[1],s,u=ur((0,S.useState)(null!==(r=window.official_commands_end)&&void 0!==r&&r),2),m=u[0],d=u[1],f,p=ur((0,S.useState)(null!==(n=window.edition)&&void 0!==n?n:""),2),v=p[0],h=p[1],g,y=ur((0,S.useState)(null!==(a=window.locale)&&void 0!==a?a:"zh"),2),E=y[0],b=y[1],w,k=ur((0,S.useState)(!1),2),N=k[0],C=k[1],_,P=ur((0,S.useState)({id:null,msg:""}),2),O=P[0],L=P[1],j=(0,S.useRef)(null);(0,S.useEffect)((function(){var e,t="ws://localhost:".concat((null===(e=window.location)||void 0===e?void 0:e.port)||"37510","/ws?state=").concat(wr.state);j.current=new WebSocket(t);var r=setInterval((function(){var e;if((null===(e=j.current)||void 0===e?void 0:e.readyState)===WebSocket.OPEN){var t={id:ie(),method:"webview/ws/ping"};j.current.send(JSON.stringify(t))}}),12e4);return j.current.onmessage=function(e){try{var t=JSON.parse(e.data);switch(t.method){case"webview/profile/update/renderPage":var r,n,a,i,o,c,s,u,m;if(Er(t.params),null!==(r=t.params)&&void 0!==r&&r.scrollbarBg&&null!==(n=t.params)&&void 0!==n&&n.scrollbarThumbBg)br(null===(o=t.params)||void 0===o?void 0:o.scrollbarBg,null===(c=t.params)||void 0===c?void 0:c.scrollbarThumbBg);if(null!==(a=t.params)&&void 0!==a&&a.locale)window.locale=null===(s=t.params)||void 0===s?void 0:s.locale,b(null===(u=t.params)||void 0===u?void 0:u.locale);if(null!==(i=t.params)&&void 0!==i&&i.edition)h(null===(m=t.params)||void 0===m?void 0:m.edition);break;case"webview/profile/update/dataPolicy":var f;l(null===(f=t.params)||void 0===f?void 0:f.agree);break;case"webview/profile/update/command/officialOrderEnd":var p;d(null===(p=t.params)||void 0===p?void 0:p.agree);break;case"webview/sendNotification":var v,g;L({id:null===(v=t.params)||void 0===v?void 0:v.notification_id,msg:null===(g=t.params)||void 0===g?void 0:g.error_msg})}}catch(e){console.warn("Webview Receive Data Error:",e)}},function(){var e;clearInterval(r),null===(e=j.current)||void 0===e||e.close()}}),[]);var M=function e(t,r){return new Promise((function(e,n){var a=3,i=0,o;(function a(){if(!j.current||j.current.readyState!==WebSocket.OPEN)return i<3?(i++,void setTimeout(a,1e3)):void n(new Error((0,W.W)("通义灵码连接中，请重启插件或稍后重试","Lingma is connecting, please restart the IDE and try again later")));var o=ie(),c={id:o,method:t,params:r},l=setTimeout((function(){var e;null===(e=j.current)||void 0===e||e.removeEventListener("message",s),n(new Error((0,W.W)("通义灵码连接中，请重启插件或稍后重试","Lingma is connecting, please restart the IDE and try again later")))}),1e4),s=function t(r){try{var a=JSON.parse(r.data),i;if(a.id===o)clearTimeout(l),null===(i=j.current)||void 0===i||i.removeEventListener("message",t),e(a)}catch(e){var c;clearTimeout(l),null===(c=j.current)||void 0===c||c.removeEventListener("message",t),n(e)}};j.current.addEventListener("message",s),j.current.send(JSON.stringify(c))})()}))},I=function e(t,r){if(j.current&&j.current.readyState===WebSocket.OPEN){var n={id:ie(),method:t,params:r};j.current.send(JSON.stringify(n))}},A=function e(t){l(t),I("webview/profile/update/dataPolicy",{agree:t})},T=function e(){I("webview/openUrl",{url:Nr})},G=function e(t){I("webview/openUrl",{url:t})};(0,S.useEffect)((function(){var e=function e(t){switch(t.code){case"KeyC":(t.ctrlKey||t.metaKey)&&document.execCommand("copy");break;case"KeyX":(t.ctrlKey||t.metaKey)&&document.execCommand("cut");break;case"KeyV":(t.ctrlKey||t.metaKey)&&document.execCommand("paste");break;case"KeyA":(t.ctrlKey||t.metaKey)&&document.execCommand("selectAll")}};return window.addEventListener("keydown",e),function(){window.removeEventListener("keydown",e)}}),[]);var B=function e(){window.parent.postMessage({type:"MSG_BACK_TO_CHAT"},"*")};return x().createElement(lr.V9,{hashPriority:"high",transformers:[lr.IJ]},x().createElement("div",{className:"profile"},x().createElement("div",{className:"page-header page-header-profile"},x().createElement("span",null,(0,W.W)("个人设置","Your Settings")),wr.bg&&wr.activeBg&&x().createElement(ke.ZP,{type:"link",size:"small",onClick:B,icon:x().createElement(he.G_,{className:"back-to-chat",type:"withdraw-line"})},(0,W.W)("返回会话","Back to chat"))),x().createElement("div",{className:"page-section page-section-user"},x().createElement("div",{className:"page-section-header"},(0,W.W)("基础信息","Basic Information")),x().createElement("div",{className:"page-section-body"},window.user_avatar?x().createElement("div",{className:"section-item",style:{marginBottom:17}},x().createElement("div",{className:"section-item-label"},(0,W.W)("头像","Avatar")),x().createElement("div",{className:"section-item-value"},x().createElement("img",{className:"account-avatar",src:window.user_avatar}))):null,x().createElement("div",{className:"section-item"},x().createElement("div",{className:"section-item-label"},(0,W.W)("账号","Account")),x().createElement("div",{className:"section-item-value"},window.user_name)),x().createElement("div",{className:"section-item"},x().createElement("div",{className:"section-item-label"},(0,W.W)("版本","Edition")),x().createElement("div",{className:"section-item-value"},oe(v,E))),window.org_name?x().createElement("div",{className:"section-item"},x().createElement("div",{className:"section-item-label"},(0,W.W)("组织","Organization")),x().createElement("div",{className:"section-item-value"},window.org_name)):null,wr.bg&&wr.activeBg&&v===U.Individual?x().createElement("div",{className:"section-item"},x().createElement("div",{className:"section-item-label"},(0,W.W)("改进计划","Privacy plan")),x().createElement("div",{className:"section-item-value"},x().createElement("a",{href:"#",onClick:function e(){C(!0)}},(0,W.W)("改进计划","Improvement Plan")," ",x().createElement(he.G_,{type:"arrow-right-2-line"})))):null)),wr.bg&&wr.activeBg&&x().createElement(x().Fragment,null,x().createElement(Fe,{memoryId:wr.memoryId,sendWSMessageWithRetry:M}),x().createElement(ar,{mcpListView:"true"===wr.mcpListView,sendWSMessage:I,sendWSMessageWithRetry:M,jumpMarketplace:G,notification:O})),wr.bg&&N&&v===U.Individual&&x().createElement("div",{className:"improvement-plan"},x().createElement("div",{className:"page-header improvement-plan-detail-header"},x().createElement("span",{onClick:function e(){C(!1)},className:"improvement-plan-detail-header-back"},x().createElement(he.G_,{type:"arrow-left-2-line"}),(0,W.W)("返回","Back")),x().createElement("span",{className:"improvement-plan-detail-header-title"},(0,W.W)("改进计划","Improvement Plan"))),x().createElement("div",{className:"improvement-plan-detail-content"},x().createElement(xr,{policyAgreed:c,onChangePolicyAgreed:A,onClickPolicyUrl:T}))),!wr.bg&&v===U.Individual&&x().createElement("div",{className:"page-section improvement-plan-before"},x().createElement("div",{className:"page-section-header"},(0,W.W)("改进计划","Improvement Plan")),x().createElement("div",{className:"page-section-body"},x().createElement(xr,{policyAgreed:c,onChangePolicyAgreed:A,onClickPolicyUrl:T}))),x().createElement(fe,{edition:v,chat_filter:window.chat_filter?JSON.parse(window.chat_filter):void 0,post_chat_filter:window.post_chat_filter?JSON.parse(window.post_chat_filter):void 0,completion_filter:window.completion_filter?JSON.parse(window.completion_filter):void 0,post_completion_filter:window.post_completion_filter?JSON.parse(window.post_completion_filter):void 0}),x().createElement(ve,{edition:v,commands:window.commands?JSON.parse(window.commands):void 0,sendWSMessage:I,officialCommandsEnd:m,setOfficialCommandsEnd:d})))};const Sr=undefined;var xr=function e(t){var r=t.policyAgreed,n=t.onChangePolicyAgreed,a=t.onClickPolicyUrl;return x().createElement(x().Fragment,null,x().createElement(_.Checkbox,{checked:r,onChange:n},(0,W.W)("同意通义灵码使用我的信息进行产品优化","Allow Lingma to use my information for product improvements")),x().createElement("div",{className:"improvement-plan-tips color-gray"},(0,W.W)("经您同意后，通义灵码将使用您的代码片段、上下文信息进行产品的优化，以提升代码建议质量和性能。更多信息可参考","With your permission, Lingma will use your code snippets and context for product improvements in suggestions and performance. See more information in "),x().createElement("a",{href:"#",onClick:a},(0,W.W)("通义灵码隐私政策","Lingma Privacy Policy")),(0,W.W)("。",".")))},_r;const Wr=Cr},51768:(e,t,r)=>{r.d(t,{W:()=>i,a:()=>o});var n=r(39607),a=r.n(n),i=function e(t,r){var n=window.locale||window.navigator.language||window.navigator.userLanguage;return 0===(null==n?void 0:n.indexOf("zh"))?t:r},o=function e(){var t=window.locale||window.navigator.language||window.navigator.userLanguage;return 0!==(null==t?void 0:t.indexOf("zh"))}}}]);