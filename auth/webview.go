package auth

import (
	"cosy/config"
	"cosy/definition"
	"cosy/extension"
	"cosy/extension/mcpconfig"
	"cosy/extension/rule"
	"cosy/global"
	"cosy/log"
	"cosy/stable"
	"cosy/user"
	"cosy/util"
	cosyWebsocket "cosy/websocket"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/sourcegraph/jsonrpc2"

	"github.com/google/uuid"
	"github.com/gorilla/websocket"
	"golang.org/x/net/context"
)

var WebViewAuth sync.Map

const (
	MaxWebSocketClientChanNum  = 200
	MaxWebSocketRequestChanNum = 200
	ConnectionTimeoutInMinute  = 10
)

func init() {
}

type WebViewSavedParams struct {
	BaseUrl          string `json:"baseUrl"`
	State            string `json:"state"`
	FontColor        string `json:"fontColor"`
	FontColorGray    string `json:"fontColorGray"`
	CardBg           string `json:"cardBg"`
	Bg               string `json:"bg"`
	Locale           string `json:"locale"`
	IdeType          string `json:"ideType"`
	WorkspacePath    string `json:"workspacePath"`
	ActiveBg         string `json:"activeBg"`
	SelectBg         string `json:"selectBg"`
	MemoryId         string `json:"memoryId"`
	FocusBorder      string `json:"focusBorder"`
	InputBg          string `json:"inputBg"`
	ScrollbarBg      string `json:"scrollbarBg"`
	ScrollbarThumbBg string `json:"scrollbarThumbBg"`
	McpListView      string `json:"mcpListView"`
	TextLinkColor    string `json:"textLinkColor"`
	WidgetBorder     string `json:"widgetBorder"`
	WidgetShadow     string `json:"widgetShadow"`
	DividerColor     string `json:"dividerColor"`
	McpMarketView    string `json:"mcpMarketView"`
	TabColor         string `json:"tabColor"`
	ThemeColor       string `json:"themeColor"`
	ButtonBg         string `json:"buttonBg"`
	ButtonTextColor  string `json:"buttonTextColor"`
	ButtonHoverBg    string `json:"buttonHoverBg"`
	EditorBackground string `json:"editorBackground"`
}

type WebViewClient struct {
	State   string          `json:"state"`
	IdeType string          `json:"ideType"`
	Conn    *websocket.Conn `json:"conn"`
	Timer   *time.Timer     `json:"timer"`
	Mtx     sync.Mutex      `json:"mtx"`
}

// WebViewWireResponse 定义WebSocket响应结构
type WebViewWireResponse struct {
	VersionTag cosyWebsocket.VersionTag `json:"jsonrpc"`
	Id         jsonrpc2.ID              `json:"id"`              // 与请求ID匹配
	Result     json.RawMessage          `json:"result"`          // 响应结果
	Error      *WebViewError            `json:"error,omitempty"` // 可选的错误信息
}

// WebViewError 定义错误信息结构
type WebViewError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

type WebViewClientManager struct {
	//clients    map[*websocket.Conn]*WebViewClient
	clients    *sync.Map
	forward    chan *definition.InnerWebViewRequest
	register   chan *WebViewClient
	unregister chan *WebViewClient
}

func (c *WebViewClient) watchTimeout() {
	select {
	case <-c.Timer.C:
		// Connection inactive for 10 minutes
		log.Debugf("state deleted after 10min timeout: %s", c.State)
		WebViewAuth.Delete(c.State)
	}
}

func InitWebViewClientManager() WebViewClientManager {
	manager := WebViewClientManager{
		//clients:    make(map[*websocket.Conn]*WebViewClient),
		clients:    &sync.Map{},
		forward:    make(chan *definition.InnerWebViewRequest, MaxWebSocketRequestChanNum),
		register:   make(chan *WebViewClient, MaxWebSocketClientChanNum),
		unregister: make(chan *WebViewClient, MaxWebSocketClientChanNum),
	}

	go manager.Run()

	return manager
}

func (cm *WebViewClientManager) Run() {
	for {
		select {
		case client := <-cm.register:
			log.Debugf("Webview client %s registered", client.State)
			cm.clients.Store(client.Conn, client)
			go client.watchTimeout()
			client.Conn.SetPingHandler(func(appData string) error {
				log.Debugf("webview client %s: received ping", client.State)
				client.Timer.Reset(ConnectionTimeoutInMinute * time.Minute)
				return nil
			})
		case client := <-cm.unregister:
			log.Debugf("Webview client %s unregistered", client.State)
			if _, ok := cm.clients.Load(client.Conn); ok {
				cm.clients.Delete(client.Conn)
				err := client.Conn.Close()
				if err != nil {
					log.Warnf("Close websocket connection error: %v", err)
					return
				}
			}
		case req := <-cm.forward:
			stable.GoSafe(context.Background(), func() {

				// 当使用广播请求时，在实际传输数据时加锁，分发时不加锁
				if req.IsBroadCast {
					cm.DispatchMethod(context.Background(), req)
					return
				}

				value, ok := cm.clients.Load(req.Conn)
				if !ok {
					return
				}

				var client *WebViewClient
				if client, ok = value.(*WebViewClient); !ok {
					return
				}

				client.Mtx.Lock()
				defer client.Mtx.Unlock()
				// JB 端不支持心跳 ping，webview/ws/ping 用于 JB 端防止断联
				if req.Method == "webview/ws/ping" {
					log.Debugf("webview client %s: received ping", client.State)
					client.Timer.Reset(ConnectionTimeoutInMinute * time.Minute)
					return
				}
				cm.DispatchMethod(context.Background(), req)
			}, stable.SceneWebView)
		}
	}
}

func (cm *WebViewClientManager) DispatchMethod(ctx context.Context, req *definition.InnerWebViewRequest) {
	_, cancel := context.WithTimeout(ctx, 30*time.Second)
	// After the request is completed, close current ctx and all children goroutine
	defer cancel()

	log.Debugf("call auth webview method >> %s", req.Method)

	// 查找请求来源的连接
	var sourceConn *websocket.Conn
	// 直接使用请求中的连接
	if req.Conn != nil {
		sourceConn = req.Conn
	} else {
		// 如果请求中没有连接，可以通过其他方式查找（例如根据IdeType）
		// 这里我们保留空逻辑，因为当前实现中req.Conn应该已经设置了
		log.Warnf("Request has no connection specified, may not be able to respond")
	}

	// 创建WebViewService实例
	webViewService := NewWebViewService()
	switch req.Method {
	case "webview/memory/list":
		// 调用Service层处理memory列表请求
		if err := webViewService.HandleMemoryList(req, sourceConn); err != nil {
			log.Errorf("Handle memory list failed: %v", err)
		}

	case "webview/memory/count":
		// 调用Service层处理memory记录计数请求
		if err := webViewService.HandleMemoryCount(req, sourceConn); err != nil {
			log.Errorf("Handle memory count failed: %v", err)
		}

	case "webview/memory/delete":
		// 调用Service层处理memory删除请求
		if err := webViewService.HandleMemoryDelete(req, sourceConn); err != nil {
			log.Errorf("Handle memory delete failed: %v", err)
		}

	case "webview/profile/update/dataPolicy":
		// 调用Service层处理数据策略更新请求
		if err := webViewService.HandleUpdateDataPolicy(ctx, req, cm); err != nil {
			log.Errorf("Handle update data policy failed: %v", err)
		}

	case "webview/profile/update/renderPage":
		// 调用Service层处理渲染页面更新请求
		if err := webViewService.HandleUpdateRenderPage(req, cm); err != nil {
			log.Errorf("Handle update render page failed: %v", err)
		}

	case "webview/profile/update/command/officialOrderEnd":
		// 调用Service层处理官方命令结束请求
		if err := webViewService.HandleUpdateOfficialCommandEnd(req, cm); err != nil {
			log.Errorf("Handle update official command end failed: %v", err)
		}

	case "webview/openUrl":
		// 调用Service层处理打开URL请求
		if err := webViewService.HandleOpenUrl(req); err != nil {
			log.Errorf("Handle open url failed: %v", err)
		}

	case "webview/mcpSetting/listServers":
		// 调用Service层处理mcp列表查询请求
		if err := webViewService.HandleMcpSeverList(req, sourceConn); err != nil {
			log.Errorf("Handle mcpServer list failed: %v", err)
		}

	case "webview/mcpSetting/getServerInfo":
		// 调用Service层处理mcpSever详情查询请求
		if err := webViewService.HandleMcpDetail(req, sourceConn); err != nil {
			log.Errorf("Handle mcpServer detail failed: %v", err)
		}

	case "webview/mcpSetting/verify":
		// 调用Service层处理mcpSever配置校验服务
		if err := webViewService.HandleMcpVerify(req, sourceConn); err != nil {
			log.Errorf("Handle mcpServer verify failed: %v", err)
		}

	case "webview/mcpSetting/addServer":
		// 调用Service层处理mcpSever配置添加服务
		if err := webViewService.HandleAddMcpSever(req, sourceConn); err != nil {
			log.Errorf("Handle mcpServer add failed: %v", err)
		}

	case "webview/mcpSetting/updateServer":
		// 调用Service层处理mcpSever配置添加服务
		if err := webViewService.HandleUpdateMcpSever(req, sourceConn); err != nil {
			log.Errorf("Handle mcpServer update failed: %v", err)
		}

	case "webview/mcpSetting/configServer":
		// 调用Service层处理mcpSever运维操作相关服务
		if err := webViewService.HandleConfigMcpSever(req, sourceConn); err != nil {
			log.Errorf("Handle mcpServer config failed: %v", err)
		}

	case "webview/mcpSetting/overview":
		// 调用Service层处理mcpSever配置查询相关服务
		if err := webViewService.HandleMcpConfigOverView(req, sourceConn); err != nil {
			log.Errorf("Handle mcpServer overview failed: %v", err)
		}

	case "webview/mcpMarket/listRecommended":
		// mcp市场推荐列表
		if err := webViewService.HandleMcpMarketRecommend(req, sourceConn); err != nil {
			log.Errorf("Handle mcpMarket recommend failed: %v", err)
		}

	case "webview/mcpMarket/searchMarket":
		// 搜索mcp市场
		if err := webViewService.HandleMcpMarketSearch(req, sourceConn); err != nil {
			log.Errorf("Handle mcpMarket search failed: %v", err)
		}

	case "webview/mcpMarket/install":
		// 安装
		if err := webViewService.HandleMcpMarketInstall(req, sourceConn); err != nil {
			log.Errorf("Handle mcpMarket install failed: %v", err)
		}

	case "webview/mcpMarket/installWithEnv":
		// 带env安装
		if err := webViewService.HandleMcpMarketInstallWithEnv(req, sourceConn); err != nil {
			log.Errorf("Handle mcpMarket install with env failed: %v", err)
		}
	case "webview/sendNotification":
		if err := webViewService.HandleSendNotification(req, cm); err != nil {
			log.Errorf("Handle send notification failed: %v", err)
		}
	case "webview/projectRule/add":
		if err := webViewService.AddProjectRule(req, sourceConn); err != nil {
			log.Errorf("WebViewService.AddProjectRule failed: %v", err)
		}
	case "webview/projectRule/edit":
		if err := webViewService.EditProjectRule(req, sourceConn); err != nil {
			log.Errorf("WebViewService.EditProjectRule failed: %v", err)
		}
	case "webview/projectRule/delete":
		if err := webViewService.DeleteProjectRule(req, sourceConn); err != nil {
			log.Errorf("WebViewService.DeleteProjectRule failed: %v", err)
		}
	case "webview/projectRule/getByName":
		if err := webViewService.GetProjectRuleByName(req, sourceConn); err != nil {
			log.Errorf("WebViewService.GetProjectRuleByPath failed: %v", err)
		}
	case "webview/projectRule/list":
		if err := webViewService.ListProjectRules(req, sourceConn); err != nil {
			log.Errorf("WebViewService.ListProjectRules failed: %v", err)
		}
	case "webview/projectRule/count":
		if err := webViewService.CountProjectRules(req, sourceConn); err != nil {
			log.Errorf("WebViewService.CountProjectRules failed: %v", err)
		}
	case "webview/settings/codebase/fetchStatus":
		if err := webViewService.HandleCodebaseSettingFetchStatus(req, sourceConn); err != nil {
			log.Errorf("Handle codebase setting fetch status failed: %v", err)
		}
	case "webview/settings/codebase/updateStatus":
		if err := webViewService.HandleCodebaseSettingUpdateStatus(req, sourceConn); err != nil {
			log.Errorf("Handle codebase setting update status failed: %v", err)
		}
	case "webview/settings/codebase/startIndex":
		if err := webViewService.HandleCodebaseSettingStartIndex(req, sourceConn); err != nil {
			log.Errorf("Handle codebase setting start index failed: %v", err)
		}
	case "webview/settings/codebase/cancelIndex":
		if err := webViewService.HandleCodebaseSettingCancelIndex(req, sourceConn); err != nil {
			log.Errorf("Handle codebase setting cancel index failed: %v", err)
		}
	default:
		log.Warnf("Unknown method: %s", req.Method)
	}

	log.Debugf("call auth webview method finish >> %s", req.Method)
}

func (cm *WebViewClientManager) BroadcastClient(req *definition.InnerWebViewRequest, sameIdeRequired bool) {
	wireRequest := &definition.WebViewWireRequest{
		Id:     req.Id,
		Method: req.Method,
		Params: req.Params,
	}

	broadCastCnt := atomic.Int32{}
	broadCastCnt.Store(0)
	cm.clients.Range(func(key, value interface{}) bool {
		stable.GoSafe(context.Background(), func() {
			var client *WebViewClient
			var ok bool
			client, ok = value.(*WebViewClient)
			if !ok || client == nil {
				return
			}

			if sameIdeRequired && client.IdeType != req.IdeType {
				return
			}

			err := cm.WriteJSON(client, wireRequest)
			if err != nil {
				log.Debugf("unregister client, broadcast client write to client failed: %v", err)
				cm.unregister <- client
				return
			}

			broadCastCnt.Add(1)
		}, stable.SceneWebView)

		return true
	})

	log.Infof("%d client has received the message", broadCastCnt.Load())
}

func (cm *WebViewClientManager) WriteJSON(client *WebViewClient, wireRequest *definition.WebViewWireRequest) error {
	if client == nil || client.Conn == nil {
		return errors.New("client is nil or conn is nil")
	}
	if wireRequest == nil {
		return errors.New("wireRequest is nil")
	}

	client.Mtx.Lock()
	defer client.Mtx.Unlock()

	return client.Conn.WriteJSON(wireRequest)
}

func (s *HttpServer) HandleWebViewWebSocket(w http.ResponseWriter, r *http.Request) {
	var upgrader = websocket.Upgrader{
		ReadBufferSize:  1024 * 1024,
		WriteBufferSize: 1024 * 1024,
		CheckOrigin: func(r *http.Request) bool {
			// TODO 跨域处理
			return true
		},
	}
	params := util.ParseParameters(r)
	// 解析插件传来的参数
	if !checkParamsValidity(params) {
		log.Errorf("illegal websocket request has been denied")
		_, _ = fmt.Fprint(w, errors.New("illegal websocket request"))
		return
	}

	conn, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Errorf("upgrade websocket failed,%s", err.Error())
		_, _ = fmt.Fprint(w, errors.New("upgrade websocket failed"))
		return
	}

	urlParams, ok := getWebviewSavedParams(params["state"])
	if !ok {
		log.Errorf("faied to get webview saved params")
		return
	}

	if len(urlParams.State) >= 10 {
		log.Infof("%s ws build success", urlParams.State[0:10])
	}

	client := &WebViewClient{
		State:   urlParams.State,
		IdeType: urlParams.IdeType,
		Conn:    conn,
		Mtx:     sync.Mutex{},
		Timer:   time.NewTimer(ConnectionTimeoutInMinute * time.Minute),
	}

	log.Debugf("%s ws register success", urlParams.State)
	s.Cm.register <- client

	defer func() {
		// 兜底关闭连接
		log.Debugf("%s ws close success", urlParams.State)
		s.Cm.unregister <- client
	}()

	for {
		_, message, err := conn.ReadMessage()
		if err != nil {
			log.Warnf("%s ws read message failed: %v", urlParams.State, err)
			break
		}
		var req definition.WebViewWireRequest
		err = json.Unmarshal(message, &req)
		if err != nil {
			continue
		}
		innerReq := definition.InnerWebViewRequest{
			WebViewWireRequest: definition.WebViewWireRequest{
				Id:     req.Id,
				Method: req.Method,
				Params: req.Params,
			},
			IdeType:       urlParams.IdeType,
			Conn:          conn,
			WorkspacePath: urlParams.WorkspacePath,
		}

		s.Cm.forward <- &innerReq
	}
}

func (s *HttpServer) HandleWebViewPage(w http.ResponseWriter, r *http.Request) {
	userInfo := user.GetCachedUserInfo()
	if userInfo == nil {
		log.Errorf("Profile Invalid login parameters")
		_, _ = fmt.Fprint(w, s.loginResultPageWithError(-1, ErrorParameterCheck, nil))
		return
	}

	w.Header().Set("Access-Control-Allow-Origin", "*")
	params := util.ParseParameters(r)

	// 返回参数为空
	if len(params) == 0 {
		log.Errorf("missing request url parameters")
		_, _ = fmt.Fprint(w, s.loginResultPageWithError(-1, ErrorParameterCheck, nil))
		return
	}

	// 解析插件传来的参数
	if !checkParamsValidity(params) {
		log.Errorf("profile request url parameters")
		_, _ = fmt.Fprint(w, s.loginResultPageWithError(-1, ErrorParameterCheck, nil))
		return
	}

	profilePage, err := s.fillProfilePageWithParams(params["state"], userInfo)
	if err != nil {
		log.Errorf("profile request url parameters")
		_, _ = fmt.Fprint(w, s.loginResultPageWithError(-1, ErrorParameterCheck, nil))
		return
	}

	// 写回profile
	_, _ = fmt.Fprint(w, profilePage)
}

func getWebviewSavedParams(state string) (*WebViewSavedParams, bool) {
	if savedParamsInterface, ok := WebViewAuth.Load(state); ok {
		if savedParams, ok2 := savedParamsInterface.(*WebViewSavedParams); ok2 {
			return savedParams, true
		}
	}
	return nil, false
}

func checkParamsValidity(params map[string]string) bool {
	if _, ok := params["state"]; !ok {
		log.Warnf("state don't exist in param")
		return false
	}

	if savedParams, ok := getWebviewSavedParams(params["state"]); !ok || savedParams == nil {
		log.Warnf("%s don't exist in WebViewAuth", params["state"])
		return false
	} else {
		if savedParams.State != params["state"] {
			log.Warnf("state don't match: saved state (%s) != param state (%s)", savedParams.State, params["state"])
			return false
		}
	}

	return true
}
func getLocale(paramsLocale string) string {
	locale := ""
	if strings.Contains(paramsLocale, definition.LocaleZh) {
		locale = definition.LocaleZh
	} else if strings.Contains(paramsLocale, definition.LocaleEn) {
		locale = definition.LocaleEn
	}

	return locale
}
func getEdition(userInfo *user.CachedUserInfo) string {
	if config.OnPremiseMode {
		return definition.WebViewEditionEnEnterprisePrivate
	}

	edition := definition.WebViewEditionEnPersonalStandard
	if userInfo == nil {
		return edition
	}

	if userInfo.UserType == definition.UserTypePersonalStandard ||
		userInfo.UserType == definition.UserTypePersonalProfessional {
		edition = definition.WebViewEditionEnPersonalStandard
	} else if userInfo.UserType == definition.UserTypeEnterpriseStandard {
		edition = definition.WebViewEditionEnEnterpriseStandard
	} else if config.IsDedicatedEndpointConfigured() {
		edition = definition.WebViewEditionEnEnterpriseProfessional
	}

	return edition
}

func (s *HttpServer) fillProfilePageWithParams(state string, userInfo *user.CachedUserInfo) (string, error) {
	if _, ok := getWebviewSavedParams(state); !ok {
		return "", errors.New("error state in profile page")
	}

	webViewConfig := getWebViewGlobalConfig(state, userInfo)

	placeholders := map[string]string{
		"{IS_SELECT_ACCOUNT}":          strconv.FormatBool(webViewConfig.IsSelectAccount),
		"{ERROR_CODE}":                 strconv.Itoa(webViewConfig.ErrorCode),
		"{ERROR_MESSAGE}":              webViewConfig.ErrorMessage,
		"{ERROR_MESSAGE_CODE}":         strconv.Itoa(webViewConfig.ErrorMessageCode),
		"{USER_INFO}":                  webViewConfig.UserInfo,
		"{ON_PREMISE}":                 strconv.FormatBool(webViewConfig.OnPremise),
		"{EDITION}":                    webViewConfig.Edition,
		"{USER_NAME}":                  webViewConfig.UserName,
		"{USER_ID}":                    webViewConfig.UserId,
		"{ORG_NAME}":                   webViewConfig.OrgName,
		"{POLICY_AGREED}":              strconv.FormatBool(webViewConfig.PolicyAgreed),
		"{USER_AVATAR}":                webViewConfig.UserAvatar,
		"{RESOURCE_VERSION}":           webViewConfig.ResourceVersion,
		"{TEAMIX_UI_RESOURCE_VERSION}": webViewConfig.TeamixUiResourceVersion,
		"{CHAT_FILTER}":                webViewConfig.ChatFilter,
		"{COMPLETION_FILTER}":          webViewConfig.CompletionFilter,
		"{POST_CHAT_FILTER}":           webViewConfig.PostChatFilter,
		"{POST_COMPLETION_FILTER}":     webViewConfig.PostCompletionFilter,
		"{OFFICIAL_COMMANDS_END}":      strconv.FormatBool(webViewConfig.OfficialCommandsEnd),
		"{COMMANDS}":                   webViewConfig.Commands,
		"{REGION_ENV}":                 webViewConfig.RegionEnv,
		"{MEMORY_COUNT}":               strconv.Itoa(webViewConfig.MemoryCount),
		"{WORKSPACE_PATH}":             strconv.Quote(webViewConfig.WorkspacePath),
		"{MCP_DOCS_URL}":               webViewConfig.McpDocsUrl,
		"{MCP_CONFIG_OVERVIEW}":        webViewConfig.McpConfigOverview,
		"{PROJECT_RULE_COUNT}":         strconv.Itoa(webViewConfig.ProjectRuleCount),
	}

	// 不要直接修改原始模板内容
	page := loginResultHtml
	for k, v := range placeholders {
		page = strings.ReplaceAll(page, k, v)
	}
	return page, nil
}

func (s *HttpServer) GenerateProfileUrl(params *definition.WebViewParams) (string, definition.WebViewUrlParams) {
	state := strings.ReplaceAll(uuid.NewString(), "-", "")
	locale := getLocale(params.Locale)

	urlParams := map[string]string{
		"state":            state,
		"fontColor":        params.FontColor,
		"fontColorGray":    params.FontColorGray,
		"cardBg":           params.CardBg,
		"bg":               params.Bg,
		"locale":           params.Locale,
		"activeBg":         params.ActiveBg,
		"selectBg":         params.SelectBg,
		"memoryId":         params.MemoryId,
		"focusBorder":      params.FocusBorder,
		"inputBg":          params.InputBg,
		"scrollbarBg":      params.ScrollbarBg,
		"scrollbarThumbBg": params.ScrollbarThumbBg,
		"mcpListView":      params.McpListView,
		"textLinkColor":    params.TextLinkColor,
		"widgetBorder":     params.WidgetBorder,
		"widgetShadow":     params.WidgetShadow,
		"dividerColor":     params.DividerColor,
		"mcpMarketView":    params.McpMarketView,
		"tabColor":         params.TabColor,
		"themeColor":       params.ThemeColor,
		"buttonBg":         params.ButtonBg,
		"buttonTextColor":  params.ButtonTextColor,
		"buttonHoverBg":    params.ButtonHoverBg,
		"editorBackground": params.EditorBackground,
	}

	baseUrl := "http://127.0.0.1:" + strconv.Itoa(s.HttpPort) + "/profile"
	WebViewAuth.Store(state, &WebViewSavedParams{
		BaseUrl:          baseUrl,
		State:            state,
		FontColor:        params.FontColor,
		FontColorGray:    params.FontColorGray,
		CardBg:           params.CardBg,
		Bg:               params.Bg,
		Locale:           locale,
		IdeType:          params.IdeType,
		WorkspacePath:    params.WorkspacePath,
		ActiveBg:         params.ActiveBg,
		SelectBg:         params.SelectBg,
		MemoryId:         params.MemoryId,
		FocusBorder:      params.FocusBorder,
		InputBg:          params.InputBg,
		ScrollbarBg:      params.ScrollbarBg,
		ScrollbarThumbBg: params.ScrollbarThumbBg,
		McpListView:      params.McpListView,
		TextLinkColor:    params.TextLinkColor,
		WidgetBorder:     params.WidgetBorder,
		WidgetShadow:     params.WidgetShadow,
		DividerColor:     params.DividerColor,
		McpMarketView:    params.McpMarketView,
		TabColor:         params.TabColor,
		ThemeColor:       params.ThemeColor,
		ButtonBg:         params.ButtonBg,
		ButtonTextColor:  params.ButtonTextColor,
		ButtonHoverBg:    params.ButtonHoverBg,
		EditorBackground: params.EditorBackground,
	})

	reqUrl := baseUrl + "?" + util.BuildParameter(urlParams)
	return state, definition.WebViewUrlParams{
		Url: reqUrl,
	}
}

func (s *HttpServer) GetGlobalConfig(params *definition.WebViewParams) definition.WebViewConfigParams {
	state, _ := s.GenerateProfileUrl(params)
	userInfo := user.GetCachedUserInfo()
	if userInfo == nil {
		userInfo = &user.CachedUserInfo{}
	}
	return getWebViewGlobalConfig(state, userInfo)
}

func getWebViewGlobalConfig(state string, userInfo *user.CachedUserInfo) definition.WebViewConfigParams {
	assertVersion := _assertVersion
	if global.BuildOption.AssertVersion != "" {
		assertVersion = global.BuildOption.AssertVersion
	}

	edition := getEdition(userInfo)
	avatar := userInfo.AvatarUrl
	if avatar == "" {
		if user.GetCachedAuthStatus() != nil {
			cachedAuthStatus := user.GetCachedAuthStatus()
			avatar = cachedAuthStatus.AvatarUrl
		}
	}

	// 获取用户长期记忆数量
	memoryCount := 0
	webViewService := NewWebViewService()
	if userInfo.Uid != "" {
		count, err := webViewService.GetLongTermMemoryCount(userInfo.Uid)
		if err != nil {
			log.Warnf("Get user long term memory count failed: %v", err)
		} else {
			memoryCount = count
		}
	}

	// 前置过滤器元数据渲染
	chatFilterRes := extension.GetContextFilterSettingByBizType(extension.BizTypeChatAsk)
	chatFilter := definition.WebViewFilterJsObject{
		IsConfigured:   chatFilterRes.IsConfigured,
		Enabled:        chatFilterRes.State == extension.EnableState,
		IsScript:       chatFilterRes.ComponentType == extension.ScriptType,
		HandleStrategy: chatFilterRes.Strategy,
	}

	chatFilterObj, err := json.Marshal(chatFilter)
	if err != nil {
		log.Errorf("marshal chat filter failed, %s", err.Error())
	}

	completionFilterRes := extension.GetContextFilterSettingByBizType(extension.BizTypeCompletion)
	completionFilter := definition.WebViewFilterJsObject{
		IsConfigured:   completionFilterRes.IsConfigured,
		Enabled:        completionFilterRes.State == extension.EnableState,
		IsScript:       completionFilterRes.ComponentType == extension.ScriptType,
		HandleStrategy: completionFilterRes.Strategy,
	}
	completionFilterObj, err := json.Marshal(completionFilter)
	if err != nil {
		log.Errorf("marshal completion filter failed, %s", err.Error())
	}

	// 后置过滤器元数据渲染
	postChatFilterRes := extension.GetPostContextFilterSettingByBizType(extension.BizTypeChatAsk)
	postChatFilter := definition.WebViewFilterJsObject{
		IsConfigured:   postChatFilterRes.IsConfigured,
		Enabled:        postChatFilterRes.State == extension.EnableState,
		IsScript:       postChatFilterRes.ComponentType == extension.ScriptType,
		HandleStrategy: postChatFilterRes.Strategy,
	}

	postChatFilterObj, err := json.Marshal(postChatFilter)
	if err != nil {
		log.Errorf("marshal postChatFilter failed, %s", err.Error())
	}

	postCompletionFilterRes := extension.GetPostContextFilterSettingByBizType(extension.BizTypeCompletion)
	postCompletionFilter := definition.WebViewFilterJsObject{
		IsConfigured:   postCompletionFilterRes.IsConfigured,
		Enabled:        postCompletionFilterRes.State == extension.EnableState,
		IsScript:       postCompletionFilterRes.ComponentType == extension.ScriptType,
		HandleStrategy: postCompletionFilterRes.Strategy,
	}
	postCompletionFilterObj, err := json.Marshal(postCompletionFilter)
	if err != nil {
		log.Errorf("marshal post completion filter failed, %s", err.Error())
	}

	command, err := json.Marshal(extension.GlobalProfileData.CommandOrder.Commands)
	if err != nil {
		log.Errorf("marshal command failed, %s", err.Error())
	}

	// 获取mcpServer摘要信息
	mcpConfigOverview := mcpconfig.GetMcpConfigOverview()
	mcpConfigOverviewObj, err := json.Marshal(mcpConfigOverview)
	if err != nil {
		log.Errorf("marshal mcpConfigOverview failed, %s", err.Error())
	}

	//获取project rule总数
	webviewSavedParams, ok := getWebviewSavedParams(state)
	if !ok {
		log.Errorf("Failed to obtain webview saved params and the workspace path")
	}

	globalConfig := definition.WebViewConfigParams{
		State:                   state,
		ProfileWebsocketPort:    global.ProfileWebsocketPort,
		IsSelectAccount:         true,
		ErrorCode:               ErrorNone,
		ErrorMessage:            "",
		ErrorMessageCode:        -1,
		UserInfo:                "{}",
		OnPremise:               config.OnPremiseMode,
		Edition:                 edition,
		UserName:                userInfo.Name,
		UserId:                  userInfo.Uid,
		OrgName:                 userInfo.OrgName,
		PolicyAgreed:            userInfo.DataPolicyAgreed,
		UserAvatar:              avatar,
		ResourceVersion:         assertVersion,
		TeamixUiResourceVersion: "1.5.27",
		ChatFilter:              string(chatFilterObj),
		CompletionFilter:        string(completionFilterObj),
		PostChatFilter:          string(postChatFilterObj),
		PostCompletionFilter:    string(postCompletionFilterObj),
		OfficialCommandsEnd:     extension.GlobalProfileData.CommandOrder.OfficialCommandEnd,
		Commands:                string(command),
		RegionEnv:               config.RegionEnv,
		MemoryCount:             memoryCount,
		McpDocsUrl:              "https://help.aliyun.com/zh/lingma/use-of-mcp",
		McpConfigOverview:       string(mcpConfigOverviewObj),
	}

	if webviewSavedParams != nil {
		globalConfig.WorkspacePath = webviewSavedParams.WorkspacePath
		projectRuleCount, _ := rule.CountProjectRules(webviewSavedParams.WorkspacePath)
		globalConfig.ProjectRuleCount = projectRuleCount
	}

	return globalConfig
}

func (s *HttpServer) UpdateWebViewPage(params *definition.WebViewParams) error {
	userInfo := user.GetCachedUserInfo()
	if userInfo == nil {
		return errors.New("user info is nil")
	}
	edition := getEdition(userInfo)
	locale := getLocale(params.Locale)

	reqParams := definition.WebViewRenderPageParams{
		FontColor:        params.FontColor,
		FontColorGray:    params.FontColorGray,
		CardBg:           params.CardBg,
		Bg:               params.Bg,
		Locale:           locale,
		Edition:          edition,
		SelectBg:         params.SelectBg,
		ActiveBg:         params.ActiveBg,
		FocusBorder:      params.FocusBorder,
		InputBg:          params.InputBg,
		ScrollbarBg:      params.ScrollbarBg,
		ScrollbarThumbBg: params.ScrollbarThumbBg,
		TextLinkColor:    params.TextLinkColor,
		WidgetBorder:     params.WidgetBorder,
		WidgetShadow:     params.WidgetShadow,
		DividerColor:     params.DividerColor,
		TabColor:         params.TabColor,
		ThemeColor:       params.ThemeColor,
		ButtonBg:         params.ButtonBg,
		ButtonTextColor:  params.ButtonTextColor,
		ButtonHoverBg:    params.ButtonHoverBg,
		EditorBackground: params.EditorBackground,
	}

	data, err := json.Marshal(reqParams)
	if err != nil {
		log.Errorf("update webview page error: %v", err)
		return err
	}

	innerRequest := &definition.InnerWebViewRequest{
		WebViewWireRequest: definition.WebViewWireRequest{
			Id: jsonrpc2.ID{
				Num:      0,
				Str:      "",
				IsString: false,
			},
			Method: "webview/profile/update/renderPage",
			Params: json.RawMessage(data),
		},
		IdeType:     params.IdeType,
		IsBroadCast: true,
	}

	log.Infof("new profile page render success")
	s.Cm.forward <- innerRequest

	return nil
}

func (s *HttpServer) SendNotificationToWebView(notification *definition.WebViewNotification) error {
	data, err := json.Marshal(notification)
	if err != nil {
		log.Errorf("send notification to webview error: %v", err)
		return err
	}

	innerRequest := &definition.InnerWebViewRequest{
		WebViewWireRequest: definition.WebViewWireRequest{
			Id: jsonrpc2.ID{
				Num:      0,
				Str:      uuid.NewString(),
				IsString: true,
			},
			Method: "webview/sendNotification",
			Params: json.RawMessage(data),
		},
		IsBroadCast: true,
	}

	log.Infof("send notification to webview success, %v", notification)
	s.Cm.forward <- innerRequest

	return nil
}
