package mcp

import (
	"cosy/auth"
	"cosy/definition"
	"cosy/extension/mcpconfig"
	"cosy/log"
	"cosy/util/collection"
	"runtime"
	"time"

	"github.com/fsnotify/fsnotify"
)

func InitMCP() {
	// 初始化mcp参数配置
	mcpconfig.Initialize()

	// 启动mcpSever
	go mcpconfig.StartMcpSevers(startWatchUserConfig)
}

// startWatchUserConfig 开始监听用户配置文件变化
func startWatchUserConfig() {
	// 监听userConfig文件变化
	watcher, err := fsnotify.NewWatcher()
	if err != nil {
		log.Errorf("[user_config_mcp] startWatchUserConfig error:%v", err)
		return
	}
	defer func(watcher *fsnotify.Watcher) {
		if r := recover(); r != nil {
			log.Errorf("[user_config_mcp] Panic recovered in WatchUserConfig: %v", r)
		}
		err := watcher.Close()
		if err != nil {
			log.Errorf("[user_config_mcp] close watcher error:%v", err)
		}
	}(watcher)

	openWatcher := func() {
		// 加载userConfig
		userConfigExists := mcpconfig.CheckMCPConfigFileExist(mcpconfig.UserMCPConfigFilePath)
		if !userConfigExists {
			config := mcpconfig.MCPOfficialConfig{
				MCPServers: *collection.NewLinkedHashMap[string, mcpconfig.MCPOfficialConfigItem](),
			}
			mcpconfig.WriteUserMCPConfigToDisk(mcpconfig.UserMCPConfigFilePath, &config)
		}
		err = watcher.Add(mcpconfig.UserMCPConfigFilePath)
		if err != nil {
			log.Errorf("[user_config_mcp] start init user mcp config watcher error:%v", err)
		}
	}

	done := make(chan bool)
	var refreshTimer *time.Timer
	var mergeTimer *time.Timer

	refreshFunc := func() {
		// 停止之前的定时器（如果存在）
		if mergeTimer != nil {
			mergeTimer.Stop()
		}
		_, md5, loadErr := mcpconfig.LoadUserConfigFromDisk(mcpconfig.UserMCPConfigFilePath)
		if loadErr == nil {
			auth.AuthServer.SendNotificationToWebView(&definition.WebViewNotification{
				NotificationId: definition.NotificationIdMcpUserJsonNoError,
			})
			mcpconfig.GlobalUserConfigError = false
			mcpconfig.GlobalUserConfigErrorMsg = ""
			if mcpconfig.GlobalMCPSeversConfig.UserConfigMD5 == md5 {
				log.Debugf("[user_config_mcp] User config has not changed since last merge, skipping")
			} else {
				// md5发生变化才触发新的merge任务
				auth.AuthServer.SendNotificationToWebView(&definition.WebViewNotification{
					NotificationId: definition.NotificationIdMcpUserJsonUpdating,
				})
				// 创建新的定时器，x秒后触发刷新
				mergeTimer = time.AfterFunc(mcpconfig.UserConfigMergeDelay, MergeUserConfigAndRefreshServers)
			}
		} else {
			auth.AuthServer.SendNotificationToWebView(&definition.WebViewNotification{
				NotificationId: definition.NotificationIdMcpUserJsonError,
				ErrorMsg:       loadErr.Error(),
			})
			mcpconfig.GlobalUserConfigError = true
			mcpconfig.GlobalUserConfigErrorMsg = loadErr.Error()
		}
	}

	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("[user_config_mcp] Panic recovered in WatchUserConfig: %v", r)
				// 获取当前堆栈信息
				stack := make([]byte, 4096)
				stack = stack[:runtime.Stack(stack, false)]
				log.Debugf("recover from mcp config watcher crash. err: %+v, stack: %s", r, stack)
			}
		}()

		for {
			select {
			case event, ok := <-watcher.Events:
				if !ok {
					return
				}
				if event.Op&fsnotify.Write == fsnotify.Write { //文件内容被修改
					log.Infof("[user_config_mcp] user mcp file write, do refresh")
					if refreshTimer != nil {
						refreshTimer.Stop()
					}
					refreshTimer = time.AfterFunc(mcpconfig.UserConfigRefreshDelay, refreshFunc)
				} else if event.Op&fsnotify.Create == fsnotify.Create { // 文件被创建
					log.Infof("[user_config_mcp] user mcp file create, do refresh")
					if refreshTimer != nil {
						refreshTimer.Stop()
					}
					refreshTimer = time.AfterFunc(mcpconfig.UserConfigRefreshDelay, refreshFunc)
				} else if event.Op&fsnotify.Chmod == fsnotify.Chmod { // 文件被创建
					log.Infof("[user_config_mcp] user mcp file chmod, do refresh")
					if refreshTimer != nil {
						refreshTimer.Stop()
					}
					refreshTimer = time.AfterFunc(mcpconfig.UserConfigRefreshDelay, refreshFunc)
				} else if event.Op&fsnotify.Remove == fsnotify.Remove { // 文件被删除
					log.Infof("[user_config_mcp] user mcp file remove, do remove")
					// 重新开启监听
					openWatcher()
				} else if event.Op&fsnotify.Rename == fsnotify.Rename { // 文件被rename
					log.Infof("[user_config_mcp] user mcp file rename, do refresh")
					// 重新开启监听
					openWatcher()
				} else {
					log.Infof("[user_config_mcp] user mcp file event:%v, do nothing", event)
				}
			case err, ok := <-watcher.Errors:
				if !ok {
					return
				}
				log.Errorf("[user_config_mcp] watcher has error:%v", err)
			}
		}
	}()

	openWatcher()
	<-done
}

// MergeUserConfigAndRefreshServers 合并用户配置并刷新服务
func MergeUserConfigAndRefreshServers() {
	log.Debugf("[user_config_mcp] start merge user config mcp after user mcp file changed")
	// 获取用户配置
	userConfig, md5, err := mcpconfig.LoadUserConfigFromDisk(mcpconfig.UserMCPConfigFilePath)
	if err != nil {
		log.Errorf("[user_config_mcp] Failed to load user config during delayed merge: %v", err)
		return
	}
	if mcpconfig.GlobalMCPSeversConfig.UserConfigMD5 == md5 {
		log.Debugf("[user_config_mcp] User config has not changed since last merge, skipping")
		return
	}

	// 合并到本地配置
	mergeResult, err := mcpconfig.MergeUserConfig(userConfig, md5)
	if err != nil {
		auth.AuthServer.SendNotificationToWebView(&definition.WebViewNotification{
			NotificationId: definition.NotificationIdMcpUserJsonUpdated,
		})
		return
	}
	if mergeResult != nil {
		mcpconfig.RefreshMCPServersByMergeResult(mergeResult)
	}
	auth.AuthServer.SendNotificationToWebView(&definition.WebViewNotification{
		NotificationId: definition.NotificationIdMcpUserJsonUpdated,
	})
	log.Debugf("[user_config_mcp] User config successfully merged")
}
