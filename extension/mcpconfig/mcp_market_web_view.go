package mcpconfig

import (
	"cosy/client"
	"cosy/config"
	"cosy/definition"
	"cosy/errors"
	"cosy/log"
	"cosy/remote"
	"cosy/util"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"

	"github.com/google/uuid"
)

// ListMarketRecommend 获取推荐列表
func ListMarketRecommend() *definition.MCPMarketListResult {
	responseBody, err := requestLingmaServer(http.MethodGet, definition.UrlPathMcpListRecommendUrl, nil)
	if err != nil {
		return &definition.MCPMarketListResult{
			Success: false,
		}
	}
	serverResult := ServerMCPServerListResult{}
	err = json.Unmarshal(responseBody, &serverResult)
	if err != nil {
		log.Errorf("Failed to unmarshal to ServerMCPServerListResult. resp=%s, error=%v", string(responseBody), err)
		return &definition.MCPMarketListResult{
			Success: false,
		}
	}
	result := convertRecommendResult(&serverResult)
	if result.Items != nil {
		fillInstallStatus(result.Items)
	}
	return result
}

// SearchMarket 搜索MCP市场
func SearchMarket(request *definition.MCPMarketListRequest) *definition.MCPMarketListResult {
	serverRequest := &ServerMCPServerListRequest{
		PageNumber: 1,
		PageSize:   10,
	}
	if request != nil {
		if request.Page > 0 {
			serverRequest.PageNumber = request.Page
		}
		if request.PageSize > 0 {
			serverRequest.PageSize = request.PageSize
		}
		if request.Query != "" {
			serverRequest.Query = request.Query
		}
		if request.Categories != "" {
			// 现在只支持一个
			serverRequest.Category = request.Categories
		}
	}
	//url := fmt.Sprintf(ListServersUrl, serverRequest.PageNumber, serverRequest.PageSize, serverRequest.Query, serverRequest.Category)
	//responseBody, err := requestLingmaServer(http.MethodGet, url, nil)
	httpPayload := remote.HttpPayload{
		Payload:       util.ToJsonStr(serverRequest),
		EncodeVersion: config.Remote.MessageEncode,
	}
	responseBody, err := requestLingmaServer(http.MethodPost, definition.UrlPathMcpSearchServerUrl, httpPayload)
	if err != nil {
		return &definition.MCPMarketListResult{
			Success: false,
		}
	}
	serverResult := ServerMCPServerListResult{}
	err = json.Unmarshal(responseBody, &serverResult)
	if err != nil {
		log.Errorf("Failed to unmarshal to ServerMCPServerListResult. resp=%s, error=%v", string(responseBody), err)
		return &definition.MCPMarketListResult{
			Success: false,
		}
	}
	result := convertListResult(request, &serverResult)
	if result.Items != nil {
		fillInstallStatus(result.Items)
	}
	return result
}

// InstallFromMarket 从MCP市场安装
func InstallFromMarket(request *definition.MCPInstallRequest) *definition.MCPInstallResult {
	if checkInstalled(request.MarketType, request.ServerId) {
		return &definition.MCPInstallResult{
			Success:   false,
			ErrorCode: errors.MCPInstallExists,
			ErrorMsg:  "server already installed",
		}
	}
	serverItem, err := GetMarketMcpServerDetail(request.ServerId)
	if err != nil {
		return &definition.MCPInstallResult{
			Success:   false,
			ErrorCode: errors.MCPInstallServerNotFound,
			ErrorMsg:  err.Error(),
		}
	}
	serverName, configItem, err := parseOfficialConfig(serverItem.ServerConfiguration)
	if err != nil {
		return &definition.MCPInstallResult{
			Success:   false,
			ErrorCode: errors.MCPInstallFetchConfigError,
			ErrorMsg:  err.Error(),
		}
	}
	// 需要用户确认env
	if configItem.Env != nil && len(configItem.Env) > 0 {
		return &definition.MCPInstallResult{
			Success:   false,
			ErrorCode: errors.MCPInstallNeedConfirmEnv,
			ErrorMsg:  "need confirm env",
			Env:       configItem.Env,
		}
	}
	return doInstallFromMarket(serverItem, serverName, configItem)
}

// InstallWithEnvFromMarket 从MCP市场安装，带上用户确认的env
func InstallWithEnvFromMarket(request *definition.MCPInstallRequest) *definition.MCPInstallResult {
	if checkInstalled(request.MarketType, request.ServerId) {
		return &definition.MCPInstallResult{
			Success:   false,
			ErrorCode: errors.MCPInstallExists,
			ErrorMsg:  "server already installed",
		}
	}
	serverItem, err := GetMarketMcpServerDetail(request.ServerId)
	if err != nil {
		return &definition.MCPInstallResult{
			Success:   false,
			ErrorCode: errors.MCPInstallServerNotFound,
			ErrorMsg:  err.Error(),
		}
	}
	serverName, configItem, err := parseOfficialConfig(serverItem.ServerConfiguration)
	if err != nil {
		return &definition.MCPInstallResult{
			Success:   false,
			ErrorCode: errors.MCPInstallFetchConfigError,
			ErrorMsg:  err.Error(),
		}
	}
	// 使用用户确认的env进行替换
	configItem.Env = request.Env
	return doInstallFromMarket(serverItem, serverName, configItem)
}

func doInstallFromMarket(serverItem *definition.MCPMarketServerItem, serverName string, configItem *MCPOfficialConfigItem) *definition.MCPInstallResult {
	// 如果serverName存在，需要替换成一个不存在的
	_, exists := GlobalMCPSeversConfig.MCPServers[serverName]
	if exists {
		serverName = serverItem.ServerId
	}
	_, exists = GlobalMCPSeversConfig.MCPServers[serverName]
	if exists {
		serverName = fmt.Sprintf("%s_%s", serverName, uuid.NewString())
	}
	// 参数类型转化
	mcpSeversConfig := MCPSeversConfig{}
	mcpServers := make(map[string]MCPSeverConfig)
	mcpServers[serverName] = MCPSeverConfig{
		Name:    serverName,
		Command: configItem.Command,
		Env:     configItem.Env,
		Url:     configItem.Url,
		Args:    configItem.Args,
		Headers: configItem.Headers,
		Source:  SourceUser,
		From:    serverItem.MarketType,
		FromId:  serverItem.ServerId,
	}
	mcpSeversConfig.MCPServers = mcpServers
	err := addMCPSeverConfig(&mcpSeversConfig)
	if err != nil {
		return &definition.MCPInstallResult{
			Success:   false,
			ErrorCode: errors.MCPInstallCommonError,
			ErrorMsg:  err.Error(),
		}
	}
	slsAddChannel("market", serverName, serverItem.ServerId)
	return &definition.MCPInstallResult{
		Success: true,
	}
}

func mockRecommendMCPList() *definition.MCPMarketListResult {
	result := &definition.MCPMarketListResult{
		Page:       1,
		PageSize:   10,
		Total:      3,
		TotalPages: 1,
		Success:    true,
	}
	serverList := mockList()
	fillInstallStatus(serverList)
	result.Items = serverList
	return result
}

func mockQueryMCPList(request *definition.MCPMarketListRequest) *definition.MCPMarketListResult {
	result := &definition.MCPMarketListResult{
		Page:       request.Page,
		PageSize:   request.PageSize,
		Total:      100,
		TotalPages: 100,
		Success:    true,
	}
	serverList := mockList()
	fillInstallStatus(serverList)
	result.Items = serverList
	return result
}

func mockList() []*definition.MCPMarketServerItem {
	serverList := make([]*definition.MCPMarketServerItem, 0)
	serverList = append(serverList, &definition.MCPMarketServerItem{
		MarketType:          MarketTypeModelScope,
		ServerId:            "@modelcontextprotocol/github",
		MarketUrl:           "https://www.modelscope.cn/mcp/servers/@modelcontextprotocol/github",
		ServerName:          "GitHub",
		LogoUrl:             "https://resouces.modelscope.cn/studio-cover-pre/studio-cover_71c6eb33-fe4a-4142-918f-34c3fe6e6d3a.png",
		Author:              "@modelcontextprotocol",
		Description:         "用于GitHub API的MCP服务器，支持文件操作、仓库管理、搜索功能等更多功能。",
		ViewCount:           4244,
		Tag:                 []string{"search", "version-control"},
		Recommend:           true,
		ServerConfiguration: "{\"mcpServers\": {\"github\": {\"env\": {\"GITHUB_PERSONAL_ACCESS_TOKEN\": \"<YOUR_TOKEN>\"}, \"args\": [\"-y\", \"@modelcontextprotocol/server-github\"], \"command\": \"npx\"}}}",
	})
	serverList = append(serverList, &definition.MCPMarketServerItem{
		MarketType:          MarketTypeModelScope,
		ServerId:            "@GLips/Figma-Context-MCP",
		MarketUrl:           "https://www.modelscope.cn/mcp/servers/@GLips/Figma-Context-MCP",
		ServerName:          "Figma",
		LogoUrl:             "https://resouces.modelscope.cn/studio-cover-pre/studio-cover_533dd447-0a44-4f35-b723-0e331da31cde.png",
		Author:              "@GLips",
		Description:         "通过模型上下文协议，使 Cursor 能够访问 Figma 文件，从而增强其准确解释和利用设计数据进行代码生成的能力。",
		ViewCount:           2084,
		Tag:                 []string{"developer-tools"},
		Recommend:           true,
		ServerConfiguration: "{\"mcpServers\": {\"Framelink Figma MCP\": {\"args\": [\"-y\", \"figma-developer-mcp\", \"--figma-api-key=FIGMA_API_ACCESS_TOKEN\", \"--stdio\"], \"command\": \"npx\"}}}",
	})
	serverList = append(serverList, &definition.MCPMarketServerItem{
		MarketType:          MarketTypeModelScope,
		ServerId:            "@modelcontextprotocol/fetch",
		MarketUrl:           "https://www.modelscope.cn/mcp/servers/@modelcontextprotocol/fetch",
		ServerName:          "Fetch网页内容抓取",
		LogoUrl:             "https://resouces.modelscope.cn/studio-cover-pre/studio-cover_761f7bfe-fc5c-4753-b955-dcdd3288941b.png",
		Author:              "@modelcontextprotocol",
		Description:         "该服务器使大型语言模型能够检索和处理网页内容，将HTML转换为markdown格式，以便于更轻松地使用。",
		ViewCount:           24029,
		Tag:                 []string{"search"},
		Recommend:           true,
		ServerConfiguration: "{\"mcpServers\": {\"fetch\": {\"args\": [\"mcp-server-fetch\"], \"command\": \"uvx\"}}}",
	})
	return serverList
}

// fillInstallStatus 填充市场mcp server的安装状态
func fillInstallStatus(serverList []*definition.MCPMarketServerItem) {
	// 根据已经配置的server的fromId进行判断
	installIds := make([]string, 0)
	if GlobalMCPSeversConfig.MCPServers != nil {
		for _, server := range GlobalMCPSeversConfig.MCPServers {
			installIds = append(installIds, server.FromId)
		}
	}
	for _, item := range serverList {
		if util.ContainsString(installIds, item.ServerId) {
			// 已安装
			item.InstallStatus = definition.InstallStatusInstalled
		} else {
			// 未安装
			item.InstallStatus = definition.InstallStatusUninstalled
		}
		// 这个清空，不需要返回
		item.ServerConfiguration = ""
	}
}

// GetMarketMcpServerDetail 获取mcp server详情
func GetMarketMcpServerDetail(serverId string) (*definition.MCPMarketServerItem, error) {
	if serverId == "" {
		return nil, errors.New(errors.MCPInstallServerNotFound, "missing serverId")
	}
	url := fmt.Sprintf(definition.UrlPathMcpGetServerInfo, serverId)
	responseBody, err := requestLingmaServer(http.MethodGet, url, nil)
	if err != nil {
		return nil, errors.New(errors.MCPInstallFetchConfigError, err.Error())
	}
	sourceItem := ServerMCPServerItem{}
	err = json.Unmarshal(responseBody, &sourceItem)
	if err != nil {
		log.Errorf("Failed to unmarshal to ServerMCPServerListResult. resp=%s, error=%v", string(responseBody), err)
		return nil, errors.New(errors.MCPInstallFetchConfigError, err.Error())
	}
	sourceServerId := getServerId(sourceItem)
	item := &definition.MCPMarketServerItem{
		MarketType:          MarketTypeModelScope,
		ServerId:            sourceServerId,
		MarketUrl:           sourceItem.Url,
		ServerName:          sourceItem.ChineseName,
		ServerNameEn:        getNameEn(&sourceItem),
		LogoUrl:             sourceItem.LogoUrl,
		Author:              sourceItem.Author,
		SourceUrl:           sourceItem.SourceUrl,
		Description:         sourceItem.Description,
		DescriptionEn:       getDescriptionEn(&sourceItem),
		ViewCount:           sourceItem.VewCount,
		Tag:                 sourceItem.Tags,
		Recommend:           sourceItem.Recommend,
		ServerConfiguration: sourceItem.ServerConfig,
	}
	return item, nil
}

func checkInstalled(marketType string, serverId string) bool {
	if GlobalMCPSeversConfig.MCPServers != nil {
		for _, server := range GlobalMCPSeversConfig.MCPServers {
			if server.From == marketType && server.FromId == serverId {
				return true
			}
		}
	}
	return false
}

func parseOfficialConfig(serverConfiguration string) (string, *MCPOfficialConfigItem, error) {
	if serverConfiguration == "" {
		return "", nil, errors.New(errors.MCPInstallFetchConfigError, "can not found server config from modelscope")
	}
	configs := make([]*MCPOfficialConfig, 0)
	err := json.Unmarshal([]byte(serverConfiguration), &configs)
	if err != nil {
		return "", nil, errors.New(errors.MCPInstallFetchConfigError, "parse config error, "+err.Error())
	} else if len(configs) == 0 {
		return "", nil, errors.New(errors.MCPInstallFetchConfigError, "parse config error")
	}
	config := configs[0]
	for _, key := range config.MCPServers.Keys() {
		configItem, ok := config.MCPServers.Get(key)
		if ok {
			return key, &configItem, nil
		}
	}
	return "", nil, errors.New(errors.MCPInstallFetchConfigError, "can not found server config from modelscope")
}

func requestLingmaServer(httpMethod string, url string, requestBody interface{}) ([]byte, error) {
	req, err := remote.BuildBigModelAuthRequest(httpMethod, url, requestBody)
	if err != nil {
		log.Warnf("Build request error, url=%s, error=%v", url, err)
		return make([]byte, 0), err
	}
	httpClient := client.GetDefaultClient()
	resp, err := httpClient.Do(req)
	if err != nil {
		log.Warnf("Failed to send request, url=%s, error=%v", url, err)
		return make([]byte, 0), err
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		log.Warnf("Failed to get response, url=%s, status=%s", url, resp.Status)
		return make([]byte, 0), fmt.Errorf("failed to get response, status=%s", resp.Status)
	}
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Warnf("Failed to read response, url=%s, error=%v", url, err)
		return make([]byte, 0), err
	}
	return responseBody, nil
}

func convertRecommendResult(source *ServerMCPServerListResult) *definition.MCPMarketListResult {
	target := &definition.MCPMarketListResult{
		Success: true,
		Total:   len(source.MCPServerList),
	}
	target.Items = make([]*definition.MCPMarketServerItem, 0, len(source.MCPServerList))
	for _, sourceItem := range source.MCPServerList {
		serverId := getServerId(sourceItem)
		author := getAuthor(serverId, sourceItem.Author)
		item := &definition.MCPMarketServerItem{
			MarketType:    MarketTypeModelScope,
			ServerId:      serverId,
			MarketUrl:     sourceItem.Url,
			ServerName:    sourceItem.ChineseName,
			ServerNameEn:  getNameEn(&sourceItem),
			LogoUrl:       sourceItem.LogoUrl,
			Author:        author,
			SourceUrl:     sourceItem.SourceUrl,
			Description:   sourceItem.Description,
			DescriptionEn: getDescriptionEn(&sourceItem),
			ViewCount:     sourceItem.VewCount,
			Tag:           sourceItem.Tags,
			Recommend:     true,
		}
		target.Items = append(target.Items, item)
	}
	return target
}

func convertListResult(request *definition.MCPMarketListRequest, source *ServerMCPServerListResult) *definition.MCPMarketListResult {
	total := source.TotalCount
	if total > 100 {
		total = 100
	}
	target := &definition.MCPMarketListResult{
		Success:  true,
		Total:    total,
		Page:     request.Page,
		PageSize: request.PageSize,
	}
	// 计算总页数
	totalPages := (target.Total + target.PageSize - 1) / target.PageSize
	target.TotalPages = totalPages
	target.Items = make([]*definition.MCPMarketServerItem, 0, len(source.MCPServerList))
	for _, sourceItem := range source.MCPServerList {
		serverId := getServerId(sourceItem)
		author := getAuthor(serverId, sourceItem.Author)
		item := &definition.MCPMarketServerItem{
			MarketType:    MarketTypeModelScope,
			ServerId:      serverId,
			MarketUrl:     sourceItem.Url,
			ServerName:    sourceItem.ChineseName,
			ServerNameEn:  getNameEn(&sourceItem),
			LogoUrl:       sourceItem.LogoUrl,
			Author:        author,
			SourceUrl:     sourceItem.SourceUrl,
			Description:   sourceItem.Description,
			DescriptionEn: getDescriptionEn(&sourceItem),
			ViewCount:     sourceItem.VewCount,
			Tag:           sourceItem.Tags,
			Recommend:     sourceItem.Recommend,
		}
		target.Items = append(target.Items, item)
	}
	return target
}

func getServerId(sourceItem ServerMCPServerItem) string {
	serverId := sourceItem.ID
	if sourceItem.Publisher != "" {
		serverId = sourceItem.Publisher
	}
	return serverId
}

func getAuthor(serverId string, author string) string {
	if author != "" {
		return author
	}
	finalAuthor := serverId
	temps := strings.Split(serverId, "/")
	if len(temps) == 0 {
		return finalAuthor
	}
	if strings.HasPrefix(temps[0], "@") {
		finalAuthor = strings.ReplaceAll(temps[0], "@", "")
	} else {
		finalAuthor = temps[0]
	}
	return finalAuthor
}

func getNameEn(sourceItem *ServerMCPServerItem) string {
	if sourceItem.NameEn != "" {
		return sourceItem.NameEn
	}
	return sourceItem.ChineseName
}

func getDescriptionEn(sourceItem *ServerMCPServerItem) string {
	if sourceItem.DescriptionEn != "" {
		return sourceItem.DescriptionEn
	}
	return sourceItem.Description
}
