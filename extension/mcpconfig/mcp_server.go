package mcpconfig

import (
	"bufio"
	"context"
	"fmt"
	"io"
	"os"
	"os/exec"
	"regexp"
	"runtime"
	"strings"
	"sync"
	"time"

	"cosy/log"

	"github.com/mark3labs/mcp-go/client"
	"github.com/mark3labs/mcp-go/client/transport"
	"github.com/mark3labs/mcp-go/mcp"
)

// mcpSever健康检查不健康判断阈值
const failureThreshold = 3

// 健康检查的最大失败次数，超过此次数后关闭健康检查
const maxFailureLimit = 30

// mcpSever健康检查间隔
const healthCheckPeriod = 5 * time.Second
const healthCheckPeriodForHttp = 20 * time.Second

// 健康检查ping请求的超时时间
const pingTimeOutSeconds = 3

// 重连间隔时间
const reconnectInterval = 20 * time.Second

// 重连超时时间 (1小时)
const reconnectTimeout = 60 * time.Minute

var appendEnvLock sync.Mutex

// 关闭所有命令行启动的mcpServer
func ShutdownMcpServer() {
	if len(MCPHostMap) == 0 {
		return
	}
	log.Infof("start ShutdownMcpServer")
	for serverName, mcpHost := range MCPHostMap {
		if mcpHost == nil {
			continue
		}
		if mcpHost.Status == Connected && mcpHost.SeverConfig.Command != "" && mcpHost.Client != nil {
			log.Infof("Stopping mcp server %s", serverName)
			mcpHost.close()
		}
	}
}

// 启动mcpServer
func (h *MCPHost) startMcpSever() error {
	var clientInst *client.Client
	var err error
	h.Status = Connecting
	checkPeriod := healthCheckPeriod
	if h.SeverConfig.Command != "" {
		clientInst, err = h.initStdioClient()
		if err != nil {
			return err
		}
	} else if h.SeverConfig.Url != "" {
		fallback := false
		clientInst, fallback, err = h.initStreamableHttpClient()
		if err != nil {
			return err
		} else if fallback {
			clientInst, err = h.initSSEClient()
			if err != nil {
				return err
			}
		}
		checkPeriod = healthCheckPeriodForHttp
	} else {
		return fmt.Errorf(
			"failed to create MCP client for %s: %s",
			h.SeverConfig.Name,
			"command or url is required",
		)
	}
	h.Client = clientInst
	log.Infof("Initialize server success, serverName=%s", h.SeverConfig.Name)
	// 为mcpHost新增健康检查Ticker
	ticker := time.NewTicker(checkPeriod)
	// 开启健康检查
	h.HealthTicker = ticker
	go h.healthCheck()
	h.Status = Connected
	// stdio读取stderr，防止缓冲区被塞满
	if h.SeverConfig.Command != "" {
		go h.readStdioStdErr()
	}
	return nil
}

func (h *MCPHost) initStdioClient() (*client.Client, error) {
	// stdio Sever
	var env []string
	for k, v := range h.SeverConfig.Env {
		env = append(env, fmt.Sprintf("%s=%s", k, v))
	}
	if runtime.GOOS == "darwin" {
		appendUserEnvPath()
	}
	clientInst, err := client.NewStdioMCPClient(
		h.SeverConfig.Command,
		env,
		h.SeverConfig.Args...)
	if err != nil {
		return nil, fmt.Errorf(
			"failed to create MCP client for %s: %w",
			h.SeverConfig.Name,
			err,
		)
	}
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	log.Info("Initializing server...", "name", h.SeverConfig.Name)
	initRequest := buildInitializeRequest()

	_, err = clientInst.Initialize(ctx, initRequest)
	if err != nil {
		strErr := ""
		if strings.Contains(err.Error(), "context deadline exceeded") {
			// 启动超时，读一下stderr，看下有没有报错
			strErr = tryGetStdErr(clientInst)
		}
		clientInst.Close()
		if strErr == "" {
			return nil, fmt.Errorf(
				"failed to initialize MCP client for %s: %w",
				h.SeverConfig.Name,
				err,
			)
		} else {
			return nil, fmt.Errorf(
				"failed to initialize MCP client for %s: %s",
				h.SeverConfig.Name,
				strErr,
			)
		}
	}
	return clientInst, nil
}

func (h *MCPHost) initSSEClient() (*client.Client, error) {
	// sse Sever
	clientOption := transport.WithHeaders(filterHeaders(h.SeverConfig.Headers, SSEIgnoredHeaders))
	clientInst, err := client.NewSSEMCPClient(h.SeverConfig.Url, clientOption)
	if err != nil {
		return nil, fmt.Errorf(
			"failed to create MCP client for %s: %w",
			h.SeverConfig.Name,
			err,
		)
	}

	// Start the client
	err = clientInst.Start(context.Background())
	if err != nil {
		return nil, fmt.Errorf(
			"failed to create MCP client for %s: %w",
			h.SeverConfig.Name,
			err,
		)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	log.Info("Initializing server...", "name", h.SeverConfig.Name)
	initRequest := buildInitializeRequest()

	_, err = clientInst.Initialize(ctx, initRequest)
	if err != nil {
		clientInst.Close()
		return nil, fmt.Errorf(
			"failed to initialize MCP client for %s: %w",
			h.SeverConfig.Name,
			err,
		)
	}
	return clientInst, nil
}

func (h *MCPHost) initStreamableHttpClient() (*client.Client, bool, error) {
	// sse Sever
	headerOption := transport.WithHTTPHeaders(filterHeaders(h.SeverConfig.Headers, StreamableHTTPIgnoredHeaders))
	clientInst, err := client.NewStreamableHttpClient(h.SeverConfig.Url, headerOption)
	if err != nil {
		return nil, false, fmt.Errorf(
			"failed to create MCP client for %s: %w",
			h.SeverConfig.Name,
			err,
		)
	}

	// Start the client
	err = clientInst.Start(context.Background())
	if err != nil {
		return nil, false, fmt.Errorf(
			"failed to create MCP client for %s: %w",
			h.SeverConfig.Name,
			err,
		)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	log.Info("Initializing server...", "name", h.SeverConfig.Name)
	initRequest := buildInitializeRequest()

	_, err = clientInst.Initialize(ctx, initRequest)
	if err != nil {
		clientInst.Close()
		log.Errorf("Failed to initialize via Streamable HTTP, fall back to SSE. server=%s, url=%s", h.SeverConfig.Name, h.SeverConfig.Url)
		// TODO 需要判断下哪些情况fallback到sse模式
		return nil, true, nil
	}
	return clientInst, false, nil
}

func (h *MCPHost) listTools() ([]mcp.Tool, error) {
	request := mcp.ListToolsRequest{}
	tools, err := h.Client.ListTools(context.Background(), request)
	if err != nil {
		return nil, fmt.Errorf("failed to list tools: %w", err)
	}
	return tools.Tools, nil
}

func (h *MCPHost) close() {
	if h.Client == nil {
		return
	}
	if !h.HealthCheckIsStop {
		h.HealthCheckQuitChan <- 1
	}
	// 停止重连协程
	h.stopReconnectGoroutine()

	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("close mcpClient %s error", h.SeverConfig.Name)
				// 获取当前堆栈信息
				stack := make([]byte, 4096)
				stack = stack[:runtime.Stack(stack, false)]
				log.Debugf("close mcpClient crash. err: %+v, stack: %s", r, stack)
			}
		}()

		err := h.Client.Close()
		if err != nil {
			log.Errorf("failed to close MCP client for %w", err)
		}
	}()
}

func (h *MCPHost) healthCheck() {
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("healthCheck mcp %s error", h.SeverConfig.Name)
			// 获取当前堆栈信息
			stack := make([]byte, 4096)
			stack = stack[:runtime.Stack(stack, false)]
			log.Debugf("healthCheck mcp crash. err: %+v, stack: %s", r, stack)
		}
	}()
	failureCount := 0
	for {
		select {
		case <-h.HealthTicker.C:
			// 使用锁防止与重连协程冲突
			h.operationMutex.Lock()
			err := h.pingWithTimeout()
			if err != nil {
				failureCount++
				log.Errorf("mcp %s health check failed, failureCount=%d, err=%v", h.SeverConfig.Name, failureCount, err)
			} else {
				failureCount = 0
				h.Status = Connected
				// 健康检查成功，停止重连协程
				h.stopReconnectGoroutine()
			}
			h.operationMutex.Unlock()
			if failureCount >= failureThreshold {
				log.Errorf("mcp %s health check failed %d more than %d, make status to disconnected", h.SeverConfig.Name, failureCount, failureThreshold)
				h.Status = Disconnected
				_, ok := GlobalMCPToolsMap[h.SeverConfig.Name]
				if ok {
					log.Debugf("remove tools when mcp server %s disconnected", h.SeverConfig.Name)
					DeleteMCPToolMap(h.SeverConfig.Name)
				}
				// 启动重连协程（sse/streamable http协议的需要重连）
				if h.SeverConfig.Url != "" {
					h.startReconnectGoroutine()
				}
			}
		case <-h.HealthCheckQuitChan:
			log.Infof("mcp %s health check quit", h.SeverConfig.Name)
			if !h.HealthCheckIsStop {
				h.HealthTicker.Stop()
				h.HealthCheckIsStop = true
			}
			return
		}
	}
}

func (h *MCPHost) pingWithTimeout() error {
	ctx, cancel := context.WithTimeout(context.Background(), pingTimeOutSeconds*time.Second)
	defer cancel()
	return h.Client.Ping(ctx)
}

// mac系统特殊处理，兼容灵码作为jetBrain子进程时，jetBrain无法获取用户完整环境变量导致npx命令执行异常问题
func appendUserEnvPath() {
	appendEnvLock.Lock()
	defer appendEnvLock.Unlock()

	path := os.Getenv("PATH")
	// 调用用户shell获取当前用户的环境变量
	loadUserEnvPath, err := LoadUserEnvPath()
	if err != nil {
		log.Errorf("LoadUserEnvPath failed when start mcp server: %v", err)
		return
	}
	log.Debugf("origin path=%s, loadUserEnvPath=%s", path, loadUserEnvPath)
	// 合并path和loadUserEnvPath，去重
	mergedPath := path
	if path != loadUserEnvPath {
		mergedPath = MergePath(path, loadUserEnvPath)
		log.Debugf("add mergedPath to cmd env: %s", mergedPath)
	} else {
		log.Debugf("current path equals loadUserEnvPath not need merge")
	}
	os.Setenv("PATH", mergedPath)
}

// mac os获取用户的环境变量
func LoadUserEnvPath() (string, error) {
	// 获取当前用户的默认 shell
	shell := os.Getenv("SHELL")
	if shell == "" {
		shell = "/bin/zsh" // macOS 默认使用 zsh
	}

	// 使用 login shell 来获取完整的环境变量
	cmd := exec.Command(shell, "-l", "-i", "-c", "env | grep '^PATH=' | cut -d'=' -f2-")
	cmd.Stderr = nil

	output, err := cmd.Output()
	if err != nil {
		return "", fmt.Errorf("GET PATH ERROR: %v", err)
	}

	// 过滤终端引入的特殊字符
	re := regexp.MustCompile(`\x1b\][0-9]+;[^\a]*\a`)
	cleaned := re.ReplaceAllString(string(output), "")
	return strings.TrimSpace(cleaned), nil
}

func buildInitializeRequest() mcp.InitializeRequest {
	initRequest := mcp.InitializeRequest{}
	initRequest.Params.ProtocolVersion = mcp.LATEST_PROTOCOL_VERSION
	initRequest.Params.ClientInfo = mcp.Implementation{
		Name:    "mcphost",
		Version: "0.1.0",
	}
	initRequest.Params.Capabilities = mcp.ClientCapabilities{}
	return initRequest
}

func tryGetStdErr(clintInst *client.Client) string {
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("Panic in tryGetStdErr: %v", r)
			// 获取当前堆栈信息
			stack := make([]byte, 4096)
			stack = stack[:runtime.Stack(stack, false)]
			log.Debugf("tryGetStdErr panic stack: %s", stack)
		}
	}()

	stderr, ok := client.GetStderr(clintInst)
	if !ok {
		return ""
	}

	// 尝试将 stderr 转换为 *os.File 类型
	file, ok := stderr.(*os.File)
	if !ok {
		// 如果不是 *os.File 类型，直接返回空字符串，避免潜在的 goroutine 泄漏
		log.Debugf("stderr is not *os.File type, skip reading")
		return ""
	}

	// 设置读取超时
	err := file.SetReadDeadline(time.Now().Add(3 * time.Second))
	if err != nil {
		// 如果无法设置读取超时，直接返回空字符串
		log.Debugf("Failed to set read deadline: %v", err)
		return ""
	}

	// 读取 stderr
	const maxLength = 256
	buf := make([]byte, maxLength)
	n, err := file.Read(buf)

	// 读取完成后，重置读取超时
	_ = file.SetReadDeadline(time.Time{})

	if err != nil {
		if err != io.EOF {
			log.Debugf("Error reading stderr: %v", err)
		}
		return ""
	}

	if n == 0 {
		return ""
	}

	// 返回读取的内容，如果需要则截断
	resultStr := string(buf[:n])
	if n == maxLength {
		resultStr += "..."
	}
	return resultStr
}

func (h *MCPHost) readStdioStdErr() {
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("Panic in readStdioStdErr: %v", r)
			// 获取当前堆栈信息
			stack := make([]byte, 4096)
			stack = stack[:runtime.Stack(stack, false)]
			log.Errorf("readStdioStdErr panic stack: %s", stack)
		}
	}()
	stderr, ok := client.GetStderr(h.Client)
	if !ok {
		log.Debugf("serverName=%s, failed to GetStderr", h.SeverConfig.Name)
		return
	}
	scanner := bufio.NewScanner(stderr)
	for scanner.Scan() {
		line := scanner.Text()
		log.Debugf("serverName=%s, stderr: %s", h.SeverConfig.Name, line)
	}
	log.Debugf("serverName=%s, exit readStdioStdErr", h.SeverConfig.Name)
}

// filterHeaders 过滤掉 headers 中存在于 ignoreKeys 列表中的键
func filterHeaders(headers map[string]string, ignoreKeys []string) map[string]string {
	ignoreSet := make(map[string]struct{})
	for _, key := range ignoreKeys {
		ignoreSet[key] = struct{}{}
	}

	filtered := make(map[string]string)

	for k, v := range headers {
		if _, exists := ignoreSet[k]; !exists {
			filtered[k] = v
		}
	}

	return filtered
}

// startReconnectGoroutine 启动重连协程
func (h *MCPHost) startReconnectGoroutine() {
	// 防止重复启动重连协程
	if h.ReconnectGoroutineRunning {
		log.Debugf("mcp %s reconnect goroutine is already running", h.SeverConfig.Name)
		return
	}

	h.ReconnectGoroutineRunning = true
	h.ReconnectIsStop = false
	h.ReconnectStartTime = time.Now()
	h.ReconnectQuitChan = make(chan int, 1)

	log.Infof("mcp %s starting reconnect goroutine", h.SeverConfig.Name)
	go h.reconnectLoop()
}

// stopReconnectGoroutine 停止重连协程
func (h *MCPHost) stopReconnectGoroutine() {
	if !h.ReconnectGoroutineRunning {
		return
	}

	log.Infof("mcp %s stopping reconnect goroutine", h.SeverConfig.Name)

	// 发送退出信号（非阻塞）
	select {
	case h.ReconnectQuitChan <- 1:
	default:
		// 如果通道已满，说明协程可能已经在退出过程中
	}
}

// reconnectLoop 重连循环
func (h *MCPHost) reconnectLoop() {
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("reconnectLoop mcp %s error", h.SeverConfig.Name)
			// 获取当前堆栈信息
			stack := make([]byte, 4096)
			stack = stack[:runtime.Stack(stack, false)]
			log.Debugf("reconnectLoop mcp crash. err: %+v, stack: %s", r, stack)
		}

		// 清理重连状态
		h.ReconnectGoroutineRunning = false
		h.ReconnectIsStop = true
		if h.ReconnectTicker != nil {
			h.ReconnectTicker.Stop()
		}
		log.Debugf("mcp %s reconnect goroutine exited", h.SeverConfig.Name)
	}()

	// 创建定时器
	h.ReconnectTicker = time.NewTicker(reconnectInterval)
	defer h.ReconnectTicker.Stop()

	for {
		select {
		case <-h.ReconnectTicker.C:
			// 检查当前实例是否还在映射中
			currentHost := MCPHostMap[h.SeverConfig.Name]
			if currentHost != h {
				log.Infof("mcp %s instance replaced, stopping reconnect", h.SeverConfig.Name)
				return
			}
			// 检查是否超时（1小时）
			if time.Since(h.ReconnectStartTime) >= reconnectTimeout {
				log.Infof("mcp %s reconnect timeout after 1 hour, stopping reconnect", h.SeverConfig.Name)
				return
			}

			// 尝试重连
			log.Infof("mcp %s attempting reconnect", h.SeverConfig.Name)
			if h.attemptReconnect() {
				log.Infof("mcp %s reconnect successful, stopping reconnect goroutine", h.SeverConfig.Name)
				return
			}

		case <-h.ReconnectQuitChan:
			log.Infof("mcp %s received quit signal, stopping reconnect", h.SeverConfig.Name)
			return
		}
	}
}

// attemptReconnect 尝试重连
func (h *MCPHost) attemptReconnect() bool {
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("attemptReconnect mcp %s error: %v", h.SeverConfig.Name, r)
		}
	}()

	log.Debugf("mcp %s attempting to reconnect", h.SeverConfig.Name)

	// 使用锁防止与健康检查冲突
	h.operationMutex.Lock()
	defer h.operationMutex.Unlock()

	// 直接重新初始化当前MCPHost，避免创建新实例
	if h.Client != nil {
		// 关闭旧的客户端连接
		h.Client.Close()
		h.Client = nil
	}

	// 停止健康检查，准备重新启动
	if h.HealthTicker != nil && !h.HealthCheckIsStop {
		h.HealthTicker.Stop()
		h.HealthCheckIsStop = true
		defer func() {
			// 重置健康检查状态
			h.HealthCheckIsStop = false
		}()
	}

	// 重新启动MCP服务
	h.Status = Connecting
	err := h.startMcpSever()
	if err != nil {
		log.Errorf("mcp %s reconnect failed: %v", h.SeverConfig.Name, err)
		h.Status = ErrorStatus
		h.ErrorMsg = err.Error()
		return false
	}

	// 重新获取工具列表
	DeleteMCPToolMap(h.SeverConfig.Name)
	tools, err := h.listTools()
	if err != nil {
		log.Errorf("mcp %s reconnect failed to list tools: %v", h.SeverConfig.Name, err)
		return false
	}
	PutMcpToolMap(h.SeverConfig.Name, tools)

	log.Infof("mcp %s reconnect successful", h.SeverConfig.Name)
	return true
}
