package rule

import (
	"context"
	"cosy/chat/chains/common"
	"cosy/definition"
	"cosy/log"
	"cosy/sls"
	"encoding/json"
	"github.com/bmatcuk/doublestar/v4"
	"github.com/google/uuid"
	"github.com/tmc/langchaingo/chains"
	"github.com/tmc/langchaingo/memory"
	"github.com/tmc/langchaingo/schema"
	"os"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"sync"
)

const (
	maxTotalContentSize        = 100000 // 10万字符的上限
	minTruncateCapacity        = 1000   // 控制最小截断的字符数，如果太小则导致信息丢失过多，没有实际作用
	ProjectRuleMaxCharacterNum = 10000  // 单个规则文件包含的最大字符数
)

type ProjectRuleProcessChain struct {
}

func NewProjectRuleProcessChain() *ProjectRuleProcessChain {
	return &ProjectRuleProcessChain{}
}

func (c ProjectRuleProcessChain) Call(ctx context.Context, inputs map[string]any, options ...chains.ChainCallOption) (map[string]any, error) {
	workspacePath := inputs[common.KeyWorkSpacePath].(string)
	contextProviderExtras := inputs[common.KeyContextProviderExtra].([]definition.CustomContextProviderExtra)

	// 正确初始化 projectRuleContext 变量
	projectRuleContext := ProjectRuleContext{
		UserManualSelectedRules: make([]*ProjectRule, 0),
		GlobMatchedRules:        make([]*ProjectRule, 0),
		AllAlwaysRules:          make([]*ProjectRule, 0),
		AllModelDecisionRules:   make([]*ProjectRule, 0),
		AllGlobRules:            make([]*ProjectRule, 0),
	}

	// 根据上下文信息获取相应的规则
	if contextProviderExtras != nil {
		// 查找上下文中的手工引入规则
		manualRules := findManualRules(workspacePath, contextProviderExtras)
		if manualRules != nil {
			projectRuleContext.UserManualSelectedRules = manualRules
		}

		// 查找上下文中与file或folder匹配的规则
		globMatchedRules := findMatchedGlobRules(workspacePath, contextProviderExtras)
		if globMatchedRules != nil {
			projectRuleContext.GlobMatchedRules = globMatchedRules
		}
	}

	// 获取始终应用规则 (AllAlwaysRules)
	alwaysRules, err := GetProjectRulesByTrigger(workspacePath, []ProjectRulesTrigger{AlwaysOnRule})
	if err != nil {
		log.Warnf("Failed to get always rules: %v", err)
	} else {
		if alwaysRules != nil {
			projectRuleContext.AllAlwaysRules = alwaysRules
		}
	}

	// 获取模型决策规则 (AllModelDecisionRules)
	modelDecisionRules, err := GetProjectRulesByTrigger(workspacePath, []ProjectRulesTrigger{ModelDecisionRule})
	if err != nil {
		log.Warnf("Failed to get model decision rules: %v", err)
	} else {
		if modelDecisionRules != nil {
			projectRuleContext.AllModelDecisionRules = modelDecisionRules
		}
	}
	// 获取Glob类型规则 (AllGlobRules)
	globRules, err := GetProjectRulesByTrigger(workspacePath, []ProjectRulesTrigger{GlobRule})
	if err != nil {
		log.Warnf("Failed to get model decision rules: %v", err)
	} else {
		if globRules != nil {
			projectRuleContext.AllGlobRules = globRules
		}
	}

	// 规则超限控制处理
	limitProjectRuleContextContent(&projectRuleContext)

	// 将处理后的规则上下文添加到输出中
	inputs[common.KeyProjectRuleContext] = projectRuleContext
	// 上报埋点
	go reportProjectRuleContext(projectRuleContext)

	return inputs, nil
}

func reportProjectRuleContext(projectRuleContext ProjectRuleContext) {
	// 只选择性读取指定字段进行上报
	type reportRuleData struct {
		Trigger     string `json:"trigger"`
		Name        string `json:"name"`
		Description string `json:"description"`
		Glob        string `json:"glob"`
		FilePath    string `json:"filePath"`
	}

	// 合并UserManualSelectedRules和GlobMatchedRules，并根据FilePath去重
	uniqueRuleMap := make(map[string]*ProjectRule)
	for _, r := range projectRuleContext.UserManualSelectedRules {
		if r == nil || r.FilePath == "" {
			continue
		}
		uniqueRuleMap[r.FilePath] = r
	}
	for _, r := range projectRuleContext.GlobMatchedRules {
		if r == nil || r.FilePath == "" {
			continue
		}
		uniqueRuleMap[r.FilePath] = r
	}

	// 转换为reportRuleData数组
	mergedRules := make([]reportRuleData, 0, len(uniqueRuleMap))
	for _, r := range uniqueRuleMap {
		mergedRules = append(mergedRules, reportRuleData{
			Trigger:     string(r.Trigger),
			Name:        r.Name,
			Description: r.Description,
			Glob:        r.Glob,
			FilePath:    r.FilePath,
		})
	}

	eventData := make(map[string]string)
	if valueBytes, err := json.Marshal(mergedRules); err == nil {
		eventData["effectiveRules"] = string(valueBytes)
		eventData["effectiveRulesCount"] = strconv.Itoa(len(mergedRules))
	} else {
		log.Errorf("marshal projectRuleContext error: %v", err)
		eventData["effectiveRules"] = "[]"
		eventData["effectiveRulesCount"] = "0"
	}
	sls.Report(sls.EventTypeProjectRuleEffective, uuid.NewString(), eventData)
}

func findManualRules(workspacePath string, contextProviderExtras []definition.CustomContextProviderExtra) []*ProjectRule {
	// 获取特定的上下文提供器
	ruleProviders := getInputContextProvider(definition.PlatformContextProviderRule, contextProviderExtras)
	if ruleProviders == nil || len(ruleProviders) == 0 {
		log.Debugf("No rule context providers found for workspace: %s", workspacePath)
		return nil
	}
	var manualRules []*ProjectRule
	// 遍历所有规则提供器
	for _, ruleProvider := range ruleProviders {
		if ruleProvider.ParsedContextItems == nil {
			log.Warnf("Rule provider or its parsed context items is nil")
			continue
		}
		// 遍历上下文项
		for i := 0; i < len(ruleProvider.ParsedContextItems); i++ {
			contextItem := &ruleProvider.ParsedContextItems[i]
			if contextItem.ContextItem.Key == "" {
				log.Warnf("Context item is nil or has empty key")
				continue
			}
			// 检查值是否为 nil
			if contextItem.ContextItem.Value == nil {
				log.Warnf("Rule context value is nil for key: %s", contextItem.ContextItem.Key)
				continue
			}
			// 将 any 类型转换为 string
			content, ok := contextItem.ContextItem.Value.(string)
			if !ok {
				log.Warnf("Failed to convert rule context value to string for key: %s, type: %T",
					contextItem.ContextItem.Key, contextItem.ContextItem.Value)
				continue
			}
			// 跳过空内容
			if strings.TrimSpace(content) == "" {
				log.Debugf("Skipping empty rule content for key: %s", contextItem.ContextItem.Key)
				continue
			}
			fileName := filepath.Base(contextItem.ContextItem.Key)
			ruleName := strings.TrimSuffix(fileName, ".md")
			// 创建规则对象
			rule := &ProjectRule{
				Name:     ruleName,
				FilePath: contextItem.ContextItem.Key,
				Trigger:  ManualRule,
				Content:  content,
			}
			manualRules = append(manualRules, rule)
			log.Debugf("Added manual rule: %s", rule.Name)
		}
	}

	log.Infof("Found %d manual rules", len(manualRules))
	return manualRules
}

// findMatchedGlobRules 查找与文件或文件夹匹配的 glob 规则
func findMatchedGlobRules(workspacePath string, contextProviderExtras []definition.CustomContextProviderExtra) []*ProjectRule {
	// 1. 获取本地目录中创建所有glob类型的规则
	globRules, err := GetProjectRulesByTrigger(workspacePath, []ProjectRulesTrigger{GlobRule})
	if err != nil {
		log.Warnf("Failed to get glob rules: %v", err)
		return nil
	}
	// 如果没有 glob 规则，直接返回
	if len(globRules) == 0 {
		return nil
	}

	// 2. 收集所有需要检查的路径
	pathsToCheck, err := collectPathsToCheck(workspacePath, contextProviderExtras)
	if err != nil {
		log.Warnf("Failed to collect paths to check: %v", err)
		return nil
	}

	// 如果没有路径需要检查，直接返回
	if len(pathsToCheck) == 0 {
		return nil
	}

	// 3. 将glob规则与收集好的路径进行匹配
	return matchGlobRules(globRules, pathsToCheck)
}

// collectPathsToCheckOptimized 收集所有需要检查的路径
func collectPathsToCheck(workspacePath string, contextProviderExtras []definition.CustomContextProviderExtra) ([]string, error) {
	var pathsToCheck []string

	// 获取文件上下文
	filePaths, err := extractPathsFromFileOrSelectedCodeContextProvider(contextProviderExtras)
	if err != nil {
		return nil, err
	}
	for _, filePath := range filePaths {
		relPath, err := filepath.Rel(workspacePath, filePath)
		if err != nil {
			relPath = filePath
		}
		pathsToCheck = append(pathsToCheck, relPath)
	}

	// 获取文件夹上下文
	folderPaths, err := extractFolderPathsFromFolderContextProvider(contextProviderExtras)
	if err != nil {
		return nil, err
	}
	// 添加文件夹下的所有文件路径
	for _, folderPath := range folderPaths {
		if err := collectFilesFromFolder(workspacePath, folderPath, &pathsToCheck); err != nil {
			log.Warnf("Failed to collect files from folder %s: %v", folderPath, err)
		}
	}
	return pathsToCheck, nil
}

// collectFilesFromFolder 收集文件夹下的所有文件路径
func collectFilesFromFolder(workspacePath, folderPath string, pathsToCheck *[]string) error {
	// 确保文件夹路径存在
	if _, err := os.Stat(folderPath); os.IsNotExist(err) {
		return nil
	}

	// 遍历文件夹下的所有文件
	err := filepath.Walk(folderPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 跳过目录
		if info.IsDir() {
			return nil
		}

		// 获取相对于工作区的路径
		relPath, err := filepath.Rel(workspacePath, path)
		if err != nil {
			relPath = path
		}

		// 直接添加到 slice
		*pathsToCheck = append(*pathsToCheck, relPath)
		return nil
	})

	return err
}

// extractPathsFromFileOrSelectedCodeContextProvider 从#file或#selectedCode上下文中提取文件的路径
func extractPathsFromFileOrSelectedCodeContextProvider(contextProviderExtras []definition.CustomContextProviderExtra) ([]string, error) {
	var paths []string
	selectedCodeProviders := getInputContextProvider(definition.PlatformContextProviderSelectedCode, contextProviderExtras)
	if selectedCodeProviders != nil && len(selectedCodeProviders) > 0 {
		for _, provider := range selectedCodeProviders {
			if provider.ParsedContextItems == nil {
				continue
			}
			for i := 0; i < len(provider.ParsedContextItems); i++ {
				contextItem := &provider.ParsedContextItems[i]
				if contextItem.ContextItem.Key == "" {
					continue
				}
				selectedCodeFilepath := contextItem.Extra["filePath"].(string)
				if selectedCodeFilepath != "" {
					paths = append(paths, selectedCodeFilepath)
				}
			}
		}
	}

	fileProviders := getInputContextProvider(definition.PlatformContextProviderFile, contextProviderExtras)
	if fileProviders != nil && len(fileProviders) > 0 {
		for _, provider := range fileProviders {
			if provider.ParsedContextItems == nil {
				continue
			}
			for i := 0; i < len(provider.ParsedContextItems); i++ {
				contextItem := &provider.ParsedContextItems[i]
				if contextItem.ContextItem.Key == "" {
					continue
				}
				paths = append(paths, contextItem.ContextItem.Key)
			}
		}
	}

	return paths, nil
}

// extractFolderPathsFromFolderContextProvider 从#folder上下文中提取文件夹的路径
func extractFolderPathsFromFolderContextProvider(contextProviderExtras []definition.CustomContextProviderExtra) ([]string, error) {
	var paths []string
	folderProviders := getInputContextProvider(definition.PlatformContextProviderFolder, contextProviderExtras)
	if folderProviders != nil && len(folderProviders) > 0 {
		for _, provider := range folderProviders {
			if provider.ParsedContextItems == nil {
				continue
			}
			for i := 0; i < len(provider.ParsedContextItems); i++ {
				contextItem := &provider.ParsedContextItems[i]
				if contextItem.ContextItem.Key == "" {
					continue
				}
				paths = append(paths, contextItem.ContextItem.Key)
			}
		}
	}

	return paths, nil
}

// matchGlobRulesOptimized 匹配 glob 规则（优化版本）
func matchGlobRules(globRules []*ProjectRule, pathsToCheck []string) []*ProjectRule {
	var matchedRules []*ProjectRule
	matchedRuleNames := make(map[string]bool) // 用于去重

	// 预编译 glob 模式以提高性能
	type CompiledRule struct {
		rule     *ProjectRule
		patterns []string
	}
	var compiledRules []CompiledRule
	for _, rule := range globRules {
		if rule.Glob == "" {
			continue
		}
		// 解析 glob 表达式的配置，多个表达式以逗号分隔
		globPatterns := strings.Split(rule.Glob, ",")
		var validPatterns []string
		for _, pattern := range globPatterns {
			pattern = strings.TrimSpace(pattern)
			if pattern != "" {
				validPatterns = append(validPatterns, pattern)
			}
		}
		if len(validPatterns) > 0 {
			compiledRules = append(compiledRules, CompiledRule{
				rule:     rule,
				patterns: validPatterns,
			})
		}
	}

	// 并发进行匹配规则
	var mu sync.Mutex
	var wg sync.WaitGroup

	for _, compiledRule := range compiledRules {
		wg.Add(1)
		go func(cr CompiledRule) {
			defer wg.Done()
			mu.Lock()
			defer mu.Unlock()

			// 如果规则已经匹配过，跳过
			if matchedRuleNames[cr.rule.Name] {
				return
			}
			// 检查当前glob规则是否匹配任何路径
			if isPatternMatched(cr.patterns, pathsToCheck) {
				matchedRules = append(matchedRules, cr.rule)
				matchedRuleNames[cr.rule.Name] = true
			}
		}(compiledRule)
	}

	wg.Wait()

	log.Infof("Found %d matched glob rules out of %d total glob rules", len(matchedRules), len(globRules))
	return matchedRules
}

// isPatternMatchedOptimized 检查 glob 模式是否匹配任何路径（优化版本）
func isPatternMatched(patterns []string, paths []string) bool {
	for _, pattern := range patterns {
		// glob pattern为简单模式，例如：*.go、HelloWorld.java等
		if isSimplePattern(pattern) {
			matched := isSimplePatternMatched(pattern, paths)
			if matched {
				return true
			} else {
				continue
			}
		} else { // 复杂模式使用原有的匹配逻辑，例如 src/**/*.java
			for _, path := range paths {
				// 首先尝试直接匹配
				matched, err := doublestar.Match(pattern, path)
				if err != nil {
					log.Warnf("Invalid glob pattern '%s': %v", pattern, err)
					continue
				}
				if matched {
					return true
				}
			}
		}
	}
	return false
}

// isSimplePattern 检查是否为简单模式（不包含通配符或只包含简单的通配符）
func isSimplePattern(pattern string) bool {
	// 检查是否包含复杂的通配符
	if strings.Contains(pattern, "**") || strings.Contains(pattern, "[") || strings.Contains(pattern, "?") {
		return false
	}

	// 检查是否包含多个 *
	starCount := strings.Count(pattern, "*")
	return starCount <= 1
}

// isSimplePatternMatched 简单模式的快速匹配
func isSimplePatternMatched(pattern string, paths []string) bool {
	// 如果模式不包含通配符，使用字符串比较
	if !strings.Contains(pattern, "*") {
		// 对于精确匹配，使用map查找
		pathSet := make(map[string]bool)
		fileNameSet := make(map[string]bool)
		for _, path := range paths {
			pathSet[path] = true
			fileNameSet[filepath.Base(path)] = true
		}
		return pathSet[pattern] || fileNameSet[pattern]
	}

	// 包含单个 * 的情况，使用前缀和后缀匹配
	if strings.Count(pattern, "*") == 1 {
		parts := strings.Split(pattern, "*")
		if len(parts) == 2 {
			prefix := parts[0]
			suffix := parts[1]

			// 对于常见的文件扩展名模式，使用快速检查
			if prefix == "" && strings.HasPrefix(suffix, ".") {
				// 模式类似 "*.go"，检查文件扩展名
				return hasFileWithExtension(paths, suffix)
			}

			if suffix == "" && prefix != "" {
				// 模式类似 "prefix*"，检查文件名前缀
				return hasFileWithPrefix(paths, prefix)
			}

			// 通用情况：检查前缀和后缀
			for _, path := range paths {
				// 检查全路径匹配
				if strings.HasPrefix(path, prefix) && strings.HasSuffix(path, suffix) {
					return true
				}

				// 检查文件名匹配
				fileName := filepath.Base(path)
				if strings.HasPrefix(fileName, prefix) && strings.HasSuffix(fileName, suffix) {
					return true
				}
			}
		}
	}

	return false
}

// hasFileWithExtension 检查是否有指定扩展名的文件
func hasFileWithExtension(paths []string, extension string) bool {
	for _, path := range paths {
		if strings.HasSuffix(path, extension) {
			return true
		}
		fileName := filepath.Base(path)
		if strings.HasSuffix(fileName, extension) {
			return true
		}
	}
	return false
}

// hasFileWithPrefix 检查是否有指定前缀的文件
func hasFileWithPrefix(paths []string, prefix string) bool {
	for _, path := range paths {
		if strings.HasPrefix(path, prefix) {
			return true
		}
		fileName := filepath.Base(path)
		if strings.HasPrefix(fileName, prefix) {
			return true
		}
	}
	return false
}

// getInputContextProvider 获取project rule处理时可能需要的context provider，包括：#file,#folder,#rule
func getInputContextProvider(providerName string, contextProviderExtras []definition.CustomContextProviderExtra) []definition.CustomContextProviderExtra {
	if contextProviderExtras == nil || len(contextProviderExtras) <= 0 {
		return nil
	}
	var ruleProviderExtras []definition.CustomContextProviderExtra
	for i := 0; i < len(contextProviderExtras); i++ {
		if providerName == contextProviderExtras[i].Name {
			ruleProviderExtras = append(ruleProviderExtras, contextProviderExtras[i])
		}
	}
	return ruleProviderExtras
}

func (c ProjectRuleProcessChain) GetMemory() schema.Memory {
	return memory.NewSimple()
}

func (c ProjectRuleProcessChain) GetInputKeys() []string {
	return []string{}
}

func (c ProjectRuleProcessChain) GetOutputKeys() []string {
	return []string{}
}

// limitProjectRuleContextContent 对ProjectRuleContext进行内容上限控制处理
// 各种类型的规则一起的内容总长度不能超过maxContentSize（去重计算），如果超过，则按照优先级进行裁剪，优先级从高到低为：
// 规则优先级：UserManualSelectedRules > GlobMatchedRules > AllAlwaysRules > AllModelDecisionRules > AllGlobRules
// 裁剪的要求是遵循优先级的前提下尽量挑选能够放入且完整的规则内容；如果无法完整放入，则对规则进行截断（需要按行进行截断）
func limitProjectRuleContextContent(context *ProjectRuleContext) {
	// 用于记录已经计算过的规则名称，避免重复计算
	processedRuleNames := make(map[string]bool)
	totalContentSize := 0

	// 按优先级顺序处理各个规则数组
	// 规则优先级：UserManualSelectedRules > GlobMatchedRules > AllAlwaysRules > AllModelDecisionRules > AllGlobRules
	ruleArrays := []struct {
		name  string
		rules []*ProjectRule
	}{
		{"UserManualSelectedRules", context.UserManualSelectedRules},
		{"GlobMatchedRules", context.GlobMatchedRules},
		{"AllAlwaysRules", context.AllAlwaysRules},
		{"AllModelDecisionRules", context.AllModelDecisionRules},
		{"AllGlobRules", context.AllGlobRules},
	}

	// 处理每个规则数组
	for _, ruleArray := range ruleArrays {
		if ruleArray.rules == nil || len(ruleArray.rules) == 0 {
			continue
		}

		// 过滤掉已处理的规则和无效规则，并预处理超长规则
		var validRules []*ProjectRule
		for _, rule := range ruleArray.rules {
			if rule == nil || rule.Name == "" {
				continue
			}
			if processedRuleNames[rule.Name] {
				log.Debugf("Skipping duplicate rule: %s", rule.Name)
				continue
			}

			// 检查规则内容是否超过单个规则的最大字符数限制
			if len(rule.Content) > ProjectRuleMaxCharacterNum {
				log.Debugf("Rule %s content exceeds max character limit (%d > %d), truncating...",
					rule.Name, len(rule.Content), ProjectRuleMaxCharacterNum)
				truncatedRule := truncateRuleContent(rule, ProjectRuleMaxCharacterNum)
				if truncatedRule != nil {
					validRules = append(validRules, truncatedRule)
				}
			} else {
				validRules = append(validRules, rule)
			}
		}

		if len(validRules) == 0 {
			continue
		}

		// 按内容大小排序，优先处理小规则（贪心策略）
		sort.Slice(validRules, func(i, j int) bool {
			return len(validRules[i].Content) < len(validRules[j].Content)
		})

		var selectedRules = make([]*ProjectRule, 0)
		var remainingRules []*ProjectRule

		// 第一轮：尝试放入完整的规则
		for _, rule := range validRules {
			//内容为空则不放入
			if strings.TrimSpace(rule.Content) == "" {
				continue
			}
			contentSize := len(rule.Content)
			if totalContentSize+contentSize <= maxTotalContentSize {
				selectedRules = append(selectedRules, rule)
				processedRuleNames[rule.Name] = true
				totalContentSize += contentSize
				log.Debugf("Added complete rule: %s from %s (size: %d, total: %d)",
					rule.Name, ruleArray.name, contentSize, totalContentSize)
			} else {
				remainingRules = append(remainingRules, rule)
			}
		}

		// 第二轮：如果还有剩余容量且大于最小截断阈值，尝试截断规则
		remainingCapacity := maxTotalContentSize - totalContentSize
		if remainingCapacity >= minTruncateCapacity && len(remainingRules) > 0 {
			// 按截断比例排序，优先选择截断比例高的规则（尽量能放入接近完整的内容）
			sort.Slice(remainingRules, func(i, j int) bool {
				ratioI := float64(remainingCapacity) / float64(len(remainingRules[i].Content))
				ratioJ := float64(remainingCapacity) / float64(len(remainingRules[j].Content))
				return ratioI > ratioJ
			})

			// 尝试截断第一个规则（截断后的内容相对完整内容比例最高的）
			truncatedRule := truncateRuleContent(remainingRules[0], remainingCapacity)
			if truncatedRule != nil {
				selectedRules = append(selectedRules, truncatedRule)
				processedRuleNames[truncatedRule.Name] = true
				totalContentSize += len(truncatedRule.Content)
				log.Debugf("Added truncated rule: %s from %s (truncated size: %d, total: %d)",
					truncatedRule.Name, ruleArray.name, len(truncatedRule.Content), totalContentSize)
			}
		}

		// 更新原数组
		switch ruleArray.name {
		case "UserManualSelectedRules":
			context.UserManualSelectedRules = selectedRules
		case "GlobMatchedRules":
			context.GlobMatchedRules = selectedRules
		case "AllAlwaysRules":
			context.AllAlwaysRules = selectedRules
		case "AllModelDecisionRules":
			context.AllModelDecisionRules = selectedRules
		case "AllGlobRules":
			context.AllGlobRules = selectedRules
		}
	}

	log.Infof("Content limit processing completed. Total content size: %d/%d characters",
		totalContentSize, maxTotalContentSize)
}

// truncateRuleContent 截断规则内容，保证末尾行的完整性
func truncateRuleContent(rule *ProjectRule, maxSize int) *ProjectRule {
	if rule == nil || len(rule.Content) <= maxSize {
		return rule
	}

	// 按行分割内容
	lines := strings.Split(rule.Content, "\n")

	// 如果内容只有一行，直接按 maxSize 截断前半段
	if len(lines) == 1 {
		if maxSize <= 0 {
			return nil
		}

		// 截断到 maxSize 大小，保留前半段
		truncatedContent := rule.Content
		if len(truncatedContent) > maxSize {
			truncatedContent = truncatedContent[:maxSize]
		}

		// 创建截断后的规则副本
		truncatedRule := &ProjectRule{
			Name:        rule.Name,
			FilePath:    rule.FilePath,
			Trigger:     rule.Trigger,
			Content:     truncatedContent,
			Glob:        rule.Glob,
			Description: rule.Description,
		}

		log.Debugf("Truncated single-line rule %s: %d -> %d characters (%.1f%%)",
			rule.Name, len(rule.Content), len(truncatedRule.Content),
			float64(len(truncatedRule.Content))/float64(len(rule.Content))*100)

		return truncatedRule
	}

	// 多行内容的处理逻辑保持不变
	var truncatedLines []string
	currentSize := 0

	// 逐行添加，确保不超过最大大小
	for _, line := range lines {
		lineSize := len(line) + 1 //预留换行符
		if currentSize+lineSize <= maxSize {
			truncatedLines = append(truncatedLines, line)
			currentSize += lineSize
		} else {
			// 如果当前行加上换行符会超过限制，则停止
			break
		}
	}

	// 如果没有内容可以放入，返回nil
	if len(truncatedLines) == 0 {
		return nil
	}

	// 创建截断后的规则副本
	truncatedRule := &ProjectRule{
		Name:        rule.Name,
		FilePath:    rule.FilePath,
		Trigger:     rule.Trigger,
		Content:     strings.Join(truncatedLines, "\n"),
		Glob:        rule.Glob,
		Description: rule.Description,
	}

	log.Debugf("Truncated multi-line rule %s: %d -> %d characters (%.1f%%)",
		rule.Name, len(rule.Content), len(truncatedRule.Content),
		float64(len(truncatedRule.Content))/float64(len(rule.Content))*100)

	return truncatedRule
}
