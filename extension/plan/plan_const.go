package plan

// TaskStatus 定义任务状态的枚举类型
//   - PENDING: 任务等待开始
//   - IN_PROGRESS: 任务进行中
//   - CANCELLED: 任务已取消
//   - COMPLETE: 任务已完成
//   - ERROR: 任务发生错误
type TaskStatus string

const (
	PENDING     TaskStatus = "PENDING"
	IN_PROGRESS TaskStatus = "IN_PROGRESS"
	CANCELLED   TaskStatus = "CANCELLED"
	COMPLETE    TaskStatus = "COMPLETE"
	ERROR       TaskStatus = "ERROR"
)

func IsFinishStatus(status TaskStatus) bool {
	return status == COMPLETE || status == ERROR || status == CANCELLED
}
