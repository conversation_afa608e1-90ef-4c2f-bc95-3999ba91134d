package graph

import (
	cosy_definition "cosy/definition"
	"cosy/indexing/index_setting"
	"cosy/log"
	"cosy/server"
	"cosy/sls"
	cosy_storage "cosy/storage"
	cosy_tree "cosy/tree"
	"cosy/user"
	cosy_graph "cosy/util/graph"
	"os"
	"path/filepath"
	"runtime/debug"
	"sort"
	"strconv"
	"strings"
	"time"

	"code.alibaba-inc.com/cosy/mtree"

	"code.alibaba-inc.com/cosy/lingma-codebase-graph/definition"
	"code.alibaba-inc.com/cosy/lingma-codebase-graph/storage"
	"code.alibaba-inc.com/cosy/lingma-codebase-graph/storage/graphsqlite"
	"code.alibaba-inc.com/cosy/lingma-codebase-graph/util/graph"
	"github.com/google/uuid"
)

type GraphManager struct {
	cosyServer *server.CosyServer
	lock       *GraphLock
}

func NewGraphManager(cosyServer *server.CosyServer, lock *GraphLock) *GraphManager {
	return &GraphManager{
		cosyServer: cosyServer,
		lock:       lock,
	}
}

func (gm *GraphManager) Run() {
	defer func() {
		if r := recover(); r != nil {
			panicStr := string(debug.Stack())
			data := make(map[string]string)
			data["panic_msg"] = panicStr
			data["function"] = "graph_manager"
			userType := user.GetUserType()
			data["user_type"] = userType
			log.Error("[codebase-graph] manager recover panic", panicStr)
			sls.Report(sls.EventTypeChatCodebaseGraphPanic, uuid.NewString(), data)
		}
	}()

	compensateMTreeCount := 1
	gcCount := 1
	for {
		if !cosy_graph.GetGlobalGraphSwitch() {
			log.Infof("[codebase-graph] graph indexing is disabled")
			time.Sleep(1 * time.Minute)
			continue
		}

		infos, err := gm.cosyServer.GetWorkspaceInfos()
		if err != nil {
			log.Errorf("[codebase-graph] cosyServer.GetWorkspaceInfos error: %v", err)
			continue
		}

		round := cosy_graph.GetManagerCompensateRound()
		gcRound := round * 2
		for _, info := range infos {
			workspaceDir := info.WorkspaceFolders[0].URI
			graphStore := cosy_storage.GetGraphStore(workspaceDir)
			if graphStore != nil {
				graphStore.Init()
				// 尝试释放workspace锁
				gm.releaseWorkerLock(graphStore, workspaceDir)
				// 推进 stage
				gm.advanceNextStage(graphStore, info.WorkspaceFolders[0].URI)
				// 处理事件
				gm.processEvent(graphStore, info.WorkspaceFolders[0].URI)
				// 处理长时间处于doing的异常数据
				gm.handleExceptionRecord(graphStore, workspaceDir)
				// 通过MTree补偿
				if compensateMTreeCount%round == 0 {
					if !index_setting.GetAutoIndexSetting(workspaceDir) {
						continue
					}
					gm.setIndexFinish(graphStore, workspaceDir)
					gm.compensateByMTree(graphStore, workspaceDir)
				}
				// 定期执行GC操作
				if gcCount%gcRound == 0 {
					log.Debugf("[codebase-graph] do graphdb gc:%s", workspaceDir)
					err := graphStore.Gc()
					if err != nil {
						log.Errorf("[codebase-graph] graph gc %s error: %v", workspaceDir, err)
					}
				}
			}
		}
		if compensateMTreeCount%round == 0 {
			compensateMTreeCount = 0
		}
		compensateMTreeCount += 1

		if gcCount%gcRound == 0 {
			gcCount = 0
		}
		gcCount += 1
		time.Sleep(time.Duration(cosy_graph.GetManagerScanInterval()) * time.Millisecond)
	}
}

func (gm *GraphManager) releaseWorkerLock(graphStore storage.GraphStore, workspace string) {
	if _, ok := cosy_graph.GraphWorkspaceWorkerLock.Load(workspace); ok {
		return
	}
	acceptLanguageExt := cosy_graph.GetAcceptParseNodeLanguageExt()
	nextThreshold := cosy_graph.GetNextStageMinThreshold()
	maxFileCount := cosy_graph.GetMaxFileCount()

	// 统计代码文件数目，如果比例超过阈值，则释放锁
	recordCount, err := graphStore.CountFileRecord([]storage.GraphCondition{})
	if err != nil {
		log.Errorf("[codebase-graph] count file record error: %v", err)
		return
	}
	if float64(recordCount)/float64(maxFileCount) >= nextThreshold {
		cosy_graph.GraphWorkspaceWorkerLock.Store(workspace, true)
		return
	}
	// 通过tree来拿到所有file
	oldTree := cosy_tree.NewWorkspaceMerkleTree(workspace)
	if oldTree == nil {
		return
	}

	if oldTree.Tree == nil {
		return
	}
	mainTree := oldTree.Tree.Clone()
	if mainTree == nil {
		log.Errorf("[codebase-graph] get main tree is nil: %v", err)
		return
	}
	allCount := 0
	nodes := mainTree.GetAllLeafNode()
	for _, node := range nodes {
		ext := strings.ToLower(filepath.Ext(node.RelativePath))
		if acceptLanguageExt[ext] {
			allCount += 1
		}
	}
	if float64(recordCount)/float64(allCount) >= nextThreshold {
		cosy_graph.GraphWorkspaceWorkerLock.Store(workspace, true)
	}
}

func (gm *GraphManager) advanceNextStage(graphStore storage.GraphStore, workspace string) {
	workspaceRecord, err := graphStore.FindWorkspaceRecord(nil, nil)
	nextThreshold := cosy_graph.GetNextStageMinThreshold()
	if err != nil {
		log.Errorf("[codebase-graph] get workspace record error: %v", err)
		return
	}
	if len(workspaceRecord) == 0 {
		record := storage.GraphWorkspaceRecord{
			WorkspaceDir: workspace,
			Stage:        definition.ProcessNode,
			GmtCreate:    time.Now().UnixNano(),
		}
		if err := graphStore.AddWorkspaceRecordIfAbsent(record); err != nil {
			log.Errorf("[codebase-graph] add workspace record error: %v", err)
			return
		}
		workspaceRecord = append(workspaceRecord, storage.GraphWorkspaceRecord{
			Stage:        definition.ProcessNode,
			WorkspaceDir: workspace,
			GmtCreate:    time.Now().UnixNano(),
		})
	} else {
		gm.sortWorkspaceByStage(workspaceRecord)
	}

	currentWorkspaceRecord := workspaceRecord[0]

	// 推进前面遗漏的文件
	if currentWorkspaceRecord.Stage != definition.ProcessNode {
		beforeStages, err := graph.GetBeforeStages(currentWorkspaceRecord.Stage)
		if err != nil {
			log.Errorf("[codebase-graph] get before stage error: %v", err)
			return
		}
		for _, stage := range beforeStages {
			nextStage, err := graph.GetNextStage(stage)
			if err != nil {
				log.Errorf("[codebase-graph] get next stage error: %v", err)
				continue
			}
			state := definition.INIT
			if nextStage == definition.Success {
				state = definition.SUCCESS
			}
			condition1 := storage.NewGraphCondition(definition.FileTableName, graphsqlite.FileRecordStage, definition.OperatorEqual, stage)
			condition2 := storage.NewGraphCondition(definition.FileTableName, graphsqlite.FileRecordFileState, definition.OperatorEqual, definition.SUCCESS)
			updateField1 := storage.NewGraphUpdateField(definition.FileTableName, graphsqlite.FileRecordFileState, state)
			updateField2 := storage.NewGraphUpdateField(definition.FileTableName, graphsqlite.FileRecordStage, nextStage)
			updateField3 := storage.NewGraphUpdateField(definition.FileTableName, graphsqlite.FileRecordGmtModified, time.Now().UnixNano())
			_, err = graphStore.UpdateFileRecord([]storage.GraphUpdateField{updateField1, updateField2, updateField3}, []storage.GraphCondition{condition1, condition2})
			if err != nil {
				log.Errorf("[codebase-graph] update file record error: %v", err)
			}
		}
	}

	// 推进到下一个 stage
	if currentWorkspaceRecord.Stage != definition.Success {
		afterStage, err := graph.GetNextStage(currentWorkspaceRecord.Stage)
		condition1 := storage.NewGraphCondition(definition.FileTableName, graphsqlite.FileRecordStage, definition.OperatorEqual, currentWorkspaceRecord.Stage)
		condition2 := storage.NewGraphCondition(definition.FileTableName, graphsqlite.FileRecordFileState, definition.OperatorEqual, definition.SUCCESS)

		nums, err := graphStore.CountFileRecord([]storage.GraphCondition{condition1, condition2})
		if err != nil {
			log.Errorf("[codebase-graph] count file record error: %v", err)
			return
		}
		condition := storage.NewGraphCondition(definition.FileTableName, graphsqlite.FileRecordStage, definition.OperatorEqual, currentWorkspaceRecord.Stage)
		allNums, err := graphStore.CountFileRecord([]storage.GraphCondition{condition})
		if err != nil {
			log.Errorf("[codebase-graph] count all file record error: %v", err)
			return
		}

		if float64(nums)/float64(allNums) >= nextThreshold {
			record := storage.GraphWorkspaceRecord{
				WorkspaceDir: workspace,
				Stage:        afterStage,
				GmtCreate:    time.Now().UnixNano(),
			}
			if afterStage == definition.Success {
				record.GmtModified = time.Now().UnixNano()
				log.Debugf("[codebase-graph] finish build index, set index success flag %v", workspace)
				index_setting.FinishBuildIndex(workspace, index_setting.IndexModeGraph)
			}
			updateField := storage.NewGraphUpdateField(definition.WorkspaceTableName, graphsqlite.WorkspaceRecordGmtModified, time.Now().UnixNano())
			condition = storage.NewGraphCondition(definition.WorkspaceTableName, graphsqlite.WorkspaceRecordStage, definition.OperatorEqual, currentWorkspaceRecord.Stage)
			_, err := graphStore.UpdateWorkspaceRecord([]storage.GraphUpdateField{updateField}, []storage.GraphCondition{condition})
			if err != nil {
				log.Errorf("[codebase-graph] update file record error: %v", err)
			}
			if err := graphStore.AddWorkspaceRecordIfAbsent(record); err != nil {
				log.Errorf("[codebase-graph] add workspace record error: %v", err)
				return
			}
			log.Info("[codebase-graph] advance to next stage,", workspace, afterStage)
			if currentWorkspaceRecord.Stage != definition.Success {
				nextStage, err := graph.GetNextStage(currentWorkspaceRecord.Stage)
				if err != nil {
					log.Errorf("[codebase-graph] get next stage error: %v", err)
					return
				}
				state := definition.INIT
				if nextStage == definition.Success {
					state = definition.SUCCESS
				}
				condition1 := storage.NewGraphCondition(definition.FileTableName, graphsqlite.FileRecordStage, definition.OperatorEqual, currentWorkspaceRecord.Stage)
				condition2 := storage.NewGraphCondition(definition.FileTableName, graphsqlite.FileRecordFileState, definition.OperatorEqual, definition.SUCCESS)
				updateField1 := storage.NewGraphUpdateField(definition.FileTableName, graphsqlite.FileRecordFileState, state)
				updateField2 := storage.NewGraphUpdateField(definition.FileTableName, graphsqlite.FileRecordStage, nextStage)
				updateField3 := storage.NewGraphUpdateField(definition.FileTableName, graphsqlite.FileRecordGmtModified, time.Now().UnixNano())
				_, err = graphStore.UpdateFileRecord([]storage.GraphUpdateField{updateField1, updateField2, updateField3}, []storage.GraphCondition{condition1, condition2})
				if err != nil {
					log.Errorf("[codebase-graph] update file record error: %v", err)
				}
			}
			go gm.addSlsLog(graphStore, workspace, currentWorkspaceRecord.Stage)
		}
	}
}

func (gm *GraphManager) processEvent(graphStore storage.GraphStore, workspace string) {
	events, err := graphStore.FindEventRecord(nil, nil)
	if err != nil {
		log.Errorf("[codebase-graph] get event record error: %v", err)
		return
	}

	for _, event := range events {
		gm.processOneEvent(graphStore, workspace, event)
	}
}

func (gm *GraphManager) handleExceptionRecord(graphStore storage.GraphStore, workspace string) {
	// 定义超时时间：5分钟
	timeoutNano := int64(5 * time.Minute)
	currentTime := time.Now().UnixNano()

	// 查找所有处于 DOING 状态的文件记录
	condition := storage.NewGraphCondition(definition.FileTableName, graphsqlite.FileRecordFileState, definition.OperatorEqual, definition.DOING)
	records, err := graphStore.FindFileRecord([]storage.GraphCondition{condition}, nil)
	if err != nil {
		log.Errorf("[codebase-graph] find doing file records error: %v", err)
		return
	}

	// 检查每个记录是否超时
	var exceptionCount int
	for _, record := range records {
		// 计算记录已经处于 DOING 状态的时间
		referenceTime := record.GmtModified
		// 如果超过超时时间，将状态设置为 FAIL
		if currentTime > referenceTime && (currentTime-referenceTime) > timeoutNano {
			updateCondition := storage.NewGraphCondition(definition.FileTableName, graphsqlite.FileRecordFileAbsPath, definition.OperatorEqual, record.FileAbsPath)
			updateField1 := storage.NewGraphUpdateField(definition.FileTableName, graphsqlite.FileRecordFileState, definition.INIT)
			updateField2 := storage.NewGraphUpdateField(definition.FileTableName, graphsqlite.FileRecordNextExecuteTime, currentTime+(10*1e9)) // 10秒后可重试
			updateField3 := storage.NewGraphUpdateField(definition.FileTableName, graphsqlite.FileRecordStage, definition.ProcessNode)
			updateField4 := storage.NewGraphUpdateField(definition.FileTableName, graphsqlite.FileRecordGmtModified, time.Now().UnixNano())

			_, err = graphStore.UpdateFileRecord([]storage.GraphUpdateField{updateField1, updateField2, updateField3, updateField4}, []storage.GraphCondition{updateCondition})
			if err != nil {
				log.Errorf("[codebase-graph] update exception file record error: %v, file: %s", err, record.FileAbsPath)
			} else {
				exceptionCount++
				log.Warnf("[codebase-graph] handle exception record: file %s has been in DOING state for too long, updated to FAIL", record.FileAbsPath)
			}
		}
	}

	if exceptionCount > 0 {
		log.Infof("[codebase-graph] handled %d exception records in workspace %s", exceptionCount, workspace)
	}
}

func (gm *GraphManager) processOneEvent(graphStore storage.GraphStore, workspace string, event storage.GraphEventRecord) {
	key := ProcessFilePrefix + event.FileAbsPath
	if gm.lock.TryLock(key, 1*time.Second) {
		defer gm.lock.Unlock(key)
		condition := storage.NewGraphCondition(definition.FileTableName, graphsqlite.FileRecordFileAbsPath, definition.OperatorEqual, event.FileAbsPath)
		count, err := graphStore.CountFileRecord([]storage.GraphCondition{condition})
		if err != nil {
			log.Errorf("[codebase-graph] count file record error: %v", err)
			return
		}
		sha256 := uuid.NewString()
		fileContent, err := os.ReadFile(event.FileAbsPath)
		if err == nil {
			sha256 = cosy_definition.GetFileId(fileContent)
		}

		if count == 0 {
			sha256 := cosy_definition.GetFileId(fileContent)
			record := storage.GraphFileRecord{
				WorkspaceDir:    workspace,
				FileAbsPath:     event.FileAbsPath,
				Stage:           definition.ProcessNode,
				FileState:       definition.INIT,
				GmtCreate:       time.Now().UnixNano(),
				GmtModified:     time.Now().UnixNano(),
				Tag:             sha256,
				NextExecuteTime: time.Now().UnixNano(),
			}
			if err := graphStore.AddFileRecordIfAbsent(record); err != nil {
				log.Errorf("[codebase-graph] add file record error: %v", err)
				return
			}
		} else {
			condition = storage.NewGraphCondition(definition.FileTableName, graphsqlite.FileRecordFileAbsPath, definition.OperatorEqual, event.FileAbsPath)
			updateField1 := storage.NewGraphUpdateField(definition.FileTableName, graphsqlite.FileRecordFileState, definition.INIT)
			updateField2 := storage.NewGraphUpdateField(definition.FileTableName, graphsqlite.FileRecordStage, definition.ProcessNode)
			updateField3 := storage.NewGraphUpdateField(definition.FileTableName, graphsqlite.FileRecordTag, sha256)
			updateField4 := storage.NewGraphUpdateField(definition.FileTableName, graphsqlite.FileRecordGmtModified, time.Now().UnixNano())

			_, err = graphStore.UpdateFileRecord([]storage.GraphUpdateField{updateField1, updateField2, updateField3, updateField4}, []storage.GraphCondition{condition})
			if err != nil {
				log.Errorf("[codebase-graph] update file record error: %v", err)
				return
			}
		}
		condition = storage.NewGraphCondition(definition.FileTableName, graphsqlite.FileRecordFileAbsPath, definition.OperatorEqual, event.FileAbsPath)
		err = graphStore.DeleteEventRecord([]storage.GraphCondition{condition})
		if err != nil {
			log.Errorf("[codebase-graph] delete event record error: %v", err)
		}
	}
}

func (gm *GraphManager) sortWorkspaceByStage(records []storage.GraphWorkspaceRecord) {
	stagePriority := map[string]int{
		definition.SUCCESS:         0,
		definition.ProcessRelation: 1,
		definition.CompleteNode:    2,
		definition.ProcessNode:     3,
	}

	// 使用 sort.Slice 进行排序
	sort.Slice(records, func(i, j int) bool {
		return stagePriority[records[i].Stage] < stagePriority[records[j].Stage]
	})
}

func (gm *GraphManager) addSlsLog(graphStore storage.GraphStore, workspace, currentStage string) {
	requestId := uuid.NewString()
	condition := storage.NewGraphCondition(definition.WorkspaceTableName, graphsqlite.WorkspaceRecordStage, definition.OperatorEqual, currentStage)
	records, err := graphStore.FindWorkspaceRecord([]storage.GraphCondition{condition}, nil)
	if err == nil && len(records) > 0 {
		count, err := graphStore.CountFileRecord([]storage.GraphCondition{condition})
		if err != nil {
			record := records[0]
			eventData := map[string]string{
				"workspace": workspace,
				"stage":     currentStage,
				"startTime": strconv.FormatInt(record.GmtCreate, 10),
				"endTime":   strconv.FormatInt(record.GmtModified, 10),
				"cost":      strconv.FormatInt(record.GmtModified-record.GmtCreate, 10),
				"fileCount": strconv.FormatInt(count, 10),
			}
			sls.Report(sls.EventTypeChatCodebaseGraphStageFinish, requestId, eventData)
		}
	}
}

func (gm *GraphManager) setIndexFinish(graphStore storage.GraphStore, workspace string) {
	record, err := graphStore.FindWorkspaceRecord(nil, nil)
	if err != nil {
		log.Warn("[codebase-graph] find workspace record err", err)
		return
	}
	if len(record) >= 4 {
		index_setting.FinishBuildIndex(workspace, index_setting.IndexModeGraph)
		log.Debugf("[codebase-graph] finish build index, set index success flag %v", workspace)
	}
}

func (gm *GraphManager) compensateByMTree(graphStore storage.GraphStore, workspace string) {
	mainCosyTree := cosy_tree.NewWorkspaceMerkleTree(workspace)
	maxFileCount := cosy_graph.GetMaxFileCount()
	if mainCosyTree == nil {
		log.Errorf("[codebase-graph] main cosy tree is nil")
		return
	}

	if mainCosyTree.Tree == nil {
		log.Errorf("[codebase-graph] main cosy tree is nil")
		return
	}
	mainTree := mainCosyTree.Tree.Clone()
	if mainTree == nil {
		log.Errorf("[codebase-graph] main mtree is nil")
		return
	}

	records, err := graphStore.FindFileRecord([]storage.GraphCondition{}, nil)
	if err != nil {
		log.Errorf("[codebase-graph] find file record error: %v", err)
		return
	}
	acceptLanguageExt := cosy_graph.GetAcceptParseNodeLanguageExt()

	recordTreeNodes := []*mtree.MerkleNode{}
	recordCount := len(records)
	for _, record := range records {
		rePath := strings.Replace(record.FileAbsPath, workspace, "", 1)
		if strings.HasPrefix(rePath, string(os.PathSeparator)) {
			rePath = rePath[1:]
		}
		recordTreeNodes = append(recordTreeNodes, &mtree.MerkleNode{
			RelativePath: rePath,
			Hash:         record.Tag,
			Type:         mtree.TypeLeaf,
		})
	}
	recordTree := mtree.BuildMerkleTreeWithNodes(workspace, recordTreeNodes)
	if recordTree == nil {
		log.Errorf("[codebase-graph] record mtree is nil")
		return
	}

	nodes := cosy_tree.DiffMTreeNodes(mainTree, recordTree, true, true)
	if nodes != nil && len(nodes) != 0 {
		for _, node := range nodes {
			ext := strings.ToLower(filepath.Ext(node.RelativePath))
			if !acceptLanguageExt[ext] {
				continue
			}
			condition := storage.NewGraphCondition(definition.FileTableName, graphsqlite.FileRecordFileAbsPath, definition.OperatorEqual, filepath.Join(workspace, node.RelativePath))
			updateField1 := storage.NewGraphUpdateField(definition.FileTableName, graphsqlite.FileRecordFileState, definition.INIT)
			updateField2 := storage.NewGraphUpdateField(definition.FileTableName, graphsqlite.FileRecordStage, definition.ProcessNode)
			updateField3 := storage.NewGraphUpdateField(definition.FileTableName, graphsqlite.FileRecordTag, node.Hash)
			updateField4 := storage.NewGraphUpdateField(definition.FileTableName, graphsqlite.FileRecordGmtModified, time.Now().UnixNano())

			count, err := graphStore.UpdateFileRecord([]storage.GraphUpdateField{updateField1, updateField2, updateField3, updateField4}, []storage.GraphCondition{condition})
			if err != nil {
				log.Errorf("[codebase-graph] update file record error: %v", err)
				continue
			} else if count == 0 {
				if recordCount >= int(maxFileCount) {
					continue
				}
				record := storage.GraphFileRecord{
					WorkspaceDir:    workspace,
					FileAbsPath:     filepath.Join(workspace, node.RelativePath),
					Stage:           definition.ProcessNode,
					FileState:       definition.INIT,
					GmtCreate:       time.Now().UnixNano(),
					GmtModified:     time.Now().UnixNano(),
					Tag:             node.Hash,
					NextExecuteTime: time.Now().UnixNano(),
				}
				if err := graphStore.AddFileRecordIfAbsent(record); err != nil {
					log.Errorf("[codebase-graph] add file record error: %v", err)
					continue
				} else {
					recordCount += 1
				}
			}
		}
	}

	nodes = cosy_tree.DiffMTreeNodes(recordTree, mainTree, true, true)
	if nodes != nil && len(nodes) != 0 {
		for _, node := range nodes {
			ext := strings.ToLower(filepath.Ext(node.RelativePath))
			if !acceptLanguageExt[ext] {
				continue
			}
			sha256 := uuid.NewString()
			fileContent, err := os.ReadFile(filepath.Join(workspace, node.RelativePath))
			if err == nil {
				sha256 = cosy_definition.GetFileId(fileContent)
			}
			log.Info("[codebase-graph] detect changed file by mtree", workspace, node.RelativePath)
			condition := storage.NewGraphCondition(definition.FileTableName, graphsqlite.FileRecordFileAbsPath, definition.OperatorEqual, filepath.Join(workspace, node.RelativePath))
			updateField1 := storage.NewGraphUpdateField(definition.FileTableName, graphsqlite.FileRecordFileState, definition.INIT)
			updateField2 := storage.NewGraphUpdateField(definition.FileTableName, graphsqlite.FileRecordStage, definition.ProcessNode)
			updateField3 := storage.NewGraphUpdateField(definition.FileTableName, graphsqlite.FileRecordTag, sha256)
			updateField4 := storage.NewGraphUpdateField(definition.FileTableName, graphsqlite.FileRecordGmtModified, time.Now().UnixNano())
			_, err = graphStore.UpdateFileRecord([]storage.GraphUpdateField{updateField1, updateField2, updateField3, updateField4}, []storage.GraphCondition{condition})
			if err != nil {
				log.Errorf("[codebase-graph] update file record error: %v", err)
				continue
			}
		}
	}
}
