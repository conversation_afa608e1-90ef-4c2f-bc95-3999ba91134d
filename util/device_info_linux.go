package util

import (
	"cosy/log"
	"fmt"
	"io/ioutil"
	"net"
	"os"
	"os/exec"
	"strings"
)

func runCommand(name string, arg ...string) (string, error) {
	cmd := exec.Command(name, arg...)
	output, err := cmd.Output()
	if err != nil {
		return "", err
	}
	return strings.TrimSpace(string(output)), nil
}

func GetMachineSerialNumberV2() string {
	content, err := ioutil.ReadFile("/etc/machine-id")
	if err != nil {
		log.Errorf("Error getting machine id: %v\n", err)
		return ""
	}
	machineId := strings.TrimSpace(string(content))
	return machineId
}

func GetDiskSerialNumberV2() (string, error) {
	executable, err := os.Executable()
	if err != nil {
		log.Errorf("Error getting executable path: %v\n", err)
		return "", err
	}

	// 获取文件所在的文件系统
	cmd := exec.Command("df", executable)
	output, err := cmd.Output()
	if err != nil {
		log.Errorf("Error getting output: %v\n", err)
		return "", err
	}
	lines := strings.Split(string(output), "\n")
	if len(lines) < 2 {
		return "", fmt.Errorf("unexpected df output")
	}
	device := strings.Fields(lines[1])[0]

	// 使用 blkid 获取磁盘 ID
	cmd = exec.Command("blkid", "-s", "UUID", "-o", "value", device)
	output, err = cmd.Output()
	if err != nil {
		return "", err
	}
	return strings.TrimSpace(string(output)), nil
}

// 通常由硬件制造商生成，存储在 BIOS 或 UEFI 固件中。
func GetHardwareIDV2() (string, error) {
	// 获取主板序列号
	motherboardSerial, err := runCommand("sudo", "dmidecode", "-s", "system-uuid")
	if err != nil {
		log.Errorf("Failed to get hardware id: , error: %v", err)
		return "", err
	}
	return motherboardSerial, nil
}
func GetMacAddressV2() (string, error) {
	interfaces, err := net.Interfaces()
	if err != nil {
		log.Errorf("Failed to get mac address: , error: %v", err)
		return "", fmt.Errorf("")
	}
	for _, intf := range interfaces {
		// 排除虚拟网卡
		if intf.Flags&net.FlagUp != 0 && intf.Flags&net.FlagPointToPoint == 0 && intf.Flags&net.FlagLoopback == 0 &&
			!PathExists("/sys/devices/virtual/net/"+intf.Name) {
			mac := intf.HardwareAddr.String()
			if mac != "" {
				return strings.Replace(mac, ":", "", -1), nil
			}
		}
	}
	return "", fmt.Errorf("")
}
