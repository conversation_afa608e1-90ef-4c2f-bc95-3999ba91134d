package util

// device_xxx(例如device_darwin)中的方法用于用户本地文件加密的密钥，修改了其中的方法可能会导致用户文件无法打开，需要重新登录。由于device_xxx
//中的有些方法在个别操作系统上没有实现，直接返回了空字符串，此次都升级了V2版本的接口来修改，避免原接口被改动影响用户
import (
	"bytes"
	"cosy/log"
	"fmt"
	"net"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
)

func GetMachineSerialNumberV2() string {
	buf := &bytes.Buffer{}
	c := exec.Command("/usr/sbin/ioreg", "-rd1", "-c", "IOPlatformExpertDevice")
	c.Stdin = os.Stdin
	c.Stdout = buf
	err := c.Run()
	if err != nil {
		log.Debugf("Failed to execute ioreg: %s", err)
		return ""
	}
	for _, line := range strings.Split(buf.String(), "\n") {
		if strings.Contains(line, "IOPlatformSerialNumber") {
			parts := strings.SplitAfter(line, `" = "`)
			if len(parts) == 2 {
				return strings.TrimRight(parts[1], `"`)
			}
		}
	}
	log.Debugf("Failed to find serial number in ioreg output")
	return ""
}

func GetMacAddressV2() (string, error) {
	interfaces, err := net.Interfaces()
	if err != nil {
		log.Debugf("Failed to interfaces: %s", err)
		return "", err
	}
	for _, intf := range interfaces {
		if intf.Name == "en0" && intf.HardwareAddr != nil {
			return strings.Replace(intf.HardwareAddr.String(), ":", "", -1), nil
		}
	}
	// 若没有默认名称的网卡，取第一张有Mac地址的网卡
	for _, intf := range interfaces {
		if intf.Flags&net.FlagUp != 0 && intf.Flags&net.FlagPointToPoint == 0 && intf.Flags&net.FlagLoopback == 0 {
			mac := intf.HardwareAddr.String()
			if mac != "" {
				return strings.Replace(mac, ":", "", -1), nil
			}
		}
	}
	return "", fmt.Errorf("")
}

func GetHardwareIDV2() (string, error) {
	// Mac 的硬件UUID，可以在系统报告》硬件中查看
	cmd := exec.Command("system_profiler", "SPHardwareDataType")
	output, err := cmd.Output()
	if err != nil {
		log.Errorf("Failed to get hardware id: , error: %v", err)
		return "", err
	}

	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		if strings.Contains(line, "Hardware UUID:") {
			parts := strings.Split(line, ":")
			if len(parts) == 2 {
				return strings.TrimSpace(parts[1]), nil
			}
		}
	}
	log.Debugf("Failed to find hardware UUID in system_profiler output")
	return "", fmt.Errorf("Hardware UUID not found")
}

// GetDiskSerialNumber 获取所在磁盘分卷的UUID
func GetDiskSerialNumberV2() (string, error) {
	exePath, err := os.Executable()
	if err != nil {
		log.Errorf("Error get executable file: %v\n", err)
		return "", err
	}
	// 获取路径所在的挂载点
	vol := filepath.VolumeName(exePath)
	if vol == "" {
		vol = "/"
	}

	// 执行 diskutil 命令
	cmd := exec.Command("diskutil", "info", vol)
	output, err := cmd.Output()
	if err != nil {
		return "", err
	}

	// 解析输出以获取 UUID
	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		if strings.Contains(line, "Volume UUID:") {
			parts := strings.Split(line, ":")
			if len(parts) > 1 {
				volumeId := strings.TrimSpace(parts[1])
				return volumeId, nil
			}
		}
	}

	return "", fmt.Errorf("Volume UUID not found")
}

func GetBoardIDV2() (string, error) {
	cmd := exec.Command("ioreg", "-l")
	output, err := cmd.Output()
	if err != nil {
		log.Errorf("Error get board id: %v\n", err)
		return "", err
	}

	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		if strings.Contains(line, "board-id") {
			return strings.Trim(strings.Split(line, "=")[1], " \"<>"), nil
		}
	}
	return "", fmt.Errorf("Board ID not found")
}
