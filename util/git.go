package util

import (
	"cosy/definition"
	"cosy/log"
	"fmt"
	"io"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"

	"github.com/go-git/go-git/v5"
	"github.com/go-git/go-git/v5/plumbing/object"
)

const (
	add      = "ADD"
	modified = "MODIFIED"
	deleted  = "DELETED"
)

// normalizeLineEndings 统一换行符为 LF，用于忽略换行符差异
func normalizeLineEndings(content string) string {
	// 先将 CRLF 替换为 LF，再将单独的 CR 替换为 LF
	content = strings.ReplaceAll(content, "\r\n", "\n")
	content = strings.ReplaceAll(content, "\r", "\n")
	return content
}

// contentEquals 比较两个内容是否相等（忽略换行符差异）
func contentEquals(oldContent, newContent string) bool {
	return normalizeLineEndings(oldContent) == normalizeLineEndings(newContent)
}

func GetGitHeadCommitId(workspace string) (string, error) {
	cmd := exec.Command("git", "rev-parse", "HEAD")
	cmd.Dir = workspace // 设置工作目录

	output, err := cmd.Output()
	if err != nil {
		return "", err
	}
	return strings.TrimSpace(string(output)), nil
}

// GetWorkspaceFileChanges 返回git工作区的变更（包括未stage）的前后变化完整内容。 文件路径是相对路径 以 ./ 起始
func GetWorkspaceFileChanges(workspace string) ([]definition.FileChangeInfo, error) {
	repo, err := git.PlainOpen(workspace)
	if err != nil {
		log.Errorf("failed to open git repository: %v", err)
		return nil, err
	}
	workTree, err := repo.Worktree()
	if err != nil {
		log.Errorf("failed to get worktree: %v", err)
		return nil, err
	}
	status, err := workTree.Status()
	if err != nil {
		log.Errorf("failed to get status: %v", err)
		return nil, err
	}
	head, err := repo.Head()
	if err != nil {
		log.Errorf("failed to get head: %v", err)
		return nil, err
	}
	commit, err := repo.CommitObject(head.Hash())
	if err != nil {
		log.Errorf("failed to get commit: %v", err)
		return nil, err
	}
	var fileChangeInfos []definition.FileChangeInfo
	for file, fileStatus := range status {
		log.Debugf("file: %s, status: %s", file, fileStatus)
		if isBinaryFile(file) {
			log.Infof("skip binary file: %s", file)
			continue
		}
		oldContent, err := getOldContent(repo, commit, file)
		if err != nil {
			log.Errorf("failed to get old content: %v", err)
		}
		newContent, err := getNewContent(workTree, file)
		if err != nil {
			log.Errorf("failed to get new content: %v", err)
		}
		if !isTextFile(oldContent) || !isTextFile(newContent) {
			log.Infof("skip non-text file: %s", file)
			continue
		}

		// 检查是否只是换行符差异，如果是则跳过
		if contentEquals(oldContent, newContent) {
			log.Debugf("skip file with only line ending differences: %s", file)
			continue
		}

		editMode := modified
		if oldContent == "" {
			editMode = add
		} else if newContent == "" {
			editMode = deleted
		}
		fileChangeInfos = append(fileChangeInfos, definition.FileChangeInfo{
			EditMode:        editMode,
			FilePath:        fmt.Sprintf("./%s", file),
			ModifiedContent: newContent,
			OriginalContent: oldContent,
		})
	}
	return fileChangeInfos, nil
}
func getOldContent(repo *git.Repository, commit *object.Commit, file string) (string, error) {
	tree, err := commit.Tree()
	if err != nil {
		return "", err
	}

	// 尝试从最新提交中获取文件内容
	entry, err := tree.FindEntry(file)
	if err != nil {
		return "", err
	}

	blob, err := repo.BlobObject(entry.Hash)
	if err != nil {
		return "", err
	}

	reader, err := blob.Reader()
	if err != nil {
		return "", err
	}
	defer reader.Close()

	content, err := io.ReadAll(reader)
	if err != nil {
		return "", err
	}

	return string(content), nil
}

func getNewContent(worktree *git.Worktree, file string) (string, error) {
	// 直接从工作目录读取文件内容，包括未暂存的变更
	content, err := os.ReadFile(filepath.Join(worktree.Filesystem.Root(), file))
	if err != nil {
		return "", err
	}
	return string(content), nil
}

func isBinaryFile(filename string) bool {
	// 常见的二进制文件扩展名
	binaryExtensions := []string{
		".pyc", ".pyo", ".pyd", // Python
		".class", ".jar", // Java
		".o", ".a", ".so", // C/C++
		".dll", ".exe", // Windows
		".png", ".jpg", ".gif", // 图片
		".zip", ".rar", ".7z", // 压缩文件
		".pdf", ".doc", ".xls", // 文档
	}

	ext := filepath.Ext(filename)
	for _, binaryExt := range binaryExtensions {
		if strings.EqualFold(ext, binaryExt) {
			return true
		}
	}
	return false
}
func isTextFile(content string) bool {
	// 空文件先假设是文本文件
	if len(content) == 0 {
		return true
	}
	// 检查前1024个字节
	limit := len(content)
	if limit > 1024 {
		limit = 1024
	}

	for _, b := range content[:limit] {
		// 如果出现控制字符（除了换行、回车、tab）
		if b < 32 && b != 9 && b != 10 && b != 13 {
			return false
		}
	}

	return true
}

// CommitWorkspace 提交指定工作区的所有变更
func CommitWorkspace(workspacePath string, message string) error {
	// 验证工作区路径是否存在
	if _, err := os.Stat(workspacePath); os.IsNotExist(err) {
		return fmt.Errorf("workspace path does not exist: %s", workspacePath)
	}

	// 打开 Git 仓库
	repo, err := git.PlainOpen(workspacePath)
	if err != nil {
		return fmt.Errorf("failed to open repository: %v", err)
	}

	// 获取工作树
	w, err := repo.Worktree()
	if err != nil {
		return fmt.Errorf("failed to get worktree: %v", err)
	}

	// 获取仓库状态
	status, err := w.Status()
	if err != nil {
		return fmt.Errorf("failed to get status: %v", err)
	}

	// 如果没有变更，直接返回
	if status.IsClean() {
		log.Infof("No changes to commit for workspace: %s", workspacePath)
		return nil
	}

	// 添加所有变更文件到暂存区
	err = w.AddWithOptions(&git.AddOptions{
		All: true, // 添加所有变更文件，包括新增、修改和删除
	})
	if err != nil {
		return fmt.Errorf("failed to add files: %v", err)
	}

	// 准备提交信息
	if message == "" {
		message = fmt.Sprintf("Workspace commit at %s", time.Now().Format(time.RFC3339))
	}

	// 提交
	commitHash, err := w.Commit(message, &git.CommitOptions{
		Author: &object.Signature{
			Name:  "Workspace Commit",
			Email: "workspace@local",
			When:  time.Now(),
		},
	})
	if err != nil {
		return fmt.Errorf("failed to commit: %v", err)
	}

	// 打印提交信息
	log.Infof("Workspace committed: %s\n", commitHash)

	return nil
}
