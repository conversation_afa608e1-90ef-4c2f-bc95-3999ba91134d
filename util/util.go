package util

import (
	"archive/zip"
	"context"
	. "cosy/definition"
	"cosy/global"
	"cosy/log"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"math/rand"
	"net/url"
	"os"
	"path/filepath"
	"regexp"
	"runtime"
	"sort"
	"strconv"
	"strings"
	"time"
	"unicode"

	"github.com/shirou/gopsutil/v4/process"

	"github.com/google/uuid"

	"github.com/gammazero/deque"
)

// maps used to do pair searching
var (
	// pairSet          = map[rune]bool{'{': true, '}': true, '[': true, ']': true, '(': true, ')': true, '<': true, '>': true}
	rightLeftPairMap = map[rune]rune{'}': '{', ']': '[', ')': '(', '>': '<'}
	leftRightPairMap = map[rune]rune{'{': '}', '[': ']', '(': ')', '<': '>'}
	bracketPair      = [][]rune{{'[', ']'}, {'{', '}'}, {'(', ')'}, {'<', '>'}}
	pairSymbols      = [][]rune{{'(', ')'}, {'{', '}'}, {'[', ']'}, {'<', '>'}, {'\'', '\''}, {'`', '`'}, {'"', '"'}}

	SingleSplitRegex = regexp.MustCompile("\\s+|[a-zA-Z0-9_@/$]+|[^\\s\\w@_/$]")

	URLCheckRegex = regexp.MustCompile(`^(https?|http)://[^\s/$.?#].[^\s]*$`)
)

var machineId = ""

var (
	machineSerialNum = ""
	hardwareId       = ""
	macAddress       = ""
	diskSerialNumber = ""
)

type bracketPos struct {
	ch  rune
	pos int
}

func GetIdeSeries(ctx context.Context) string {
	ideSeries, ok := ctx.Value("ide").(string)
	if !ok {
		if ideConfig, ok := ctx.Value(ContextKeyIdeConfig).(*IdeConfig); ok {
			ideSeries = ideConfig.IdeSeries
		}
	}

	return ideSeries
}

// IsLetter returns whether the given rune is an English letter [a-zA-Z]
func IsLetter(ch rune) bool {
	if (ch >= 'A' && ch <= 'Z') || (ch >= 'a' && ch <= 'z') {
		return true
	}
	return false
}

// ToJsonStr converts something to a string, it returns an empty string and prints a log if error happens
func ToJsonStr(v interface{}) string {
	if v == nil {
		return ""
	}
	if result, err := json.Marshal(v); err == nil {
		return string(result)
	} else {
		log.Info("Cannot convert object to json:", err)
	}
	return ""
}

func UnmarshalToObject(jsonStr string, target interface{}) error {
	if err := json.Unmarshal([]byte(jsonStr), target); err != nil {
		log.Info("Failed to parse chat system event params")
		return err
	}
	return nil
}

// 将字符串转换为整数（假设字符串是十进制整数）
func StrToInt(s string) (int, error) {
	if s == "" {
		return -1, errors.New("str is empty")
	}
	i, err := strconv.Atoi(s)
	if err != nil {
		return -1, err
	}
	return i, nil
}

// ByteOffsetForPosition returns byte offset of given position
func ByteOffsetForPosition(contents []rune, p Position) (offset int, valid bool, whyInvalid string) {
	runeOffset, valid, why := RuneOffsetForPosition(contents, p)
	if !valid {
		return 0, valid, why
	}
	return len([]byte(string(contents[:runeOffset]))), valid, why
}

// RuneOffsetForPosition returns rune offset of given position(NOT byte offset)
func RuneOffsetForPosition(contents []rune, p Position) (offset int, valid bool, whyInvalid string) {
	line := 0
	col := 0
	for _, b := range contents {
		if line == int(p.Line) && col == int(p.Character) {
			return offset, true, ""
		}
		if (line == int(p.Line) && col > int(p.Character)) || line > int(p.Line) {
			return 0, false, fmt.Sprintf("character %d (zero-based) is beyond line %d boundary (zero-based)", int(p.Character), int(p.Line))
		}
		offset++
		if b == '\n' {
			line++
			col = 0
		} else {
			col++
		}
	}
	if line == int(p.Line) && col == int(p.Character) {
		return offset, true, ""
	}
	if line == 0 {
		return 0, false, fmt.Sprintf("character %d (zero-based) is beyond first line boundary", int(p.Character))
	}
	return 0, false, fmt.Sprintf("file only has %d lines", line+1)
}

// GetPrevContent returns the string before given position p in contents
func GetPrevContent(contents string, p Position) (string, error) {
	offset, ok, why := RuneOffsetForPosition([]rune(contents), p)
	if !ok {
		log.Info()
		return "", errors.New("Cannot get prefix for current completionParam:" + why)
	}
	prefix := string([]rune(contents)[:offset])
	return prefix, nil
}

// GetPostContent returns the string after given position p in contents
func GetPostContent(contents string, p Position, length int) (string, error) {
	offset, ok, why := RuneOffsetForPosition([]rune(contents), p)
	if !ok {
		log.Info()
		return "", errors.New("Cannot get suffix for current completionParam:" + why)
	}
	fullContent := []rune(contents)
	if offset >= len(fullContent) {
		return "", nil
	}
	if offset+length > len(fullContent) {
		return string(fullContent[offset:]), nil
	}
	return string(fullContent[offset : offset+length]), nil
}

// GetParsedPrevContent extract the prefix of the parsed content
func GetParsedPrevContent(parsedContents string, rawContents string, prevToken string, p Position) (string, error) {
	// firstly, located to the current line, for both content, save prev lines
	parsedLines := regexp.MustCompile("\n|\r\n|\r").Split(parsedContents, -1)
	rawLines := regexp.MustCompile("\n|\r\n|\r").Split(rawContents, -1)

	if len(rawLines) <= int(p.Line) {
		return parsedContents, nil
	}
	parsedPrevLine := strings.Join(parsedLines[:int(p.Line)], "\n")
	parsedCurLine := parsedLines[int(p.Line)]
	rawCurLine := rawLines[int(p.Line)]

	// second, located to the current char in the raw content and tokenize
	rawCurLineRunes := []rune(rawCurLine)
	if len(rawCurLineRunes) < int(p.Character) {
		return parsedContents, nil
	}
	curLinePrefix := string(rawCurLineRunes[:int(p.Character)])
	curLineSuffix := string(rawCurLineRunes[int(p.Character):])
	posInSymbol := false
	var prefixEndingSymbol string
	if p.Character > 0 && len([]rune(curLinePrefix)) > int(p.Character)-1 && !unicode.IsLetter([]rune(curLinePrefix)[int(p.Character)-1]) && len([]rune(curLineSuffix)) > 0 && !unicode.IsLetter([]rune(curLineSuffix)[0]) {
		// 这种情况，补全光标的前后均为symbol，不是字母，现在的正则表达式会把这一堆全都搞到一起，导致prefix不对
		// 如"while(myList.[光标])"这种情况，如果不分开，那么prefix就是"while(myList.)"，导致ngram没有结果
		// 需要把curLinePrefix结尾的symbol(即上面的".")存起来，添加到prefix中
		posInSymbol = true
		for i := len(curLinePrefix) - 1; i >= 0; i-- {
			if unicode.IsSymbol(rune(curLinePrefix[i])) || unicode.IsPunct(rune(curLinePrefix[i])) {
				prefixEndingSymbol = string(curLinePrefix[i]) + prefixEndingSymbol
			} else {
				break
			}
		}
	}
	simpleSplitRegex := regexp.MustCompile("\\$[a-zA-Z0-9_\\[\\].]*\\$|\\s+|[a-zA-Z0-9_@$]+|[^\\s\\w@_$]+|/")
	token := simpleSplitRegex.FindAllString(curLinePrefix, -1)

	// 区分最后一个token是不是没写完
	parsedToken := simpleSplitRegex.FindAllString(parsedCurLine, -1)
	var curLine string
	if len(prevToken) > 0 || posInSymbol {
		curLine = strings.Join(parsedToken[:len(token)-1], "")
		if posInSymbol {
			curLine += prefixEndingSymbol
		}
	} else {
		curLine = strings.Join(parsedToken[:len(token)], "")
	}
	return strings.TrimLeft(parsedPrevLine+"\n"+curLine, "\n"), nil
}

// GetCurrentLineContext returns the token before the given position, content of current line and line-level prefix, suffix
func GetCurrentLineContext(content string, lineNum int, colNum int, canPrevTokenStartWithNum bool, extraAllowedChars ...rune) (string, string, string, string, error) {
	lines := strings.Split(content, "\n")
	if len(lines) <= lineNum {
		return "", "", "", "", errors.New(fmt.Sprintf("invalid line num %d for line count:%d", lineNum, len(lines)))
	}
	currentLine := lines[lineNum]
	if len(currentLine) < colNum {
		return "", currentLine, "", "", errors.New(fmt.Sprintf("invalid col num %d for col count:%d", colNum, len(currentLine)))
	}
	lineRunes := []rune(currentLine)
	linePrefix := lineRunes[:colNum]
	lineSuffix := []rune{}
	if colNum < len(lineRunes) {
		lineSuffix = lineRunes[colNum:]
	}
	prevToken := getPrevToken(linePrefix, canPrevTokenStartWithNum, extraAllowedChars...)
	return prevToken, currentLine, string(linePrefix), string(lineSuffix), nil
}

func getPrevToken(linePrefix []rune, canPrevTokenStartWithNum bool, extraAllowedChars ...rune) string {
	first := true
	prevToken := ""
	for i := len(linePrefix) - 1; i >= 0; i-- {
		ch := linePrefix[i]
		if (ch >= 'A' && ch <= 'Z') || (ch >= 'a' && ch <= 'z') || (ch >= '0' && ch <= '9') || ch == '$' ||
			ch == '_' || IsContains(ch, extraAllowedChars) {
			prevToken = string(ch) + prevToken
		} else {
			if first {
				return ""
			} else {
				break
			}
		}
		first = false
	}
	if prevToken != "" && !canPrevTokenStartWithNum {
		firstChar := []rune(prevToken)[0]
		if firstChar >= '0' && firstChar <= '9' {
			return ""
		}
	}
	return prevToken
}

// 数组去重
func RemoveDuplicateElement(array []string) []string {
	result := make([]string, 0, len(array))
	temp := map[string]struct{}{}
	for _, item := range array {
		if _, ok := temp[item]; !ok {
			temp[item] = struct{}{}
			result = append(result, item)
		}
	}
	return result
}

func IsContains(ch rune, extraChars []rune) bool {
	if extraChars == nil {
		return false
	}
	for _, v := range extraChars {
		if v == ch {
			return true
		}
	}
	return false
}

// AddPlaceholder add $0 to completion result
func AddPlaceholder(content string) string {
	if strings.Contains(content, "$0") {
		return content
	}

	// Remove placeholders like $1, $2, ...
	content = strings.ReplaceAll(content, "$1", "")
	content = strings.ReplaceAll(content, "$2", "")

	// Add $0
	if strings.Index(content, "\"\"") > 0 {
		content = strings.Replace(content, "\"\"", "\"$0\"", 1)
	} else if strings.Index(content, "[0]") > 0 {
		content = strings.Replace(content, "[0]", "[$0]", 1)
	} else if strings.Contains(content, ".)") {
		content = strings.Replace(content, ".)", "$0)", 1)
	}
	if strings.HasSuffix(content, "=>") {
		content = content + " {$0}"
	}
	return content
}

/**
 * 统计字符串中指定括号和花括号的个数
 */
func StatBracketCount(data string, pairIndex int, bracketPairs [][]rune) map[rune]int {
	prefixRune := []rune(data)
	statResult := make(map[rune]int)
	for _, pair := range bracketPairs {
		statResult[pair[pairIndex]] = 0
	}
	for _, ch := range prefixRune {
		for _, pair := range bracketPairs {
			if ch == pair[pairIndex] {
				statResult[pair[pairIndex]]++
			} else if ch == pair[1-pairIndex] {
				statResult[pair[pairIndex]]--
			}
		}
	}
	return statResult
}

/**
 * 从右向左找到指定数量的指定字符，从最短的位置截断字符串
 */
func RemoveBracketCharFromRight(content string, specChar rune, count int) string {
	if count <= 0 {
		return content
	}
	chars := []rune(content)
	latestIndex := len(chars)
	for i := len(chars) - 1; i >= 0; i-- {
		ch := chars[i]
		if ch == specChar {
			latestIndex = i
			count--
		}
		if count == 0 {
			break
		}
	}
	return string(chars[:latestIndex])
}

// RemoveOverlappedChars merges given strings and removes overlapping between prev and post strings
// It returns merged string and overlapped part
// For example, prev = "cross", post = "sing", this function returns "crossing" and "s"
func RemoveOverlappedChars(prev, post string) (string, string) {
	if len(post) == 0 || len(prev) == 0 {
		return prev + post, ""
	}
	firstCh := rune(post[0])
	// Iterate through prev string, find matching position
	for i, ch := range prev {
		if ch == firstCh {
			// Found potential matching position, start to match
			if strings.HasPrefix(post, prev[i:]) {
				// Found duplicate!
				return prev[:i] + post, prev[i:]
			}
		}
	}
	return prev + post, ""
}

// FixQuotes fixes all unpaired quotes
func FixQuotes(content string) string {
	if strings.Count(content, "'")%2 != 0 {
		content = content[:strings.LastIndex(content, "'")]
	}
	if strings.Count(content, "`")%2 != 0 {
		content = content[:strings.LastIndex(content, "`")]
	}
	if strings.Count(content, "\"")%2 != 0 {
		content = content[:strings.LastIndex(content, "\"")]
	}
	return content
}

// FixPairOrder exchanges symbols in pairs if the order is wrong after bracket closing and pair matching
func FixPairOrder(prefix, suffix, content, prevToken, fileContent string, pos Position) string {
	if len(prevToken) > 0 && strings.HasPrefix(content, prevToken) {
		prefix = strings.TrimSuffix(prefix, prevToken)
	}

	line := prefix + content + suffix
	validStart := len(prefix)
	validEnd := len(prefix + content)

	// stack
	bracketSeq := deque.Deque{}
	orderSeq := deque.Deque{}
	fixes := deque.Deque{}

	// First, scan and record all incorrect brackets' position
	for i, ch := range line {
		if _, ok := leftRightPairMap[ch]; ok {
			// If ch is a left bracket
			bracketSeq.PushBack(ch)
			orderSeq.PushBack(i)
		} else if left, ok := rightLeftPairMap[ch]; ok {
			// If ch is a right bracket
			if bracketSeq.Len() > 0 {
				if bracketSeq.Back() == left {
					// Matched last item in the queue, just pop it
					bracketSeq.PopBack()
					orderSeq.PopBack()
				} else if i < validEnd && i >= validStart {
					// Skip "=>"
					if ch == '>' && i-1 >= 0 && line[i-1] == '=' && i+1 < len(line) && line[i+1] != '<' {
						continue
					}
					// Fix current pos
					right := leftRightPairMap[bracketSeq.Back().(rune)]
					bracketSeq.PopBack()
					orderSeq.PopBack()
					fixes.PushBack(bracketPos{
						ch:  right,
						pos: i,
					})
				}
			}
		}
	}

	// Then, fix all pairs
	for fixes.Len() > 0 {
		if fix, ok := fixes.PopBack().(bracketPos); ok {
			contentBytes := []byte(content)
			replacePos := fix.pos - validStart
			if replacePos >= 0 && replacePos < len(contentBytes) {
				// Don't replace ")" when prefix contains "for" and "if"
				if contentBytes[replacePos] == ')' && fix.ch == '>' && (strings.Contains(prefix, "for") || strings.Contains(prefix, "if")) {
					continue
				}
				contentBytes[replacePos] = byte(fix.ch)
				content = string(contentBytes)
			} else {
				log.Info("Invalid replace pos:", replacePos, " for content:", content)
			}
		}
	}

	// Finally, remove all redundant right brackets
	rightBracketToRemove := map[rune]int{}
	numToBeRemoved := 0
	_, _, contentExceptCurrentLine, err := GetCodeContextExceptCurrentLine(fileContent, pos)
	if err != nil {
		log.Error("Get code context err: ", err)
	}
	for left, right := range leftRightPairMap {
		// Count brackets in completion item
		line, _ = RemoveOverlappedChars(prefix, content)
		// Overlapped suffix need to be removed
		// so we don't use RemoveOverlappedChars between content and suffix here
		line = line + suffix
		if diff := strings.Count(line, string(right)) - strings.Count(line, string(left)); diff > 0 {
			rightBracketToRemove[right] = diff
			numToBeRemoved += diff
		}

		// For brackets except "<>", count them in file content, update the num of brackets to be removed
		if left != '<' {
			if diff := strings.Count(contentExceptCurrentLine, string(left)) - strings.Count(contentExceptCurrentLine, string(right)); diff > 0 {
				if rightBracketToRemove[right] > 0 {
					rightBracketToRemove[right] = rightBracketToRemove[right] - diff
				}
				if rightBracketToRemove[right] == 0 {
					delete(rightBracketToRemove, right)
				}
				numToBeRemoved -= diff
			}
		}
	}

	if len(rightBracketToRemove) > 0 {
		index := len(content) - 1
		cutoffIndex := index + 1
		for numToBeRemoved > 0 && index >= 0 {
			if rightBracketToRemove[rune(content[index])] > 0 {
				rightBracketToRemove[rune(content[index])] = rightBracketToRemove[rune(content[index])] - 1
				numToBeRemoved--
				prevLine, _ := RemoveOverlappedChars(prefix, content)
				if content[index] == '>' && strings.Contains(prevLine, ") =>") {
					continue
				}
				cutoffIndex = index
				// If we need to remove ">", check "/>" case
				if content[index] == '>' && index > 0 && content[index-1] == '/' {
					cutoffIndex--
				}
			}
			index--
		}
		content = content[:cutoffIndex]
	}
	return content
}

// CloseBrackets appends right brackets if the given content is not closed properly
func CloseBrackets(prefix, suffix, prevToken, content string, bracketPairs [][]rune) string {
	suffix = strings.TrimSpace(suffix)
	line := strings.TrimSuffix(prefix, prevToken) + content
	// stack
	bracketSeq := deque.Deque{}
	allBrackets := map[rune]bool{}
	// Search left bracket using right
	leftBracketMap := map[rune]rune{}
	rightBracketMap := map[rune]rune{}

	for _, pair := range bracketPairs {
		allBrackets[pair[0]] = true
		allBrackets[pair[1]] = true
		leftBracketMap[pair[1]] = pair[0]
		rightBracketMap[pair[0]] = pair[1]
	}

	if IsForOrIfStmt(line) {
		delete(allBrackets, '<')
		delete(allBrackets, '>')
		delete(rightBracketMap, '<')
		delete(leftBracketMap, '>')
	}

	for i, ch := range line {
		if allBrackets[ch] == true {
			// The previous char is not a letter, means this < is not a generic bracket
			// Skip this < in this case
			if ch == '<' && i > 0 {
				if !unicode.IsLetter(rune(line[i-1])) {
					continue
				}
			}

			if bracketSeq.Len() > 0 && bracketSeq.Back() == leftBracketMap[ch] {
				// Close a bracket, pop from bracket sequence
				bracketSeq.PopBack()
			} else if _, ok := leftBracketMap[ch]; ok && leftBracketMap[ch] != ch {
				// Current char is right bracket, but don't close existing bracket
				// Skip
			} else {
				// Current char is left bracket
				bracketSeq.PushBack(ch)
			}
		}
	}

	// If current line has keyword "if" or "for", don't close "<>" pair
	firstComplete := true
	for bracketSeq.Len() > 0 {
		r := bracketSeq.Back().(rune)
		if (strings.Contains(prefix, "if") || strings.Contains(prefix, "while") || strings.Contains(prefix, "for")) && (r == '<' || r == '>') {
			bracketSeq.PopBack()
		} else if strings.Contains(suffix, string(rightBracketMap[r])) {
			suffix = strings.Replace(suffix, string(rightBracketMap[r]), "", 1)
			bracketSeq.PopBack()
		} else {
			if firstComplete && !strings.Contains(content, "$0") {
				content += "$0"
			}
			content += string(rightBracketMap[bracketSeq.PopBack().(rune)])
			firstComplete = false
		}
	}

	return content
}

// TrimBrackets removes right brackets if the number of right brackets is larger than left in completed current line
func TrimBrackets(linePrefix, lineSuffix, content string, bracketPairs [][]rune) string {
	for _, bracketPair := range bracketPairs {
		left := bracketPair[0]
		right := bracketPair[1]
		rightCntInPrefix := strings.Count(linePrefix, string(right))
		leftCntInPrefix := strings.Count(linePrefix, string(left))
		cnt := leftCntInPrefix - rightCntInPrefix

		// 如果linePrefix已匹配，且补全结果中第一个出现的是右括号，则截断至第一个右括号之前
		if cnt > 0 {
			for i, ch := range content {
				if rune(ch) == left {
					cnt++
				} else if rune(ch) == right {
					cnt--
				}
				// cnt < 0，说明多了一个右括号，则就在此截断
				if cnt < 0 {
					content = content[:i]
					break
				}
			}
		}
		// 重新计算，再处理后缀可能存在的多余的右括号
		// 如：`method(String v1, String v|, String v3)`，补全可能为`v2)`，下面的逻辑是把`v2)`中的右括号去掉
		leftCntInContent := strings.Count(content, string(left))
		leftCnt := leftCntInPrefix + strings.Count(lineSuffix, string(left)) + leftCntInContent
		rightCntInContent := strings.Count(content, string(right))
		rightCnt := rightCntInPrefix + strings.Count(lineSuffix, string(right)) + rightCntInContent
		diff := rightCnt - leftCnt
		if diff > 0 && rightCntInContent >= diff {
			content = RemoveBracketCharFromRight(content, right, diff)
		}
	}
	return content
}

// IsBracketBalance returns true if given string has the same number of bracket pairs {}()[]<>, except '>' in '=>'
func IsBracketBalance(code string) bool {
	for _, pair := range bracketPair {
		left := pair[0]
		right := pair[1]
		leftNum := strings.Count(code, string(left))
		rightNum := strings.Count(code, string(right))
		arrowExceptNum := strings.Count(code, ") =>")
		if left == '<' {
			if leftNum+arrowExceptNum-rightNum != 0 {
				return false
			}
		} else if leftNum-rightNum != 0 {
			return false
		}
	}
	return true
}

func RemoveSurplusBracket(prefix string, suffix string, content string, bracketPairs [][]rune) string {
	leftStatMap := StatBracketCount(prefix, 0, bracketPairs)
	rightStatMap := StatBracketCount(suffix, 1, bracketPairs)
	curStatMap := StatBracketCount(content, 1, bracketPairs)
	for _, pair := range bracketPairs {
		leftBracketCount := leftStatMap[pair[0]]
		rightBracketCount := rightStatMap[pair[1]]
		curBracketCount := curStatMap[pair[1]]
		diffBracketCount := leftBracketCount - rightBracketCount
		if diffBracketCount < 0 {
			diffBracketCount = 0
		}
		content = RemoveBracketCharFromRight(content, pair[1], curBracketCount-diffBracketCount)
	}
	return content
}

func RemoveDedupSuffix(suffix string, content string) string {
	suffix_rune := []rune(suffix)
	for i := 0; i < len(suffix_rune); i++ {
		str := suffix_rune[0 : i+1]
		if strings.HasSuffix(content, string(str)) {
			return strings.TrimRight(content, string(str))
		}
	}
	return content
}

// ReadFiles walks the given paths and returns
func ReadFiles(languageId string, paths []string, ch chan TextDocumentItem, fileSuffixes ...string) {
	// Read source code files in all workspaces
	// Read at most 3000 files
	cnt := 0
	for _, folder := range paths {
		filepath.Walk(folder, func(path string, info os.FileInfo, err error) error {
			if cnt > 3000 {
				return nil
			}
			//删除file://等前缀
			path = removeSchemePrefix(path)

			// Support only java and javascript for now
			// Don't check node_modules and .git folder
			if strings.HasPrefix(path, folder+"/node_modules") {
				return nil
			}
			if strings.Contains(path, ".git") {
				return nil
			}
			lowerPath := strings.ToLower(path)
			skip := true
			if info != nil && info.IsDir() {
				return nil
			}
			for _, suffix := range fileSuffixes {
				if strings.HasSuffix(lowerPath, suffix) {
					skip = false
					break
				}
			}
			if !skip {
				content, err := ioutil.ReadFile(path)
				if err != nil {
					log.Info("Cannot read file:", err)
					return nil
				}
				cnt += 1
				ch <- TextDocumentItem{
					URI:        DocumentURI(path),
					LanguageID: languageId,
					Text:       string(content),
				}
			}
			return nil
		})
		if cnt > 3000 {
			break
		}
	}
	// Close channel
	close(ch)
}

func ParseGitConfig(configContent string) map[string]map[string]string {
	lines := strings.Split(configContent, "\n")
	config := map[string]map[string]string{}
	// If first line is not like "[xxx]", return empty map
	if len(lines) > 0 && (!strings.HasPrefix(lines[0], "[") || !strings.HasSuffix(lines[0], "]")) {
		return config
	}
	currentConfigTag := ""
	for _, line := range lines {
		if strings.HasPrefix(line, "[") && strings.HasSuffix(line, "]") {
			// config tag
			currentConfigTag = strings.Trim(strings.TrimSpace(line), "[]")
			config[currentConfigTag] = map[string]string{}
		} else {
			configTokens := strings.Split(strings.TrimSpace(line), "=")
			if len(configTokens) != 2 {
				continue
			} else {
				configSet := config[currentConfigTag]
				configSet[strings.TrimSpace(configTokens[0])] = strings.TrimSpace(configTokens[1])
			}
		}
	}
	return config
}

func GetCodeContextExceptCurrentLine(fileContent string, pos Position) (prev, post, content string, err error) {
	lines := strings.Split(fileContent, "\n")
	if int(pos.Line) >= len(lines) {
		return "", "", fileContent, errors.New(fmt.Sprintf("illegal position row num %d out of %d", int(pos.Line), len(lines)))
	}
	prev = strings.Join(lines[:int(pos.Line)], "\n")
	if int(pos.Line)+1 < len(lines) {
		post = strings.Join(lines[int(pos.Line)+1:], "\n")
	}
	return prev, post, prev + "\n" + post, nil
}

func IsForOrIfStmt(line string) bool {
	line = strings.TrimSpace(line)
	return strings.HasPrefix(line, "for(") || strings.HasPrefix(line, "if(") || strings.HasPrefix(line, "while(") ||
		strings.HasPrefix(line, "for (") || strings.HasPrefix(line, "if (") || strings.HasPrefix(line, "while (")

}

// GetDynamicLibraryFilename returns onnxruntime's library filename for different platforms
// macOS: libonnxruntime.1.12.0.dylib
// linux: libonnxruntime.so.1.12.0
// windows: onnxruntime.dll
// If it returns an empty string, the current platform isn't supported
func GetDynamicLibraryFilename() string {
	filename := ""
	switch runtime.GOOS {
	case "darwin":
		filename = "libonnxruntime.1.12.0.dylib"
	case "windows":
		filename = "onnxruntime.dll"
	case "linux":
		filename = "libonnxruntime.so.1.12.0"
	default:
		log.Error("Unsupported platform: ", runtime.GOOS)
	}
	return filename
}

// GetDynamicLibraryPath returns full path of onnxruntime dynamic library
func GetDynamicLibraryPath() (string, error) {
	// Check if the dynamic lib file exists
	cacheDir := GetCosyCachePath()
	filename := GetDynamicLibraryFilename()
	if filename == "" {
		return "", errors.New("platform unsupported")
	}

	libFilePath := filepath.Join(cacheDir, "env", filename)
	return libFilePath, nil
}

// 查找代码中是否有重复子串
func RepeatCodeTokens(linePrefix string, item string, lineSuffix string, prevToken string) bool {
	var repeatResult [][]string
	if prevToken != "" && strings.HasSuffix(linePrefix, prevToken) {
		linePrefix = linePrefix[0 : len(linePrefix)-len(prevToken)]
	}
	line := linePrefix + item + lineSuffix
	candTokens := global.SplitRegex.FindAllString(line, -1)
	var suffixList []string
	for i := 0; i < len(candTokens); i++ {
		suffix := candTokens[i:]
		suffixList = append(suffixList, strings.Join(suffix, ""))
	}
	if len(suffixList) > 1 {
		sort.Strings(suffixList)
		var suffixTokensList [][]string
		for _, line := range suffixList {
			suffixTokens := SingleSplitRegex.FindAllString(line, -1)
			suffixTokensList = append(suffixTokensList, suffixTokens)
		}
		for i := 0; i < len(suffixTokensList)-1; i++ {
			source := suffixTokensList[i]
			target := suffixTokensList[i+1]
			var repeatTokens []string
			validRepeatCount := 0
			for j := 0; j < IntMin(len(source), len(target)); j++ {
				if source[j] != target[j] {
					break
				}
				if global.IdentifierRegex.MatchString(source[j]) {
					validRepeatCount += 1
				}
				repeatTokens = append(repeatTokens, source[j])
			}
			if validRepeatCount > 1 {
				repeatResult = append(repeatResult, repeatTokens)
			}
		}
		for _, r := range repeatResult {
			line := strings.Join(r, "")
			if strings.Index(item, line) >= 0 {
				return true
			}
		}
	}
	return false
}

// ProcessId 返回所有进程id
func ProcessId() []int32 {
	var result []int32
	pids, _ := process.Pids()
	for _, p := range pids {
		result = append(result, p)
	}
	return result
}

// KillProcessByName 根据指定进程名称杀掉进程
func KillProcessByName(processName string) {
	pids := ProcessId()
	for _, pid := range pids {
		pn, _ := process.NewProcess(pid)
		pName, _ := pn.Name()
		// 去除扩展名
		dotIndex := strings.LastIndex(pName, ".")
		if dotIndex > 0 {
			pName = pName[:dotIndex-1]
		}
		if strings.ToLower(pName) == strings.ToLower(processName) {
			err := pn.Kill()
			if err != nil {
				log.Error("fail to kill process ", pid)
			}
		}
	}
}

func removeSchemePrefix(path string) string {
	if strings.HasPrefix(path, "file://") {
		return strings.TrimPrefix(path, "file://")
	}
	return path
}

// MergeMap 合并map
func MergeMap(maps ...map[string]string) map[string]string {
	result := map[string]string{}
	for _, m := range maps {
		for k, v := range m {
			result[k] = v
		}
	}
	return result
}

// MapPutAll 将指定map的数据添加进目标map
func MapPutAll(source *map[string]string, target *map[string]string) {
	for k, v := range *source {
		(*target)[k] = v
	}
}

// MapPutAnyAll 将指定map的数据添加进目标map
func MapPutAnyAll[T any](source *map[string]T, target *map[string]T) {
	for k, v := range *source {
		(*target)[k] = v
	}
}

// MapMergeArrayAnyAll 将指定map的数据添加进目标map，将值数据合并，而不是覆盖
func MapMergeArrayAnyAll[T any](source *map[string][]T, target *map[string][]T) {
	for k, v := range *source {
		(*target)[k] = append((*target)[k], v...)
	}
}

func GetWindowsCmdPath() string {
	if FileExists("C:\\Windows\\System32\\cmd.exe") {
		return "C:\\Windows\\System32\\cmd.exe"
	}
	return "cmd.exe"
}

func GetWindowsTaskKillPath() string {
	if FileExists("C:\\Windows\\System32\\taskkill.exe") {
		return "C:\\Windows\\System32\\taskkill.exe"
	}
	return "taskkill.exe"
}

func FileExists(filename string) bool {
	info, err := os.Stat(filename)
	if os.IsNotExist(err) || info == nil {
		return false
	}
	return !info.IsDir()
}

func IsDir(filename string) bool {
	info, err := os.Stat(filename)
	if os.IsNotExist(err) || info == nil {
		return false
	}
	return info.IsDir()
}

func DirExists(filename string) bool {
	info, err := os.Stat(filename)
	if os.IsNotExist(err) {
		return false
	}
	if info == nil {
		return false
	}
	return info.IsDir()
}

func Exists(filename string) (bool, error) {
	_, err := os.Stat(filename)
	if err != nil {
		if os.IsNotExist(err) {
			return false, nil
		}

		return false, err
	}

	return true, nil
}

func NextRandNumber(maxNum int) int {
	// 设置随机数种子，使用当前时间确保每次运行结果不同
	rand.Seed(time.Now().UnixNano())

	// 生成1000以内的随机数，注意上限是1000，但Intn的参数需要写999，因为它是开区间 [0, n)
	return rand.Intn(maxNum)
}

func IsElementExists(slice []string, target string) bool {
	for _, element := range slice {
		if element == target {
			return true
		}
	}
	return false
}

// AppendArray 追加数组，带浅拷贝
func AppendArray[T any](data []T, a ...T) []T {
	args := append(data, a...)
	target := make([]T, len(args))
	copy(target, args)
	return target
}

// ParseFeatureSwitchConfig 启用的特性，key:value;key:value结构
func ParseFeatureSwitchConfig(config string) map[string]string {
	pairs := strings.Split(config, ";")
	result := make(map[string]string)
	for _, pair := range pairs {
		kv := strings.SplitN(pair, ":", 2)
		if len(kv) == 2 {
			result[strings.TrimSpace(kv[0])] = strings.TrimSpace(kv[1])
		}
	}
	return result
}

// GetMachineId 21位长度的设备ID
func GetMachineId(useCache bool) string {
	// 如果已经缓存过，直接返回
	if useCache && machineId != "" {
		return machineId
	}
	if serialNumber := getMachineSerialNumber(); serialNumber != "" {
		return encodeAndSaveMachineId(fitLength(serialNumber, 20))
	}
	// 8位磁盘序列号
	serialNumber, _ := getDiskSerialNumber()
	// 12位Mac地址
	macAddr, _ := getMacAddress()
	if serialNumber != "" && macAddr != "" {
		return encodeAndSaveMachineId(fitLength(serialNumber, 8) + fitLength(macAddr, 12))
	}
	// 尝试从缓存文件读取
	machineId = readMachineId()
	if machineId != "" {
		return machineId
	}
	// 使用随机ID并记录缓存
	return encodeAndSaveMachineId(randomString(20))
}

func readMachineId() string {
	idFilePath := filepath.Join(GetCosyCachePath(), IdFile)
	bytes, err := os.ReadFile(idFilePath)
	if err != nil {
		return ""
	}
	return strings.Trim(string(bytes), "\n")
}

func encodeAndSaveMachineId(rawId string) string {
	machineUuid, _ := uuid.NewRandomFromReader(strings.NewReader(addSystemMark(rawId)))
	machineId = machineUuid.String()
	idFilePath := filepath.Join(GetCosyCachePath(), IdFile)
	_ = os.WriteFile(idFilePath, []byte(machineId+"\n"), 0644)
	return machineId
}

func fitLength(s string, length int) string {
	if len(s) >= length {
		return s[:length]
	} else if len(s) == 0 {
		return ""
	}
	repeatNum := (length + len(s) - 1) / len(s) // 计算需要重复的次数
	return (s + strings.Repeat(s, repeatNum))[:length]
}

// randomInt 生成指定范围内的随机整数
func randomInt(min, max int) int {
	return min + rand.Intn(max-min+1)
}

// randomString 生成指定长度的随机字符串
func randomString(length int) string {
	var src = rand.NewSource(time.Now().UnixNano())
	b := make([]byte, length)
	for i := range b {
		b[i] = toChar(int(src.Int63()) % 62)
	}
	return string(b)
}

func toChar(n int) byte {
	if n < 10 {
		return byte(rune('0' + n))
	} else if n < 36 {
		return byte(rune('a' + n - 10))
	}
	return byte(rune('A' + n - 36))
}

// zipFiles 压缩文件列表到指定的zip文件
func ZipFiles(filename string, files []string) error {
	newZipFile, err := os.Create(filename)
	if err != nil {
		return err
	}
	defer newZipFile.Close()

	zipWriter := zip.NewWriter(newZipFile)
	defer zipWriter.Close()

	// 将每个文件添加到zip文件中
	for _, file := range files {
		if err = addFileToZip(zipWriter, file); err != nil {
			return err
		}
	}
	return nil
}

// addFileToZip 将单个文件添加到zip文件中
func addFileToZip(zipWriter *zip.Writer, filename string) error {
	fileToZip, err := os.Open(filename)
	if err != nil {
		return err
	}
	defer fileToZip.Close()

	// 获取文件信息
	info, err := fileToZip.Stat()
	if err != nil {
		return err
	}

	header, err := zip.FileInfoHeader(info)
	if err != nil {
		return err
	}

	// 使用文件名作为ZIP中的文件名
	header.Name = filepath.Base(filename)

	// 创建一个新的文件条目
	writer, err := zipWriter.CreateHeader(header)
	if err != nil {
		return err
	}

	// 将文件内容复制到ZIP文件中
	_, err = io.Copy(writer, fileToZip)
	return err
}

func GetFileLines(fileContent string) int {
	lines := strings.Split(fileContent, "\n")
	return len(lines)
}

// GetExclusiveList 返回在 sourceList 中但不在 exclusiveList 中的列表
func GetExclusiveList(sourceList, exclusiveList []string) []string {
	// 判空处理
	if sourceList == nil {
		return nil
	}
	if exclusiveList == nil {
		return sourceList
	}
	var resultList []string

	contains := func(list []string, item string) bool {
		for _, v := range list {
			if v == item {
				return true
			}
		}
		return false
	}

	for _, path := range sourceList {
		if !contains(exclusiveList, path) {
			resultList = append(resultList, path)
		}
	}

	return resultList
}

// 校验url是否合法
func CheckUrlValidate(rawUrl string) bool {
	if !URLCheckRegex.MatchString(rawUrl) {
		return false
	}
	parsedURL, err := url.Parse(rawUrl)
	if err != nil || parsedURL.Scheme == "" || parsedURL.Host == "" {
		return false
	}
	switch parsedURL.Scheme {
	case "http", "https":
		return true
	default:
		return false
	}
}

// BuildDeviceInformation 构建设备信息
func BuildDeviceInformation() map[string]string {
	info := make(map[string]string)
	// 获取设备序列号
	if machineSerialNum != "" {
		info["device_machine_serial_num"] = machineSerialNum
	}

	// 获取mac地址
	if macAddress != "" {
		info["device_mac_address"] = macAddress
	}

	// 获取磁盘序列号
	if diskSerialNumber != "" {
		info["device_disk_serial_num"] = diskSerialNumber
	}

	// 获取硬件ID
	if hardwareId != "" {
		info["device_hardware_id"] = hardwareId
	}
	return info
}

func init() {
	// 获取设备序列号
	machineSerialNum = GetMachineSerialNumberV2()
	log.Debug("device info machineSerialNum is: " + machineSerialNum)

	// 获取mac地址
	macAddress, _ = GetMacAddressV2()
	log.Debug("device info macAddress is: " + macAddress)

	// 获取磁盘序列号
	diskSerialNumber, _ = GetDiskSerialNumberV2()
	log.Debug("device info diskSerialNumber is: " + diskSerialNumber)

	// 获取硬件ID
	hardwareId, _ = GetHardwareIDV2()
	log.Debug("device info hardwareId is: " + hardwareId)

}
