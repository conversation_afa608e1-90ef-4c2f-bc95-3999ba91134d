package rag

// 处理用于RAG的代码片段拆分的工具API

import (
	"bufio"
	"bytes"
	"cosy/tokenizer"
	"crypto/md5"
	"errors"
	"fmt"
	"io"
	"os"
	"strings"
)

// GetCodeChunkByLimit 获取检索代码默认逻辑，根据行号，获取指定行上下各20行的代码
func GetCodeChunkByLimit(code string, row uint32, aboveRowLimit uint32, belowRowLimit uint32) ([]string, error) {
	lines := strings.Split(code, "\n")
	var startRow uint32
	if aboveRowLimit >= row {
		startRow = 0
	} else {
		startRow = row - aboveRowLimit
	}
	endRow := row + belowRowLimit
	if endRow > uint32(len(lines)) {
		endRow = uint32(len(lines))
	}
	retrieveLines := make([]string, 0)
	// validLineCount 排除空行及import后的有效行数
	validLineCount := 0
	for i := startRow; i < endRow; i++ {
		if strings.Trim(lines[i], " \t") == "" {
			continue
		}
		if !strings.HasPrefix(lines[i], "import ") &&
			!strings.HasPrefix(lines[i], "include ") &&
			!strings.HasPrefix(lines[i], "from ") &&
			!strings.HasPrefix(lines[i], "using ") &&
			!strings.HasPrefix(lines[i], "use ") {
			validLineCount++
		}
		retrieveLines = append(retrieveLines, lines[i])
	}
	if len(retrieveLines) == 0 || validLineCount == 0 {
		return []string{}, nil
	}
	return []string{strings.Join(retrieveLines, "\n")}, nil
}

// GetChunkId 获取代码片段的ID，MD5(文件路径+chunk内容)
func GetChunkId(filePath string, content string) string {
	hashContent := fmt.Sprintf("%s%s", filePath, content)
	return fmt.Sprintf("%x", md5.Sum([]byte(hashContent)))
}

func GetFileContentByLine(filePath string, startLine uint32, endLine uint32) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", fmt.Errorf("an error occurred while opening the file, filepath: %s, start line:%d, end line:%d, err: %v", filePath, startLine, endLine, err)
	}
	defer file.Close()
	reader := bufio.NewReaderSize(file, 4*1024*1024) // 4MB初始缓冲区

	var currentLine uint32
	var buffer bytes.Buffer

	for {
		line, isPrefix, err := reader.ReadLine()
		if err != nil {
			if err == io.EOF {
				break
			}
			return "", fmt.Errorf("an error occurred while reading the file, filepath: %s, start line:%d, end line:%d, err: %v", filePath, startLine, endLine, err)
		}
		// 检查是否线被截断，需要继续读取
		for isPrefix {
			more, newIsPrefix, err := reader.ReadLine()
			if err != nil {
				return "", fmt.Errorf("an error occurred while reading the file, filepath: %s, start line:%d, end line:%d, err: %v", filePath, startLine, endLine, err)
			}
			line = append(line, more...)
			isPrefix = newIsPrefix
		}
		// 现在line是完整的行
		if currentLine >= startLine && currentLine <= endLine {
			buffer.Write(line)
			buffer.WriteByte('\n')
		} else if currentLine > endLine {
			break
		}
		currentLine++
	}

	return strings.TrimSuffix(buffer.String(), "\n"), nil
}

// GetFileContentByOffset 按照偏移量来读取文件内容
func GetFileContentByOffset(filePath string, startOffset, endOffset uint32) (string, error) {
	// 打开文件
	file, err := os.Open(filePath)
	if err != nil {
		return "", fmt.Errorf("failed to open file: %w", err)
	}
	defer file.Close()

	// 获取文件大小
	fileInfo, err := file.Stat()
	if err != nil {
		return "", fmt.Errorf("failed to get file info: %w", err)
	}
	fileSize := uint32(fileInfo.Size())

	// 检查偏移范围是否有效
	if endOffset <= startOffset || startOffset > fileSize {
		return "", fmt.Errorf("invalid offset range: start=%d, end=%d, fileSize=%d", startOffset, endOffset, fileSize)
	}

	// 自动调整结束偏移到文件末尾
	if endOffset > fileSize {
		endOffset = fileSize
	}

	// 定位到起始偏移
	_, err = file.Seek(int64(startOffset), io.SeekStart)
	if err != nil {
		return "", fmt.Errorf("failed to seek to start offset: %w", err)
	}

	// 计算需要读取的字节数
	length := endOffset - startOffset
	buffer := make([]byte, length)

	//// 读取指定范围的内容
	//_, err = io.ReadFull(file, buffer)
	//if err != nil && err != io.EOF {
	//	return "", fmt.Errorf("failed to read file content: %w", err)
	//}

	_, err = file.Read(buffer)
	if err != nil && !errors.Is(err, io.EOF) {
		return "", fmt.Errorf("failed to read file: %w", err)
	}

	// 返回实际读取的内容
	return string(buffer), nil
}

// GetContentByLimitTokens 根据token数量限制获取代码片段, 需要保留完整行
// 先按行拆分，然后按token数量拆分，保留完整行
// 参数:
// - code: 待处理的代码字符串。
// - tokenizer: 用于将代码字符串分词的分词器对象。
// - limitTokens: 限制的令牌数量，用于控制返回的代码内容中包含的令牌数量上限。
// 返回值:
// - string: 处理后的代码内容字符串，保证其中包含的令牌数量不超过limitTokens。
// - int: 实际返回的代码内容中包含的令牌数量。
func GetContentByLimitTokens(code string, tokenizer tokenizer.Tokenizer, limitTokens int) (string, int) {
	result := make([]string, 0)
	totalTokens := 0
	lines := strings.Split(code, "\n")
	for i := 0; i < len(lines); i++ {
		if len(lines[i]) > 0 {
			tokens, err := tokenizer.Tokenize(lines[i])
			if err != nil {
				continue
			}
			if totalTokens+len(tokens) > limitTokens {
				break
			}
			totalTokens += len(tokens)
			result = append(result, lines[i])
		} else {
			result = append(result, lines[i])
		}
	}
	return strings.Join(result, "\n"), totalTokens
}
