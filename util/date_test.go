package util

import (
	"github.com/stretchr/testify/assert"
	"testing"
	"time"
)

func Test_GetEndOfNextDay(t *testing.T) {
	assert.Equal(t, int64(1672675199999), GetEndOfNextDay(1672537523456))
}

func TestTimeToString(t *testing.T) {
	testTime := time.Date(2023, 1, 1, 12, 30, 45, 0, time.UTC)

	// 测试标准日期时间格式
	result := TimeToString(testTime, DateTimeFormat)
	assert.Equal(t, "2023-01-01 12:30:45", result)

	// 测试日期格式
	result = TimeToString(testTime, DateFormat)
	assert.Equal(t, "2023-01-01", result)

	// 测试时间格式
	result = TimeToString(testTime, TimeFormat)
	assert.Equal(t, "12:30:45", result)
}

func TestTimeToDateTimeString(t *testing.T) {
	testTime := time.Date(2023, 1, 1, 12, 30, 45, 0, time.UTC)
	result := TimeToDateTimeString(testTime)
	assert.Equal(t, "2023-01-01 12:30:45", result)
}

func TestTimeToDateString(t *testing.T) {
	testTime := time.Date(2023, 1, 1, 12, 30, 45, 0, time.UTC)
	result := TimeToDateString(testTime)
	assert.Equal(t, "2023-01-01", result)
}

func TestTimeToTimeString(t *testing.T) {
	testTime := time.Date(2023, 1, 1, 12, 30, 45, 0, time.UTC)
	result := TimeToTimeString(testTime)
	assert.Equal(t, "12:30:45", result)
}

func TestTimestampToString(t *testing.T) {
	// 2023-01-01 12:30:45 UTC 对应的时间戳
	timestamp := int64(1672574445000)
	result := TimestampToString(timestamp, DateTimeFormat)
	assert.Equal(t, "2023-01-01 12:30:45", result)
}

func TestTimestampToDateTimeString(t *testing.T) {
	timestamp := int64(1672574445000)
	result := TimestampToDateTimeString(timestamp)
	assert.Equal(t, "2023-01-01 12:30:45", result)
}

func TestStringToTime(t *testing.T) {
	// 测试标准日期时间格式
	timeStr := "2023-01-01 12:30:45"
	result, err := StringToTime(timeStr, DateTimeFormat)
	assert.NoError(t, err)
	assert.Equal(t, 2023, result.Year())
	assert.Equal(t, time.January, result.Month())
	assert.Equal(t, 1, result.Day())
	assert.Equal(t, 12, result.Hour())
	assert.Equal(t, 30, result.Minute())
	assert.Equal(t, 45, result.Second())

	// 测试无效格式
	_, err = StringToTime("invalid", DateTimeFormat)
	assert.Error(t, err)
}

func TestStringToTimeWithDefault(t *testing.T) {
	defaultTime := time.Date(2022, 1, 1, 0, 0, 0, 0, time.UTC)

	// 测试有效日期
	result := StringToTimeWithDefault("2023-01-01 12:30:45", defaultTime)
	assert.Equal(t, 2023, result.Year())

	// 测试无效日期，应返回默认值
	result = StringToTimeWithDefault("invalid", defaultTime)
	assert.Equal(t, defaultTime, result)
}

func TestStringToTimestamp(t *testing.T) {
	timeStr := "2023-01-01 12:30:45"
	result, err := StringToTimestamp(timeStr, DateTimeFormat)
	assert.NoError(t, err)
	assert.True(t, result > 0)

	// 测试无效格式
	_, err = StringToTimestamp("invalid", DateTimeFormat)
	assert.Error(t, err)
}

func TestStringToTimestampWithDefault(t *testing.T) {
	defaultTimestamp := int64(1640995200000) // 2022-01-01 00:00:00

	// 测试有效日期
	result := StringToTimestampWithDefault("2023-01-01 12:30:45", defaultTimestamp)
	assert.NotEqual(t, defaultTimestamp, result)

	// 测试无效日期，应返回默认值
	result = StringToTimestampWithDefault("invalid", defaultTimestamp)
	assert.Equal(t, defaultTimestamp, result)
}

func TestNowString(t *testing.T) {
	result := NowString(DateFormat)
	// 检查结果格式是否正确（长度应为10，格式为YYYY-MM-DD）
	assert.Len(t, result, 10)
	assert.Contains(t, result, "-")
}

func TestNowDateTimeString(t *testing.T) {
	result := NowDateTimeString()
	// 检查结果格式是否正确（长度应为19，格式为YYYY-MM-DD HH:MM:SS）
	assert.Len(t, result, 19)
	assert.Contains(t, result, "-")
	assert.Contains(t, result, ":")
}

func TestNowDateString(t *testing.T) {
	result := NowDateString()
	assert.Len(t, result, 10)
	assert.Contains(t, result, "-")
}

func TestNowTimeString(t *testing.T) {
	result := NowTimeString()
	assert.Len(t, result, 8)
	assert.Contains(t, result, ":")
}

func TestIsValidDateString(t *testing.T) {
	// 测试有效日期
	assert.True(t, IsValidDateString("2023-01-01 12:30:45", DateTimeFormat))
	assert.True(t, IsValidDateString("2023-01-01", DateFormat))
	assert.True(t, IsValidDateString("12:30:45", TimeFormat))

	// 测试无效日期
	assert.False(t, IsValidDateString("invalid", DateTimeFormat))
	assert.False(t, IsValidDateString("2023-13-01", DateFormat))
}

func TestFormatDuration(t *testing.T) {
	// 测试秒
	result := FormatDuration(30 * time.Second)
	assert.Contains(t, result, "秒")

	// 测试分钟
	result = FormatDuration(5 * time.Minute)
	assert.Contains(t, result, "分钟")

	// 测试小时
	result = FormatDuration(2 * time.Hour)
	assert.Contains(t, result, "小时")

	// 测试天
	result = FormatDuration(25 * time.Hour)
	assert.Contains(t, result, "天")
}

func TestGetTimestamp(t *testing.T) {
	result := GetTimestamp()
	assert.True(t, result > 0)
	// 检查是否是毫秒级时间戳（13位数字）
	assert.True(t, result > 1000000000000)
}

func TestGetTimestampSecond(t *testing.T) {
	result := GetTimestampSecond()
	assert.True(t, result > 0)
	// 检查是否是秒级时间戳（10位数字）
	assert.True(t, result > 1000000000)
	assert.True(t, result < 10000000000)
}
