package util

import (
	"fmt"
	"time"
)

// GetEndOfNextDay 返回指定时间第二天的最后一毫秒时间
func GetEndOfNextDay(currentTime int64) int64 {
	// 将当前时间戳转换为time.Time类型
	currentTimeObj := time.Unix(currentTime/1000, 0)

	// 计算明天的日期
	tomorrow := currentTimeObj.Add(24 * time.Hour)

	// 设置明天的时间为23:59:59.999999999，即明天的最后一纳秒时间
	tomorrowLastSecond := time.Date(tomorrow.Year(), tomorrow.Month(), tomorrow.Day(), 23, 59, 59, 999999999, tomorrow.Location())

	// 将明天的最后一纳秒时间转换为时间戳，并返回
	return tomorrowLastSecond.UnixMilli()
}

func GetTimeAfter(currentTime int64, duration time.Duration) int64 {
	// 将当前时间戳转换为time.Time类型
	currentTimeObj := time.Unix(currentTime/1000, 0)

	// 计算明天的日期
	timeAfter := currentTimeObj.Add(duration)

	// 返回时间戳
	return timeAfter.UnixMilli()
}

const (
	// 常用日期格式常量
	DateTimeFormat    = "2006-01-02 15:04:05"
	DateFormat        = "2006-01-02"
	TimeFormat        = "15:04:05"
	DateTimeFormatMs  = "2006-01-02 15:04:05.000"
	DateTimeFormatISO = "2006-01-02T15:04:05Z07:00"
	DateTimeFormatRFC = time.RFC3339
)

// TimeToString 将时间转换为指定格式的字符串
func TimeToString(t time.Time, format string) string {
	return t.Format(format)
}

// TimeToDateTimeString 将时间转换为标准日期时间字符串 (yyyy-MM-dd HH:mm:ss)
func TimeToDateTimeString(t time.Time) string {
	return t.Format(DateTimeFormat)
}

// TimeToDateString 将时间转换为日期字符串 (yyyy-MM-dd)
func TimeToDateString(t time.Time) string {
	return t.Format(DateFormat)
}

// TimeToTimeString 将时间转换为时间字符串 (HH:mm:ss)
func TimeToTimeString(t time.Time) string {
	return t.Format(TimeFormat)
}

// TimestampToString 将时间戳转换为指定格式的字符串
func TimestampToString(timestamp int64, format string) string {
	return time.Unix(timestamp/1000, (timestamp%1000)*1000000).Format(format)
}

// TimestampToDateTimeString 将时间戳转换为标准日期时间字符串
func TimestampToDateTimeString(timestamp int64) string {
	return TimestampToString(timestamp, DateTimeFormat)
}

// StringToTime 将字符串转换为时间，支持指定格式
func StringToTime(dateStr, format string) (time.Time, error) {
	return time.Parse(format, dateStr)
}

// StringToTimeWithDefault 将字符串转换为时间，支持多种格式，失败时返回默认值
func StringToTimeWithDefault(dateStr string, defaultTime time.Time) time.Time {
	// 尝试常用格式
	formats := []string{
		DateTimeFormat,
		DateFormat,
		TimeFormat,
		DateTimeFormatMs,
		DateTimeFormatISO,
		DateTimeFormatRFC,
		time.RFC822,
		time.RFC822Z,
		time.RFC850,
		time.RFC1123,
		time.RFC1123Z,
	}

	for _, format := range formats {
		if t, err := time.Parse(format, dateStr); err == nil {
			return t
		}
	}

	return defaultTime
}

// StringToTimestamp 将字符串转换为时间戳（毫秒）
func StringToTimestamp(dateStr, format string) (int64, error) {
	t, err := time.Parse(format, dateStr)
	if err != nil {
		return 0, err
	}
	return t.UnixMilli(), nil
}

// StringToTimestampWithDefault 将字符串转换为时间戳，失败时返回默认值
func StringToTimestampWithDefault(dateStr string, defaultTimestamp int64) int64 {
	t := StringToTimeWithDefault(dateStr, time.Unix(defaultTimestamp/1000, 0))
	return t.UnixMilli()
}

// NowString 获取当前时间的字符串表示
func NowString(format string) string {
	return time.Now().Format(format)
}

// NowDateTimeString 获取当前日期时间字符串
func NowDateTimeString() string {
	return time.Now().Format(DateTimeFormat)
}

// NowDateString 获取当前日期字符串
func NowDateString() string {
	return time.Now().Format(DateFormat)
}

// NowTimeString 获取当前时间字符串
func NowTimeString() string {
	return time.Now().Format(TimeFormat)
}

// IsValidDateString 检查字符串是否为有效的日期格式
func IsValidDateString(dateStr, format string) bool {
	_, err := time.Parse(format, dateStr)
	return err == nil
}

// FormatDuration 格式化时间间隔为可读字符串
func FormatDuration(d time.Duration) string {
	if d < time.Minute {
		return fmt.Sprintf("%.1f秒", d.Seconds())
	}
	if d < time.Hour {
		return fmt.Sprintf("%.1f分钟", d.Minutes())
	}
	if d < 24*time.Hour {
		return fmt.Sprintf("%.1f小时", d.Hours())
	}
	return fmt.Sprintf("%.1f天", d.Hours()/24)
}

// GetTimestamp 获取当前时间戳（毫秒）
func GetTimestamp() int64 {
	return time.Now().UnixMilli()
}

// GetTimestampSecond 获取当前时间戳（秒）
func GetTimestampSecond() int64 {
	return time.Now().Unix()
}
