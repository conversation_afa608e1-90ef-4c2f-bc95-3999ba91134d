package util

import (
	"cosy/log"
	"errors"
	"fmt"
	wmiSupport "github.com/StackExchange/wmi"
	"golang.org/x/sys/windows"
	"golang.org/x/sys/windows/registry"
	"net"
	"os"
	"strings"
)

type Win32_BIOS struct {
	SerialNumber string
}
type Win32_Processor struct {
	ProcessorId string
}

type Win32_BaseBoard struct {
	SerialNumber string
}

// GetDeviceId windows 设置中的设备ID
func GetMachineSerialNumberV2() string {
	k, err := registry.OpenKey(registry.LOCAL_MACHINE, `SOFTWARE\Microsoft\SQMClient`,
		registry.QUERY_VALUE)
	if err != nil {
		log.Debug("Error opening registry key: %v\n", err)
		return ""
	}
	defer k.Close()

	deviceID, _, err := k.GetStringValue("MachineId")
	if err != nil {
		log.Debug("Error reading MachineId: %v\n", err)
		return ""
	}
	if deviceID == "" {
		return deviceID
	}
	deviceID = strings.ReplaceAll(deviceID, "{", "")
	deviceID = strings.ReplaceAll(deviceID, "}", "")
	return deviceID
}

// TODO 获取主板序列号，但总是返回 没有可用实例
func GetBaseBoardSerialNumberV2() string {
	var board []Win32_BaseBoard
	q := wmiSupport.CreateQuery(&board, "")
	err := wmiSupport.Query(q, &board)
	if err != nil {
		log.Errorf("Error getting base board: %v\n", err)
		return ""
	}
	log.Debug("GetBaseBoardSerialNumber board len is: ", len(board))
	if len(board) > 0 {
		boardId := board[0].SerialNumber
		log.Debug("BaseBoard Serial Number: ", boardId)
		return boardId
	}
	return ""
}

// GetHardwareID windows 系统UUID，通常是由 BIOS 或 UEFI 固件生成的，用于识别计算机的硬件配置
func GetHardwareIDV2() (string, error) {
	var dst []Win32_BIOS
	q := wmiSupport.CreateQuery(&dst, "")
	err := wmiSupport.Query(q, &dst)
	if err != nil {
		log.Errorf("Failed to get hardware id: , error: %v", err)
		return "", err
	}
	if len(dst) > 0 {
		return dst[0].SerialNumber, nil
	}
	return "", fmt.Errorf("Hardware UUID not found")
}

// GetDiskSerialNumber 获取执行文件所在磁盘分区的序列号
func GetDiskSerialNumberV2() (string, error) {
	exePath, err := os.Executable()
	if err != nil {
		log.Errorf("Error get executable file: %v\n", err)
		return "", err
	}
	file, err := windows.Open(exePath, windows.O_RDONLY, 0)
	if err != nil {
		log.Errorf("Error open executable file: %v\n", err)
		return "", err
	}
	defer windows.CloseHandle(file)
	var info windows.ByHandleFileInformation
	err = windows.GetFileInformationByHandle(file, &info)
	if err != nil {
		log.Errorf("Error get file info: %v\n", err)
		return "", err
	}
	// 以16进制数输出
	return fmt.Sprintf("%08x", info.VolumeSerialNumber), nil
}

// 处理器ID
func GetProcessorIdV2() (string, error) {
	var dst []Win32_Processor
	q := wmiSupport.CreateQuery(&dst, "")
	err := wmiSupport.Query(q, &dst)
	if err != nil {
		log.Errorf("Error get processor id: %v\n", err)
		return "", err
	}
	if len(dst) > 0 {
		return dst[0].ProcessorId, nil
	}
	return "", errors.New("no valid processor id")

}

func GetMacAddressV2() (string, error) {
	netInterfaces, err := net.Interfaces()
	if err != nil {
		panic(err.Error())
	}
	for i := 0; i < len(netInterfaces); i++ {
		if (netInterfaces[i].Flags&net.FlagLoopback) == 0 &&
			strings.Contains(netInterfaces[i].Flags.String(), "broadcast") {
			if isEthernet(netInterfaces[i].Index) {
				return netInterfaces[i].HardwareAddr.String(), nil
			}
		}
	}
	return "", errors.New("no valid mac address")
}
