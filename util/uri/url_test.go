package uri

import (
	"cosy/definition"
	"fmt"
	"runtime"
	"testing"
)

func TestURIToPath_Windows(t *testing.T) {
	if runtime.GOOS == "windows" {
		if URIToPath("file:///c:/home/<USER>/test.txt") != "c:\\home\\user\\test.txt" {
			t.<PERSON>rror("URIToPath failed", URIToPath("file:///c:/home/<USER>/test.txt"))
		}
		if URIToPath("c:/home/<USER>/test.txt") != "c:\\home\\user\\test.txt" {
			t.Error("URIToPath failed", URIToPath("c:/home/<USER>/test.txt"))
		}
	}
	if URIToPath("c:\\home\\user\\test.txt") != "c:\\home\\user\\test.txt" {
		t.Error("URIToPath failed")
	}
}

func TestURIToPath_OSX_Linux(t *testing.T) {
	if URIToPath("file:///home/<USER>/test.txt") != "/home/<USER>/test.txt" {
		t.Error("URIToPath failed")
	}
	if URIToPath("/home/<USER>/test.txt") != "/home/<USER>/test.txt" {
		t.Error("URIToPath failed")
	}
}

func TestBuildUrlWithParams(t *testing.T) {
	var params = map[string]string{
		"sessionId": "123",
		"requestId": "456",
	}
	newUri := BuildUrlWithParams(definition.UrlPathDeleteChatByRequestId, params)
	fmt.Println(newUri)
}
