package util

import "testing"

func TestBuildUrlQuery(t *testing.T) {
	tests := []struct {
		name     string
		url      string
		query    map[string]any
		expected string
	}{
		{
			name:     "空查询参数",
			url:      "https://example.com",
			query:    map[string]any{},
			expected: "https://example.com",
		},
		{
			name:     "字符串参数",
			url:      "https://example.com",
			query:    map[string]any{"name": "test", "action": "query"},
			expected: "https://example.com?name=test&action=query",
		},
		{
			name:     "整数参数",
			url:      "https://example.com",
			query:    map[string]any{"page": 1, "size": 10, "count": int64(100)},
			expected: "https://example.com?page=1&size=10&count=100",
		},
		{
			name:     "布尔值参数",
			url:      "https://example.com",
			query:    map[string]any{"active": true, "deleted": false},
			expected: "https://example.com?active=true&deleted=false",
		},
		{
			name:     "混合类型参数",
			url:      "https://example.com",
			query:    map[string]any{"name": "test", "page": 1, "active": true, "ratio": 3.14},
			expected: "https://example.com?name=test&page=1&active=true&ratio=3.14",
		},
		{
			name:     "URL已有查询参数",
			url:      "https://example.com?existing=param",
			query:    map[string]any{"new": "value", "count": 5},
			expected: "https://example.com?existing=param&new=value&count=5",
		},
		{
			name:     "需要URL编码的值",
			url:      "https://example.com",
			query:    map[string]any{"search": "hello world", "special": "a+b=c&d"},
			expected: "https://example.com?search=hello+world&special=a%2Bb%3Dc%26d",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := BuildUrlQuery(tt.url, tt.query)

			// 由于map遍历顺序不确定，我们需要验证结果包含所有预期的参数
			if len(tt.query) == 0 {
				if result != tt.expected {
					t.Errorf("BuildUrlQuery() = %v, want %v", result, tt.expected)
				}
				return
			}

			// 检查URL基础部分
			if tt.query != nil && len(tt.query) > 0 {
				// 验证结果是否以正确的基础URL开始
				baseURL := tt.url
				if result[0:len(baseURL)] != baseURL {
					t.Errorf("URL基础部分不匹配: got %v, want to start with %v", result, baseURL)
				}

				// 验证是否包含正确的分隔符
				if tt.url[len(tt.url)-1:] == "?" || containsQuery(tt.url) {
					// 应该包含 &
					if !containsAmpersand(result) && len(tt.query) > 0 {
						t.Errorf("预期结果应包含 & 分隔符: %v", result)
					}
				} else {
					// 应该包含 ?
					if !containsQuestion(result) && len(tt.query) > 0 {
						t.Errorf("预期结果应包含 ? 分隔符: %v", result)
					}
				}
			}
		})
	}
}

// 测试特定类型转换
func TestBuildUrlQueryTypeConversion(t *testing.T) {
	t.Run("整数类型转换", func(t *testing.T) {
		result := BuildUrlQuery("https://example.com", map[string]any{
			"int":    123,
			"int8":   int8(8),
			"int16":  int16(16),
			"int32":  int32(32),
			"int64":  int64(64),
			"uint":   uint(100),
			"uint8":  uint8(88),
			"uint16": uint16(1616),
			"uint32": uint32(3232),
			"uint64": uint64(6464),
		})

		// 验证所有整数都被正确转换
		expectedParts := []string{"int=123", "int8=8", "int16=16", "int32=32", "int64=64",
			"uint=100", "uint8=88", "uint16=1616", "uint32=3232", "uint64=6464"}

		for _, part := range expectedParts {
			if !contains(result, part) {
				t.Errorf("结果中缺少预期的参数: %s, 实际结果: %s", part, result)
			}
		}
	})

	t.Run("布尔值类型转换", func(t *testing.T) {
		result := BuildUrlQuery("https://example.com", map[string]any{
			"true_val":  true,
			"false_val": false,
		})

		if !contains(result, "true_val=true") {
			t.Errorf("布尔值true转换错误: %s", result)
		}
		if !contains(result, "false_val=false") {
			t.Errorf("布尔值false转换错误: %s", result)
		}
	})

	t.Run("浮点数类型转换", func(t *testing.T) {
		result := BuildUrlQuery("https://example.com", map[string]any{
			"float32": float32(3.14),
			"float64": float64(2.718),
		})

		if !contains(result, "float32=3.14") {
			t.Errorf("float32转换错误: %s", result)
		}
		if !contains(result, "float64=2.718") {
			t.Errorf("float64转换错误: %s", result)
		}
	})
}

// 辅助函数
func contains(s, substr string) bool {
	return len(s) >= len(substr) &&
		(s == substr ||
			(len(s) > len(substr) &&
				(s[:len(substr)] == substr ||
					s[len(s)-len(substr):] == substr ||
					containsSubstring(s, substr))))
}

func containsSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

func containsQuery(url string) bool {
	return containsSubstring(url, "?")
}

func containsAmpersand(url string) bool {
	return containsSubstring(url, "&")
}

func containsQuestion(url string) bool {
	return containsSubstring(url, "?")
}
