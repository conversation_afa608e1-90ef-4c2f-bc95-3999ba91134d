package util

import (
	"cosy/definition"
	"cosy/util/ignore"
	"os"
	"path/filepath"
	"runtime"
	"strings"
)

var (
	IgnoreExtensions = []string{".png", ".jpg", ".jpeg", ".gif", ".bmp", ".ico", ".svg", ".webp", ".mp4", ".mp3", ".wav",
		".mpg", ".mpeg", ".avi", ".mov", ".flv", ".mkv", ".web", ".pyi", ".so", ".dll", ".dylib", ".jar", ".zip", "tar.gz",
		//".md", md文件对模型理解有帮助
		".csv", ".conf.js", ".svn", ".pb.go",
	}
	KeepFiles   = []string{"readme.md", "readme", "readme.txt", ".umirc.ts", ".npmrc"}
	IgnoreFiles = []string{"LICENSE", "VERSION", "CHANGES.md", "CODEOWNERS", "MANIFEST.in", "NOTICE", "MANIFEST.MF", "settings.json", "workspace.json", "package-info.java"}
	IgnoreDirs  = []string{
		string(os.PathSeparator) + "node_modules" + string(os.PathSeparator),
		string(os.PathSeparator) + ".svn" + string(os.PathSeparator),
		string(os.PathSeparator) + ".git" + string(os.PathSeparator),
		string(os.PathSeparator) + ".idea" + string(os.PathSeparator),
		string(os.PathSeparator) + ".vscode" + string(os.PathSeparator),
		string(os.PathSeparator) + ".vscode-test" + string(os.PathSeparator),
		string(os.PathSeparator) + ".vscode-oss-dev" + string(os.PathSeparator),
		string(os.PathSeparator) + ".vscode-oss-test" + string(os.PathSeparator),
		string(os.PathSeparator) + ".vscode-oss-dev-test" + string(os.PathSeparator),
		string(os.PathSeparator) + "python_stub" + string(os.PathSeparator),
		string(os.PathSeparator) + "site-packages" + string(os.PathSeparator),
		string(os.PathSeparator) + "dist" + string(os.PathSeparator),
		string(os.PathSeparator) + "venv" + string(os.PathSeparator),
		string(os.PathSeparator) + "gradle" + string(os.PathSeparator),
		string(os.PathSeparator) + "__pypackages__" + string(os.PathSeparator),
		string(os.PathSeparator) + "target" + string(os.PathSeparator) + "classes" + string(os.PathSeparator),
		string(os.PathSeparator) + "classes" + string(os.PathSeparator),
		string(os.PathSeparator) + "vendor" + string(os.PathSeparator),
		string(os.PathSeparator) + "mod" + string(os.PathSeparator),
		string(os.PathSeparator) + "x" + string(os.PathSeparator),
		string(os.PathSeparator) + "github.com" + string(os.PathSeparator),
		string(os.PathSeparator) + "gitlab.com" + string(os.PathSeparator),
		string(os.PathSeparator) + "gopkg" + string(os.PathSeparator),
		string(os.PathSeparator) + ".org" + string(os.PathSeparator),
		string(os.PathSeparator) + ".com" + string(os.PathSeparator),
		string(os.PathSeparator) + ".in" + string(os.PathSeparator),
		string(os.PathSeparator) + "testData" + string(os.PathSeparator),
	}
	RootIgnoreDirs = []string{
		string(os.PathSeparator) + "build" + string(os.PathSeparator),
		string(os.PathSeparator) + "out" + string(os.PathSeparator),
		string(os.PathSeparator) + "docs" + string(os.PathSeparator),
	}
)

// GitRuleIgnore wraps a list of ignore pattern.
type GitRuleIgnore struct {
	Matcher       ignore.Matcher
	WorkspacePath string
}

func NewGitRuleIgnore(workspacePath string, ignoreFilePath string) (*GitRuleIgnore, error) {
	matcher, err := ignore.ParseIgnoreFile(workspacePath, ignoreFilePath)
	if err != nil || matcher == nil {
		return nil, err
	}

	return &GitRuleIgnore{
		Matcher:       matcher,
		WorkspacePath: workspacePath,
	}, nil
}

func (g *GitRuleIgnore) Clone() *GitRuleIgnore {
	return &GitRuleIgnore{
		Matcher:       g.Matcher.Clone(),
		WorkspacePath: g.WorkspacePath,
	}
}

// IsGlobalIgnoreDir checks whether the given file path is in the ignore list
// 必须确保传进来的filePath参数必须是目录
func IsGlobalIgnoreDir(workspacePath string, filePath string) bool {
	if workspacePath == filePath {
		return false
	}
	fileName := GetFileName(filePath)
	relateFilePath := strings.TrimPrefix(filePath, workspacePath)
	if strings.HasPrefix(fileName, ".") || strings.HasPrefix(fileName, "__") {
		return true
	}
	if relateFilePath == "" {
		return false
	}

	// 确保目录分割符包裹相对目录
	if !strings.HasPrefix(relateFilePath, string(os.PathSeparator)) {
		relateFilePath = string(os.PathSeparator) + relateFilePath
	}
	if !strings.HasSuffix(relateFilePath, string(os.PathSeparator)) {
		relateFilePath = relateFilePath + string(os.PathSeparator)
	}

	for _, dir := range RootIgnoreDirs {
		if strings.HasPrefix(relateFilePath, dir) {
			return true
		}
	}

	// 需要确保不存在部分匹配，必须全部匹配
	for _, dir := range IgnoreDirs {
		if strings.Contains(relateFilePath, dir) {
			return true
		}
	}
	return false
}

// IsGlobalIgnoreFile returns true if given file path is in the ignore list
func IsGlobalIgnoreFile(filePath string) bool {
	fileName := GetFileName(filePath)
	// 如果匹配保留的文件，则不走后面的过滤逻辑
	for _, keepFile := range KeepFiles {
		if strings.ToLower(keepFile) == strings.ToLower(fileName) {
			return false
		}
	}
	// 过滤无效的文件名
	if strings.HasPrefix(fileName, ".") || strings.HasPrefix(fileName, "__") {
		if fileName != "__init__.py" {
			return true
		}
	}
	// 过滤忽略的扩展名
	for _, ext := range IgnoreExtensions {
		if strings.HasSuffix(filePath, ext) {
			return true
		}
	}
	lang := GetLanguageExtByFilePath(filePath, true)
	if lang == definition.PlainText || lang == definition.Others {
		// 忽略非代码文件
		return true
	}
	// 忽略指定的文件名
	for _, ignoreName := range IgnoreFiles {
		if ignoreName == fileName {
			return true
		}
	}
	if strings.Contains(filePath, "node_modules") {
		if !strings.HasSuffix(filePath, ".d.ts") {
			return true
		} else if strings.Count(filePath, "node_modules") > 1 {
			return true
		}
	}
	if IsBinaryFile(filePath) {
		return true
	}
	return false
}

// IsIllegalExistingWorkspace 检查现有路径是否为非法工作区
// 除了进行基础的路径校验外，还会检查路径是否存在以及是否为目录
func IsIllegalExistingWorkspace(workspacePath string) bool {
	// 首先进行基础的路径校验
	if IsIllegalWorkspace(workspacePath) {
		return true
	}

	// 路径存在性和目录性检查
	if stat, err := os.Stat(workspacePath); err != nil {
		// 错误目录不合法（路径不存在或无法访问）
		return true
	} else {
		// 非目录不合法
		if !stat.IsDir() {
			return true
		}
	}

	return false
}

func IsIllegalWorkspace(workspacePath string) bool {
	// 基础校验
	if workspacePath == "" {
		return true
	}

	// 去除首尾空格
	trimmedPath := strings.TrimSpace(workspacePath)
	if trimmedPath == "" {
		return true
	}

	// 单点和双点目录是非法的
	if trimmedPath == "." || trimmedPath == ".." {
		return true
	}

	// 只包含点和路径分隔符的路径是非法的
	if trimmedPath == "/" || trimmedPath == "\\" ||
		trimmedPath == ".\\" || trimmedPath == "./" ||
		trimmedPath == "..\\" || trimmedPath == "../" {
		return true
	}

	// 清理路径
	cleanPath := filepath.Clean(workspacePath)

	// 清理后如果变成了单点，也是非法的
	if cleanPath == "." || cleanPath == ".." {
		return true
	}

	// 获取用户目录
	userHome, err := os.UserHomeDir()
	if err != nil {
		// 如果无法获取用户目录，则采用保守策略
		return true
	}

	// 清理用户目录路径
	cleanUserHome := filepath.Clean(userHome)

	// 转换为小写以实现不区分大小写比较
	cleanPath = strings.ToLower(cleanPath)
	cleanUserHome = strings.ToLower(cleanUserHome)

	// 用户目录本身是非法的（精确匹配）
	if cleanPath == cleanUserHome {
		return true
	}

	// 用户目录下的子目录是合法的
	if strings.HasPrefix(cleanPath, cleanUserHome+string(os.PathSeparator)) {
		return false
	}

	// 根据操作系统检查系统核心目录
	switch runtime.GOOS {
	case "windows":
		return isWindowsIllegalPath(cleanPath)
	case "linux":
		return isLinuxIllegalPath(cleanPath)
	case "darwin":
		return isDarwinIllegalPath(cleanPath)
	default:
		// 未知系统，采用保守策略
		return false
	}
}

// Windows系统非法路径检查
func isWindowsIllegalPath(cleanPath string) bool {
	// 用户相关目录和桌面目录（完全匹配） - 优先检查
	illegalExactPaths := []string{
		"c:",
		"c:\\",
		"c:\\users",
		"c:\\users\\<USER>\\users\\public",
		"c:\\users\\<USER>\Desktop，检查是否以 \desktop 结尾且不包含子目录
	if strings.HasSuffix(cleanPath, "\\desktop") && !strings.Contains(cleanPath, "\\desktop\\") {
		return true
	}

	// Windows系统核心目录（前缀匹配）
	illegalPrefixes := []string{
		"c:\\windows\\",
		// "c:\\program files\\",
		// "c:\\program files (x86)\\",
		"c:\\program data\\",
		"c:\\system volume information\\",
		"c:\\recovery\\",
		"c:\\$recycle.bin\\",
		"c:\\config.msi\\",
		"c:\\msocache\\",
		"c:\\perflogs\\",
		"c:\\boot\\",
		"c:\\efi\\",
		"c:\\temp\\",
		"c:\\inetpub\\",
		"c:\\drivers\\",
		"c:\\$windows.~bt\\",
		"c:\\$windows.~ws\\",
		"c:\\$getupdate\\",
		"c:\\$winperecovery\\",
	}

	// 检查是否以非法前缀开头
	for _, prefix := range illegalPrefixes {
		if strings.HasPrefix(cleanPath, prefix) {
			return true
		}
	}

	return false
}

// Linux系统非法路径检查
func isLinuxIllegalPath(cleanPath string) bool {
	// 用户相关目录和桌面目录（完全匹配） - 优先检查
	illegalExactPaths := []string{
		"/root",
	}

	for _, exactPath := range illegalExactPaths {
		if cleanPath == exactPath {
			return true
		}
	}

	// Linux桌面目录检查（完全匹配）
	// Linux桌面路径可能是 Desktop、桌面等，兼容不同语言环境
	// 注意：这些模式都已转换为小写，因为 cleanPath 已经是小写的
	desktopPatterns := []string{
		"/desktop",          // 英文
		"/桌面",               // 中文
		"/рабочий стол",     // 俄文 (小写)
		"/bureau",           // 法文 (小写)
		"/arbeitsfläche",    // 德文 (小写)
		"/escritorio",       // 西班牙文 (小写)
		"/área de trabalho", // 葡萄牙文 (小写)
		"/scrivania",        // 意大利文 (小写)
	}

	for _, pattern := range desktopPatterns {
		if strings.HasSuffix(cleanPath, pattern) && !strings.Contains(cleanPath, pattern+"/") {
			return true
		}
	}

	// Linux系统核心目录（前缀匹配）
	illegalPrefixes := []string{
		"/bin/",
		"/sbin/",
		"/usr/",
		"/etc/",
		"/var/",
		"/tmp/",
		"/sys/",
		"/proc/",
		"/dev/",
		"/run/",
		"/boot/",
		"/lib/",
		"/lib64/",
		"/opt/",
		"/lost+found/",
		"/media/",
		"/srv/",
	}

	// 检查是否以非法前缀开头
	for _, prefix := range illegalPrefixes {
		if strings.HasPrefix(cleanPath, prefix) {
			return true
		}
	}

	return false
}

// macOS系统非法路径检查
func isDarwinIllegalPath(cleanPath string) bool {
	// 用户相关目录和桌面目录（完全匹配） - 优先检查
	illegalExactPaths := []string{
		"/users/shared",
		"/users/guest",
		"/users/.localized",
		"/users/deleted users",
	}

	for _, exactPath := range illegalExactPaths {
		if cleanPath == exactPath {
			return true
		}
	}

	// macOS桌面目录检查（完全匹配）
	// macOS桌面路径固定为 /Users/<USER>/Desktop
	if (strings.HasSuffix(cleanPath, "/desktop") && !strings.Contains(cleanPath, "/desktop/")) ||
		(strings.HasSuffix(cleanPath, "/Desktop") && !strings.Contains(cleanPath, "/Desktop/")) {
		return true
	}

	// macOS系统核心目录（前缀匹配）
	illegalPrefixes := []string{
		"/system/",
		"/usr/",
		"/bin/",
		"/sbin/",
		"/etc/",
		"/var/",
		"/tmp/",
		"/private/",
		"/library/",
		"/volumes/",
		"/cores/",
		"/dev/",
		"/opt/",
		"/network/",
		"/.vol/",
		"/net/",
		"/home/",
		"/automount/",
		"/installer logs/",
		"/previous systems/",
		"/applications/",
		"/sw/",
		"/x11/",
		"/darwin/",
		"/extra/",
		"/update/",
		"/rescue/",
		"/installer/",
		"/recovery/",
		"/preboot/",
		"/vm/",
		"/procfs/",
		"/temp/",
		"/.trashes/",
		"/.fseventsd/",
		"/.spotlight-v100/",
		"/.documentrevisions-v100/",
		"/.temporaryitems/",
		"/lost+found/",
		"/proc/",
		"/sys/",
		"/run/",
		"/srv/",
		"/media/",
		"/caches/",
		"/logs/",
	}

	// 检查是否以非法前缀开头
	for _, prefix := range illegalPrefixes {
		if strings.HasPrefix(cleanPath, prefix) {
			return true
		}
	}

	return false
}
