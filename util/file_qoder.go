//go:build qoder

package util

import (
	"os"
	"path/filepath"
	"strings"

	"cosy/global"
)

// GetCosyHomePath returns path for cosy binary and model
// 最新版返回cosy数据存储目录, since v2.1.4
// By default, it should be $home/.lingma
// If the dir doesn't exist, create it
func GetCosyHomePath() string {
	cosyHome := os.Getenv("QODER_HOME")
	if cosyHome != "" {
		return cosyHome
	}
	if global.WorkDir != "" {
		return global.WorkDir
	}

	if execPath, err := os.Executable(); err == nil {
		if resolvedPath, err := filepath.EvalSymlinks(execPath); err == nil {
			path := filepath.ToSlash(resolvedPath)
			if index := strings.LastIndex(path, "/bin/"); index > 0 {
				cosyHome = filepath.FromSlash(path[:index])
				return cosyHome
			}
		}
	}

	return GetCosyHomePathByDefault()
}

// GetCosyProcessPath 获取当前进程执行的目录
func GetCosyProcessPath() string {
	cosyProcessHome := os.Getenv("QODER_PROCESS_HOME")
	if cosyProcessHome != "" {
		return cosyProcessHome
	}
	cosyHome := os.Getenv("QODER_HOME")
	if execPath, err := os.Executable(); err == nil {
		if resolvedPath, err := filepath.EvalSymlinks(execPath); err == nil {
			path := filepath.ToSlash(resolvedPath)
			if index := strings.LastIndex(path, "/bin/"); index > 0 {
				cosyHome = filepath.FromSlash(path[:index])
				return cosyHome
			}
		}
	}

	return GetCosyHomePathByDefault()
}

// GetCosyCachePath return cache path
// 最新版返回cosy数据存储目录，since v2.1.4
// Will try user's custom path if set
// Otherwise return system cache path
func GetCosyCachePath() string {
	if global.WorkDir != "" {
		return global.WorkDir
	}
	cosyHome := GetCosyHomePath()
	systemCache := GetSystemCachePath()
	if cosyHome == systemCache {
		// 如果用户自定义的目录和系统缓存目录相同，则使用系统缓存目录
		// 设置了自定义存储目录
		return systemCache
	}
	userHome, err := os.UserHomeDir()
	if err != nil {
		return systemCache
	}
	if cosyHome != filepath.Join(userHome, global.LingmaDir) {
		return filepath.Join(cosyHome, "cache")
	}

	return systemCache
}

// GetCosyHomePathByDefault 默认cosy工作目录
func GetCosyHomePathByDefault() string {
	home, err := os.UserHomeDir()
	if err != nil {
		// 兜底到Cache目录
		home = GetSystemCachePath()
	}
	if global.IsBuildForPlugin() {
		return filepath.Join(home, global.LingmaClientDir)
	}
	return filepath.Join(home, global.LingmaDir)
}

func GetSystemCachePath() string {
	cacheDir, err := os.UserCacheDir()
	if err != nil {
		cacheDir = os.TempDir()
	}
	if global.IsBuildForPlugin() {
		return filepath.Join(cacheDir, global.LingmaClientDir)
	}
	return filepath.Join(cacheDir, global.LingmaDir)
}
