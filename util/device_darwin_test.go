package util

import (
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
)

func Test_addSystemMark(t *testing.T) {
	assert.Equal(t, "1234567-890m123-4567890", addSystemMark("12345678901234567890"))
}

func Test_getMachineSerialNumber(t *testing.T) {
	assert.Equal(t, "XMYW91H7N5", GetMachineSerialNumberV2())
}

func Test_getMacAddress(t *testing.T) {
	macAddress, _ := GetMacAddressV2()
	assert.Equal(t, "c889f3bd79d2", macAddress)
}

func Test_getMotherBoardIdForDarwin(t *testing.T) {
	motherBoardID, _ := GetHardwareIDV2()
	assert.Equal(t, "3C012D4D-C801-5815-AA3F-C85CDCC2E5AA", motherBoardID)
}

func Test_getDiskSerialNumberForDarwin(t *testing.T) {
	diskSerialNum, _ := GetDiskSerialNumberV2()
	assert.Equal(t, "5A31D157-9B42-3423-8E6F-C776CF0F980F", diskSerialNum)
}

func Test_getBoardId(t *testing.T) {
	boardId, _ := GetBoardIDV2()
	assert.Equal(t, "5A31D157-9B42-3423-8E6F-C776CF0F980F", boardId)
	deviceID := "{48E8F9BE-6E32-4BB3-A4CE-E8B658E5AF20}"
	deviceID = strings.ReplaceAll(deviceID, "{", "")
	assert.Equal(t, "48E8F9BE-6E32-4BB3-A4CE-E8B658E5AF20", deviceID)
}
