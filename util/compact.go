package util

import (
	agentDefinition "code.alibaba-inc.com/cosy/lingma-agent-graph/definition"
	"context"
	toolCommon "cosy/chat/agents/tool/common"
	"cosy/chat/chains/common"
	"cosy/definition"
	"cosy/log"
	"encoding/json"
	"fmt"
	"regexp"
	"strings"
)

// == 工具函数组1：用于去除内容 ==

// RemoveLastMessageExtra 移除最后一个消息的extra信息
func RemoveLastMessageExtra(messages []*agentDefinition.Message) {
	orgLastMessage := messages[len(messages)-1]
	if orgLastMessage.Role == agentDefinition.RoleTypeTool {
		//情况一下tool message的extra信息
		orgLastMessage.Extra = nil
	}
}

// ProcessOneTurnAssemblyMessages 处理单轮对话消息集合，清理用户消息中的上下文内容
func ProcessOneTurnAssemblyMessages(messages []*agentDefinition.Message) []*agentDefinition.Message {
	for _, message := range messages {
		if message.Role == agentDefinition.RoleTypeUser {
			//去除文件内容 和 codebase、teamDocs引入的内容
			message.Content = RemoveContextContent(message.Content)
			continue
		}
	}
	return messages
}

// RemoveContextContent 去除文件内容
func RemoveContextContent(content string) string {
	content = RemoveFolderFileContent(content)
	content = RemoveFileContent(content)
	content = RemoveSelectedCodeContent(content)
	content = RemoveCodeBaseContent(content)
	content = RemoveTeamDocsContent(content)
	content = RemoveGitCommitContent(content)
	content = RemoveCodeChangeContent(content)
	content = RemoveRuleContent(content)
	content = RemoveUserMemories(content)
	content = RemoveProjectInstructions(content)
	return content
}

// RemoveFolderFileContent 去除文件夹文件内容
func RemoveFolderFileContent(content string) string {
	projectPattern := `<file_content[\s\S]*?</file_content>`
	// 编译正则表达式
	re := regexp.MustCompile(projectPattern)
	// 替换匹配的文本为空字符串
	result := re.ReplaceAllString(content, "")
	result = strings.TrimSpace(result)
	return result
}

// RemoveFileContent 去除文件内容
func RemoveFileContent(content string) string {
	projectPattern := `<file>[\s\S]*?</file>`
	// 编译正则表达式
	re := regexp.MustCompile(projectPattern)
	// 替换匹配的文本为空字符串
	result := re.ReplaceAllString(content, "")
	result = strings.TrimSpace(result)
	return result
}

// RemoveSelectedCodeContent 去除圈选代码内容
func RemoveSelectedCodeContent(content string) string {
	projectPattern := `<selected_codes>[\s\S]*?</selected_codes>`
	// 编译正则表达式
	re := regexp.MustCompile(projectPattern)
	matches := re.FindStringSubmatch(content)
	if len(matches) > 0 {
		selectedCodeContent := matches[0]
		//圈选的代码少于10K则不做处理
		if len(selectedCodeContent) < 10_000 {
			return content
		}
	}
	// 替换匹配的文本为空字符串
	result := re.ReplaceAllString(content, "")
	result = strings.TrimSpace(result)
	return result
}

// RemoveCodeBaseContent 去除代码库内容
func RemoveCodeBaseContent(content string) string {
	projectPattern := `<source_code_excerpts>[\s\S]*?</source_code_excerpts>`
	// 编译正则表达式
	re := regexp.MustCompile(projectPattern)
	// 替换匹配的文本为空字符串
	result := re.ReplaceAllString(content, "")
	result = strings.TrimSpace(result)
	return result
}

// RemoveTeamDocsContent 去除团队文档内容
func RemoveTeamDocsContent(content string) string {
	projectPattern := `<teamDocs>[\s\S]*?</teamDocs>`
	// 编译正则表达式
	re := regexp.MustCompile(projectPattern)
	// 替换匹配的文本为空字符串
	result := re.ReplaceAllString(content, "")
	result = strings.TrimSpace(result)
	return result
}

// RemoveGitCommitContent 去除git提交内容
func RemoveGitCommitContent(content string) string {
	projectPattern := `<git_commits>[\s\S]*?</git_commits>`
	// 编译正则表达式
	re := regexp.MustCompile(projectPattern)
	// 替换匹配的文本为空字符串
	result := re.ReplaceAllString(content, "")
	result = strings.TrimSpace(result)
	return result
}

// RemoveCodeChangeContent 去除代码变更内容
func RemoveCodeChangeContent(content string) string {
	projectPattern := `<code_change>[\s\S]*?</code_change>`
	// 编译正则表达式
	re := regexp.MustCompile(projectPattern)
	// 替换匹配的文本为空字符串
	result := re.ReplaceAllString(content, "")
	result = strings.TrimSpace(result)
	return result
}

// RemoveRuleContent 去除规则内容
func RemoveRuleContent(content string) string {
	projectPattern := `<rule_content>[\s\S]*?</rule_content>`
	// 编译正则表达式
	re := regexp.MustCompile(projectPattern)
	// 替换匹配的文本为空字符串
	result := re.ReplaceAllString(content, "")
	result = strings.TrimSpace(result)
	return result
}

// RemoveUserMemories 去除用户记忆
func RemoveUserMemories(content string) string {
	// 创建正则表达式模式
	pattern := `<user_memories>[\s\S]*?</user_memories>`
	// 编译正则表达式
	re := regexp.MustCompile(pattern)
	// 替换匹配的文本为空字符串
	result := re.ReplaceAllString(content, "")
	result = strings.TrimSpace(result)
	return result
}

// RemoveProjectInstructions 去除项目指令
func RemoveProjectInstructions(content string) string {
	projectPattern := `<project_instructions>[\s\S]*?</project_instructions>`
	// 编译正则表达式
	re := regexp.MustCompile(projectPattern)
	// 替换匹配的文本为空字符串
	result := re.ReplaceAllString(content, "")
	result = strings.TrimSpace(result)
	return result
}

func containsProjectRule(finalMessages []*agentDefinition.Message) bool {
	for i := len(finalMessages) - 1; i >= 0; i-- {
		message := finalMessages[i]
		if message.Role == agentDefinition.RoleTypeUser {
			if strings.Contains(message.Content, "<user_mandatory_instructions>") {
				return true
			}
		}
	}
	return false
}

// == 工具函数组2：提取工具参数 ==

// GetToolFilePathArg 从工具调用参数中提取文件路径
// 适用于 edit_file、create_file、read_file 工具调用参数中提取文件路径
func GetToolFilePathArg(arguments string) string {
	var args struct {
		FilePath string `json:"file_path"`
	}
	if err := json.Unmarshal([]byte(arguments), &args); err != nil {
		log.Error(fmt.Sprintf("getToolFilePathArg err, arguments: %s, error: %v", arguments, err))
		return ""
	}
	return args.FilePath
}

// GetToolReadFileArg 获取tool read_file的参数
// 适用于 read_file 工具调用参数中提取文件路径
func GetToolReadFileArg(arguments string) string {
	var args struct {
		FilePath  string `json:"file_path"`
		StartLine int    `json:"start_line"`
		EndLine   int    `json:"end_line"`
	}
	if err := json.Unmarshal([]byte(arguments), &args); err != nil {
		log.Error(fmt.Sprintf("getToolReadFileArg err, arguments: %s, error: %v", arguments, err))
		return ""
	}
	str := fmt.Sprintf("%s:L%d-%d", args.FilePath, args.StartLine, args.EndLine)
	return str
}

// GetToolRelativeWorkspacePathArg 获取tool的relative_workspace_path参数
// 适用于包含 relative_workspace_path 参数的工具
func GetToolRelativeWorkspacePathArg(arguments string) string {
	var args struct {
		FilePath string `json:"relative_workspace_path"`
	}
	if err := json.Unmarshal([]byte(arguments), &args); err != nil {
		log.Error(fmt.Sprintf("getToolRelativeWorkspacePathArg err, arguments: %s, error: %v", arguments, err))
		return ""
	}
	return args.FilePath
}

// GetToolExplanation 获取tool的explanation参数
func GetToolExplanation(arguments string) string {
	var args struct {
		Explanation string `json:"explanation"`
	}
	if err := json.Unmarshal([]byte(arguments), &args); err != nil {
		log.Error(fmt.Sprintf("getToolExplanation err, arguments: %s, error: %v", arguments, err))
		return ""
	}
	if args.Explanation != "" {
		//llm偶现直接返回了explanation参数的原文prompt
		if strings.HasPrefix(args.Explanation, toolCommon.ExplanationDefaultDesc) {
			args.Explanation = ""
		}
	}
	return args.Explanation
}

// == 工具函数组3：生成新的消息内容 ==

// MakeEditFileListContent 生成编辑过的文件列表
func MakeEditFileListContent(editFiles []string) string {
	content := "\n你之前已经编辑过的文件列表:\n" + "<edited_file_list>\n"
	for _, editFile := range editFiles {
		content = content + editFile + "\n"
	}
	content = content + "</edited_file_list>\n"
	return content
}

// GetSummaryStr 获取总结字符串
func GetSummaryStr(ctx context.Context) string {
	preferredLanguage, ok := ctx.Value(common.KeyPreferredLanguage).(string)
	if !ok || preferredLanguage == "" {
		preferredLanguage = definition.LocaleZh
	}
	if preferredLanguage == definition.LocaleZh || preferredLanguage == definition.LocalZhCn {
		return "以下是对你之前回复的总结，不是用户新的提问：\n<assistant_response_summary>\n"
	}
	return "The following is a summary of your previous response, not a new user query:\n<assistant_response_summary>\n"
}

// == 工具函数组4：获取消息列表 ==

// GetLastThreeAssemblyUserMessage 获取最后三个用户消息
func GetLastThreeAssemblyUserMessage(messages []*agentDefinition.Message) []*agentDefinition.Message {
	userCount := 0
	for i := len(messages) - 1; i >= 0; i-- {
		if messages[i].Role == agentDefinition.RoleTypeUser {
			userCount++
			if userCount > 6 {
				return messages[i+1:]
			}
		}
	}
	return messages
}

// FindLastUserMessage 获取现有消息列表：最后一条user message
func FindLastUserMessage(messages []*agentDefinition.Message) *agentDefinition.Message {
	if messages == nil || len(messages) == 0 {
		return nil
	}
	for i := len(messages) - 1; i >= 0; i-- {
		message := messages[i]
		if message.Role != agentDefinition.RoleTypeUser {
			continue
		}
		return message
	}
	return nil
}

// SimpleMessage 简化的消息结构体
type SimpleMessage struct {
	Role         agentDefinition.RoleType           `json:"role"`
	Content      string             `json:"content"`
	MultiContent []agentDefinition.ChatMessagePart  `json:"contents,omitempty"`
	ToolCalls    []agentDefinition.ToolCall        `json:"tool_calls,omitempty"`
}

// SerializeMessages 将消息列表序列化为简化的JSON字符串
func SerializeMessages(messages []*agentDefinition.Message) (string, error) {
	// 创建简化消息切片
	simpleMessages := make([]SimpleMessage, len(messages))

	// 转换消息
	for i, msg := range messages {
		simpleMessages[i] = SimpleMessage{
			Role:         msg.Role,
			Content:      msg.Content,
			MultiContent: msg.MultiContent,
			ToolCalls:    msg.ToolCalls,
		}
	}

	// 序列化简化消息
	jsonBytes, err := json.Marshal(simpleMessages)
	if err != nil {
		return "", fmt.Errorf("error marshaling simple messages: %w", err)
	}

	return string(jsonBytes), nil
}