package util

import (
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"testing"

	// _ "github.com/asg017/sqlite-vec-go-bindings/cgo"
	_ "github.com/mattn/go-sqlite3"
	"github.com/stretchr/testify/assert"
)

func TestIsGlobalIgnoreDir(t *testing.T) {
	workspacePath := "/Users/<USER>/repo"
	ignoreDirs := []string{
		"/Users/<USER>/repo/.git",
		"/Users/<USER>/repo/.idea",
		"/Users/<USER>/repo/.vscode",
		"/Users/<USER>/repo/build",
		"/Users/<USER>/repo/out",
		"/Users/<USER>/repo/docs",
		"/Users/<USER>/repo/node_modules",
		"/Users/<USER>/repo/.svn",
		"/Users/<USER>/repo/x/",
		"/Users/<USER>/repo/x",
		"/Users/<USER>/repo/x/y",
		"/Users/<USER>/repo/gradle",
		"/Users/<USER>/repo/classes",
		"/Users/<USER>/repo/classes",
		"/Users/<USER>/repo/target/classes",
		"/Users/<USER>/repo/target/classes/",
		"/Users/<USER>/repo/.in",
		"/Users/<USER>/repo/testData",
	}
	for _, ignoreDir := range ignoreDirs {
		assert.True(t, IsGlobalIgnoreDir(workspacePath, ignoreDir))
	}
}

// 测试基础校验逻辑（公共部分）
func TestIsIllegalWorkspace_BasicValidation(t *testing.T) {
	tests := []struct {
		name     string
		path     string
		expected bool
		desc     string
	}{
		// 基础校验测试
		{"空字符串", "", true, "空字符串应该是非法的"},
		{"只有空格", "   ", true, "只有空格的字符串应该是非法的"},
		{"只有制表符", "\t\t", true, "只有制表符的字符串应该是非法的"},
		{"混合空白字符", " \t \n ", true, "只有空白字符的字符串应该是非法的"},

		// 特殊目录测试
		{"单点目录", ".", true, "单点目录应该是非法的"},
		{"双点目录", "..", true, "双点目录应该是非法的"},
		{"根目录斜杠", "/", true, "根目录应该是非法的"},
		{"根目录反斜杠", "\\", true, "Windows根目录应该是非法的"},

		// 点和路径分隔符组合
		{"点斜杠", "./", true, "点斜杠应该是非法的"},
		{"点反斜杠", ".\\", true, "点反斜杠应该是非法的"},
		{"双点斜杠", "../", true, "双点斜杠应该是非法的"},
		{"双点反斜杠", "..\\", true, "双点反斜杠应该是非法的"},

		// 路径规范化测试
		{"带前后空格的合法路径", " /users/john/project ", false, "带空格的合法路径应该被正确处理"},
		{"带制表符的合法路径", "\t/users/john/project\t", false, "带制表符的合法路径应该被正确处理"},
		{"多个连续斜杠", "/users//john///project", false, "多个连续斜杠应该被normalize"},

		// 合法的相对路径
		{"简单项目名", "myproject", false, "简单项目名应该是合法的"},
		{"相对路径", "projects/myapp", false, "相对路径应该是合法的"},
		{"复杂相对路径", "work/projects/web/frontend", false, "复杂相对路径应该是合法的"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsIllegalWorkspace(tt.path)
			if result != tt.expected {
				t.Errorf("IsIllegalWorkspace(%q) = %v, 期望 %v. %s",
					tt.path, result, tt.expected, tt.desc)
			}
		})
	}
}

// 测试在当前系统（macOS）上的集成功能
func TestIsIllegalWorkspace_MacOSIntegration(t *testing.T) {
	// 只有在macOS系统上运行时才执行这些测试
	if runtime.GOOS != "darwin" {
		t.Skip("这些测试只在macOS上运行")
	}

	tests := []struct {
		name     string
		path     string
		expected bool
		desc     string
	}{
		// macOS桌面目录测试
		{"macOS桌面", "/users/john/Desktop", true, "macOS桌面目录应该是非法的"},
		{"macOS桌面小写", "/users/john/desktop", true, "macOS桌面目录（小写）应该是非法的"},
		{"macOS桌面子目录", "/users/john/Desktop/myproject", false, "macOS桌面子目录应该是合法的"},

		// macOS系统目录测试
		{"System目录", "/System/Library/Frameworks", true, "System目录应该是非法的"},
		{"usr目录", "/usr/local/bin", true, "usr目录应该是非法的"},
		{"Applications目录", "/Applications/Safari.app", true, "Applications目录应该是非法的"},

		// macOS用户目录测试
		{"用户共享目录", "/Users/<USER>", true, "用户共享目录应该是非法的"},
		{"用户访客目录", "/Users/<USER>", true, "用户访客目录应该是非法的"},

		// 合法的macOS路径
		{"用户文档目录", "/users/john/Documents/myproject", false, "用户文档目录应该是合法的"},
		{"用户项目目录", "/users/john/projects/myapp", false, "用户项目目录应该是合法的"},
		{"用户下载目录", "/users/john/Downloads/project", false, "用户下载目录应该是合法的"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsIllegalWorkspace(tt.path)
			if result != tt.expected {
				t.Errorf("IsIllegalWorkspace(%q) = %v, 期望 %v. %s",
					tt.path, result, tt.expected, tt.desc)
			}
		})
	}
}

func TestIsIllegalWorkspace_OperatingSystemSpecific(t *testing.T) {
	// 根据当前操作系统进行特定测试
	switch runtime.GOOS {
	case "windows":
		testWindowsSpecific(t)
	case "linux":
		testLinuxSpecific(t)
	case "darwin":
		testDarwinSpecific(t)
	}
}

func testWindowsSpecific(t *testing.T) {
	tests := []struct {
		name     string
		path     string
		expected bool
		desc     string
	}{
		// Windows系统目录
		{"Windows目录", "c:\\windows\\system32", true, "Windows系统目录应该是非法的"},
		{"Program Data", "c:\\program data\\test", true, "Program Data目录应该是非法的"},
		{"用户默认目录", "c:\\users\\<USER>\\users\\public", true, "用户公共目录应该是非法的"},

		// Windows桌面目录测试
		{"Windows桌面", "c:\\users\\<USER>\\desktop", true, "Windows桌面目录应该是非法的"},
		{"Windows桌面子目录", "c:\\users\\<USER>\\desktop\\myproject", false, "Windows桌面子目录应该是合法的"},

		// Windows用户子目录 - 现在这些都应该是合法的（因为删除了用户子目录检查）
		{"AppData目录", "c:\\users\\<USER>\\appdata\\local", false, "AppData目录现在应该是合法的"},
		{"开始菜单目录", "c:\\users\\<USER>\\start menu", false, "开始菜单目录现在应该是合法的"},
		{"Application Data目录", "c:\\users\\<USER>\\application data", false, "Application Data目录现在应该是合法的"},
		{"Local Settings目录", "c:\\users\\<USER>\\local settings", false, "Local Settings目录现在应该是合法的"},

		// 合法的Windows目录
		{"用户项目目录", "c:\\users\\<USER>\\projects\\myapp", false, "用户项目目录应该是合法的"},
	}

	for _, tt := range tests {
		t.Run("Windows_"+tt.name, func(t *testing.T) {
			result := IsIllegalWorkspace(tt.path)
			if result != tt.expected {
				t.Errorf("IsIllegalWorkspace(%q) = %v, 期望 %v. %s",
					tt.path, result, tt.expected, tt.desc)
			}
		})
	}
}

func testLinuxSpecific(t *testing.T) {
	tests := []struct {
		name     string
		path     string
		expected bool
		desc     string
	}{
		// Linux系统目录
		{"usr目录", "/usr/local/test", true, "usr目录应该是非法的"},
		{"etc目录", "/etc/config", true, "etc目录应该是非法的"},
		{"var目录", "/var/log/test", true, "var目录应该是非法的"},
		{"root目录", "/root", true, "root目录本身应该是非法的"},

		// Linux桌面目录测试
		{"Linux桌面英文", "/home/<USER>/Desktop", true, "Linux桌面目录（英文）应该是非法的"},
		{"Linux桌面中文", "/home/<USER>/桌面", true, "Linux桌面目录（中文）应该是非法的"},
		{"Linux桌面俄文", "/home/<USER>/Рабочий стол", true, "Linux桌面目录（俄文）应该是非法的"},
		{"Linux桌面法文", "/home/<USER>/Bureau", true, "Linux桌面目录（法文）应该是非法的"},
		{"Linux桌面德文", "/home/<USER>/Arbeitsfläche", true, "Linux桌面目录（德文）应该是非法的"},
		{"Linux桌面西班牙文", "/home/<USER>/Escritorio", true, "Linux桌面目录（西班牙文）应该是非法的"},
		{"Linux桌面葡萄牙文", "/home/<USER>/Área de Trabalho", true, "Linux桌面目录（葡萄牙文）应该是非法的"},
		{"Linux桌面意大利文", "/home/<USER>/Scrivania", true, "Linux桌面目录（意大利文）应该是非法的"},
		{"Linux桌面子目录", "/home/<USER>/Desktop/myproject", false, "Linux桌面子目录应该是合法的"},

		// 合法的Linux目录
		{"root子目录", "/root/myproject", false, "root子目录应该是合法的"},
		{"home用户目录", "/home/<USER>/myproject", false, "home用户目录应该是合法的"},
	}

	for _, tt := range tests {
		t.Run("Linux_"+tt.name, func(t *testing.T) {
			result := IsIllegalWorkspace(tt.path)
			if result != tt.expected {
				t.Errorf("IsIllegalWorkspace(%q) = %v, 期望 %v. %s",
					tt.path, result, tt.expected, tt.desc)
			}
		})
	}
}

func testDarwinSpecific(t *testing.T) {
	tests := []struct {
		name     string
		path     string
		expected bool
		desc     string
	}{
		// macOS系统目录
		{"System目录", "/system/library/test", true, "System目录应该是非法的"},
		{"usr目录", "/usr/local/test", true, "usr目录应该是非法的"},
		{"Library目录", "/library/application support", true, "Library目录应该是非法的"},
		{"Applications目录", "/applications/test.app", true, "Applications目录应该是非法的"},
		{"Volumes目录", "/volumes/test", true, "Volumes目录应该是非法的"},

		// macOS用户相关目录
		{"用户共享目录", "/users/shared", true, "用户共享目录应该是非法的"},
		{"用户访客目录", "/users/guest", true, "用户访客目录应该是非法的"},

		// 合法的macOS目录
		{"用户项目目录", "/users/john/projects/myapp", false, "用户项目目录应该是合法的"},
		{"用户共享子目录", "/users/shared/myproject", false, "用户共享子目录应该是合法的"},
	}

	for _, tt := range tests {
		t.Run("macOS_"+tt.name, func(t *testing.T) {
			result := IsIllegalWorkspace(tt.path)
			if result != tt.expected {
				t.Errorf("IsIllegalWorkspace(%q) = %v, 期望 %v. %s",
					tt.path, result, tt.expected, tt.desc)
			}
		})
	}
}

// 直接测试Windows特定逻辑
func TestWindowsIllegalPath_Detailed(t *testing.T) {
	tests := []struct {
		name     string
		path     string
		expected bool
		desc     string
	}{
		// Windows完全匹配的非法目录
		{"用户默认目录", "c:\\users\\<USER>\\users\\public", true, "用户公共目录应该是非法的"},
		{"用户All Users目录", "c:\\users\\<USER>\\documents and settings", true, "Documents and Settings目录应该是非法的"},

		// Windows桌面目录测试（完全匹配）
		{"Windows桌面", "c:\\users\\<USER>\\desktop", true, "Windows桌面目录应该是非法的"},
		{"Windows桌面大写", "C:\\USERS\\<USER>\\DESKTOP", true, "Windows桌面目录（大写）应该是非法的"},
		{"Windows桌面混合大小写", "C:\\Users\\<USER>\\Desktop", true, "Windows桌面目录（混合大小写）应该是非法的"},
		{"Windows桌面子目录", "c:\\users\\<USER>\\desktop\\myproject", false, "Windows桌面子目录应该是合法的"},
		{"Windows桌面子目录深层", "c:\\users\\<USER>\\desktop\\work\\project", false, "Windows桌面深层子目录应该是合法的"},

		// Windows系统目录测试（前缀匹配）
		{"Windows系统目录", "c:\\windows\\system32", true, "Windows系统目录应该是非法的"},
		{"Windows系统子目录", "c:\\windows\\temp\\test", true, "Windows系统子目录应该是非法的"},
		{"Program Data目录", "c:\\program data\\test", true, "Program Data目录应该是非法的"},
		{"System Volume Information", "c:\\system volume information\\test", true, "System Volume Information目录应该是非法的"},
		{"Recovery目录", "c:\\recovery\\test", true, "Recovery目录应该是非法的"},
		{"回收站目录", "c:\\$recycle.bin\\test", true, "回收站目录应该是非法的"},
		{"Boot目录", "c:\\boot\\test", true, "Boot目录应该是非法的"},
		{"EFI目录", "c:\\efi\\test", true, "EFI目录应该是非法的"},
		{"临时目录", "c:\\temp\\test", true, "临时目录应该是非法的"},
		{"IIS目录", "c:\\inetpub\\test", true, "IIS目录应该是非法的"},

		// 用户目录下的合法目录（现在这些都应该是合法的，因为删除了用户子目录检查）
		{"AppData目录", "c:\\users\\<USER>\\appdata\\local", false, "AppData目录现在应该是合法的"},
		{"AppData Roaming", "c:\\users\\<USER>\\appdata\\roaming", false, "AppData Roaming目录现在应该是合法的"},
		{"开始菜单目录", "c:\\users\\<USER>\\start menu", false, "开始菜单目录现在应该是合法的"},
		{"Application Data目录", "c:\\users\\<USER>\\application data", false, "Application Data目录现在应该是合法的"},
		{"Local Settings目录", "c:\\users\\<USER>\\local settings", false, "Local Settings目录现在应该是合法的"},
		{"My Documents目录", "c:\\users\\<USER>\\my documents", false, "My Documents目录现在应该是合法的"},
		{"Cookies目录", "c:\\users\\<USER>\\cookies", false, "Cookies目录现在应该是合法的"},
		{"Templates目录", "c:\\users\\<USER>\\templates", false, "Templates目录现在应该是合法的"},

		// 合法的Windows目录
		{"用户文档目录", "c:\\users\\<USER>\\documents\\myproject", false, "用户文档目录应该是合法的"},
		{"用户项目目录", "c:\\users\\<USER>\\projects\\myapp", false, "用户项目目录应该是合法的"},
		{"用户下载目录", "c:\\users\\<USER>\\downloads\\project", false, "用户下载目录应该是合法的"},
		{"D盘项目", "d:\\projects\\myapp", false, "D盘项目应该是合法的"},
		{"网络路径", "\\\\server\\share\\project", false, "网络路径应该是合法的"},

		// 边界情况
		{"包含desktop但不以desktop结尾", "c:\\users\\<USER>\\my-desktop-app", false, "包含desktop但不以desktop结尾应该是合法的"},
		{"desktop在路径中间", "c:\\users\\<USER>\\desktop-backup\\project", false, "desktop在路径中间应该是合法的"},
	}

	for _, tt := range tests {
		t.Run("Windows_"+tt.name, func(t *testing.T) {
			// 直接调用Windows特定的函数进行测试
			cleanPath := strings.ToLower(filepath.Clean(tt.path))
			result := isWindowsIllegalPath(cleanPath)
			if result != tt.expected {
				t.Errorf("isWindowsIllegalPath(%q) = %v, 期望 %v. %s",
					tt.path, result, tt.expected, tt.desc)
			}
		})
	}
}

// 直接测试Linux特定逻辑
func TestLinuxIllegalPath_Detailed(t *testing.T) {
	tests := []struct {
		name     string
		path     string
		expected bool
		desc     string
	}{
		// Linux完全匹配的非法目录
		{"root目录", "/root", true, "root目录本身应该是非法的"},
		{"root目录子路径", "/root/myproject", false, "root目录子路径应该是合法的"},
		{"root目录深层子路径", "/root/work/project", false, "root目录深层子路径应该是合法的"},

		// Linux桌面目录测试（多语言支持）
		{"Linux桌面英文", "/home/<USER>/Desktop", true, "Linux桌面目录（英文）应该是非法的"},
		{"Linux桌面英文小写", "/home/<USER>/desktop", true, "Linux桌面目录（英文小写）应该是非法的"},
		{"Linux桌面中文", "/home/<USER>/桌面", true, "Linux桌面目录（中文）应该是非法的"},
		{"Linux桌面俄文", "/home/<USER>/Рабочий стол", true, "Linux桌面目录（俄文）应该是非法的"},
		{"Linux桌面法文", "/home/<USER>/Bureau", true, "Linux桌面目录（法文）应该是非法的"},
		{"Linux桌面德文", "/home/<USER>/Arbeitsfläche", true, "Linux桌面目录（德文）应该是非法的"},
		{"Linux桌面西班牙文", "/home/<USER>/Escritorio", true, "Linux桌面目录（西班牙文）应该是非法的"},
		{"Linux桌面葡萄牙文", "/home/<USER>/Área de Trabalho", true, "Linux桌面目录（葡萄牙文）应该是非法的"},
		{"Linux桌面意大利文", "/home/<USER>/Scrivania", true, "Linux桌面目录（意大利文）应该是非法的"},

		// Linux桌面子目录（应该合法）
		{"Linux桌面子目录英文", "/home/<USER>/Desktop/myproject", false, "Linux桌面子目录（英文）应该是合法的"},
		{"Linux桌面子目录中文", "/home/<USER>/桌面/我的项目", false, "Linux桌面子目录（中文）应该是合法的"},
		{"Linux桌面深层子目录", "/home/<USER>/Desktop/work/project", false, "Linux桌面深层子目录应该是合法的"},

		// Linux系统目录测试（前缀匹配）
		{"bin目录", "/bin/bash", true, "bin目录应该是非法的"},
		{"sbin目录", "/sbin/init", true, "sbin目录应该是非法的"},
		{"usr目录", "/usr/local/bin", true, "usr目录应该是非法的"},
		{"usr子目录", "/usr/share/applications", true, "usr子目录应该是非法的"},
		{"etc目录", "/etc/passwd", true, "etc目录应该是非法的"},
		{"etc子目录", "/etc/systemd/system", true, "etc子目录应该是非法的"},
		{"var目录", "/var/log/syslog", true, "var目录应该是非法的"},
		{"var子目录", "/var/cache/apt", true, "var子目录应该是非法的"},
		{"tmp目录", "/tmp/test", true, "tmp目录应该是非法的"},
		{"sys目录", "/sys/class/net", true, "sys目录应该是非法的"},
		{"proc目录", "/proc/cpuinfo", true, "proc目录应该是非法的"},
		{"dev目录", "/dev/null", true, "dev目录应该是非法的"},
		{"run目录", "/run/user", true, "run目录应该是非法的"},
		{"boot目录", "/boot/grub", true, "boot目录应该是非法的"},
		{"lib目录", "/lib/systemd", true, "lib目录应该是非法的"},
		{"lib64目录", "/lib64/ld-linux-x86-64.so.2", true, "lib64目录应该是非法的"},
		{"opt目录", "/opt/google/chrome", true, "opt目录应该是非法的"},
		{"lost+found目录", "/lost+found/test", true, "lost+found目录应该是非法的"},
		{"media目录", "/media/usb", true, "media目录应该是非法的"},
		{"srv目录", "/srv/www", true, "srv目录应该是非法的"},

		// 合法的Linux目录
		{"home用户目录", "/home/<USER>/documents/myproject", false, "home用户目录应该是合法的"},
		{"home用户子目录", "/home/<USER>/projects/myapp", false, "home用户子目录应该是合法的"},
		{"home用户下载目录", "/home/<USER>/Downloads/project", false, "home用户下载目录应该是合法的"},
		{"mnt挂载目录", "/mnt/external/project", false, "mnt挂载目录现在应该是合法的"},
		{"自定义根目录", "/data/projects", false, "自定义根目录应该是合法的"},
		{"网络挂载", "/nfs/shared/project", false, "网络挂载应该是合法的"},

		// 边界情况
		{"包含desktop但不以desktop结尾", "/home/<USER>/my-desktop-app", false, "包含desktop但不以desktop结尾应该是合法的"},
		{"desktop在路径中间", "/home/<USER>/desktop-backup/project", false, "desktop在路径中间应该是合法的"},
	}

	for _, tt := range tests {
		t.Run("Linux_"+tt.name, func(t *testing.T) {
			// 直接调用Linux特定的函数进行测试
			cleanPath := strings.ToLower(filepath.Clean(tt.path))
			result := isLinuxIllegalPath(cleanPath)
			if result != tt.expected {
				t.Errorf("isLinuxIllegalPath(%q) = %v, 期望 %v. %s",
					tt.path, result, tt.expected, tt.desc)
			}
		})
	}
}

// 直接测试macOS特定逻辑
func TestDarwinIllegalPath_Detailed(t *testing.T) {
	tests := []struct {
		name     string
		path     string
		expected bool
		desc     string
	}{
		// macOS完全匹配的非法目录
		{"用户共享目录", "/users/shared", true, "用户共享目录应该是非法的"},
		{"用户访客目录", "/users/guest", true, "用户访客目录应该是非法的"},
		{"用户本地化目录", "/users/.localized", true, "用户本地化目录应该是非法的"},

		// 用户相关目录的子目录（应该合法）
		{"用户共享子目录", "/users/shared/myproject", false, "用户共享子目录应该是合法的"},
		{"用户访客子目录", "/users/guest/documents", false, "用户访客子目录应该是合法的"},

		// macOS桌面目录测试
		{"macOS桌面大写", "/users/john/Desktop", true, "macOS桌面目录（大写）应该是非法的"},
		{"macOS桌面小写", "/users/john/desktop", true, "macOS桌面目录（小写）应该是非法的"},
		{"macOS桌面混合大小写", "/Users/<USER>/Desktop", true, "macOS桌面目录（混合大小写）应该是非法的"},
		{"macOS桌面子目录", "/users/john/Desktop/myproject", false, "macOS桌面子目录应该是合法的"},
		{"macOS桌面深层子目录", "/users/john/Desktop/work/project", false, "macOS桌面深层子目录应该是合法的"},

		// macOS系统目录测试（前缀匹配）
		{"System目录", "/system/library/frameworks", true, "System目录应该是非法的"},
		{"System子目录", "/system/library/coreservices", true, "System子目录应该是非法的"},
		{"usr目录", "/usr/local/bin", true, "usr目录应该是非法的"},
		{"usr子目录", "/usr/share/man", true, "usr子目录应该是非法的"},
		{"bin目录", "/bin/bash", true, "bin目录应该是非法的"},
		{"sbin目录", "/sbin/mount", true, "sbin目录应该是非法的"},
		{"etc目录", "/etc/passwd", true, "etc目录应该是非法的"},
		{"var目录", "/var/log/system.log", true, "var目录应该是非法的"},
		{"tmp目录", "/tmp/test", true, "tmp目录应该是非法的"},
		{"private目录", "/private/var/db", true, "private目录应该是非法的"},
		{"Library目录", "/library/application support", true, "Library目录应该是非法的"},
		{"Library子目录", "/library/preferences", true, "Library子目录应该是非法的"},
		{"Volumes目录", "/volumes/external", true, "Volumes目录应该是非法的"},
		{"Volumes子目录", "/volumes/macintosh hd", true, "Volumes子目录应该是非法的"},
		{"cores目录", "/cores/test", true, "cores目录应该是非法的"},
		{"dev目录", "/dev/disk0", true, "dev目录应该是非法的"},
		{"Applications目录", "/applications/safari.app", true, "Applications目录应该是非法的"},
		{"Applications子目录", "/applications/utilities/terminal.app", true, "Applications子目录应该是非法的"},
		{"sw目录", "/sw/bin", true, "sw目录应该是非法的"},
		{"x11目录", "/x11/bin", true, "x11目录应该是非法的"},
		{"darwin目录", "/darwin/test", true, "darwin目录应该是非法的"},
		{"extra目录", "/extra/test", true, "extra目录应该是非法的"},

		// 合法的macOS目录
		{"用户文档目录", "/users/john/documents/myproject", false, "用户文档目录应该是合法的"},
		{"用户项目目录", "/users/john/projects/myapp", false, "用户项目目录应该是合法的"},
		{"用户下载目录", "/users/john/downloads/project", false, "用户下载目录应该是合法的"},
		{"用户图片目录", "/users/john/pictures/project", false, "用户图片目录应该是合法的"},
		{"用户音乐目录", "/users/john/music/project", false, "用户音乐目录应该是合法的"},
		{"用户视频目录", "/users/john/movies/project", false, "用户视频目录应该是合法的"},
		{"自定义根目录", "/data/projects", false, "自定义根目录应该是合法的"},
		{"网络挂载", "/nfs/shared/project", false, "网络挂载应该是合法的"},

		// 边界情况
		{"包含desktop但不以desktop结尾", "/users/john/my-desktop-app", false, "包含desktop但不以desktop结尾应该是合法的"},
		{"desktop在路径中间", "/users/john/desktop-backup/project", false, "desktop在路径中间应该是合法的"},
		{"包含system但不以system开头", "/users/john/system-config", false, "包含system但不以system开头应该是合法的"},
	}

	for _, tt := range tests {
		t.Run("macOS_"+tt.name, func(t *testing.T) {
			// 直接调用macOS特定的函数进行测试
			cleanPath := strings.ToLower(filepath.Clean(tt.path))
			result := isDarwinIllegalPath(cleanPath)
			if result != tt.expected {
				t.Errorf("isDarwinIllegalPath(%q) = %v, 期望 %v. %s",
					tt.path, result, tt.expected, tt.desc)
			}
		})
	}
}

func TestIsIllegalWorkspace_EdgeCases(t *testing.T) {
	tests := []struct {
		name     string
		path     string
		expected bool
		desc     string
	}{
		// 边界情况
		{"带制表符的路径", "\t/users/user/project\t", false, "带制表符的路径应该被正确处理"},
		{"路径末尾有空格", "/users/user/project ", false, "路径末尾空格应该被正确处理"},
		{"路径开头有空格", " /users/user/project", false, "路径开头空格应该被正确处理"},
		{"多个连续斜杠", "/users//user///project", false, "多个连续斜杠应该被normalize"},

		// macOS特定测试（因为运行环境是macOS）
		{"macOS桌面大小写", "/Users/<USER>/DESKTOP", true, "macOS应该区分大小写但桌面目录有特殊处理"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsIllegalWorkspace(tt.path)
			if result != tt.expected {
				t.Errorf("IsIllegalWorkspace(%q) = %v, 期望 %v. %s",
					tt.path, result, tt.expected, tt.desc)
			}
		})
	}
}

// 测试路径存在性和目录性检查
func TestIsIllegalExistingWorkspace_PathExistenceAndType(t *testing.T) {
	// 获取用户目录，在用户目录下创建临时目录
	userHome, err := os.UserHomeDir()
	if err != nil {
		t.Fatalf("无法获取用户目录: %v", err)
	}

	// 在用户目录下创建一个测试子目录
	testBaseDir := filepath.Join(userHome, "test_workspace_temp")
	err = os.MkdirAll(testBaseDir, 0755)
	if err != nil {
		t.Fatalf("无法创建测试基础目录: %v", err)
	}
	defer os.RemoveAll(testBaseDir)

	// 创建临时目录作为测试用的合法工作区
	tempDir, err := os.MkdirTemp(testBaseDir, "workspace_")
	if err != nil {
		t.Fatalf("无法创建临时目录: %v", err)
	}

	// 创建一个临时文件（非目录）
	tempFile, err := os.CreateTemp(testBaseDir, "test_file_")
	if err != nil {
		t.Fatalf("无法创建临时文件: %v", err)
	}
	tempFileName := tempFile.Name()
	tempFile.Close()

	// 创建一个子目录（合法的工作区）
	subDir := filepath.Join(tempDir, "subdir")
	err = os.Mkdir(subDir, 0755)
	if err != nil {
		t.Fatalf("无法创建子目录: %v", err)
	}

	tests := []struct {
		name     string
		path     string
		expected bool
		desc     string
	}{
		// 路径不存在的情况
		{"不存在的路径", "/absolutely/nonexistent/path/12345", true, "不存在的路径应该是非法的"},
		{"不存在的相对路径", "./nonexistent/path", true, "不存在的相对路径应该是非法的"},

		// 存在但不是目录的情况
		{"文件路径", tempFileName, true, "文件路径应该是非法的"},

		// 存在且是目录的情况
		{"合法的临时目录", tempDir, false, "存在的目录应该是合法的（在用户目录下）"},
		{"合法的子目录", subDir, false, "存在的子目录应该是合法的（在用户目录下）"},

		// 基础校验仍然有效
		{"空字符串", "", true, "空字符串应该是非法的"},
		{"用户目录本身", userHome, true, "用户目录本身应该是非法的"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsIllegalExistingWorkspace(tt.path)
			if result != tt.expected {
				t.Errorf("IsIllegalExistingWorkspace(%q) = %v, 期望 %v. %s",
					tt.path, result, tt.expected, tt.desc)
			}
		})
	}
}

// 测试基础路径校验（不检查存在性）
func TestIsIllegalWorkspace_PathValidationOnly(t *testing.T) {
	tests := []struct {
		name     string
		path     string
		expected bool
		desc     string
	}{
		// 不存在的路径在基础校验中应该是合法的（只要路径格式正确）
		{"不存在的用户项目路径", "/users/john/projects/myapp", false, "不存在但格式正确的用户项目路径应该是合法的"},
		{"不存在的文档路径", "/users/john/Documents/project", false, "不存在但格式正确的文档路径应该是合法的"},

		// 系统目录仍然非法
		{"系统目录", "/usr/local/project", true, "系统目录应该是非法的"},
		{"macOS系统目录", "/System/project", true, "macOS系统目录应该是非法的"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsIllegalWorkspace(tt.path)
			if result != tt.expected {
				t.Errorf("IsIllegalWorkspace(%q) = %v, 期望 %v. %s",
					tt.path, result, tt.expected, tt.desc)
			}
		})
	}
}

// 测试权限问题（如果有的话）
func TestIsIllegalWorkspace_PermissionIssues(t *testing.T) {
	// 这个测试主要是为了确保在权限问题时函数能正确处理
	// 在不同操作系统上，权限处理可能不同，所以只测试一些通用情况

	tests := []struct {
		name     string
		path     string
		expected bool
		desc     string
	}{
		// 在macOS上测试一些可能需要权限的系统路径
		{"系统根目录", "/", true, "根目录通常不应该作为工作区"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsIllegalWorkspace(tt.path)
			if result != tt.expected {
				t.Errorf("IsIllegalWorkspace(%q) = %v, 期望 %v. %s",
					tt.path, result, tt.expected, tt.desc)
			}
		})
	}
}
