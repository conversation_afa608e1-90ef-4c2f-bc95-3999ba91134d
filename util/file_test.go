package util

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"testing"

	"cosy/log"

	"github.com/stretchr/testify/assert"
)

func TestIsExists(t *testing.T) {
	//log.Info(PathExists("/Users/<USER>/Projects/extensions/cosy-vscode/bin/0.0.12/common/typescript/bundle.js"))
	//log.Info(PathExists("/Users/<USER>/Projects/extensions/cosy-vscode/bin/0.0.13/common/typescript/bundle.js"))
}

func TestGetFileType(t *testing.T) {
	log.Info(GetFileType("a.b.c.d"))
	log.Info(GetFileType(""))
	log.Info(GetFileType("a"))
	log.Info(GetFileType("asd/"))
	log.Info(GetFileType("asd/sdf/qw.e"))
}

func TestConvertPythonPath(t *testing.T) {
	workspace := "/home/<USER>/pydemo"
	currentPath := "/home/<USER>/pydemo/work/check/test.py"
	ret, _ := ConvertPythonPath("demo.token", workspace, currentPath, "")
	assert.Equal(t, "/home/<USER>/pydemo/demo/token", ret)
	ret, _ = ConvertPythonPath(".token", workspace, currentPath, ".py")
	assert.Equal(t, "/home/<USER>/pydemo/work/check/token.py", ret)
	ret, _ = ConvertPythonPath("..token", workspace, currentPath, ".py")
	assert.Equal(t, "/home/<USER>/pydemo/work/token.py", ret)
	ret, _ = ConvertPythonPath("...token.ok", workspace, currentPath, ".py")
	assert.Equal(t, "/home/<USER>/pydemo/token/ok.py", ret)
	ret, _ = ConvertPythonPath(".", workspace, currentPath, ".py")
	assert.Equal(t, "/home/<USER>/pydemo/work/check.py", ret)
	ret, _ = ConvertPythonPath("..", workspace, currentPath, ".py")
	assert.Equal(t, "/home/<USER>/pydemo/work.py", ret)
}

func TestIsBinaryFile(t *testing.T) {
	assert.Equal(t, false, IsBinaryFile("/Users/<USER>/Documents/codes/projects/cosy/util/file.go"))
	assert.Equal(t, false, IsBinaryFile("/Users/<USER>/Documents/codes/projects/cosy/cmd/version.go"))
	assert.Equal(t, true, IsBinaryFile("/Users/<USER>/.lingma/model/env/darwin-amd64"))
	assert.Equal(t, true, IsBinaryFile("/Users/<USER>/Pictures/图片素材/codota_search_demo.gif"))
	assert.Equal(t, true, IsBinaryFile("/Users/<USER>/Downloads/core-1.2.5.jar.src.zip"))
}

// TestIsLargeFile 测试IsLargeFile函数
func TestIsLargeFile(t *testing.T) {
	// 定义测试用例
	tests := []struct {
		content []byte
		want    bool
	}{
		{[]byte("This is a small file"), false},
		{[]byte(strings.Repeat("a", 300001)), true},
		{[]byte(strings.Repeat("a", 1025)), true},
		{[]byte(strings.Repeat("a", 1000)), false},
	}

	// 执行测试
	for _, tt := range tests {
		t.Run("", func(t *testing.T) {
			if got := IsLargeFile(tt.content); got != tt.want {
				t.Errorf("IsLargeFile() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCollectFilesInDir(t *testing.T) {
	dir, err := os.MkdirTemp("", "TestCollectFilesInDir")
	assert.NoError(t, err)
	defer func() {
		assert.NoError(t, os.RemoveAll(dir))
	}()

	assert.NoError(t, os.MkdirAll(filepath.Join(dir, "a"), os.ModePerm))
	mustTouch(t, filepath.Join(dir, "a/1.png"))
	files, err := CollectFilesInDir(dir, true, IsGlobalIgnoreFile)
	assert.NoError(t, err)
	assert.Equal(t, 0, len(files))

	mustTouch(t, filepath.Join(dir, "a/1.go"))
	files, err = CollectFilesInDir(dir, false, IsGlobalIgnoreFile)
	assert.NoError(t, err)
	assert.Equal(t, 0, len(files))
	files, err = CollectFilesInDir(dir, true, IsGlobalIgnoreFile)
	assert.NoError(t, err)
	assert.Equal(t, 1, len(files))

	err = os.MkdirAll(filepath.Join(dir, "b"), os.ModePerm)
	if err != nil {
		panic(err)
	}
}

func mustTouch(t *testing.T, path string) {
	file, err := os.Create(path)
	_, err = file.WriteString("Hello World")
	assert.NoError(t, err)
	assert.NoError(t, err)
	assert.NoError(t, file.Close())
}

func TestCountLine(t *testing.T) {
	cases := []string{
		"",
		"\n",
		"a\nb\nc\nd\ne\n",
		"\t\t \t \nabc",
	}
	assert.Equal(t, CountLine(cases[0], 0), 0)
	assert.Equal(t, CountLine(cases[0], 10), 0)

	assert.Equal(t, CountLine(cases[1], 10), 1)
	assert.Equal(t, CountLine(cases[1], 0), 0)
	assert.Equal(t, CountLine(cases[1], 1), 1)

	assert.Equal(t, CountLine(cases[2], 100), 5)
	assert.Equal(t, CountLine(cases[2], 1), 0)
	assert.Equal(t, CountLine(cases[2], 2), 1)
	assert.Equal(t, CountLine(cases[2], 3), 1)
	assert.Equal(t, CountLine(cases[2], 4), 2)

	assert.Equal(t, CountLine(cases[3], 10), 1)
	assert.Equal(t, CountLine(cases[3], 5), 0)
	assert.Equal(t, CountLine(cases[3], 6), 1)
}

func TestReadLimitedChars(t *testing.T) {
	// Create temporary test directory
	tempDir, err := os.MkdirTemp("", "char_reader_test")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Test cases
	testCases := []struct {
		name          string
		content       string
		charLimit     int
		expectedChars int
		expectError   bool
	}{
		{
			name:          "Read fewer than limit",
			content:       "Hello, world!",
			charLimit:     20,
			expectedChars: 13, // Length of "Hello, world!"
			expectError:   false,
		},
		{
			name:          "Read exactly the limit",
			content:       "Hello, world!",
			charLimit:     13,
			expectedChars: 13,
			expectError:   false,
		},
		{
			name:          "Read up to the limit",
			content:       "Hello, world! This is a test.",
			charLimit:     13,
			expectedChars: 13,
			expectError:   false,
		},
		{
			name:          "Empty file",
			content:       "",
			charLimit:     10,
			expectedChars: 0,
			expectError:   false,
		},
		{
			name:          "Unicode characters",
			content:       "你好，世界！",
			charLimit:     5,
			expectedChars: 5,
			expectError:   false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Create test file
			testFile := filepath.Join(tempDir, "test_file.txt")
			err := os.WriteFile(testFile, []byte(tc.content), 0644)
			if err != nil {
				t.Fatalf("Failed to create test file: %v", err)
			}

			// Call the function
			result, err := ReadLimitedChars(testFile, tc.charLimit)

			// Check error
			if tc.expectError && err == nil {
				t.Errorf("Expected error but got none")
			}
			if !tc.expectError && err != nil {
				t.Errorf("Unexpected error: %v", err)
			}

			// Check result length
			if len([]rune(result)) != tc.expectedChars {
				t.Errorf("Expected %d characters, got %d", tc.expectedChars, len([]rune(result)))
			}

			// Check content
			expectedContent := tc.content
			if len([]rune(tc.content)) > tc.charLimit {
				expectedContent = string([]rune(tc.content)[:tc.charLimit])
			}
			if result != expectedContent {
				t.Errorf("Expected content: %q, got: %q", expectedContent, result)
			}
		})
	}

	// Test with non-existent file
	t.Run("Non-existent file", func(t *testing.T) {
		_, err := ReadLimitedChars(filepath.Join(tempDir, "non_existent.txt"), 10)
		if err == nil {
			t.Errorf("Expected error for non-existent file but got none")
		}
	})
}

func TestTailTruncateFileByLines(t *testing.T) {
	type args struct {
		file       string
		targetSize int64
	}
	tests := []struct {
		name    string
		args    args
		wantErr assert.ErrorAssertionFunc
	}{
		{
			name: "TestTailTruncateFileByLines",
			args: args{
				file:       "/Users/<USER>/Downloads/diagnosis.bin-3.log",
				targetSize: 1024 * 1024 * 0.5,
			},
			wantErr: assert.NoError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.wantErr(t, TailTruncateFileByLines(tt.args.file, tt.args.targetSize), fmt.Sprintf("TailTruncateFileByLines(%v, %v)", tt.args.file, tt.args.targetSize))
		})
	}
}

// TestFindFileUntilRootOnce 测试FindFileUntilRootOnce函数
func TestFindFileUntilRootOnce(t *testing.T) {
	// 创建临时测试目录
	tempDir, err := os.MkdirTemp("", "TestFindFileUntilRootOnce")
	assert.NoError(t, err)
	defer func() {
		assert.NoError(t, os.RemoveAll(tempDir))
	}()

	// 创建测试目录结构
	// tempDir/
	//   ├── a/
	//   │   ├── b/
	//   │   │   ├── c/
	//   │   │   │   └── current.go
	//   │   │   └── config.json
	//   │   └── package.json
	//   └── README.md

	assert.NoError(t, os.MkdirAll(filepath.Join(tempDir, "a", "b", "c"), os.ModePerm))

	// 创建测试文件
	mustTouch(t, filepath.Join(tempDir, "README.md"))
	mustTouch(t, filepath.Join(tempDir, "a", "package.json"))
	mustTouch(t, filepath.Join(tempDir, "a", "b", "config.json"))
	mustTouch(t, filepath.Join(tempDir, "a", "b", "c", "current.go"))

	tests := []struct {
		name            string
		workspace       string
		filePath        string
		targetFilePaths []string
		expectedPath    string
		expectError     bool
	}{
		{
			name:            "找到直接目录下的文件",
			workspace:       tempDir,
			filePath:        filepath.Join(tempDir, "a", "b", "c", "current.go"),
			targetFilePaths: []string{"config.json"},
			expectedPath:    filepath.Join(tempDir, "a", "b", "config.json"),
			expectError:     false,
		},
		{
			name:            "找到上级目录中的文件",
			workspace:       tempDir,
			filePath:        filepath.Join(tempDir, "a", "b", "c", "current.go"),
			targetFilePaths: []string{"package.json"},
			expectedPath:    filepath.Join(tempDir, "a", "package.json"),
			expectError:     false,
		},
		{
			name:            "找到根目录中的文件",
			workspace:       tempDir,
			filePath:        filepath.Join(tempDir, "a", "b", "c", "current.go"),
			targetFilePaths: []string{"README.md"},
			expectedPath:    filepath.Join(tempDir, "README.md"),
			expectError:     false,
		},
		{
			name:            "找到多个目标文件中的第一个",
			workspace:       tempDir,
			filePath:        filepath.Join(tempDir, "a", "b", "c", "current.go"),
			targetFilePaths: []string{"not-exist.txt", "config.json", "package.json"},
			expectedPath:    filepath.Join(tempDir, "a", "b", "config.json"),
			expectError:     false,
		},
		{
			name:            "没有找到任何目标文件",
			workspace:       tempDir,
			filePath:        filepath.Join(tempDir, "a", "b", "c", "current.go"),
			targetFilePaths: []string{"not-exist.txt", "another-not-exist.conf"},
			expectedPath:    "",
			expectError:     true,
		},
		{
			name:            "从根目录开始查找",
			workspace:       tempDir,
			filePath:        filepath.Join(tempDir, "README.md"),
			targetFilePaths: []string{"README.md"},
			expectedPath:    filepath.Join(tempDir, "README.md"),
			expectError:     false,
		},
		{
			name:            "空目标文件列表",
			workspace:       tempDir,
			filePath:        filepath.Join(tempDir, "a", "b", "c", "current.go"),
			targetFilePaths: []string{},
			expectedPath:    "",
			expectError:     true,
		},
		{
			name:            "文件路径等于workspace路径",
			workspace:       tempDir,
			filePath:        tempDir,
			targetFilePaths: []string{"README.md"},
			expectedPath:    "",
			expectError:     true,
		},
		{
			name:            "文件路径在workspace外部",
			workspace:       tempDir,
			filePath:        filepath.Dir(tempDir),
			targetFilePaths: []string{"README.md"},
			expectedPath:    "",
			expectError:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := FindFileUntilRootOnce(tt.workspace, tt.filePath, tt.targetFilePaths)

			if tt.expectError {
				assert.Error(t, err)
				assert.Equal(t, "", result)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedPath, result)
				// 验证返回的文件确实存在
				assert.True(t, PathExists(result))
			}
		})
	}
}
