package util

import (
	"github.com/spf13/cast"
	"net/http"
	"net/url"
	"strings"
)

func BuildParameter(params map[string]string) string {
	p := ""
	for k, v := range params {
		p = p + k + "=" + url.QueryEscape(v) + "&"
	}
	return p[:len(p)-1]
}

func ParseParameters(r *http.Request) map[string]string {
	reqUrl := r.URL.String()
	params := map[string]string{}
	index := strings.Index(reqUrl, "?")
	if index < 1 {
		return params
	}
	parts := strings.Split(reqUrl[index+1:], "&")
	for _, p := range parts {
		pp := strings.SplitN(p, "=", 2)
		if len(pp) == 2 {
			params[pp[0]], _ = url.QueryUnescape(pp[1])
		}
	}
	return params
}

func IsValidURL(str string) bool {
	u, err := url.Parse(str)
	return err == nil && u.Scheme != "" && u.Host != ""
}

func BuildUrlQuery(u string, query map[string]any) string {
	if len(query) == 0 {
		return u
	}

	var params []string
	for key, value := range query {
		params = append(params, key+"="+url.QueryEscape(cast.ToString(value)))
	}

	separator := "?"
	if strings.Contains(u, "?") {
		separator = "&"
	}

	return u + separator + strings.Join(params, "&")
}
