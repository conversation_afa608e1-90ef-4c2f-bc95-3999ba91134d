package util

import (
	"archive/zip"
	"bufio"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"io/fs"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/patrickmn/go-cache"

	"cosy/log"
)

var (
	fileFindCache = cache.New(2*time.Minute, 5*time.Minute)
)

// PathExists returns false if given path isn't exist
func PathExists(path string) bool {
	_, err := os.Stat(path)
	return err == nil
}

func ParseFileType(filename string) string {
	var extension = filepath.Ext(filename)
	if extension == ".js" {
		return "JS"
	}
	if extension == ".py" {
		return "PY"
	}
	return "Common"
}

// GetFileSize returns the file size in MB
func GetFileSize(filePath string) float32 {
	stat, err := os.Stat(filePath)
	if err != nil {
		return 0
	}
	return float32(stat.Size()) / (1024.0 * 1024.0)
}

// GetFileSizeByte returns the file size in MB
func GetFileSizeByte(filePath string) int64 {
	stat, err := os.Stat(filePath)
	if err != nil {
		return 0
	}
	return stat.Size()
}

// GetFileType returns file's extension if given file path
// If given path is a folder's path, an empty string will be returned
func GetFileType(path string) string {
	words := strings.Split(path, ".")
	if len(words) <= 1 {
		return ""
	}
	return strings.ToLower(words[len(words)-1])
}

func GetFileName(path string) string {
	index := strings.LastIndexAny(path, "/\\")
	if index == -1 {
		return path
	}
	return path[index+1:]
}

// Zip 将文件压缩成zip文件
// filepaths：原文件路径 -> 压缩包内的文件路径
// zipFilePath：生成的压缩包文件路径
func Zip(filepaths map[string]string, zipFilePath string) error {
	// 创建一个新的zip文件
	zipFile, err := os.Create(zipFilePath)
	if err != nil {
		return err
	}
	defer zipFile.Close()

	// 创建一个zip.Writer
	zipWriter := zip.NewWriter(zipFile)
	defer zipWriter.Close()

	// 将指定文件添加到zip包中
	addFileToZip := func(fromPath, toPath string) error {
		file, err2 := os.Open(fromPath)
		if err2 != nil {
			return err2
		}
		defer file.Close()

		// 创建一个zip文件的文件头
		fileInfo, err2 := file.Stat()
		if err2 != nil {
			return err2
		}
		header, err2 := zip.FileInfoHeader(fileInfo)
		if err2 != nil {
			return err2
		}

		// 设置zip文件的名称为原始文件名
		header.Name = toPath

		// 将文件头写入zip包
		writer, err2 := zipWriter.CreateHeader(header)
		if err2 != nil {
			return err2
		}

		// 将文件内容写入zip包
		_, err2 = io.Copy(writer, file)
		if err2 != nil {
			return err2
		}

		return nil
	}

	// 将每个文件添加到zip包中
	for fromPath, toPath := range filepaths {
		log.Debugf("Zipping file %s", fromPath)
		err = addFileToZip(fromPath, toPath)
		if err != nil {
			log.Debugf("File %s skipped: %s", fromPath, err.Error())
		}
	}
	return nil
}

// Unzip decompresses .zip fil to dest
func Unzip(src, dest string) error {
	// Delete zip file before return
	defer func(name string) {
		err := os.Remove(name)
		if err != nil {
			log.Debug(err)
		}
	}(src)

	r, err := zip.OpenReader(src)
	if err != nil {
		return err
	}
	defer func() {
		if err := r.Close(); err != nil {
			return
		}
	}()

	os.MkdirAll(dest, 0755)

	// Closure to address file descriptors issue with all the deferred .Close() methods
	extractAndWriteFile := func(f *zip.File) error {
		rc, err := f.Open()
		if err != nil {
			return err
		}
		defer func() {
			if err := rc.Close(); err != nil {
				return
			}
		}()

		path := filepath.Join(dest, f.Name)

		// Check for ZipSlip (Directory traversal)
		if !strings.HasPrefix(path, filepath.Clean(dest)+string(os.PathSeparator)) {
			return errors.New("illegal file path: " + path)
		}

		if f.FileInfo().IsDir() {
			os.MkdirAll(path, f.Mode())
		} else {
			os.MkdirAll(filepath.Dir(path), f.Mode())
			f, err := os.OpenFile(path, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, f.Mode())
			if err != nil {
				return err
			}
			defer func() {
				if err := f.Close(); err != nil {
					return
				}
			}()

			_, err = io.Copy(f, rc)
			if err != nil {
				return err
			}
		}
		return nil
	}

	for _, f := range r.File {
		err := extractAndWriteFile(f)
		if err != nil {
			return err
		}
	}

	return nil
}

// IsExcludeFile checks whether the given file path is in the exclude list
func IsExcludeFile(filepath string, excludes []string) bool {
	for _, exclude := range excludes {
		if filepath == exclude {
			return true
		}
	}
	return false
}

// GetFileNameWithoutExt returns file name without extension
func GetFileNameWithoutExt(filePath string) string {
	index := strings.LastIndexAny(filePath, "/\\")
	var nameWithExt string
	if index == -1 {
		nameWithExt = filePath
	} else {
		nameWithExt = filePath[index+1:]
	}
	return strings.Split(nameWithExt, ".")[0]
}

// IsBinaryFile 判断是否是二进制文件
func IsBinaryFile(path string) bool {
	file, err := os.Open(path)
	if err != nil {
		return false
	}
	defer file.Close()

	r := bufio.NewReader(file)
	buf := make([]byte, 1024)
	n, err := r.Read(buf)
	if n == 0 {
		return false
	}

	whiteByte := 0
	for i := 0; i < n; i++ {
		if (buf[i] >= 0x20 && buf[i] <= 0xff) || buf[i] == 9 || buf[i] == 10 || buf[i] == 13 {
			whiteByte++
		} else if buf[i] <= 6 || (buf[i] >= 14 && buf[i] <= 31) {
			return true
		}
	}
	if whiteByte >= 1 {
		return false
	}
	return true
}

// IsNeedFilterContent 根据文件内容判断是否需要过滤文件; 内容长度不能超过1MB，不能小于20字节
func IsNeedFilterContent(content []byte) bool {
	if len(content) > 1042*1024 || len(content) < 20 {
		return true
	}
	lines := strings.Split(string(content), "\n")
	// 过滤行数下于3行
	if len(lines) < 3 {
		return true
	}
	// 过滤单行超过512个字节
	for _, line := range lines {
		if len(line) > 2048 {
			return true
		}
	}
	return false
}

// IsLargeFileByPath 判断是否是超大文件
func IsLargeFileByPath(filePath string) bool {
	bytes, err := os.ReadFile(filePath)
	if err != nil {
		return false
	}
	return IsLargeFile(bytes)
}

// IsLargeFile 判断是否是超大文件
func IsLargeFile(content []byte) bool {
	if len(content) > 300000 {
		return true
	}
	lines := strings.Split(string(content), "\n")
	// 判断前3行是否单行过长
	for i := 0; i < 3 && i < len(lines); i++ {
		if len(lines[i]) > 2048 {
			return true
		}
	}
	return false
}

// ConvertPythonPath converts python module path to absolute path
func ConvertPythonPath(modulePath string, workspacePath string, currentPath string, ext string) (string, string) {
	// 获得头部点号的数量
	dotCount := 0
	for i := 0; i < len(modulePath); i++ {
		if modulePath[i] == '.' {
			dotCount += 1
		} else {
			break
		}
	}
	if dotCount == 0 {
		path := filepath.Join(workspacePath, strings.Replace(modulePath, ".", string(os.PathSeparator), -1))
		return findRealPythonFilePath(workspacePath, path, ext)
	}
	prefixSep := ""
	relativePath := modulePath[dotCount:]
	if dotCount == 1 {
		prefixSep = fmt.Sprintf(".%s", string(os.PathSeparator))
	} else if dotCount > 1 {
		upDirSep := fmt.Sprintf("..%s", string(os.PathSeparator))
		prefixSep = strings.Repeat(upDirSep, dotCount-1)
	}
	path := strings.Replace(relativePath, ".", string(os.PathSeparator), -1)
	realPath := prefixSep
	if path != "" {
		realPath += path
	}
	filePath := filepath.Join(filepath.Dir(currentPath), realPath)
	return findRealPythonFilePath(workspacePath, filePath, ext)
}

// findRealPythonFilePath 逐级向前查找真实存在的文件路径
func findRealPythonFilePath(workspacePath string, filePath string, ext string) (string, string) {
	subModulePath := ""
	filePath = strings.TrimPrefix(filePath, workspacePath)
	for i := 0; i < strings.Count(filePath, string(os.PathSeparator)); i++ {
		path := filepath.Join(workspacePath, filePath)
		if _, err := os.Stat(path); err == nil {
			return path, subModulePath
		}
		path = filepath.Join(workspacePath, filePath+ext)
		if _, err := os.Stat(path); err == nil {
			return path, subModulePath
		}
		idx := strings.LastIndexAny(filePath, "\\/")
		if idx != -1 {
			if subModulePath != "" {
				subModulePath = fmt.Sprintf("%s.%s", filePath[idx+1:], subModulePath)
			} else {
				subModulePath = filePath[idx+1:]
			}
			filePath = filePath[:idx]
		} else {
			break
		}
	}
	return "", subModulePath
}

// FindFileUntilRootOnce 从当前filePath开始查找指定文件路径（相对路径），直到workspace根目录，只要找到其中一个就返回
func FindFileUntilRootOnce(workspace string, filePath string, targetFilePaths []string) (string, error) {
	cacheKey := fmt.Sprintf("%s-%s-%s", workspace, filePath, strings.Join(targetFilePaths, "_"))
	if value, ok := fileFindCache.Get(cacheKey); ok {
		if valueStr, ok := value.(string); ok {
			if valueStr == "" {
				return "", errors.New("not found")
			}
			return valueStr, nil
		}
	}
	// 限制次数，避免意外死循环
	for i := 0; i < 10; i++ {
		dirPath := filepath.Dir(filePath)
		//log.Debugf("Searching file from %s for %v", dirPath, targetFilePaths)
		for _, targetFilePath := range targetFilePaths {
			targetFilePath = filepath.Join(dirPath, targetFilePath)
			if _, err := os.Stat(targetFilePath); err == nil {
				fileFindCache.Set(cacheKey, targetFilePath, cache.DefaultExpiration)
				return targetFilePath, nil
			}
		}
		rootPath := strings.TrimPrefix(dirPath, workspace)
		if rootPath == dirPath || dirPath == "" {
			return "", errors.New("not found")
		}
		rootPath = strings.Trim(rootPath, string(os.PathSeparator))
		if rootPath == "" || dirPath == workspace || rootPath == "/" || strings.HasSuffix(rootPath, ":\\") {
			fileFindCache.Set(cacheKey, "", cache.DefaultExpiration)
			return "", errors.New("not found")
		}
		filePath = dirPath
	}
	return "", errors.New("not found")
}

func GetFileContent(filePath string) ([]byte, error) {
	stat, err := os.Stat(filePath)
	if err != nil {
		return nil, err
	}
	if stat.IsDir() {
		return nil, errors.New("is dir")
	}
	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, err
	}
	return data, nil
}

func CollectFilesInDir(dirPath string, recursive bool, filterFileFn func(path string) bool) ([]string, error) {
	files := make([]string, 0)

	if err := filepath.WalkDir(dirPath, func(path string, d fs.DirEntry, err error) error {
		if d.IsDir() {
			if path != dirPath && !recursive {
				return filepath.SkipDir
			}
		} else {
			if !filterFileFn(path) {
				files = append(files, path)
			}
		}

		return nil
	}); err != nil {
		return nil, err
	}

	return files, nil
}

// CopyDir 复制 src 到 dst 目录
func CopyDir(srcDir, dstDir string) error {
	srcInfo, err := os.Stat(srcDir)
	if err != nil {
		return fmt.Errorf("stat: %w", err)
	}
	if !srcInfo.IsDir() {
		return errors.New("source is not a directory")
	}

	return filepath.Walk(srcDir, func(path string, info os.FileInfo, err error) error {
		// 生成目标路径
		relPath, err := filepath.Rel(srcDir, path)
		if err != nil {
			return err
		}
		dstPath := filepath.Join(dstDir, relPath)

		// 如果是目录，则创建对应的目录
		if info.IsDir() {
			err := os.MkdirAll(dstPath, os.ModePerm)
			if err != nil {
				return err
			}
		} else {
			// 如果是文件，则复制文件到目标目录
			err := CopyFile(path, dstPath)
			if err != nil {
				return err
			}
		}
		return nil
	})
}

// CopyFile 复制文件函数
func CopyFile(src, dst string) error {
	sourceFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer sourceFile.Close()

	destFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer destFile.Close()

	_, err = io.Copy(destFile, sourceFile)
	if err != nil {
		return err
	}
	err = destFile.Sync()
	if err != nil {
		return err
	}

	return nil
}

func NewFile(filePath string, content string) error {
	if _, err := os.Stat(filepath.Dir(filePath)); os.IsNotExist(err) {
		dirPath := filepath.Dir(filePath)
		if err := os.MkdirAll(dirPath, os.ModePerm); err != nil {
			return err
		}
	}
	file, err := os.Create(filePath)
	if err != nil {
		return err
	}
	defer file.Close()
	if _, err := file.WriteString(content); err != nil {
		return err
	}
	return nil
}

// 递归记录新创建的目录
func mkdirAllWithRecord(path string, perm os.FileMode, createdDirs *[]string) error {
	absPath, err := filepath.Abs(path)
	if err != nil {
		return err
	}

	// 递归到上级目录
	parent := filepath.Dir(absPath)
	if parent != absPath { // 防止根目录死循环
		if _, err := os.Stat(parent); os.IsNotExist(err) {
			// 递归创建父目录
			if err := mkdirAllWithRecord(parent, perm, createdDirs); err != nil {
				return err
			}
		}
	}

	// 检查当前目录是否存在
	if _, err := os.Stat(absPath); os.IsNotExist(err) {
		if err := os.Mkdir(absPath, perm); err != nil {
			return err
		}
		*createdDirs = append(*createdDirs, absPath)
	}
	return nil
}

func clearDirs(createdDirs []string) {
	// 逆序删除（从子目录到父目录）
	for i := len(createdDirs) - 1; i >= 0; i-- {
		os.Remove(createdDirs[i])
	}
}

// 是否可以创建指定路径文件
func CanNewFile(filePath string) bool {
	// 如果路径存在，则认为可以创建/修改
	if FileExists(filePath) {
		return true
	}
	createdDirs := []string{}
	defer clearDirs(createdDirs)
	if _, err := os.Stat(filepath.Dir(filePath)); os.IsNotExist(err) {
		dirPath := filepath.Dir(filePath)
		if err := mkdirAllWithRecord(dirPath, os.ModePerm, &createdDirs); err != nil {
			return false
		}
	}
	file, err := os.Create(filePath)
	if err != nil {
		return false
	}
	defer os.Remove(filePath)
	defer file.Close()
	return true
}

// CountLine CountLine查找字符串中某个offset之前的行数
func CountLine(code string, offset int) int {
	lineNum := 0
	for i := 0; i < len(code) && i < (offset) && offset > 0; i++ {
		if code[i] == '\n' {
			lineNum++
		}
	}
	return lineNum
}

// ExtractEmptyCharacterBefore 找到offset之前的连续空字符
func ExtractEmptyCharacterBefore(code string, offset int) string {
	if offset > len(code) {
		offset = len(code)
	}
	ed := offset
	offset--
	for offset >= 0 && (code[offset] == '\t' || code[offset] == ' ') {
		offset--
	}
	offset++
	return code[offset:ed]
}

func ReadFileContentByFilePath(filePath string, maxReadSize int64) (string, error) {
	// 打开文件
	file, err := os.Open(filePath)
	if err != nil {
		log.Errorf("failed to open file: %v", err)
		return "", err
	}
	defer file.Close()

	// 读取文件内容，最多读取maxReadSize大小的内容
	if maxReadSize <= 0 {
		maxReadSize = 16 * 1024
	}
	buffer := make([]byte, maxReadSize)
	n, err := file.Read(buffer)
	if err != nil && err != io.EOF {
		log.Errorf("failed to read file: %v", err)
		return "", err
	}

	return string(buffer[:n]), nil
}

// ReadLimitedChars reads up to the specified number of characters from a file.
// If the file contains fewer characters than the limit, it returns all characters.
// The count is based on characters, not bytes.
func ReadLimitedChars(filePath string, charLimit int) (string, error) {
	// Open the file
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	// Create a buffered reader
	reader := bufio.NewReader(file)

	// Buffer to store the result
	var result []rune
	count := 0

	// Read runes (characters) one by one
	for count < charLimit {
		r, _, err := reader.ReadRune()
		if err != nil {
			// If we reached EOF, it's not an error for our purpose
			if err == io.EOF {
				break
			}
			return "", err
		}

		// Add the rune to our result and increment the count
		result = append(result, r)
		count++
	}

	// Convert rune slice back to string
	return string(result), nil
}

// GetCodeRange 获取指定行范围的代码
func GetCodeRange(content string, startLine, endLine int) string {
	lines := strings.Split(content, "\n")
	return GetCodeRangeFromLines(lines, startLine, endLine)
}

// GetCodeRangeFromLines 获取指定行范围的代码
func GetCodeRangeFromLines(lines []string, startLine, endLine int) string {
	if len(lines) == 0 {
		return ""
	}
	if startLine > endLine {
		return ""
	}
	if startLine < 0 {
		startLine = 0
	}
	if endLine >= len(lines) {
		endLine = len(lines) - 1
	}

	// 截取指定范围的行
	selectedLines := lines[startLine : endLine+1]
	return strings.Join(selectedLines, "\n")
}

// AppendToFileBuffered 追加内容到文件，使用缓冲区
func AppendToFileBuffered(filename string, content string) error {
	f, err := os.OpenFile(filename, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		return err
	}
	defer f.Close()

	writer := bufio.NewWriter(f)
	if _, err := writer.WriteString(content); err != nil {
		return err
	}

	// 确保将缓冲区的内容写入文件
	return writer.Flush()
}

func ReplaceFileWithSpecificLines(fileContent string, startLine, endLine int, replaceCode string) string {
	//替换fileContent中的startLine和endLine为codeToRewrite
	lines := strings.Split(fileContent, "\n")
	// 获取需要替换的行
	beforeLines := lines[:startLine]
	afterLines := lines[endLine+1:]

	// 构建新的文件内容
	newLines := make([]string, 0)
	newLines = append(newLines, beforeLines...)
	newLines = append(newLines, strings.Split(replaceCode, "\n")...)
	newLines = append(newLines, afterLines...)

	// 更新文件内容
	fileContent = strings.Join(newLines, "\n")

	return fileContent
}

// 按行从后往前截断文件，日志文件场景能保留最新的
func TailTruncateFileByLines(file string, targetSize int64) error {
	// 检查文件是否存在
	fileInfo, err := os.Stat(file)
	if err != nil {
		log.Errorf("failed to get file info: %v", err)
		return err
	}

	// 如果文件已经小于等于目标大小，无需截断
	if fileInfo.Size() <= targetSize {
		return nil
	}

	// 读取文件所有内容
	content, err := os.ReadFile(file)
	if err != nil {
		log.Errorf("failed to read file: %v", err)
		return err
	}

	// 按行分割
	lines := strings.Split(string(content), "\n")
	if len(lines) == 0 {
		return nil
	}

	// 从后往前累计行的大小，找到截断点
	var truncatedLines []string
	var currentSize int64 = 0

	// 从最后一行开始往前遍历
	for i := len(lines) - 1; i >= 0; i-- {
		lineWithNewline := lines[i]
		if i < len(lines)-1 {
			lineWithNewline += "\n" // 添加换行符，除了最后一行
		}

		lineSize := int64(len([]byte(lineWithNewline)))

		// 如果加上这一行会超过目标大小，则停止
		if currentSize+lineSize > targetSize {
			break
		}

		// 在前面插入这一行
		truncatedLines = append([]string{lines[i]}, truncatedLines...)
		currentSize += lineSize
	}

	// 如果没有任何行可以保留（第一行就超过目标大小），至少保留最后一行
	if len(truncatedLines) == 0 && len(lines) > 0 {
		truncatedLines = []string{lines[len(lines)-1]}
	}

	// 将截断后的内容写回文件
	truncatedContent := strings.Join(truncatedLines, "\n")
	err = os.WriteFile(file, []byte(truncatedContent), fileInfo.Mode())
	if err != nil {
		log.Errorf("failed to write truncated file: %v", err)
		return err
	}

	log.Infof("file %s truncated from %d bytes to %d bytes (%d lines)",
		file, fileInfo.Size(), int64(len([]byte(truncatedContent))), len(truncatedLines))

	return nil
}

func ReadJsonFile[T any](filePath string) (*T, error) {
	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, err
	}

	var entity T
	err = json.Unmarshal(data, &entity)
	if err != nil {
		return nil, err
	}

	return &entity, nil
}

func WriteJsonFile[T any](filePath string, entity *T) error {
	data, err := json.Marshal(entity)
	if err != nil {
		return err
	}

	return os.WriteFile(filePath, data, 0644)
}

// CopyFileWithTempFile
// 更安全的复制，src -> copy tmp file -> rename tmp file
// 防止复制的文件内容不完整
func CopyFileWithTempFile(src, dst string) error {
	// 打开源文件
	sourceFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer sourceFile.Close()

	// 创建目标临时文件
	fileName := filepath.Base(dst)
	destTempPath := filepath.Join(filepath.Dir(dst), "tmp"+"-"+fileName)
	destTempFile, err := os.Create(destTempPath)
	if err != nil {
		return err
	}
	destTempFileClosed := false
	defer func() {
		if !destTempFileClosed {
			if err := destTempFile.Close(); err != nil {
				log.Errorf("close destTempFile failed: %v", err)
			}
		}
	}()

	// 使用io.Copy复制文件内容
	_, err = io.Copy(destTempFile, sourceFile)
	if err != nil {
		return err
	}

	// 复制完成后关闭临时文件
	err = destTempFile.Close()
	if err != nil {
		return err
	}
	destTempFileClosed = true

	// 重命名临时文件
	if err := os.Rename(destTempFile.Name(), dst); err != nil {
		return err
	}
	return nil
}
