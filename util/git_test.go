package util

import "testing"

func TestGetWorkspaceFileChanges(t *testing.T) {
	workspace := "/Users/<USER>/code/vllm"
	fileChanges, err := GetWorkspaceFileChanges(workspace)
	if err != nil {
		t.Fatalf("failed to get workspace file changes: %v", err)
	}
	for _, fileChange := range fileChanges {
		t.Logf("---%s %s---", fileChange.FilePath, fileChange.EditMode)
		t.Logf("origin: %v", fileChange.OriginalContent)
		t.Logf("modified: %v", fileChange.ModifiedContent)
	}
}
