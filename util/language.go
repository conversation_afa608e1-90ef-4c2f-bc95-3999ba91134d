package util

import (
	"context"
	"cosy/definition"
	"cosy/util/lang"
	"path/filepath"
	"strings"
)

type LangTypeSet map[string]bool

var GoStdPackages = []string{
	"archive", "bufio", "builtin", "bytes", "cmp", "compress", "container", "context", "crypto", "database",
	"debug", "embed", "encoding", "errors", "expvar", "flag", "fmt", "go", "hash", "html", "image", "index",
	"io", "log", "maps", "math", "mime", "net", "os", "path", "plugin", "reflect", "regexp", "runtime",
	"slices", "sort", "strconv", "strings", "sync", "syscall", "testing", "text", "time", "unicode", "unsafe",
}
var GoStdPackageSet map[string]struct{}

func init() {
	GoStdPackageSet = make(map[string]struct{}, len(GoStdPackages))
	for _, pkg := range GoStdPackages {
		GoStdPackageSet[pkg] = struct{}{}
	}
}

var (
	JavaKeywords = []string{"abstract", "assert", "boolean", "break", "byte",
		"case", "catch", "char", "class", "const", "continue", "default",
		"do", "double", "else", "enum", "extends", "false", "final", "finally",
		"float", "for", "goto", "if", "implements", "import", "instanceof",
		"int", "interface", "long", "native", "new", "null", "package",
		"private", "protected", "public", "return", "short", "static", "strictfp",
		"super", "switch", "synchronized", "this", "throw", "throws",
		"transient", "try", "true", "void", "volatile", "while"}
	JavaBuiltinMethods = []string{"getClass(", "hashCode(", "equals(", "clone(", "notify(", "notifyAll(", "wait(", "finalize(", "toString("}
	RelateLanguage     = buildRelateLanguage()
	UntitledLanguage   = "untitled"
	AllLanguages       = []string{
		definition.AppleScript,
		definition.Assembly,
		definition.Awk,
		definition.Batch,
		definition.C_Cpp,
		definition.CSharp,
		definition.Clojure,
		definition.CMake,
		definition.CoffeeScript,
		definition.CommonLisp,
		definition.CSS,
		definition.CUDA,
		definition.Dart,
		definition.Dockerfile,
		definition.Elixir,
		definition.Erlang,
		definition.FSharp,
		definition.Golang,
		definition.Groovy,
		definition.Haskell,
		definition.HTML,
		definition.Java,
		definition.JSP,
		definition.JavaScript,
		definition.JSON,
		definition.Julia,
		definition.Kotlin,
		definition.Lua,
		definition.Makefile,
		definition.Maple,
		definition.Markdown,
		definition.Mathematica,
		definition.MATLAB,
		definition.OCaml,
		definition.Perl,
		definition.PHP,
		definition.PowerShell,
		definition.Prolog,
		definition.ProtocolBuffer,
		definition.Python,
		definition.R,
		definition.RestructuredText,
		definition.RMarkdown,
		definition.Ruby,
		definition.Rust,
		definition.SAS,
		definition.Scala,
		definition.Shell,
		definition.SQL,
		definition.Tcsh,
		definition.Thrift,
		definition.Toml,
		definition.TypeScript,
		definition.Vue,
		definition.VHDL,
		definition.VisualBasic,
		definition.Yacc,
		definition.YAML,
		definition.XML,
		definition.ANTLR,
		definition.PlantUML,
	}

	// 语言内置类型
	LangDataTypes = map[string]LangTypeSet{
		definition.Java: {
			"void":                           true,
			"boolean":                        true,
			"byte":                           true,
			"char":                           true,
			"short":                          true,
			"int":                            true,
			"long":                           true,
			"float":                          true,
			"double":                         true,
			"String":                         true,
			"Long":                           true,
			"Integer":                        true,
			"Double":                         true,
			"Boolean":                        true,
			"Character":                      true,
			"Byte":                           true,
			"Short":                          true,
			"Float":                          true,
			"StringBuffer":                   true,
			"StringBuilder":                  true,
			"ArrayList":                      true,
			"HashMap":                        true,
			"HashSet":                        true,
			"LinkedList":                     true,
			"LinkedHashMap":                  true,
			"LinkedHashSet":                  true,
			"TreeMap":                        true,
			"TreeSet":                        true,
			"Date":                           true,
			"Calendar":                       true,
			"SimpleDateFormat":               true,
			"Random":                         true,
			"Math":                           true,
			"System":                         true,
			"Collections":                    true,
			"Arrays":                         true,
			"List":                           true,
			"Set":                            true,
			"Map":                            true,
			"Iterator":                       true,
			"Comparator":                     true,
			"Comparable":                     true,
			"Cloneable":                      true,
			"Serializable":                   true,
			"Runnable":                       true,
			"Thread":                         true,
			"ThreadGroup":                    true,
			"ThreadLocal":                    true,
			"StackTraceElement":              true,
			"StackTrace":                     true,
			"Stack":                          true,
			"StackFrame":                     true,
			"StackFrameInfo":                 true,
			"StackFrameInfoList":             true,
			"Object":                         true,
			"Exception":                      true,
			"Throwable":                      true,
			"Error":                          true,
			"RuntimeException":               true,
			"NullPointerException":           true,
			"ClassCastException":             true,
			"ArrayIndexOutOfBoundsException": true,
			"IllegalArgumentException":       true,
			"IllegalStateException":          true,
			"IndexOutOfBoundsException":      true,
			"NoSuchElementException":         true,
			"NoSuchFieldException":           true,
			"NoSuchMethodException":          true,
			"UnsupportedOperationException":  true,
			"ArithmeticException":            true,
			"IllegalThreadStateException":    true,
			"InterruptedException":           true,
			"ClassNotFoundException":         true,
			"IOException":                    true,
			"FileNotFoundException":          true,
			"EOFException":                   true,
			"UnsupportedEncodingException":   true,
			"CloneNotSupportedException":     true,
			"IllegalAccessException":         true,
			"InstantiationException":         true,
			"InvocationTargetException":      true,
		},
		definition.Golang: {
			"void":    true,
			"bool":    true,
			"byte":    true,
			"int8":    true,
			"int16":   true,
			"int32":   true,
			"int64":   true,
			"uint8":   true,
			"uint16":  true,
			"uint32":  true,
			"uint64":  true,
			"int":     true,
			"uint":    true,
			"uintptr": true,
			"float":   true,
			"float32": true,
			"float64": true,
			"string":  true,
			"error":   true,
			"map":     true,
			"any":     true,
		},
		definition.C_Cpp: {
			"void":        true,
			"enum":        true,
			"byte":        true,
			"char":        true,
			"short":       true,
			"int":         true,
			"long":        true,
			"float":       true,
			"double":      true,
			"int64_t":     true,
			"int32_t":     true,
			"int16_t":     true,
			"int8_t":      true,
			"uint64_t":    true,
			"uint32_t":    true,
			"uint16_t":    true,
			"uint8_t":     true,
			"size_t":      true,
			"ssize_t":     true,
			"off_t":       true,
			"ptrdiff_t":   true,
			"time_t":      true,
			"clock_t":     true,
			"intptr_t":    true,
			"uintptr_t":   true,
			"intmax_t":    true,
			"uintmax_t":   true,
			"int8":        true,
			"int16":       true,
			"int32":       true,
			"int64":       true,
			"uint8":       true,
			"uint16":      true,
			"uint32":      true,
			"uint64":      true,
			"vector":      true,
			"std::vector": true,
		},
		definition.CSharp: {
			"System":  true,
			"sbyte":   true,
			"byte":    true,
			"short":   true,
			"ushort":  true,
			"int":     true,
			"uint":    true,
			"long":    true,
			"ulong":   true,
			"float":   true,
			"double":  true,
			"decimal": true,
			"bool":    true,
			"char":    true,
			"string":  true,
			"String":  true,
			"object":  true,
			"dynamic": true,
			"void":    true,
			"Task":    true,
			"Action":  true,
			"Func":    true,
		},
		definition.Python: {
			"None":   true,
			"True":   true,
			"False":  true,
			"bool":   true,
			"byte":   true,
			"char":   true,
			"short":  true,
			"int":    true,
			"long":   true,
			"float":  true,
			"double": true,
			"string": true,
			"list":   true,
			"dict":   true,
			"set":    true,
		},
		definition.Kotlin: {
			"Int":           true,
			"Long":          true,
			"Short":         true,
			"Byte":          true,
			"Float":         true,
			"Double":        true,
			"Char":          true,
			"String":        true,
			"Boolean":       true,
			"StringBuffer":  true,
			"StringBuilder": true,
			"ArrayList":     true,
			"HashMap":       true,
			"HashSet":       true,
			"LinkedList":    true,
			"LinkedHashMap": true,
			"LinkedHashSet": true,
			"TreeMap":       true,
			"TreeSet":       true,
			"Date":          true,
		},
	}

	// LangCommonImportPrefixes 语言通用导入包前缀
	LangCommonImportPrefixes = map[string][]string{
		definition.Java: {
			"java.",
			"javax.",
			"javafx.",
			"sun.",
			"org.apache.",
			"org.springframework.",
			"lombok.",
			"org.projectlombok",
			"org.aspectj.",
			"org.hibernate.",
			"org.junit.",
			"org.mockito.",
			"org.powermock.",
			"org.testng.",
			"org.jmock.",
			"org.slf4j",
			"ch.qos.logback",
			"kotlin.",
			"com.fasterxml.",
			"com.alibaba.fastjson",
			"org.mockito",
			"org.mybatis.",
			"io.swagger.",
			"io.springfox.",
			"com.github.swaggerdoc.",
			"org.springdoc.",
			"net.logstash.logback",
			"android.",
			"androidx.",
		},
	}

	LangCommonImportFilters = map[string]func(importContent string) bool{
		definition.Java:   IsJavaCommonImport,
		definition.Golang: IsGoCommonImport,
	}
)

func GetLanguageByFilePath(uri string) string {
	if lang.LangTypeMapping.IsValid() {
		fileName := filepath.Base(uri)
		return lang.LangTypeMapping.GetLanguages(fileName)
	}
	return GetLanguageExtByFilePath(uri, false)
}

// GetLanguageExtByFilePath 通过文件路径获取语言类型
// - useOther 是否使用其他语言，如果是false时，遇到其他无法识别的扩展名时，将返回扩展名；如果是true，则返回others
func GetLanguageExtByFilePath(uri string, useOther bool) string {
	name := strings.ToLower(uri)
	switch {
	case strings.HasSuffix(name, ".ada") || strings.HasSuffix(name, ".ads") || strings.HasSuffix(name, ".adb"):
		return definition.Ada
	case strings.HasSuffix(name, ".agda"):
		return definition.Agda
	case strings.HasSuffix(name, ".als"):
		return definition.Alloy
	case strings.HasSuffix(name, ".g"):
		return definition.ANTLR
	case strings.HasSuffix(name, ".applescript"):
		return definition.AppleScript
	case strings.HasSuffix(name, ".asm"):
		return definition.Assembly
	case strings.HasSuffix(name, ".aug"):
		return definition.Augeas
	case strings.HasSuffix(name, ".awk"):
		return definition.Awk
	case strings.HasSuffix(name, ".bat") || strings.HasSuffix(name, ".cmd"):
		return definition.Batch
	case strings.HasSuffix(name, ".bsv"):
		return definition.BlueSpec
	case strings.HasSuffix(name, ".cs"):
		return definition.CSharp
	case strings.HasSuffix(name, ".clj"):
		return definition.Clojure
	case strings.HasSuffix(name, ".cmake"):
		return definition.CMake
	case strings.HasSuffix(name, ".coffee"):
		return definition.CoffeeScript
	case strings.HasSuffix(name, ".lisp") || strings.HasSuffix(name, ".cl") || strings.HasSuffix(name, ".lsp"):
		return definition.CommonLisp
	case strings.HasSuffix(name, ".cpp") || strings.HasSuffix(name, ".cxx") || strings.HasSuffix(name, ".cc") ||
		strings.HasSuffix(name, ".hpp") || strings.HasSuffix(name, ".c") || strings.HasSuffix(name, ".h"):
		return definition.C_Cpp
	case strings.HasSuffix(name, ".css") || strings.HasSuffix(name, ".scss"):
		return definition.CSS
	case strings.HasSuffix(name, ".cu"):
		return definition.CUDA
	case strings.HasSuffix(name, ".dart"):
		return definition.Dart
	case strings.HasSuffix(name, "dockerfile"):
		return definition.Dockerfile
	case strings.HasSuffix(name, ".ex") || strings.HasSuffix(name, ".exs"):
		return definition.Elixir
	case strings.HasSuffix(name, ".elm"):
		return definition.Elm
	case strings.HasSuffix(name, ".el"):
		return definition.EmacsLisp
	case strings.HasSuffix(name, ".erl"):
		return definition.Erlang
	case strings.HasSuffix(name, ".fs"):
		return definition.FSharp
	case strings.HasSuffix(name, ".f90") || strings.HasSuffix(name, ".f"):
		return definition.Fortran
	case strings.HasSuffix(name, ".glsl"):
		return definition.GLSL
	case strings.HasSuffix(name, ".go"):
		return definition.Golang
	case strings.HasSuffix(name, ".groovy"):
		return definition.Groovy
	case strings.HasSuffix(name, ".hs"):
		return definition.Haskell
	case strings.HasSuffix(name, ".html") || strings.HasSuffix(name, ".htm") || strings.HasSuffix(name, ".phtml"):
		return definition.HTML
	case strings.HasSuffix(name, ".idr"):
		return definition.Idris
	case strings.HasSuffix(name, ".thy"):
		return definition.Isabelle
	case strings.HasSuffix(name, ".java"):
		return definition.Java
	case strings.HasSuffix(name, ".jsp"):
		return definition.JSP
	case strings.HasSuffix(name, ".js") || strings.HasSuffix(name, ".jsx") || strings.HasSuffix(name, ".mjs"):
		return definition.JavaScript
	case strings.HasSuffix(name, ".json"):
		return definition.JSON
	case strings.HasSuffix(name, ".jl"):
		return definition.Julia
	case strings.HasSuffix(name, ".kt") || strings.HasSuffix(name, ".kts"):
		return definition.Kotlin
	case strings.HasSuffix(name, ".lean"):
		return definition.Lean
	case strings.HasSuffix(name, ".lua"):
		return definition.Lua
	case strings.HasSuffix(name, "makefile"):
		return definition.Makefile
	case strings.HasSuffix(name, ".mpl"):
		return definition.Maple
	case strings.HasSuffix(name, ".md") || strings.HasSuffix(name, ".markdown"):
		return definition.Markdown
	case strings.HasSuffix(name, ".m"):
		return definition.Mathematica
	case strings.HasSuffix(name, ".matlab"):
		return definition.MATLAB
	case strings.HasSuffix(name, ".ml") || strings.HasSuffix(name, ".mli"):
		return definition.OCaml
	case strings.HasSuffix(name, ".pas"):
		return definition.Pascal
	case strings.HasSuffix(name, ".pl"):
		return definition.Perl
	case strings.HasSuffix(name, ".php"):
		return definition.PHP
	case strings.HasSuffix(name, ".ps1") || strings.HasSuffix(name, ".psm1") || strings.HasSuffix(name, ".psd1"):
		return definition.PowerShell
	case strings.HasSuffix(name, ".pl"):
		return definition.Prolog
	case strings.HasSuffix(name, ".proto"):
		return definition.ProtocolBuffer
	case strings.HasSuffix(name, ".py"):
		return definition.Python
	case strings.HasSuffix(name, ".r"):
		return definition.R
	case strings.HasSuffix(name, ".rkt"):
		return definition.Racket
	case strings.HasSuffix(name, ".rst"):
		return definition.RestructuredText
	case strings.HasSuffix(name, ".rmd"):
		return definition.RMarkdown
	case strings.HasSuffix(name, ".rb"):
		return definition.Ruby
	case strings.HasSuffix(name, ".rs"):
		return definition.Rust
	case strings.HasSuffix(name, ".sas"):
		return definition.SAS
	case strings.HasSuffix(name, ".scala"):
		return definition.Scala
	case strings.HasSuffix(name, ".scm") || strings.HasSuffix(name, ".ss") || strings.HasSuffix(name, ".sls") || strings.HasSuffix(name, ".sps"):
		return definition.Scheme
	case strings.HasSuffix(name, ".sh"):
		return definition.Shell
	case strings.HasSuffix(name, ".st"):
		return definition.Smalltalk
	case strings.HasSuffix(name, ".sol"):
		return definition.Solidity
	case strings.HasSuffix(name, ".sparql"):
		return definition.SPARQL
	case strings.HasSuffix(name, ".sql"):
		return definition.SQL
	case strings.HasSuffix(name, ".stan"):
		return definition.Stan
	case strings.HasSuffix(name, ".sml"):
		return definition.StandardML
	case strings.HasSuffix(name, ".do"):
		return definition.Stata
	case strings.HasSuffix(name, ".sv"):
		return definition.SystemVerilog
	case strings.HasSuffix(name, ".tcl"):
		return definition.Tcl
	case strings.HasSuffix(name, ".csh") || strings.HasSuffix(name, ".tcsh"):
		return definition.Tcsh
	case strings.HasSuffix(name, ".tex"):
		return definition.TeX
	case strings.HasSuffix(name, ".thrift"):
		return definition.Thrift
	case strings.HasSuffix(name, ".toml"):
		return definition.Toml
	case strings.HasSuffix(name, ".ts") || strings.HasSuffix(name, ".tsx"):
		return definition.TypeScript
	case strings.HasSuffix(name, ".vue"):
		return definition.Vue
	case strings.HasSuffix(name, ".v"):
		return definition.Verilog
	case strings.HasSuffix(name, ".vhd") || strings.HasSuffix(name, ".vhdl"):
		return definition.VHDL
	case strings.HasSuffix(name, ".vb"):
		return definition.VisualBasic
	case strings.HasSuffix(name, ".xsl") || strings.HasSuffix(name, ".xslt"):
		return definition.XSLT
	case strings.HasSuffix(name, ".y"):
		return definition.Yacc
	case strings.HasSuffix(name, ".yaml") || strings.HasSuffix(name, ".yml"):
		return definition.YAML
	case strings.HasSuffix(name, ".xml"):
		return definition.XML
	case strings.HasSuffix(name, ".ftl"):
		return definition.FreeMarker
	case strings.HasSuffix(name, ".vm"):
		return definition.Velocity
	case strings.HasSuffix(name, ".ets"):
		return definition.Arkts
	case strings.HasSuffix(name, ".puml") || strings.HasSuffix(name, ".plantuml"):
		return definition.PlantUML
	case strings.HasSuffix(name, ".atg"):
		return definition.Coco
	case strings.HasSuffix(name, ".boo"):
		return definition.Boo
	case strings.HasSuffix(name, ".asp") || strings.HasSuffix(name, ".aspx") || strings.HasSuffix(name, ".asax") ||
		strings.HasSuffix(name, ".asmx") || strings.HasSuffix(name, ".master") || strings.HasSuffix(name, ".ascx"):
		return definition.ASP_XHTML
	case strings.HasSuffix(name, ".diff") || strings.HasSuffix(name, ".patch"):
		return definition.Patch
	case strings.HasSuffix(name, ".as"):
		return definition.ActionScript
	case strings.HasSuffix(name, ".fx"):
		return definition.HLSL
	case strings.HasSuffix(name, ".cfg") || strings.HasSuffix(name, ".conf") || strings.HasSuffix(name, ".ini") || strings.HasSuffix(name, ".iss"):
		return definition.Ini
	case strings.HasSuffix(name, ".nut"):
		return definition.Squirrel
	case strings.HasSuffix(name, ".properties"):
		return definition.Properties
	case strings.HasSuffix(name, ".ipynb"):
		return definition.IPYNB
	case strings.HasSuffix(name, ".vtl") || strings.HasSuffix(name, ".vm"):
		return definition.VTL
	case strings.HasSuffix(name, ".txt"):
		return definition.Txt
	case strings.HasSuffix(name, ".log"):
		return definition.PlainText
	default:
		return GetFileLanguageSuffix(name, useOther)
	}
}

// GetFileLanguageSuffix 根据file全路径解析文件名
func GetFileLanguageSuffix(uri string, useOther bool) string {
	if len(uri) <= 0 {
		return ""
	}
	if isVscodeUntitled(uri) {
		return definition.Untitled
	}
	var fileName = ""
	if slashIndex := strings.LastIndex(uri, string(filepath.Separator)); slashIndex >= 0 {
		fileName = uri[slashIndex:]
	} else {
		fileName = uri
	}
	if index := strings.LastIndex(fileName, "."); index >= 0 {
		// 存在扩展名
		if useOther {
			return definition.Others
		} else {
			return fileName[index+1:]
		}
	}
	if containsIgnoreCase(fileName, "dockerfile") {
		return definition.Dockerfile
	} else if containsIgnoreCase(fileName, "makefile") {
		return definition.Makefile
	}
	return definition.PlainText
}

// vscode 新建空文件 Untitled-1，Untitled-2
func isVscodeUntitled(uri string) bool {
	return strings.HasPrefix(uri, "untitled")
}

func containsIgnoreCase(fileName, fileIdentifier string) bool {
	if len(fileName) > 0 && len(fileIdentifier) > 0 {
		return strings.Contains(strings.ToLower(fileName), strings.ToLower(fileIdentifier))
	}
	return false
}

func GetLanguageComment(language string) string {
	switch language {
	case definition.Batch, definition.CMake, definition.Python,
		definition.Dockerfile, definition.Lua, definition.Makefile, definition.SQL:
		return "--"
	}
	return "//"
}

func buildRelateLanguage() map[string][]string {
	relateLanguage := make(map[string][]string)
	relateLanguage[definition.JavaScript] = []string{definition.CSS, definition.TypeScript}
	relateLanguage[definition.TypeScript] = []string{definition.CSS, definition.JavaScript}
	relateLanguage[definition.Vue] = []string{definition.CSS, definition.JavaScript, definition.TypeScript}
	relateLanguage[definition.JSP] = []string{definition.Java}
	relateLanguage[definition.XML] = []string{definition.Java, definition.Python}
	relateLanguage[definition.Java] = []string{definition.YAML, definition.Properties}
	relateLanguage[definition.Vue] = []string{definition.CSS, definition.JavaScript, definition.TypeScript}
	relateLanguage[definition.HTML] = []string{definition.CSS, definition.JavaScript, definition.TypeScript}
	relateLanguage[definition.IPYNB] = []string{definition.Python}
	return relateLanguage
}

// GetRelateLanguage 获得相关的语言
func GetRelateLanguage(language string) map[string]bool {
	result := map[string]bool{}
	if langs, ok := RelateLanguage[language]; ok {
		for _, lang := range langs {
			result[lang] = true
		}
	}
	return result
}

// GetAllLanguage 获得所有语言
func GetAllLanguage(excludes []string) []string {
	result := []string{}
	for _, lang := range AllLanguages {
		ignore := false
		for _, exclude := range excludes {
			if lang == exclude {
				ignore = true
				break
			}
		}
		if ignore {
			continue
		}
		result = append(result, lang)
	}
	return result
}

// GetNormFilePath 获得归一化的文件路径
// 1. 如果是class文件，则判断路径是否包含.jar，如果包含，则从.jar之后最近的一个文件路径分隔符开始截断
// 2. 如果是class后缀，则将后缀名替换为java
func GetNormFilePath(filePath string) string {
	if strings.HasSuffix(filePath, ".class") {
		idx := strings.LastIndex(filePath, ".jar")
		if idx >= 0 {
			filePath = strings.TrimLeft(filePath[idx+4:], "/\\!:")
		}
		filePath = strings.ReplaceAll(filePath, ".class", ".java")
	}
	return filePath
}

// IsLangCommonImport 判断是否是常用三方包的导入
func IsLangCommonImport(lang string, importContent string) bool {
	if fn, ok := LangCommonImportFilters[lang]; ok {
		return fn(importContent)
	}

	return false
}

func IsJavaCommonImport(importContent string) bool {
	if commonImportItems, ok := LangCommonImportPrefixes[definition.Java]; ok {
		for _, commonImportItem := range commonImportItems {
			if strings.HasPrefix(importContent, commonImportItem) {
				return true
			}
		}
	}

	return false
}

func IsGoCommonImport(importContent string) bool {
	if importContent == "" {
		return true
	}

	res := strings.Split(importContent, "/")
	if _, ok := GoStdPackageSet[res[0]]; ok {
		return true
	}

	return false
}

// IsJavaIgnoreToken 判断是否是java语言中需要忽略的token
func IsJavaIgnoreToken(token string) bool {
	if typeSet, ok := LangDataTypes[definition.Java]; ok {
		if _, ok := typeSet[token]; ok {
			return true
		}
	}
	for _, k := range JavaKeywords {
		if strings.HasPrefix(k, token) {
			return true
		}
	}
	return false
}

// GetIndexedImportSet 找出所有已经建立索引的包
func GetIndexedImportSet(ctx context.Context, lang string, importDefs map[string]string) map[string]struct{} {
	switch lang {
	case definition.Golang:
		indexedImportSet := make(map[string]struct{})
		for shortName, _ := range importDefs {
			// NB: for golang, we only need the portion of the path before the first dot ('.').
			importName, _, _ := strings.Cut(shortName, ".")
			if importName == "" {
				continue
			}
			indexedImportSet[importName] = struct{}{}
		}

		return indexedImportSet
	default:
		indexedImportSet := make(map[string]struct{}, len(importDefs))
		for shortName, _ := range importDefs {
			indexedImportSet[shortName] = struct{}{}
		}
		return indexedImportSet
	}
}
