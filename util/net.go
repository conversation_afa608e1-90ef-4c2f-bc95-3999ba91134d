package util

import (
	"cosy/definition"
	"cosy/log"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
)

var (
	machineIp = ""
)

func GetWebsocketPortRange() (int, int) {
	return getPortRange(definition.CosyWebSocketDefaultPort, "LINGMA_WEBSOCKET_PORT")
}

func GetHttpPortRange() (int, int) {
	return getPortRange(definition.CosyHttpDefaultPort, "LINGMA_HTTP_PORT")
}

// getPortRange 获取端口范围
func getPortRange(preferredPort int, environmentVariable string) (int, int) {
	firstPort := preferredPort
	lastPort := 0
	preferredPortRange := os.Getenv(environmentVariable)
	if strings.Contains(preferredPortRange, "-") {
		// 如果环境变量包含数值范围，则解析为端口范围
		parts := strings.SplitN(preferredPortRange, "-", 2)
		if len(parts) == 2 {
			if port, err := strconv.Atoi(parts[0]); err == nil {
				firstPort = port
			}
			if port, err := strconv.Atoi(parts[1]); err == nil {
				lastPort = port
			}
		}
	} else if preferredPortRange != "" {
		// 如果环境变量是单个数值，则直接解析为端口
		if port, err := strconv.Atoi(preferredPortRange); err == nil {
			firstPort = port
		}
	}
	if lastPort < firstPort {
		// 如果范围不合法，重新规划结束端口值
		lastPort = firstPort + 999
	}
	return firstPort, lastPort
}

func GetFreePort(preferredPort, firstPort, lastPort int, retryTimes int) int {
	// 尝试默认端口
	if preferredPort > 0 {
		port, ok := tryPort(preferredPort)
		if ok {
			return port
		}
	}
	// 尝试找到一个可用端口
	for i := 0; i < retryTimes; i++ {
		port, ok := tryPort(randomInt(firstPort, lastPort))
		if ok {
			return port
		}
	}
	return 0
}

func tryPort(port int) (int, bool) {
	ln, err := net.Listen("tcp", "127.0.0.1:"+strconv.Itoa(port))
	if err == nil {
		err = ln.Close()
		if err == nil {
			return ln.Addr().(*net.TCPAddr).Port, true
		}
	}
	return 0, false
}

// GetHostName 提前URL中的域名
func GetHostName(rawUrl string) string {
	parsedURL, err := url.Parse(rawUrl)
	if err != nil {
		log.Warnf("parse endpoint url error. url: %s, err: %v", rawUrl, err)
		return ""
	}
	return parsedURL.Hostname()
}

// IsIP 判断是否是IP
func IsIP(ip string) bool {
	if net.ParseIP(ip) == nil {
		return false
	}
	return true
}

func GetMachineIp() string {
	if machineIp == "" {
		initMachineIp()
	}
	return machineIp
}

func initMachineIp() {
	if machineIp != "" {
		return
	}

	// 获取所有网络接口地址
	interfaceAddresses, err := net.InterfaceAddrs()
	if err != nil {
		machineIp = ""
	}
	// 遍历并打印IPv4地址（如果需要IPv6，可以适当修改条件）
	for _, address := range interfaceAddresses {
		// 检查地址是否为IPv4类型
		if ipnet, ok := address.(*net.IPNet); ok && !ipnet.IP.IsLoopback() && ipnet.IP.To4() != nil {
			validIp := ipnet.IP.String()
			log.Info("Init local Machine Ip. ip=" + validIp)
			machineIp = validIp

			break
		}
	}
}
