#!/usr/bin/env zsh
set -e

# 自动化构建脚本
# 构建tags $COSY_BUILDING_TAGS 默认 ne,prod
# -ne 用于控制jieba分词器的编译参数
# -dev 用于控制http route server等部分能力表达

# 切换到脚本所在目录（远程执行时需要）
dir=$(cd "$(dirname "$0")"; pwd)
cd $dir

# 设置默认值为环境变量值，如果未设置则为空
trial_edition="${TRIAL_EDITION:-}"
expire_time="${EXPIRE_TIME:-}"
server_url="${SERVER_URL:-}"
server_proxy="${SERVER_PROXY:-}"
cosy_version="${COSY_VERSION:-}"
cosy_build_tags="${COSY_BUILDING_TAGS}"
cosy_asset_version="${COSY_ASSET_VERSION:-}"
cosy_feature_config="${COSY_FEATURE_CONFIG:-}"
# 构建专属客户端
cosy_dedicated_client="${COSY_DEDICATED_CLIENT:-}"
# 目标平台，默认为全部
target_platforms="${TARGET_PLATFORMS:-:mac_x86:mac_arm:linux_x86:linux_arm:win_x86:win_arm:}"

extra_param=()

# 如果 go.mod 指定了 toolchain 版本，尝试切换到目标 go 版本
go_version=$(cat go.mod | grep "^toolchain " | awk '{print $2}')
if [ "${go_version}" != "" ]; then
  gvm_go_path="${HOME}/.gvm/gos/${go_version}"
  local_go_version="$(go version | awk '{print $3}')"
  if [ -d "$gvm_go_path" ]; then
    export PATH=$gvm_go_path/bin:$PATH
    export GOROOT=$gvm_go_path
    echo "🚀 Using Go version $go_version"
  elif [ "${local_go_version}" != "${go_version}" ]; then
    echo "⚠️ Go version ${go_version} specified by go.mod not match local go version ${local_go_version}"
  fi
fi

# 遍历所有命令行参数
for arg in "$@"; do
  if [[ $arg == --trial-edition=* ]]; then
    trial_edition="${arg#*=}"
  elif [[ $arg == --expire-time=* ]]; then
    expire_time="${arg#*=}"
  elif [[ $arg == --server-url=* ]]; then
    server_url="${arg#*=}"
  elif [[ $arg == --server-host=* ]]; then
    server_host="${arg#*=}"
  elif [[ $arg == --server-proxy=* ]]; then
    server_proxy="${arg#*=}"
  elif [[ $arg == --cosy-version=* ]]; then
    cosy_version="${arg#*=}"
  else
    extra_param+=("$arg")
  fi
done

project_path=$(pwd)

# 默认构建到out目录
if [ ${#extra_param} -eq 0 ]; then
  out_path="${OUT_PATH:-./out}"  # 先检查是否有OUT_PATH环境变量
  echo "Out path parameter is missing, using ${out_path}"
else
  out_path="${extra_param[1]}"
fi
echo ">> Using out path: \"${out_path}\" <<"
mkdir -p "${out_path}"

echo "Building binaries ..."
EXTRA_FLAGS=""
BUILDING_TAGS=""

EXTRA_FLAGS="${EXTRA_FLAGS} -X main.productType=lingma"
EXTRA_FLAGS="${EXTRA_FLAGS} -X main.buildFormType=plugin"

if [ "${trial_edition}" != "" ]; then
  echo ">> Build trial edition: ${trial_edition} <<"
  EXTRA_FLAGS="${EXTRA_FLAGS} -X main.trialEdition=${trial_edition}"
fi
if [ "${server_url}" != "" ]; then
  echo ">> Use server url: ${server_url} <<"
  EXTRA_FLAGS="${EXTRA_FLAGS} -X main.serverUrl=${server_url}"
fi
if [ "${server_host}" != "" ]; then
  echo ">> Use server host: ${server_host} <<"
  EXTRA_FLAGS="${EXTRA_FLAGS} -X main.serverHost=${server_host}"
fi
if [ "${server_proxy}" != "" ]; then
  echo ">> Use server proxy: ${server_proxy} <<"
  EXTRA_FLAGS="${EXTRA_FLAGS} -X main.serverProxy=${server_proxy}"
fi
if [ "${expire_time}" != "" ]; then
  echo ">> Set expire time to: ${expire_time} <<"
  expire_epoch=$(date -j -f '%Y-%m-%dT%H:%M:%S' "${expire_time}" +'%s')
  if [ "${expire_epoch}" = "" ]; then
    echo "Invalid expire time \"${expire_time}\", should in \"%Y-%m-%dT%H:%M:%S\" format."
    exit 1
  fi
  EXTRA_FLAGS="${EXTRA_FLAGS} -X main.expireTime=${expire_epoch}"
fi
if [ "${cosy_version}" = "" ]; then
  # 从当前目录下的VERSION文件中获取版本号
  cosy_version=$(cat VERSION)
  echo ">> Set CosyVersion to: ${cosy_version} <<"
  EXTRA_FLAGS="${EXTRA_FLAGS} -X cosy/global.CosyVersion=${cosy_version}"
else
  echo ">> Set CosyVersion to: ${cosy_version} <<"
  EXTRA_FLAGS="${EXTRA_FLAGS} -X cosy/global.CosyVersion=${cosy_version}"
fi
if [ "${cosy_asset_version}" != "" ]; then
  echo ">> Use cosy assert version: ${cosy_asset_version} <<"
  EXTRA_FLAGS="${EXTRA_FLAGS} -X main.assertVersion=${cosy_asset_version}"
fi
if [ "${cosy_feature_config}" != "" ]; then
  echo ">> Use cosy feature config: ${cosy_feature_config} <<"
  EXTRA_FLAGS="${EXTRA_FLAGS} -X main.featureSwitchConfig=${cosy_feature_config}"
fi
if [ "${cosy_dedicated_client}" != "" ]; then
  echo ">> Use cosy feature config: ${cosy_feature_config} <<"
  EXTRA_FLAGS="${EXTRA_FLAGS} -X main.dedicated=true"
fi
if [ "${cosy_build_tags}" = "" ]; then
  # 默认走生产构建
  BUILDING_TAGS="ne,prod,lingma"
  echo ">> Set BuildingTags to: ${BUILDING_TAGS} <<"
else
  BUILDING_TAGS=${cosy_build_tags}
  echo ">> Set BuildingTags to: ${BUILDING_TAGS} <<"
fi

echo ">> EXTRA_FLAGS: ""$EXTRA_FLAGS <<"

# Get version first
printf "{\n    \"cosy.core.version\": \"%s\"\n}" "${cosy_version}" > "${out_path}/config.json"

# Create target folder
bin_dir=$out_path/$cosy_version
echo ">> Target dir: ""$bin_dir <<"

if echo "${target_platforms}" | grep -q ":mac_x86:"; then
  echo ">> Building x86_64_darwin <<"
  mkdir -p "$bin_dir/x86_64_darwin"
  CGO_ENABLED=1 GOOS=darwin GOARCH=amd64 \
    go build -tags ${BUILDING_TAGS} -buildmode=pie -trimpath -ldflags "-s -w ${EXTRA_FLAGS}" -o "$bin_dir"/x86_64_darwin/Lingma
fi

if echo "${target_platforms}" | grep -q ":mac_arm:"; then
  echo ">> Building aarch64_darwin <<"
  mkdir -p "$bin_dir/aarch64_darwin"
  echo ${EXTRA_FLAGS}
  CGO_ENABLED=1 GOOS=darwin GOARCH=arm64 \
    go build -tags ${BUILDING_TAGS} -trimpath -ldflags "-s -w ${EXTRA_FLAGS}" -o "$bin_dir"/aarch64_darwin/Lingma
fi

if echo "${target_platforms}" | grep -q ":win_x86:"; then
  echo ">> Building x86_64_windows <<"
  mkdir -p "$bin_dir/x86_64_windows"
  CGO_ENABLED=1 CC="zig cc -target x86_64-windows -Wno-dll-attribute-on-redeclaration" \
    CXX="zig c++ -target x86_64-windows -Wno-dll-attribute-on-redeclaration" \
    GOOS=windows GOARCH=amd64 CGO_LDFLAGS="-static" \
    go build -tags ${BUILDING_TAGS} -buildmode=pie -trimpath -ldflags "-s -w ${EXTRA_FLAGS}" -o "$bin_dir"/x86_64_windows/Lingma.exe
fi

if echo "${target_platforms}" | grep -q ":win_arm:"; then
  echo ">> Building aarch64_windows <<"
  mkdir -p "$bin_dir/aarch64_windows"
  CGO_ENABLED=1 CC="zig cc -target aarch64-windows -Wno-dll-attribute-on-redeclaration" \
    CXX="zig c++ -target aarch64-windows -Wno-dll-attribute-on-redeclaration" \
    GOOS=windows GOARCH=arm64 CGO_LDFLAGS="-static" \
    go build -tags ${BUILDING_TAGS} -buildmode=pie -trimpath -ldflags "-s -w ${EXTRA_FLAGS}" -o "$bin_dir"/aarch64_windows/Lingma.exe
fi

if echo "${target_platforms}" | grep -q ":linux_x86:"; then
  echo ">> Building x86_64_linux <<"
  mkdir -p "$bin_dir/x86_64_linux"
  CGO_ENABLED=1 CC=x86_64-linux-musl-gcc CXX=x86_64-linux-musl-g++ GOARCH=amd64 GOOS=linux \
    go build -tags ${BUILDING_TAGS} -trimpath -ldflags "-linkmode external -extldflags -static -s -w ${EXTRA_FLAGS}" -o "$bin_dir"/x86_64_linux/Lingma
fi

if echo "${target_platforms}" | grep -q ":linux_arm:"; then
  echo ">> Building aarch64_linux <<"
  mkdir -p "$bin_dir/aarch64_linux"
  #CGO_ENABLED=1 CC="zig cc -target aarch64-linux-gnu" CXX="zig c++ -target aarch64-linux-gnu" \
  CGO_ENABLED=1 CC=aarch64-linux-musl-gcc CXX=aarch64-linux-musl-g++ GOARCH=arm64 GOOS=linux \
    go build -tags ${BUILDING_TAGS} -trimpath -ldflags "-linkmode external -extldflags -static -s -w ${EXTRA_FLAGS}" -o "$bin_dir"/aarch64_linux/Lingma
fi

echo ">> Done <<"