package codebase

import (
	"context"
	"cosy/definition"
	"cosy/experiment"
	"cosy/global"
	"cosy/lang/indexer"
	"cosy/lang/indexer/rag"
	"cosy/log"
	"cosy/similar"
	"cosy/similar/matcher"
	"cosy/similar/neighbor"
	"cosy/util/distance"
	"fmt"
	"path/filepath"
	"strings"
	"time"
)

const (
	thresholdEps = 0.0001
)

type CodebaseSimilarMatcher struct {
	strategy           matcher.CodeMatchStrategy
	referenceDoc       *matcher.MatcherSource
	matchCode          *matcher.MatchSourceCode
	textRetrieveEngine rag.TextRetrieveEngine
	activeFiles        []neighbor.NeighborFile
	sourceTokens       *similar.TokenSequence
	focusTokens        *similar.TokenSequence
}

func NewCodebaseSimilarMatcher(ctx context.Context, doc *matcher.MatcherSource, strategy matcher.CodeMatchStrategy, activeFiles []neighbor.NeighborFile) (*CodebaseSimilarMatcher, error) {
	textRetrieveEngine, ok := ctx.Value("completionTextRetrieveEngine").(rag.TextRetrieveEngine)
	if !ok {
		return nil, fmt.Errorf("parse code similar error: fileIndexer not found")
	}
	if textRetrieveEngine == nil {
		return nil, fmt.Errorf("parse code similar error: textRetrieveEngine not found")
	}
	startTime := time.Now()
	matchCode, err := strategy.ExtractSourceCode(doc)
	if err != nil {
		return nil, fmt.Errorf("extract source code error: %v cost: %dms", err, time.Since(startTime).Milliseconds())
	}
	if matchCode.Content == "" {
		return nil, fmt.Errorf("extract empty source code")
	}
	log.Debugf("%s extract source code: %+v", strategy.Id(), matchCode)
	matcher := &CodebaseSimilarMatcher{
		strategy:           strategy,
		referenceDoc:       doc,
		matchCode:          &matchCode,
		textRetrieveEngine: textRetrieveEngine,
		activeFiles:        activeFiles,
		sourceTokens:       similar.NewTokenSequence(matchCode.Content),
		focusTokens:        similar.NewTokenSequence(matchCode.Focus),
	}
	return matcher, nil
}

func (c *CodebaseSimilarMatcher) FindMatches(topK int) []matcher.Snippet {
	snippets := make([]matcher.Snippet, 0, topK)
	query := c.buildRetrieveQuery(topK)
	if global.DebugMode {
		log.Debugf("retrieve query: %+v", query)
	}
	result, err := c.textRetrieveEngine.Retrieve(context.Background(), query)
	if err != nil {
		log.Errorf("retrieve error: %v", err)
		return snippets
	}
	threshold := experiment.ConfigService.GetDoubleConfigValue(definition.ExperimentKeyCompletionCodebaseRagThreshold, experiment.ConfigScopeClient, 0.01)
	for _, chunk := range result.Chunks {
		score := chunk.Score
		if threshold > thresholdEps {
			indexContentTokens := similar.NewTokenSequence(chunk.IndexContent)
			focusTokens := similar.NewTokenSequence(chunk.IndexFocus)
			score = c.strategy.SimilarityScore(c.sourceTokens, indexContentTokens, c.focusTokens, focusTokens)
			editScore, _, _ := distance.CalculateWordLevenshtein(c.sourceTokens.ToTokens(), indexContentTokens.ToTokens())
			log.Debugf("edit_distance: %d score: %f", editScore, score)
			if score < threshold {
				log.Debugf("retrieve chunk: %s focus: %s score: %f < threshold: %f, ignore", chunk.IndexContent, chunk.IndexFocus, score, threshold)
				continue
			}
		}
		snippets = append(snippets, matcher.Snippet{
			Score:     score,
			StartLine: int(chunk.StartLine),
			EndLine:   int(chunk.EndLine),
			Content:   chunk.Content,
			FilePath:  chunk.FilePath,
		})
	}
	return snippets
}

func (c *CodebaseSimilarMatcher) buildRetrieveQuery(topK int) rag.TextQuery {
	fileExt := filepath.Ext(c.referenceDoc.FilePath)
	mustConditions := []rag.TextCondition{
		{
			FieldName: "index_content",
			Query:     c.matchCode.Content,
			Boost:     1.5,
		},
		{
			FieldName: "file_extension",
			Query:     fileExt,
		},
	}
	if strings.Trim(c.matchCode.Focus, " ") != "" {
		mustConditions = append(mustConditions, rag.TextCondition{
			FieldName: "index_focus",
			Query:     c.matchCode.Focus,
			Boost:     1.3,
		})
	}
	shouldConditions := []rag.TextCondition{
		{
			FieldName: "code_category",
			Query:     indexer.NormalCategory,
		},
	}
	if c.activeFiles != nil &&
		experiment.ConfigService.GetBoolConfigValue(definition.ExperimentKeyEnableCompletionCodebaseRagActiveFile, experiment.ConfigScopeClient, true) {
		for _, activeFile := range c.activeFiles {
			shouldConditions = append(shouldConditions, rag.TextCondition{
				FieldName: "file_path",
				Query:     activeFile.FilePath,
			})
		}
	}
	return rag.TextQuery{
		Fields: []string{"*"},
		Must:   mustConditions,
		Should: shouldConditions,
		MustNot: []rag.TextCondition{
			{
				FieldName: "file_path",
				Query:     c.referenceDoc.FilePath,
			},
		},
		Operator: rag.MatchOr,
		Size:     topK,
		From:     0,
	}
}
