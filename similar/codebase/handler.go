package codebase

import (
	"context"
	"cosy/components"
	"cosy/log"
	"cosy/tokenizer"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/0xd219b/textsplitter"
	"github.com/blevesearch/bleve/v2"
)

const (
	DefaultChunkSize           = 300
	DefaultChunkOverlap        = 20
	DefaultRoughSortingOverlap = 3 // 粗排时的召回数量做一些冗余
)

var GlobalCodebaseHandler = &CodebaseHandler{}

type CodebaseHandler struct {
}

func GetDefaultHandler() CodebaseToolBox {
	return GlobalCodebaseHandler
}

func calculateTokenCount(text string) int {
	cnt, err := tokenizer.CalQwenTokenCount(text)
	if err != nil {
		log.Errorf("codebase calculate token count error: %v", err)
		// 如果tokenize报错了，用长度模拟代替
		return len(text)
	}
	return cnt
}
func (handler *CodebaseHandler) SplitFileWithTextSplitter(filePath string) ([]string, error) {
	startTime := time.Now()
	fileContent, err := os.ReadFile(filePath)
	if err != nil {
		return nil, err
	}

	splitter := textsplitter.NewRecursiveCharacter(textsplitter.WithChunkOverlap(DefaultChunkOverlap),
		textsplitter.WithChunkSize(DefaultChunkSize),
		textsplitter.WithLenFunc(calculateTokenCount))

	splitDocs, err := splitter.SplitText(string(fileContent))
	if err != nil {
		return nil, err
	}
	log.Debugf("split file success, documents count: %d, cost time %fs", len(splitDocs), time.Since(startTime).Seconds())
	return splitDocs, nil
}

func (handler *CodebaseHandler) RoughSorting(documents []string, queryString string, topK int) ([]string, error) {
	startTime := time.Now()
	// Create a new mapping
	mapping := bleve.NewIndexMapping()

	// Create a temporary in-memory index
	index, err := bleve.NewMemOnly(mapping)
	if err != nil {
		return nil, err
	}
	defer index.Close()

	//var (
	//	docs struct {
	//		Id        string `json:"id"`
	//		CodeChunk string `json:"code_chunk"`
	//	}
	//)

	//codeTextFieldMapping := bleve.NewTextFieldMapping()
	//
	////// 用于存储代码元数据
	//metaMapping := bleve.NewDocumentMapping()
	//metaMapping.AddFieldMappingsAt("code_chunk", codeTextFieldMapping)
	//metaMapping.AddFieldMappingsAt("code_chunk", codeTextFieldMapping)
	//mapping.AddDocumentMapping("code", metaMapping)

	// Index all documents
	for idx, doc := range documents {
		//err = index.Index(fmt.Sprintf("doc_%d", idx), docs)
		err = index.Index(fmt.Sprintf("doc_%d", idx), doc)
		if err != nil {
			return nil, err
		}
	}

	//doc, err := index.DocCount()
	//fmt.Println("索引数量：", doc)

	// Create a query
	q := bleve.NewMatchQuery(queryString)

	//q.SetBoost(10.0)
	q.Fuzziness = 2
	// Create a search request
	searchRequest := bleve.NewSearchRequest(q)
	// 适当冗余，为后续精排服务
	searchRequest.Size = DefaultRoughSortingOverlap * topK
	searchRequest.From = 0
	// 分数降序排序
	searchRequest.SortBy([]string{"-_score"})

	// Perform the search
	done := make(chan error)
	var searchResult *bleve.SearchResult
	go func() {
		// 启动一个goroutine来执行工作
		var internalError error
		searchResult, internalError = index.SearchInContext(context.Background(), searchRequest)
		done <- internalError
	}()

	// 等待工作完成或超时
	select {
	case err = <-done:
		if err != nil {
			return nil, err
		}
	case <-time.After(10 * time.Second):
		return nil, fmt.Errorf("rough sorting searching timeout")
	}

	idxMap := make(map[int]bool)

	// Collect filtered documents while maintaining order
	filteredDocs := make([]string, 0, len(searchResult.Hits))
	for _, hit := range searchResult.Hits {
		var docIndex int
		_, err = fmt.Sscanf(hit.ID, "doc_%d", &docIndex)
		if err != nil {
			return nil, err
		}
		idxMap[docIndex] = true
		if docIndex >= 0 && docIndex < len(documents) {
			filteredDocs = append(filteredDocs, documents[docIndex])
		}
	}

	// blevesearch召回文档数量可能非常少，因此用暴力阶段逻辑补充
	for i := 0; i < len(documents); i++ {
		if len(filteredDocs) >= 2*topK {
			break
		}
		if _, ok := idxMap[i]; !ok {
			filteredDocs = append(filteredDocs, documents[i])
		}
	}
	log.Debugf("rough sorting success,query: %s, documents count: %d, cost time %fs", queryString, len(filteredDocs), time.Since(startTime).Seconds())
	return filteredDocs, nil
}

func (handler *CodebaseHandler) FineSorting(documents []string, queryString string, topK int, threshold float64) ([]string, error) {
	startTime := time.Now()
	reranker := components.NewLingmaReranker()

	ctx := context.Background()
	rerankResponse, err := reranker.RerankDocuments(ctx, queryString, documents, topK)
	if err != nil {
		return nil, err
	}

	filteredDocs := make([]string, 0)
	for _, r := range rerankResponse.Output.Results {
		if r.RelevanceScore < threshold {
			continue
		}
		filteredDocs = append(filteredDocs, r.Document.Text)
	}

	log.Debugf("fine sorting success, query: %s, documents count: %d, cost time %fs", queryString, len(filteredDocs), time.Since(startTime).Seconds())
	return filteredDocs, nil
}

// RetrieveFromFile
// topK不能超过rerank默认的最大topK值80
// threshold最大值为0.2，大于0.2将被默认设置为0.2
func (handler *CodebaseHandler) RetrieveFromFile(filePath string, query []string, topK int, threshold float64) ([]string, error) {
	if threshold <= 0.0 || threshold > components.DefaultMaxThreshold {
		// 如果超出了最大阈值，则使用最大阈值
		// 阈值过大会导致返回空结果
		threshold = components.DefaultMaxThreshold
	}
	if topK <= 0 || topK > components.DefaultTopN {
		topK = components.DefaultTopN
	}
	startTime := time.Now()
	// 文件切块
	documents, err := handler.SplitFileWithTextSplitter(filePath)
	if err != nil {
		return nil, err
	}
	if len(documents) <= 0 {
		return nil, fmt.Errorf("splitd documents is empty")
	}

	// 拼接query字符串
	joinedQueryString := strings.Join(query, " ")

	// 执行粗排
	roughDocs, err := handler.RoughSorting(documents, joinedQueryString, topK)
	if err != nil {
		return nil, err
	}
	if len(roughDocs) <= 0 {
		return nil, fmt.Errorf("rough sorting documents empty")
	}

	//totalCnt := 0
	//for _, doc := range roughDocs {
	//	cnt, err := tokenizer.CalQwenTokenCount(doc)
	//	if err != nil {
	//		log.Errorf("[knowledge doc retrieval] tokenize doc error. error: %v", err)
	//	}
	//	totalCnt += cnt
	//}
	//fmt.Printf("rerank input total token count: %d\n", totalCnt)
	// 执行精排
	fineDocs, err := handler.FineSorting(roughDocs, joinedQueryString, topK, threshold)
	if err != nil {
		// 精排出现错误时，本地记录错误后，返回粗排结果
		log.Errorf("filePath: %s, fine sorting error: %v", filePath, err)
		log.Debugf("retrieve file success with rough sorting result, cost time %fs", time.Since(startTime).Seconds())
		return roughDocs[:topK], nil
	}

	//totalCnt = 0
	//for _, doc := range fineDocs {
	//	cnt, err := tokenizer.CalQwenTokenCount(doc)
	//	if err != nil {
	//		log.Errorf("[knowledge doc retrieval] tokenize doc error. error: %v", err)
	//	}
	//	totalCnt += cnt
	//}
	//fmt.Printf("rerank output total token count: %d\n", totalCnt)
	if len(fineDocs) <= 0 {
		return nil, fmt.Errorf("fine sorting documents empty, please lower the threshold value")
	}
	log.Debugf("retrieve file success with fine sorting result, cost time %fs", time.Since(startTime).Seconds())
	return fineDocs, nil
}
