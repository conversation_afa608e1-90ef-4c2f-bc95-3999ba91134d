package log

import (
	"cosy/global"
	"regexp"
)

// 敏感词映射表，key是敏感词，value是对应的替换词
var sensitiveWordMap = map[string]string{
	"lingma": "qoder",
	"aliyun": "cloud",
}

// 中文字符替换为的字符串
const chineseWordReplacement = "***"

// 编译中文字符正则表达式
var chineseCharRegex = regexp.MustCompile(`[\p{Han}]+`)

// FilterLogMessage 过滤日志消息
// 1. 替换敏感词（忽略大小写）
// 2. 移除中文字符
func FilterLogMessage(message string) string {
	if !global.IsQoderProduct() {
		return message
	}
	// 先移除中文字符
	message = RemoveChineseChars(message)

	// 再替换敏感词
	message = ReplaceSensitiveWords(message)

	return message
}

// ReplaceSensitiveWords 替换敏感词
func ReplaceSensitiveWords(message string) string {
	result := message

	for word, replacement := range sensitiveWordMap {
		// 创建不区分大小写的正则表达式
		pattern := regexp.MustCompile(`(?i)` + regexp.QuoteMeta(word))
		result = pattern.ReplaceAllString(result, replacement)
	}

	return result
}

// RemoveChineseChars 替换中文字符
func RemoveChineseChars(message string) string {
	return chineseCharRegex.ReplaceAllString(message, chineseWordReplacement)
}

// FilterLogArgs 过滤日志参数
func FilterLogArgs(args ...interface{}) []interface{} {
	if !global.IsQoderProduct() {
		return args
	}
	filteredArgs := make([]interface{}, len(args))

	for i, arg := range args {
		switch v := arg.(type) {
		case string:
			filteredArgs[i] = FilterLogMessage(v)
		default:
			// 对于非字符串类型，暂不处理
			filteredArgs[i] = v
		}
	}

	return filteredArgs
}
