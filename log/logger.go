package log

import (
	"cosy/global"
	"os"
	"path/filepath"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"

	"github.com/natefinch/lumberjack"
)

// Use a console logger by default
var logger = zap.NewNop().Sugar()

// UseFileLogger sets the local logger
func UseFileLogger(wd string) {
	logger = createLingmaLogger(wd).Sugar()
}

// UseConsoleLogger sets the test logger
func UseConsoleLogger(enableDebug bool) {
	if enableDebug {
		global.DebugMode = true
	}
	logger = createConsoleLogger().Sugar()
}

// Error prints error level logs
func Error(args ...interface{}) {
	if logger != nil {
		filteredArgs := FilterLogArgs(args...)
		logger.Error(filteredArgs...)
	}
}

// Errorf prints formatted error level logs
func Errorf(template string, args ...interface{}) {
	if logger != nil {
		filteredTemplate := FilterLogMessage(template)
		filteredArgs := FilterLogArgs(args...)
		logger.Errorf(filteredTemplate, filteredArgs...)
	}
}

// Warn prints warn level logs
func Warn(args ...interface{}) {
	if logger != nil {
		filteredArgs := FilterLogArgs(args...)
		logger.Warn(filteredArgs...)
	}
}

// Warnf prints formatted warn level logs
func Warnf(template string, args ...interface{}) {
	if logger != nil {
		filteredTemplate := FilterLogMessage(template)
		filteredArgs := FilterLogArgs(args...)
		logger.Warnf(filteredTemplate, filteredArgs...)
	}
}

// Info prints info level logs
func Info(args ...interface{}) {
	if logger != nil {
		filteredArgs := FilterLogArgs(args...)
		logger.Info(filteredArgs...)
	}
}

// Infof prints formatted info level logs
func Infof(template string, args ...interface{}) {
	if logger != nil {
		filteredTemplate := FilterLogMessage(template)
		filteredArgs := FilterLogArgs(args...)
		logger.Infof(filteredTemplate, filteredArgs...)
	}
}

// Debug prints debug level logs
func Debug(args ...interface{}) {
	logger.Debug(args...)
}

func IsDebugEnabled() bool {
	return global.DebugMode
}

// Debugf prints formatted debug level logs
func Debugf(template string, args ...interface{}) {
	logger.Debugf(template, args...)
}

// createConsoleLogger sets the local logger
func createConsoleLogger() *zap.Logger {
	return zap.NewNop()
}

// createLingmaLogger returns a logger which writes logs to a file
func createLingmaLogger(logFolder string) *zap.Logger {
	logFile := filepath.Join(logFolder, "logs", LoggerFileName)

	useLevel := zapcore.InfoLevel
	if global.DebugMode {
		useLevel = zapcore.DebugLevel
	} else {
		useLevel = zapcore.InfoLevel
	}
	//TODO 上线前删除，qoder测试版固定debug
	if global.IsQoderProduct() && !global.IsReleaseVersion() {
		useLevel = zapcore.DebugLevel
		global.DebugMode = true
	}

	lingmaLogCore := zapcore.NewCore(getLocalEncoder(global.DebugMode), getLocalLogWriter(logFile), useLevel)

	cacheLogFile := filepath.Join(logFolder, "cache", "diagnosis.bin")
	diagnosisLogCore := zapcore.NewCore(getCacheEncoder(), getCacheLogWriter(cacheLogFile), zapcore.DebugLevel)

	cores := []zapcore.Core{lingmaLogCore, diagnosisLogCore}
	return zap.New(zapcore.NewTee(cores...), zap.AddCaller(), zap.AddCallerSkip(1))
}

// getLocalEncoder returns a logger encoder
// enableCaller: whether to enable caller
func getLocalEncoder(enableCaller bool) zapcore.Encoder {
	encoderConfig := zap.NewProductionEncoderConfig()
	encoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
	encoderConfig.EncodeLevel = zapcore.CapitalLevelEncoder

	standardEncoder := zapcore.NewConsoleEncoder(encoderConfig)

	ee := NewEncoderDelegate(enableCaller, false, standardEncoder)

	return ee
}

func getCacheEncoder() zapcore.Encoder {
	encoderConfig := zap.NewProductionEncoderConfig()
	encoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
	encoderConfig.EncodeLevel = zapcore.CapitalLevelEncoder

	standardEncoder := zapcore.NewConsoleEncoder(encoderConfig)

	// 创建自定义加密编码器
	ee := NewEncoderDelegate(true, true, standardEncoder)

	return ee
}

// getConsoleLogWriter returns a logger writer which writes logs to stderr
func getConsoleLogWriter() zapcore.WriteSyncer {
	// By default, logs are printed to console
	return zapcore.AddSync(os.Stdout)
}

// getLocalLogWriter returns a logger writer which writes logs to given file
func getLocalLogWriter(logFileName string) zapcore.WriteSyncer {
	return zapcore.AddSync(&lumberjack.Logger{
		Filename:   logFileName, // local log filename
		MaxSize:    20,          // maximum size in megabytes of the log file
		MaxBackups: 3,           // maximum number of old log files to retain
		MaxAge:     60,          // maximum number of days to retain old log files
		Compress:   false,       // don't compress log file
	})
}

// getCacheLogWriter returns a logger writer which writes logs to given file
func getCacheLogWriter(logFileName string) zapcore.WriteSyncer {
	return zapcore.AddSync(&lumberjack.Logger{
		Filename:   logFileName, // local log filename
		MaxSize:    50,          // maximum size in megabytes of the log file
		MaxBackups: 1,           // maximum number of old log files to retain
		MaxAge:     60,          // maximum number of days to retain old log files
		Compress:   false,       // don't compress log file
	})
}

func GetLogger() *zap.SugaredLogger {
	return logger
}

// 为了测试而修改的日志路径
func UseTestFileLogger(logFolder string, logFile string) {
	err := os.MkdirAll(logFolder, os.ModePerm)
	if err != nil {
		panic(err)
	}
	_, err = os.Create(logFile)
	if err != nil {
		panic(err)
	}

	useLevel := zapcore.InfoLevel
	if global.DebugMode {
		useLevel = zapcore.DebugLevel
	}
	lingmaLogCore := zapcore.NewCore(getLocalEncoder(true), getLocalLogWriter(logFile), useLevel)
	cores := []zapcore.Core{lingmaLogCore}
	logger = zap.New(zapcore.NewTee(cores...), zap.AddCaller(), zap.AddCallerSkip(1)).Sugar()
}
