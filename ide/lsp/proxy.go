package lsp

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/gorilla/websocket"
)

const (
	serverAddr = "localhost:8008"
)

var (
	lspLongTimeClient *LspClient
	lspClientMutex    sync.RWMutex
)

type LspClient struct {
	conn      *websocket.Conn
	url       string
	mu        sync.RWMutex
	closed    bool
	ctx       context.Context
	cancel    context.CancelFunc
	keepAlive *time.Ticker
	reconnect chan struct{}
}

type JSONRPCResponse struct {
	Error   interface{}   `json:"error,omitempty"`
	ID      interface{}   `json:"id"`
	JSONRPC string        `json:"jsonrpc"`
	Result  JSONRPCResult `json:"result,omitempty"`
}

type JSONRPCResult struct {
	Success      bool            `json:"success"`
	Result       json.RawMessage `json:"result"`
	ErrorMessage string          `json:"errorMessage"`
}

// NewClient creates a new client
func createLspClient() *LspClient {
	url := fmt.Sprintf("ws://%s/ws", serverAddr)

	ctx, cancel := context.WithCancel(context.Background())

	client := &LspClient{
		url:       url,
		ctx:       ctx,
		cancel:    cancel,
		reconnect: make(chan struct{}, 1),
	}

	// Start connection
	client.connect()

	return client
}

// connect establishes connection
func (c *LspClient) connect() {
	c.mu.Lock()
	defer c.mu.Unlock()

	if c.closed {
		return
	}

	conn, _, err := websocket.DefaultDialer.Dial(c.url, nil)
	if err != nil {
		log.Printf("Failed to connect to server: %v", err)
		// Retry after 5 seconds
		time.AfterFunc(5*time.Second, func() {
			c.reconnect <- struct{}{}
		})
		return
	}

	c.conn = conn
	log.Printf("Successfully connected to server: %s", c.url)

	// Start keep-alive
	c.startKeepAlive()

	// Start reconnection listener
	go c.handleReconnect()
}

// startKeepAlive starts keep-alive mechanism
func (c *LspClient) startKeepAlive() {
	if c.keepAlive != nil {
		c.keepAlive.Stop()
	}

	c.keepAlive = time.NewTicker(60 * time.Second)
	go func() {
		for {
			select {
			case <-c.keepAlive.C:
				c.sendPing()
			case <-c.ctx.Done():
				return
			}
		}
	}()
}

// sendPing sends keep-alive ping
func (c *LspClient) sendPing() {
	c.mu.RLock()
	conn := c.conn
	c.mu.RUnlock()

	if conn == nil {
		return
	}

	err := conn.WriteControl(websocket.PingMessage, []byte{}, time.Now().Add(10*time.Second))
	if err != nil {
		log.Printf("Failed to send ping: %v", err)
		c.reconnect <- struct{}{}
	}
}

// handleReconnect handles reconnection
func (c *LspClient) handleReconnect() {
	for {
		select {
		case <-c.reconnect:
			if !c.closed {
				log.Println("Attempting to reconnect...")
				c.closeConnection()
				c.connect()
			}
		case <-c.ctx.Done():
			return
		}
	}
}

// closeConnection closes the connection
func (c *LspClient) closeConnection() {
	c.mu.Lock()
	defer c.mu.Unlock()

	if c.conn != nil {
		c.conn.Close()
		c.conn = nil
	}

	if c.keepAlive != nil {
		c.keepAlive.Stop()
		c.keepAlive = nil
	}
}

// Close closes the client
func (c *LspClient) Close() {
	c.mu.Lock()
	defer c.mu.Unlock()

	c.closed = true
	c.cancel()
	c.closeConnection()
}

// IsConnected checks if connected
func (c *LspClient) IsConnected() bool {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.conn != nil && !c.closed
}

// SendRequest sends request and waits for response
// timeout: 单位ms
func (c *LspClient) SendRequest(ctx context.Context, timeout int, req interface{}) (JSONRPCResponse, error) {
	c.mu.RLock()
	conn := c.conn
	c.mu.RUnlock()

	if conn == nil {
		return JSONRPCResponse{}, fmt.Errorf("client not connected")
	}

	// Serialize request
	reqBytes, err := json.Marshal(req)
	if err != nil {
		return JSONRPCResponse{}, fmt.Errorf("failed to serialize request: %v", err)
	}

	// Send request
	err = conn.WriteMessage(websocket.TextMessage, reqBytes)
	if err != nil {
		return JSONRPCResponse{}, fmt.Errorf("failed to send request: %v", err)
	}

	// Set read timeout
	err = conn.SetReadDeadline(time.Now().Add(time.Duration(timeout) * time.Millisecond))
	if err != nil {
		return JSONRPCResponse{}, fmt.Errorf("failed to set read timeout: %v", err)
	}

	// Read response
	_, message, err := conn.ReadMessage()
	if err != nil {
		return JSONRPCResponse{}, fmt.Errorf("failed to read response: %v", err)
	}

	var response JSONRPCResponse
	err = json.Unmarshal(message, &response)
	if err != nil {
		return JSONRPCResponse{}, fmt.Errorf("failed to parse response: %v", err)
	}

	return response, nil
}

// GetLspClient gets client
func GetLspClient(ctx context.Context) (*LspClient, error) {
	// 先尝试读锁检查是否已存在
	lspClientMutex.RLock()
	if lspLongTimeClient != nil && lspLongTimeClient.IsConnected() {
		client := lspLongTimeClient
		lspClientMutex.RUnlock()
		return client, nil
	}
	lspClientMutex.RUnlock()

	// 如果不存在或连接断开，使用写锁创建新实例
	lspClientMutex.Lock()
	defer lspClientMutex.Unlock()

	// 双重检查，防止在获取写锁期间其他goroutine已经创建了实例
	if lspLongTimeClient != nil && lspLongTimeClient.IsConnected() {
		return lspLongTimeClient, nil
	}

	// 如果旧实例存在但连接断开，先关闭它
	if lspLongTimeClient != nil {
		lspLongTimeClient.Close()
	}

	// 创建新实例
	lspLongTimeClient = createLspClient()
	return lspLongTimeClient, nil
}
