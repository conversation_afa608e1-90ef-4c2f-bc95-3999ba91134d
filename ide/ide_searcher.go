package ide

import (
	"context"
	common2 "cosy/chat/chains/common"
	"cosy/config"
	cosyDefinition "cosy/definition"
	"cosy/ide/common"
	"cosy/ide/lsp"
	"encoding/json"
	"errors"
	"github.com/google/uuid"
	"time"
)

const DEFAULT_SYMBOL_TIMEOUT = 1
const DEFAULT_RELATION_TIMEOUT = 3
const DEFAULT_SEGMENT_TIMEOUT = 3

type IdeSearcher struct {
}

func NewIdeSearcher() *IdeSearcher {
	return &IdeSearcher{}
}

// Symbol 相关
type IdeSearchSymbolRequest struct {
	WorkspacePath  string `json:"workspacePath"`
	Filepath       string `json:"filepath"`
	StartLine      int32  `json:"startLine"`
	EndLine        int32  `json:"endLine"`
	Query          string `json:"query"`
	MaxResultCount int    `json:"MaxResultCount"`
}

// LspSearchSymbolRequest
type LspSearchRequest struct {
	JSONRPC string      `json:"jsonrpc"`
	Method  string      `json:"method"`
	Params  interface{} `json:"params,omitempty"`
	ID      interface{} `json:"id"`
}

type IdeSearchSymbolResponse struct {
	Symbols []Symbol `json:"symbols"`
}

type Symbol struct {
	SymbolName    string `json:"symbolName"`
	SymbolType    string `json:"symbolType"`
	WorkspacePath string `json:"workspacePath"`
	Filepath      string `json:"filepath"`
	StartLine     int32  `json:"startLine"`
	EndLine       int32  `json:"endLine"`
	StartOffset   int32  `json:"startOffset"`
	EndOffset     int32  `json:"endOffset"`
	Snippet       string `json:"snippet"`
}

// Relation 相关
type IdeSearchRelationRequest struct {
	WorkspacePath     string            `json:"workspacePath"`
	Filepath          string            `json:"filepath"`
	StartOffset       int32             `json:"startOffset"`
	EndOffset         int32             `json:"endOffset"`
	RelationshipLimit RelationshipLimit `json:"relationshipLimit"`
}

type IdeSearchRelationResponse struct {
	Relationships Relationships `json:"relationships"`
	CenterSymbol  Symbol        `json:"centerSymbol"`
}

type RelationshipLimit struct {
	Reference    int `json:"reference"`
	MethodCall   int `json:"methodCall"`
	Implement    int `json:"implement"`
	Extend       int `json:"extend"`
	ReferenceBy  int `json:"referenceBy"`
	MethodCallBy int `json:"methodCallBy"`
	ImplementBy  int `json:"implementBy"`
	ExtendBy     int `json:"extendBy"`
}

type Relationships struct {
	Reference    []Symbol `json:"reference,omitempty"`
	MethodCall   []Symbol `json:"methodCall,omitempty"`
	Implement    []Symbol `json:"implement,omitempty"`
	Extend       []Symbol `json:"extend,omitempty"`
	ReferenceBy  []Symbol `json:"referenceBy,omitempty"`
	MethodCallBy []Symbol `json:"methodCallBy,omitempty"`
	ImplementBy  []Symbol `json:"implementBy,omitempty"`
	ExtendBy     []Symbol `json:"extendBy,omitempty"`
}

// Segment 相关
type IdeSearchSegmentRequest struct {
	WorkspacePath string `json:"workspacePath"`
	Filepath      string `json:"filepath"`
	StartLine     int32  `json:"startLine"`
	EndLine       int32  `json:"endLine"`
}

type IdeSearchSegmentResponse struct {
	Segments []Segment `json:"segments"`
}

type Segment struct {
	Filepath      string `json:"filepath"`
	WorkspacePath string `json:"workspacePath"`
	StartLine     uint32 `json:"startLine"`
	EndLine       uint32 `json:"endLine"`
	Snippet       string `json:"snippet"`
}

func (t *IdeSearcher) SearchSymbolByIde(ctx context.Context, request IdeSearchSymbolRequest) (IdeSearchSymbolResponse, error) {
	if request.StartLine == -1 {
		request.StartLine = 0
	}
	if request.EndLine == -1 {
		request.EndLine = 0
	}

	if config.IsRemoteAgentMode() {
		return t.searchSymbolByLsp(ctx, request)
	}
	return t.searchSymbolByIde(ctx, request)
}

func (t *IdeSearcher) SearchRelationByIde(ctx context.Context, request IdeSearchRelationRequest) (IdeSearchRelationResponse, error) {
	if config.IsRemoteAgentMode() {
		return t.searchRelationByLsp(ctx, request)
	}
	return t.searchRelationByIde(ctx, request)
}

func (t *IdeSearcher) searchSymbolByIde(ctx context.Context, request IdeSearchSymbolRequest) (IdeSearchSymbolResponse, error) {
	requestId := ctx.Value(common2.KeyRequestId)
	if requestId == nil || requestId == "" {
		requestId = uuid.NewString()
	}

	// 添加类型断言的安全检查
	requestIdStr, ok := requestId.(string)
	if !ok {
		return IdeSearchSymbolResponse{}, errors.New("requestId is not a string")
	}
	ideToolRequest := &cosyDefinition.ToolInvokeRequest{
		RequestId:  requestIdStr,
		ToolCallId: uuid.NewString(),
		Name:       "search_symbol",
		Parameters: map[string]any{
			"workspacePath":  request.WorkspacePath,
			"query":          request.Query,
			"filepath":       request.Filepath,
			"startLine":      request.StartLine,
			"endLine":        request.EndLine,
			"maxResultCount": request.MaxResultCount,
			"timeout":        DEFAULT_SYMBOL_TIMEOUT * 1000,
		},
		Async: false,
	}

	// 使用channel和goroutine实现超时控制
	resultChan := make(chan *cosyDefinition.ToolInvokeResponse, 1)
	errChan := make(chan error, 1)

	go func() {
		ideToolResponse, err := common.InvokeTool(ctx, ideToolRequest, DEFAULT_SYMBOL_TIMEOUT)
		if err != nil {
			errChan <- err
			return
		}
		resultChan <- ideToolResponse
	}()

	// 等待结果或超时
	select {
	case ideToolResponse := <-resultChan:
		if ideToolResponse == nil {
			return IdeSearchSymbolResponse{}, errors.New("ideToolResponse is nil")
		}

		// Convert the result map to JSON and directly parse it into the response
		resultJSON, err := json.Marshal(ideToolResponse.Result)
		if err != nil {
			return IdeSearchSymbolResponse{}, errors.New("marshal result to JSON error:" + err.Error())
		}

		// Create response with request data
		response := &IdeSearchSymbolResponse{}
		// Unmarshal the JSON directly into the response
		if err := json.Unmarshal(resultJSON, response); err != nil {
			return IdeSearchSymbolResponse{}, errors.New("unmarshal JSON result error:" + err.Error())
		}

		if len(response.Symbols) == 0 {
			return IdeSearchSymbolResponse{}, errors.New("no symbols found")
		}

		return *response, nil

	case err := <-errChan:
		return IdeSearchSymbolResponse{}, err

	case <-time.After(time.Duration(DEFAULT_SYMBOL_TIMEOUT)*time.Second + 100*time.Millisecond):
		return IdeSearchSymbolResponse{}, errors.New("search symbol timeout")
	}
}

func (t *IdeSearcher) searchSymbolByLsp(ctx context.Context, request IdeSearchSymbolRequest) (IdeSearchSymbolResponse, error) {
	requestId := ctx.Value(common2.KeyRequestId)
	if requestId == nil || requestId == "" {
		requestId = uuid.NewString()
	}

	// 添加类型断言的安全检查
	requestIdStr, ok := requestId.(string)
	if !ok {
		return IdeSearchSymbolResponse{}, errors.New("requestId is not a string")
	}

	client, err := lsp.GetLspClient(ctx)
	if err != nil {
		return IdeSearchSymbolResponse{}, err
	}
	if !client.IsConnected() {
		return IdeSearchSymbolResponse{}, errors.New("lsp client not connected")
	}
	if client == nil {
		return IdeSearchSymbolResponse{}, errors.New("lsp client is nil")
	}

	response, err := client.SendRequest(ctx, DEFAULT_SYMBOL_TIMEOUT*1000+100, LspSearchRequest{
		JSONRPC: "2.0",
		Method:  "qoder/search_symbol",
		Params: map[string]interface{}{
			"query":          request.Query,
			"filepath":       request.Filepath,
			"maxResultCount": request.MaxResultCount,
			"timeout":        DEFAULT_SYMBOL_TIMEOUT * 1000,
		},
		ID: requestIdStr,
	})

	if err != nil {
		return IdeSearchSymbolResponse{}, err
	}

	var result IdeSearchSymbolResponse
	err = json.Unmarshal(response.Result.Result, &result)

	return result, err
}

func (t *IdeSearcher) searchRelationByIde(ctx context.Context, request IdeSearchRelationRequest) (IdeSearchRelationResponse, error) {
	requestId := ctx.Value(common2.KeyRequestId)
	if requestId == nil || requestId == "" {
		requestId = uuid.NewString()
	}

	// 添加类型断言的安全检查
	requestIdStr, ok := requestId.(string)
	if !ok {
		return IdeSearchRelationResponse{}, errors.New("requestId is not a string")
	}

	ideToolRequest := &cosyDefinition.ToolInvokeRequest{
		RequestId:  requestIdStr,
		ToolCallId: uuid.NewString(),
		Name:       "search_symbol_relationship",
		Parameters: map[string]any{
			"workspacePath": request.WorkspacePath,
			"filepath":      request.Filepath,
			"startOffset":   request.StartOffset,
			"endOffset":     request.EndOffset,
			"relationshipLimit": map[string]any{
				"reference":    request.RelationshipLimit.Reference,
				"methodCall":   request.RelationshipLimit.MethodCall,
				"implement":    request.RelationshipLimit.Implement,
				"extend":       request.RelationshipLimit.Extend,
				"referenceBy":  request.RelationshipLimit.ReferenceBy,
				"methodCallBy": request.RelationshipLimit.MethodCallBy,
				"implementBy":  request.RelationshipLimit.ImplementBy,
				"extendBy":     request.RelationshipLimit.ExtendBy,
			},
			"timeout": DEFAULT_RELATION_TIMEOUT * 1000,
		},
		Async: false,
	}

	// 使用channel和goroutine实现超时控制
	resultChan := make(chan *cosyDefinition.ToolInvokeResponse, 1)
	errChan := make(chan error, 1)

	go func() {
		ideToolResponse, err := common.InvokeTool(ctx, ideToolRequest, DEFAULT_RELATION_TIMEOUT)
		if err != nil {
			errChan <- err
			return
		}
		resultChan <- ideToolResponse
	}()

	// 等待结果或超时
	select {
	case ideToolResponse := <-resultChan:
		if ideToolResponse == nil {
			return IdeSearchRelationResponse{}, errors.New("ideToolResponse is nil")
		}

		// 将结果转为JSON
		resultJSON, err := json.Marshal(ideToolResponse.Result)
		if err != nil {
			return IdeSearchRelationResponse{}, errors.New("marshal result to JSON error:" + err.Error())
		}

		response := &IdeSearchRelationResponse{}
		if err := json.Unmarshal(resultJSON, response); err != nil {
			return IdeSearchRelationResponse{}, errors.New("unmarshal JSON result error:" + err.Error())
		}

		return *response, nil

	case err := <-errChan:
		return IdeSearchRelationResponse{}, err

	case <-time.After(time.Duration(DEFAULT_RELATION_TIMEOUT)*time.Second + 300*time.Millisecond):
		return IdeSearchRelationResponse{}, errors.New("search relation timeout")
	}
}

func (t *IdeSearcher) searchRelationByLsp(ctx context.Context, request IdeSearchRelationRequest) (IdeSearchRelationResponse, error) {
	requestId := ctx.Value(common2.KeyRequestId)
	if requestId == nil || requestId == "" {
		requestId = uuid.NewString()
	}

	// 添加类型断言的安全检查
	requestIdStr, ok := requestId.(string)
	if !ok {
		return IdeSearchRelationResponse{}, errors.New("requestId is not a string")
	}

	client, err := lsp.GetLspClient(ctx)
	if err != nil {
		return IdeSearchRelationResponse{}, err
	}
	if !client.IsConnected() {
		return IdeSearchRelationResponse{}, errors.New("lsp client not connected")
	}
	if client == nil {
		return IdeSearchRelationResponse{}, errors.New("lsp client is nil")
	}

	response, err := client.SendRequest(ctx, DEFAULT_RELATION_TIMEOUT*1000+300, LspSearchRequest{
		JSONRPC: "2.0",
		Method:  "qoder/symbol_relationship",
		Params: map[string]interface{}{
			"filepath":    request.Filepath,
			"startOffset": request.StartOffset,
			"endOffset":   request.EndOffset,
			"relationshipLimit": map[string]interface{}{
				"reference":    request.RelationshipLimit.Reference,
				"referenceBy":  request.RelationshipLimit.ReferenceBy,
				"methodCall":   request.RelationshipLimit.MethodCall,
				"methodCallBy": request.RelationshipLimit.MethodCallBy,
				"implement":    request.RelationshipLimit.Implement,
				"implementBy":  request.RelationshipLimit.ImplementBy,
				"extend":       request.RelationshipLimit.Extend,
				"extendBy":     request.RelationshipLimit.ExtendBy,
			},
			"timeout": DEFAULT_RELATION_TIMEOUT * 1000,
		},
		ID: requestIdStr,
	})

	if err != nil {
		return IdeSearchRelationResponse{}, err
	}

	var result IdeSearchRelationResponse
	err = json.Unmarshal(response.Result.Result, &result)

	return result, err
}

func (t *IdeSearcher) SearchSegmentByIde(ctx context.Context, request IdeSearchSegmentRequest) (IdeSearchSegmentResponse, error) {
	requestId := ctx.Value(common2.KeyRequestId)
	if requestId == nil || requestId == "" {
		requestId = uuid.NewString()
	}

	// 添加类型断言的安全检查
	requestIdStr, ok := requestId.(string)
	if !ok {
		return IdeSearchSegmentResponse{}, errors.New("requestId is not a string")
	}

	ideToolRequest := &cosyDefinition.ToolInvokeRequest{
		RequestId:  requestIdStr,
		ToolCallId: uuid.NewString(),
		Name:       "search_snippet_relationship",
		Parameters: map[string]any{
			"workspacePath": request.WorkspacePath,
			"filepath":      request.Filepath,
			"startLine":     request.StartLine,
			"endLine":       request.EndLine,
			"timeout":       DEFAULT_SEGMENT_TIMEOUT * 1000,
		},
		Async: false,
	}

	// 使用channel和goroutine实现超时控制
	resultChan := make(chan *cosyDefinition.ToolInvokeResponse, 1)
	errChan := make(chan error, 1)

	go func() {
		ideToolResponse, err := common.InvokeTool(ctx, ideToolRequest, DEFAULT_SEGMENT_TIMEOUT)
		if err != nil {
			errChan <- err
			return
		}
		resultChan <- ideToolResponse
	}()

	// 等待结果或超时
	select {
	case ideToolResponse := <-resultChan:
		if ideToolResponse == nil {
			return IdeSearchSegmentResponse{}, errors.New("ideToolResponse is nil")
		}

		// 将结果转为JSON
		resultJSON, err := json.Marshal(ideToolResponse.Result)
		if err != nil {
			return IdeSearchSegmentResponse{}, errors.New("marshal result to JSON error:" + err.Error())
		}

		response := &IdeSearchSegmentResponse{}
		if err := json.Unmarshal(resultJSON, response); err != nil {
			return IdeSearchSegmentResponse{}, errors.New("unmarshal JSON result error:" + err.Error())
		}

		return *response, nil

	case err := <-errChan:
		return IdeSearchSegmentResponse{}, err

	case <-time.After(time.Duration(DEFAULT_SEGMENT_TIMEOUT)*time.Second + 300*time.Millisecond):
		return IdeSearchSegmentResponse{}, errors.New("search segment timeout")
	}
}
