package ide

import (
	"bufio"
	"context"
	"cosy/log"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// TestCase 测试用例结构
type TestCase struct {
	SymbolName    string `json:"symbolName"`
	WorkspacePath string `json:"workspacePath"`
	Query         string `json:"query"`
}

// SymbolRelationResult 单个符号的关系搜索结果
type SymbolRelationResult struct {
	Symbol           Symbol                     `json:"symbol"`
	RelationRequest  IdeSearchRelationRequest   `json:"relationRequest"`
	RelationResponse *IdeSearchRelationResponse `json:"relationResponse,omitempty"`
	RelationError    string                     `json:"relationError,omitempty"`
	RelationDuration time.Duration              `json:"relationDuration"`
}

// TestResult 测试结果结构
type TestResult struct {
	TestCase          TestCase                 `json:"testCase"`
	SymbolSearchStart time.Time                `json:"symbolSearchStart"`
	SymbolSearchEnd   time.Time                `json:"symbolSearchEnd"`
	SymbolRequest     IdeSearchSymbolRequest   `json:"symbolRequest"`
	SymbolResponse    *IdeSearchSymbolResponse `json:"symbolResponse,omitempty"`
	SymbolError       string                   `json:"symbolError,omitempty"`
	SymbolDuration    time.Duration            `json:"symbolDuration"`

	RelationSearchStart time.Time              `json:"relationSearchStart,omitempty"`
	RelationSearchEnd   time.Time              `json:"relationSearchEnd,omitempty"`
	RelationResults     []SymbolRelationResult `json:"relationResults,omitempty"`
	RelationDuration    time.Duration          `json:"relationDuration,omitempty"`

	IsInternalSymbol bool   `json:"isInternalSymbol"`
	Summary          string `json:"summary"`
}

// TimeStats 时间统计结构
type TimeStats struct {
	Average     float64 `json:"averageMs"` // 平均耗时（毫秒）
	Max         float64 `json:"maxMs"`     // 最大耗时（毫秒）
	Min         float64 `json:"minMs"`     // 最小耗时（毫秒）
	TotalCount  int     `json:"totalCount"`
	TotalTime   float64 `json:"totalTimeMs"` // 总耗时（毫秒）
	SuccessRate float64 `json:"successRate"`
}

// TestSummary 测试总结
type TestSummary struct {
	TotalCases              int           `json:"totalCases"`
	SuccessfulSymbolCases   int           `json:"successfulSymbolCases"`
	FailedSymbolCases       int           `json:"failedSymbolCases"`
	InternalSymbolCases     int           `json:"internalSymbolCases"`
	SuccessfulRelationCases int           `json:"successfulRelationCases"`
	FailedRelationCases     int           `json:"failedRelationCases"`
	TotalDuration           time.Duration `json:"totalDuration"`
	Results                 []TestResult  `json:"results"`
	SymbolSearchStats       TimeStats     `json:"symbolSearchStats"`
	RelationSearchStats     TimeStats     `json:"relationSearchStats"`
}

// calculateTimeStats 计算时间统计
func calculateTimeStats(durations []time.Duration, totalAttempts int) TimeStats {
	if len(durations) == 0 {
		return TimeStats{
			Average:     0,
			Max:         0,
			Min:         0,
			TotalCount:  totalAttempts,
			TotalTime:   0,
			SuccessRate: 0,
		}
	}

	var total time.Duration
	min := durations[0]
	max := durations[0]

	for _, d := range durations {
		total += d
		if d < min {
			min = d
		}
		if d > max {
			max = d
		}
	}

	// 转换为毫秒
	totalMs := float64(total.Milliseconds())
	avgMs := totalMs / float64(len(durations))
	maxMs := float64(max.Milliseconds())
	minMs := float64(min.Milliseconds())

	return TimeStats{
		Average:     avgMs,
		Max:         maxMs,
		Min:         minMs,
		TotalCount:  totalAttempts,
		TotalTime:   totalMs,
		SuccessRate: float64(len(durations)) / float64(totalAttempts) * 100,
	}
}

// TestIdeSearcher 测试IDE搜索器
func TestIdeSearcher(ctx context.Context, workspacePath, testFilePath string) {
	// 设置工作空间路径
	// 读取测试用例文件
	testCases, err := readTestCases(testFilePath, workspacePath)
	if err != nil {
		log.Warnf("Failed to read test cases: %v", err)
	}

	// 创建IDE搜索器
	searcher := NewIdeSearcher()

	// 运行测试
	summary := runIdeSearcherTests(ctx, searcher, testCases)

	// 保存结果到JSON文件
	outputFile := fmt.Sprintf("ide_test_results_%s.json", time.Now().Format("20060102_150405"))
	err = saveTestResults(outputFile, summary)
	if err != nil {
		log.Errorf("Failed to save test results: %v", err)
	} else {
		log.Infof("Test results saved to: %s", outputFile)
	}

	// 打印总结
	log.Infof("Test Summary:")
	log.Infof("Total cases: %d", summary.TotalCases)
	log.Infof("Successful symbol searches: %d", summary.SuccessfulSymbolCases)
	log.Infof("Failed symbol searches: %d", summary.FailedSymbolCases)
	log.Infof("Internal symbols found: %d", summary.InternalSymbolCases)
	log.Infof("Successful relation searches: %d", summary.SuccessfulRelationCases)
	log.Infof("Failed relation searches: %d", summary.FailedRelationCases)
	log.Infof("Total duration: %v", summary.TotalDuration)

	// 打印时间统计
	log.Infof("\nSymbol Search Time Stats:")
	log.Infof("  Average: %.2fms", summary.SymbolSearchStats.Average)
	log.Infof("  Max: %.2fms", summary.SymbolSearchStats.Max)
	log.Infof("  Min: %.2fms", summary.SymbolSearchStats.Min)
	log.Infof("  Success Rate: %.2f%%", summary.SymbolSearchStats.SuccessRate)

	log.Infof("\nRelation Search Time Stats:")
	log.Infof("  Average: %.2fms", summary.RelationSearchStats.Average)
	log.Infof("  Max: %.2fms", summary.RelationSearchStats.Max)
	log.Infof("  Min: %.2fms", summary.RelationSearchStats.Min)
	log.Infof("  Success Rate: %.2f%%", summary.RelationSearchStats.SuccessRate)
}

// readTestCases 读取测试用例文件
func readTestCases(filename, workspacePath string) ([]TestCase, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	var testCases []TestCase
	scanner := bufio.NewScanner(file)

	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line == "" || strings.HasPrefix(line, "#") {
			continue // 跳过空行和注释行
		}

		testCases = append(testCases, TestCase{
			SymbolName:    line,
			WorkspacePath: workspacePath,
			Query:         line,
		})
	}

	if err := scanner.Err(); err != nil {
		return nil, err
	}

	return testCases, nil
}

// runIdeSearcherTests 运行IDE搜索器测试
func runIdeSearcherTests(ctx context.Context, searcher *IdeSearcher, testCases []TestCase) TestSummary {
	summary := TestSummary{
		TotalCases: len(testCases),
		Results:    make([]TestResult, 0, len(testCases)),
	}

	startTime := time.Now()

	// 用于收集时间统计
	var symbolDurations []time.Duration
	var relationDurations []time.Duration
	totalSymbolAttempts := 0
	totalRelationAttempts := 0

	for i, testCase := range testCases {
		log.Infof("Running test case %d/%d: %s", i+1, len(testCases), testCase.SymbolName)

		result := TestResult{
			TestCase: testCase,
		}

		// 第一步：搜索符号
		result.SymbolRequest = IdeSearchSymbolRequest{
			WorkspacePath:  testCase.WorkspacePath,
			Filepath:       "", // 不指定具体文件，全局搜索
			StartLine:      0,
			EndLine:        0,
			Query:          testCase.Query,
			MaxResultCount: 10,
		}

		result.SymbolSearchStart = time.Now()
		symbolResponse, err := searcher.SearchSymbolByIde(ctx, result.SymbolRequest)
		result.SymbolSearchEnd = time.Now()
		result.SymbolDuration = result.SymbolSearchEnd.Sub(result.SymbolSearchStart)
		// 收集符号搜索耗时统计
		totalSymbolAttempts++
		if err == nil {
			symbolDurations = append(symbolDurations, result.SymbolDuration)
		}

		if err != nil {
			result.SymbolError = err.Error()
			result.Summary = fmt.Sprintf("Symbol search failed: %s", err.Error())
			summary.FailedSymbolCases++
		} else {
			result.SymbolResponse = &symbolResponse
			result.Summary = fmt.Sprintf("Found %d symbols", len(symbolResponse.Symbols))
			summary.SuccessfulSymbolCases++

			// 检查是否有内部符号
			internalSymbols := filterInternalSymbols(symbolResponse.Symbols, testCase.WorkspacePath)
			if len(internalSymbols) > 0 {
				result.IsInternalSymbol = true
				summary.InternalSymbolCases++

				// 第二步：搜索关系（搜索所有内部符号）
				result.RelationSearchStart = time.Now()
				relationResults := searchAllSymbolRelations(ctx, searcher, internalSymbols, testCase.WorkspacePath)
				result.RelationSearchEnd = time.Now()
				result.RelationDuration = result.RelationSearchEnd.Sub(result.RelationSearchStart)
				result.RelationResults = relationResults

				// 统计成功和失败的关系搜索
				successfulRelations := 0
				failedRelations := 0
				totalRelations := 0

				for _, relResult := range relationResults {
					totalRelationAttempts++
					if relResult.RelationError == "" {
						totalRelations += countRelations(relResult.RelationResponse.Relationships)
						if totalRelations > 0 {
							successfulRelations++
						} else {
							failedRelations++
						}
						relationDurations = append(relationDurations, relResult.RelationDuration)
					} else {
						failedRelations++
					}
				}

				if successfulRelations > 0 {
					result.Summary += fmt.Sprintf("; Found %d relations from %d/%d symbols", totalRelations, successfulRelations, len(internalSymbols))
					summary.SuccessfulRelationCases++
				}
				if failedRelations > 0 {
					result.Summary += fmt.Sprintf("; %d relation searches failed", failedRelations)
					summary.FailedRelationCases++
				}
			} else {
				result.Summary += "; No internal symbols found"
			}
		}

		summary.Results = append(summary.Results, result)
	}

	summary.TotalDuration = time.Since(startTime)

	// 计算并设置时间统计
	summary.SymbolSearchStats = calculateTimeStats(symbolDurations, totalSymbolAttempts)
	summary.RelationSearchStats = calculateTimeStats(relationDurations, totalRelationAttempts)

	return summary
}

// filterInternalSymbols 过滤内部符号
func filterInternalSymbols(symbols []Symbol, workspacePath string) []Symbol {
	var internalSymbols []Symbol
	for _, symbol := range symbols {
		if strings.HasPrefix(symbol.Filepath, workspacePath) {
			internalSymbols = append(internalSymbols, symbol)
		}
	}
	return internalSymbols
}

// searchAllSymbolRelations 搜索所有符号的关系
func searchAllSymbolRelations(ctx context.Context, searcher *IdeSearcher, symbols []Symbol, workspacePath string) []SymbolRelationResult {
	if len(symbols) == 0 {
		return []SymbolRelationResult{}
	}

	var relationResults []SymbolRelationResult

	// 对每个符号进行关系搜索
	for _, symbol := range symbols {
		request := IdeSearchRelationRequest{
			WorkspacePath: workspacePath,
			Filepath:      symbol.Filepath,
			StartOffset:   symbol.StartOffset,
			EndOffset:     symbol.EndOffset,
			RelationshipLimit: RelationshipLimit{
				Reference:    10,
				MethodCall:   10,
				Implement:    10,
				Extend:       10,
				ReferenceBy:  10,
				MethodCallBy: 10,
				ImplementBy:  10,
				ExtendBy:     10,
			},
		}

		relationResult := SymbolRelationResult{
			Symbol:          symbol,
			RelationRequest: request,
		}

		searchStart := time.Now()
		response, err := searcher.SearchRelationByIde(ctx, request)
		relationResult.RelationDuration = time.Since(searchStart)

		if err != nil {
			relationResult.RelationError = err.Error()
		} else {
			relationResult.RelationResponse = &response
		}

		relationResults = append(relationResults, relationResult)
	}

	return relationResults
}

// countRelations 计算关系数量
func countRelations(relations Relationships) int {
	count := 0
	count += len(relations.Reference)
	count += len(relations.MethodCall)
	count += len(relations.Implement)
	count += len(relations.Extend)
	count += len(relations.ReferenceBy)
	count += len(relations.MethodCallBy)
	count += len(relations.ImplementBy)
	count += len(relations.ExtendBy)
	return count
}

// saveTestResults 保存测试结果到JSON文件
func saveTestResults(filename string, summary TestSummary) error {
	// 创建输出目录
	dir := filepath.Dir(filename)
	if dir != "" && dir != "." {
		err := os.MkdirAll(dir, 0755)
		if err != nil {
			return err
		}
	}

	// 序列化为JSON
	jsonData, err := json.MarshalIndent(summary, "", "  ")
	if err != nil {
		return err
	}

	// 写入文件
	return os.WriteFile(filename, jsonData, 0644)
}
