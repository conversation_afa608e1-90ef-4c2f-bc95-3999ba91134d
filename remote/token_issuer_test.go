package remote

import (
	"cosy/client"
	"cosy/config"
	"cosy/log"
	"cosy/user"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func Test_concurrentSafeRenewToken(t *testing.T) {
	client.InitClients()
	config.InitLocalConfig()
	routineNums := 30
	wg := sync.WaitGroup{}
	wg.Add(routineNums)
	userInfo := user.GetCachedUserInfo()
	cosyUser := &user.CosyUser{
		Name:               userInfo.Name,
		Aid:                userInfo.Aid,
		Uid:                userInfo.Uid,
		YxUid:              userInfo.YxUid,
		OrgId:              userInfo.OrgId,
		OrgName:            userInfo.OrgName,
		AvatarUrl:          userInfo.AvatarUrl,
		LoginTimestamp:     time.Now().Unix(),
		UserSourceChannel:  userInfo.UserSourceChannel,
		RefreshToken:       userInfo.RefreshToken,
		SecurityOauthToken: userInfo.SecurityOauthToken,
		UserType:           userInfo.UserType,
	}

	for i := 0; i < routineNums; i++ {
		go func() {
			defer wg.Done()
			err := concurrentSafeRenewToken(cosyUser)
			if err != nil {
				assert.Fail(t, "renew token error", err)
			}
		}()
	}

	wg.Wait()

}

func TestRequestRemoteToken(t *testing.T) {
	tests := []struct {
		name    string
		wantErr assert.ErrorAssertionFunc
	}{
		{
			name: "test",
		},
	}
	config.InitLocalConfig()
	client.InitClients()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := RequestRemoteToken()
			assert.Nil(t, err)
			log.Info(got)
		})
	}
}
