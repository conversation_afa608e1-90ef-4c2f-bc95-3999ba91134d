package remote

import (
	"cosy/definition"
	"net/http"
	"time"
)

const (
	// ClientDedicated 专属
	ClientDedicated = 1

	// ClientCommon 0: 通用类型
	ClientCommon = 0
)

type HttpPayload struct {

	//负载
	Payload string `json:"payload"`

	// 加密版本
	EncodeVersion string `json:"encodeVersion"`
}

type AuthQueryParam struct {
	Ak                 string                    `json:"accessKey,omitempty"`
	Sk                 string                    `json:"secretKey,omitempty"`
	SecurityToken      string                    `json:"securityToken,omitempty"`
	UserId             string                    `json:"userId,omitempty"`
	OrgId              string                    `json:"orgId,omitempty"`
	Token              string                    `json:"token,omitempty"`
	PersonalToken      string                    `json:"personalToken"`
	SecurityOauthToken string                    `json:"securityOauthToken"`
	RefreshToken       string                    `json:"refreshToken"`
	NeedRefresh        bool                      `json:"needRefresh"`
	AuthInfo           definition.CustomAuthInfo `json:"authInfo"`
}

type LogActionParam struct {
	UserId   string `json:"userId,omitempty"`
	OrgId    string `json:"orgId,omitempty"`
	ClientIp string `json:"clientIp,omitempty"`
}

// BigModelSvcRequest 定义了一个结构体，用于封装请求参数
type BigModelSvcRequest struct {
	ServiceName string      `json:"service_name"` // 服务名称
	FetchKey    string      `json:"fetch_key"`    // 获取键
	Async       bool        `json:"async"`        // 是否异步
	RequestBody interface{} `json:"request_body"` // 请求体
	RequestID   string      `json:"request_id"`   // 请求ID
	AgentID     string      `json:"agent_id"`     // 代理ID
}

type RemoteRegionConfig struct {

	/**
	 * 中心节点, 默认是主域名
	 */
	CenterNodes []string `json:"centerNodes"`

	/**
	 * 业务数据节点，首次确定后不能漂移
	 * key: remote_agent , codebase
	 * value:  [urls]
	 * 参考格式：
	    - https://repo2.qoder.sh
		- https://daily-repo2.qoder.sh
		- https://test-repo2.qoder.sh
	*/
	DataNodeMap map[string][]string `json:"dataNodeMap"` //用户数据节点

	/**
	 * 推理节点，可以按region就近漂移
	 */
	InferNodes []string `json:"inferNodes"` //推理节点
}

// IsEmpty 检查RegionUrlConfig是否为空
func (r *RemoteRegionConfig) IsEmpty() bool {
	return len(r.CenterNodes) == 0 && len(r.DataNodeMap) == 0 && len(r.InferNodes) == 0
}

// GetAllEndpoints 获取所有端点URL列表
func (r *RemoteRegionConfig) GetAllEndpoints() []string {
	var endpoints []string

	// 添加中心节点
	endpoints = append(endpoints, r.CenterNodes...)

	// 添加推理节点
	endpoints = append(endpoints, r.InferNodes...)

	// 添加数据节点
	for _, nodeList := range r.DataNodeMap {
		endpoints = append(endpoints, nodeList...)
	}

	return endpoints
}

// GetDataNodes 获取所有数据节点URL列表
func (r *RemoteRegionConfig) GetDataNodes() []string {
	var dataNodes []string
	for _, nodeList := range r.DataNodeMap {
		dataNodes = append(dataNodes, nodeList...)
	}
	return dataNodes
}

// RetryConfig 重试配置
type RetryConfig struct {
	MaxRetries     int           `json:"maxRetries"`     // 最大重试次数
	RetryInterval  time.Duration `json:"retryInterval"`  // 重试间隔
	BackoffFactor  float64       `json:"backoffFactor"`  // 退避系数
	TimeoutPerTry  time.Duration `json:"timeoutPerTry"`  // 每次重试的超时时间
	EnableFailover bool          `json:"enableFailover"` // 是否启用故障切换
}

// RequestResult HA请求结果
type RequestResult struct {
	Response     *http.Response
	Error        error
	Endpoint     string        // 实际使用的端点
	Latency      time.Duration // 请求延迟
	AttemptCount int           // 尝试次数
}
