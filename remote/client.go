package remote

import (
	"bytes"
	"context"
	"cosy/client"
	"cosy/definition"
	"cosy/log"
	"cosy/sse"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	agentClient "code.alibaba-inc.com/cosy/lingma-agent-graph/llms/openai/openaiclient"
)

// CommonAgentRequestParams 定义请求参数的结构体
type CommonAgentRequestParams struct {
	ServiceName         string
	FetchKey            string
	ModelRequest        any
	Timeout             time.Duration
	RequestId           string
	AgentId             string
	OutputFormatVersion string //模型输出格式 2，新版openAI格式解析；其他默认解析
}

type CommonAgentResponse struct {
	Text      string
	RequestId string
	Usage     definition.LlmUsage `json:"usage"`
	err       error
	Tools     []agentClient.ToolCall
}

func (c *CommonAgentResponse) IsSuccess() bool {
	return c.err == nil
}

// ExecuteStreamedAgentRequestWithModelConfig 执行流式请求
// 注：
// - requestParams.OutputFormatVersion 用于控制是否按新版openAI格式解析，OutputFormatVersion: 2，使用新版格式解析
// - 调用方需要保证OutputFormatVersion与agentService的输出匹配
// - 多模态场景下，调用方需要保证请求中Message格式与调用的service是否支持匹配
func ExecuteStreamedAgentRequestWithModelConfig(ctx context.Context, requestParams CommonAgentRequestParams, modelConfig *definition.ModelConfig) (CommonAgentResponse, error) {
	agentResponse := CommonAgentResponse{}

	timeout := requestParams.Timeout
	agentId := requestParams.AgentId

	svcRequest := BigModelSvcRequest{
		ServiceName: requestParams.ServiceName,
		FetchKey:    requestParams.FetchKey,
		Async:       true,
		RequestBody: requestParams.ModelRequest,
		RequestID:   requestParams.RequestId,
		AgentID:     requestParams.AgentId,
	}

	req, reqBuildErr := BuildBigModelSvcRequestWithConfig(svcRequest, modelConfig, nil)
	if reqBuildErr != nil {
		log.Errorf("Build new request failed")
		return agentResponse, errors.New("build agent request error")
	}
	sseClient := sse.NewSseChatClient(map[string]string{})

	doneChan := make(chan int, 1)

	//新版openAI 格式output
	isV2Output := requestParams.OutputFormatVersion == "2"

	modelOutputText := strings.Builder{}

	// Parse response
	chatResponse := agentClient.ChatCompletionResponse{
		Choices: []*agentClient.ChatCompletionChoice{
			{},
		},
	}

	go func() {
		_ = sseClient.Subscribe(req, requestParams.Timeout, func(msg *sse.Event) {
			var response definition.ChatResponse
			if string(msg.Event) == "error" {
				agentResponse.err = fmt.Errorf("execute agent request error. agentId: %s", agentId)

				log.Warnf("execute agent request error. agentId: %s", agentId)
				doneChan <- 1
				return
			}
			if string(msg.Event) == "finish" {
				agentResponse.err = nil

				doneChan <- 1
				return
			}
			err := json.Unmarshal(msg.Data, &response)
			if err != nil {
				agentResponse.err = fmt.Errorf("execute agent request unmarshal response error. agentId: %s, error: %v, data: %s", agentId, err, msg.Data)

				log.Warnf("execute agent request unmarshal response error. agentId: %s, error: %v, data: %s", agentId, err, msg.Data)
				doneChan <- 1
				return
			}
			if response.StatusCodeValue != 200 {
				message := response.Body
				agentResponse.err = fmt.Errorf("execute agent request finished unexpected, agentId: %s, reason: %s, statusCode: %v", agentId, message, response.StatusCode)

				log.Warnf("execute agent request finished unexpected, agentId: %s, reason: %s, statusCode: %v", agentId, message, response.StatusCode)

				doneChan <- 1
				return
			}
			if !isV2Output {
				body, err := definition.NewChatBody(response.Body)
				if err != nil {
					agentResponse.err = fmt.Errorf("execute agent request parse body error. agentId: %s, reason: %v", agentId, err)

					log.Warnf("execute agent request parse body error. agentId: %s, reason: %v, body: %s", agentId, err, response.Body)

					doneChan <- 1
					return
				}
				// 清空modelOutputText，老版输出全量内容
				modelOutputText.Reset()
				modelOutputText.WriteString(body.GetOutputText())
				agentResponse.RequestId = body.RequestId
				agentResponse.Usage = body.Usage
			} else {
				bodyData := response.Body
				if bodyData == "[DONE]" {
					return
				}
				var streamResponse agentClient.StreamedChatResponsePayload
				err = json.NewDecoder(bytes.NewReader([]byte(bodyData))).Decode(&streamResponse)
				if err != nil {
					log.Errorf("failed to decode stream payload: %v", err)
					return
				}
				agentResponse.RequestId = streamResponse.ID

				if streamResponse.Error != nil {
					agentResponse.err = err
					return
				}

				if streamResponse.Usage != nil {
					chatResponse.Usage.CompletionTokens = streamResponse.Usage.CompletionTokens
					chatResponse.Usage.PromptTokens = streamResponse.Usage.PromptTokens
					chatResponse.Usage.TotalTokens = streamResponse.Usage.TotalTokens
					chatResponse.Usage.CompletionTokensDetails.ReasoningTokens = streamResponse.Usage.CompletionTokensDetails.ReasoningTokens
				}

				if len(streamResponse.Choices) == 0 {
					return
				}
				choice := streamResponse.Choices[0]
				chatResponse.Choices[0].Message.Content += choice.Delta.Content
				chatResponse.Choices[0].FinishReason = choice.FinishReason
				chatResponse.Choices[0].Message.ReasoningContent = choice.Delta.ReasoningContent

				if choice.Delta.FunctionCall != nil {
					//FunctionCall not supported
					log.Warnf("execute agent request function call not supported. agentId: %s", agentId)
				}

				if len(choice.Delta.ToolCalls) > 0 {
					_, tools := updateToolCalls(chatResponse.Choices[0].Message.ToolCalls,
						choice.Delta.ToolCalls)
					chatResponse.Choices[0].Message.ToolCalls = tools
				}

			}
		}, func() {
			log.Warnf("execute agent request timeout. execute agent agentId: %s", agentId)

			agentResponse.err = fmt.Errorf("execute agent request timeout. execute agent agentId: %s", agentId)

			doneChan <- 1
		})
	}()

	select {
	case <-doneChan:
		if isV2Output {
			agentResponse.Text = chatResponse.Choices[0].Message.Content
			agentResponse.Tools = chatResponse.Choices[0].Message.ToolCalls
		} else {
			agentResponse.Text = modelOutputText.String()
		}

		break
		//return agentResponse, nil
	case <-time.After(timeout - time.Second):
		// 超时，执行相应处理，少1s让这里先超时
		log.Warnf("execute agent request async operation timed out. agentId: %s", agentId)

		return agentResponse, &definition.TimeoutError{}
	}

	return agentResponse, nil
}

func ExecuteStreamedAgentRequest(serviceName string, fetchKey string, modelRequest any, timeout time.Duration, requestId, agentId string) (CommonAgentResponse, error) {
	requestParams := CommonAgentRequestParams{
		ServiceName:  serviceName,
		FetchKey:     fetchKey,
		ModelRequest: modelRequest,
		Timeout:      timeout,
		RequestId:    requestId,
		AgentId:      agentId,
	}
	return ExecuteStreamedAgentRequestWithModelConfig(context.Background(), requestParams, nil)
}

// 不支持新版openAI出入参格式
func ExecuteAgentRequest(agentChatAskServiceName string, fetchKey string, modelRequest any,
	requestId, agentId string) (CommonAgentResponse, error) {
	agentResponse := CommonAgentResponse{}

	svcRequest := BigModelSvcRequest{
		ServiceName: agentChatAskServiceName,
		FetchKey:    fetchKey,
		Async:       false,
		RequestBody: modelRequest,
		RequestID:   requestId,
		AgentID:     agentId,
	}
	req, reqBuildErr := BuildBigModelSvcRequestWithConfig(svcRequest, nil, nil)
	if reqBuildErr != nil {
		log.Errorf("Build new request failed")
		return agentResponse, reqBuildErr
	}
	resp, err := client.GetDefaultClient().Do(req)
	if err != nil {
		log.Error("Failed to request remote model: ", err)
		return agentResponse, err
	}
	defer resp.Body.Close()
	if resp.StatusCode == http.StatusRequestTimeout {
		log.Error("Request timeout, use minimal context length in later requests")
		return agentResponse, errors.New("agent request timeout error")
	}

	// fetch result from response
	responseBody, _ := io.ReadAll(resp.Body)

	if resp.StatusCode != http.StatusOK {
		log.Error("Response not ok. statusCode: %d, data: %s", resp.StatusCode, string(responseBody))
		return agentResponse, errors.New("agent request not ok")
	}

	// parse response
	var response definition.ApiResponse
	if err = json.Unmarshal(responseBody, &response); err != nil {
		log.Debugf("Response data: %s", strings.Trim(string(responseBody), " \t\r\n"))
		log.Errorf("Failed to unmarshal response body: %s, err: %+v", string(responseBody), err)
		return agentResponse, errors.New("build agent request error")
	}

	if !response.Success {
		log.Debugf("Response data: %s", strings.Trim(string(responseBody), " \t\r\n"))

		return agentResponse, errors.New("request agent error")
	}

	var predictionResult definition.GraphResult
	if err = json.Unmarshal([]byte(response.Result.Body), &predictionResult); err != nil {
		log.Debugf("Response data: %s", strings.Trim(string(responseBody), " \t\r\n"))
		log.Errorf("Failed to unmarshal response result: %s", err)
		return agentResponse, errors.New("parse GraphResult error")
	}
	log.Debug("NodesPath: ", predictionResult.NodePath)

	rawModelOutput, ok := predictionResult.Outputs["llm_model_result"].(string)
	if !ok || !predictionResult.Success {
		log.Debugf("Response data: %s", strings.Trim(string(responseBody), " \t\r\n"))
		log.Error("Failed to fetch remote model's output")
		return agentResponse, errors.New("parse llm_model_result error")
	}
	llmResponse := definition.LlmResponse{}
	if err = json.Unmarshal([]byte(rawModelOutput), &llmResponse); err != nil {
		log.Errorf("Failed to unmarshal llmOutput result: %s", err)
		return agentResponse, errors.New("parse llmOutput error")
	}

	agentResponse.Text = llmResponse.Output.Text
	agentResponse.err = nil

	return agentResponse, nil
}

func ExecuteMultimodalAgentRequest(agentChatAskServiceName string, fetchKey string, modelRequest any,
	requestId, agentId string) (CommonAgentResponse, error) {
	agentResponse := CommonAgentResponse{}

	svcRequest := BigModelSvcRequest{
		ServiceName: agentChatAskServiceName,
		FetchKey:    fetchKey,
		Async:       false,
		RequestBody: modelRequest,
		RequestID:   requestId,
		AgentID:     agentId,
	}
	req, reqBuildErr := BuildBigModelSvcRequestWithConfig(svcRequest, nil, nil)
	if reqBuildErr != nil {
		log.Errorf("Build new request failed")
		return agentResponse, reqBuildErr
	}
	resp, err := client.GetDefaultClient().Do(req)
	if err != nil {
		log.Error("Failed to request remote model: ", err)
		return agentResponse, err
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusRequestTimeout {
		log.Error("Request timeout, use minimal context length in later requests")
		return agentResponse, errors.New("agent request timeout error")
	}

	// fetch result from response
	responseBody, _ := io.ReadAll(resp.Body)

	if resp.StatusCode != http.StatusOK {
		log.Error("Response not ok. statusCode: %d, data: %s", resp.StatusCode, string(responseBody))
		return agentResponse, errors.New("agent request not ok")
	}

	// parse response
	var response definition.ApiResponse
	if err = json.Unmarshal(responseBody, &response); err != nil {
		log.Debugf("Response data: %s", strings.Trim(string(responseBody), " \t\r\n"))
		log.Errorf("Failed to unmarshal response body: %s, err: %+v", string(responseBody), err)
		return agentResponse, errors.New("build agent request error")
	}

	if !response.Success {
		log.Debugf("Response data: %s", strings.Trim(string(responseBody), " \t\r\n"))

		return agentResponse, errors.New("request agent error")
	}

	var predictionResult definition.GraphResult
	if err = json.Unmarshal([]byte(response.Result.Body), &predictionResult); err != nil {
		log.Debugf("Response data: %s", strings.Trim(string(responseBody), " \t\r\n"))
		log.Errorf("Failed to unmarshal response result: %s", err)
		return agentResponse, errors.New("parse GraphResult error")
	}
	log.Debug("NodesPath: ", predictionResult.NodePath)

	rawModelOutput, ok := predictionResult.Outputs["llm_model_result"].(string)
	if !ok || !predictionResult.Success {
		log.Debugf("Response data: %s", strings.Trim(string(responseBody), " \t\r\n"))
		log.Error("Failed to fetch remote model's output")
		return agentResponse, errors.New("parse llm_model_result error")
	}

	chatBody, err := definition.NewChatBody(rawModelOutput)
	if err != nil {
		log.Errorf("Failed to new chatBody result: %s", err)
		return agentResponse, errors.New("parse llmOutput error")
	}
	agentResponse.Text = chatBody.GetOutputText()
	agentResponse.err = nil

	return agentResponse, nil
}

// 只会有一个tool调用
func updateToolCalls(tools []agentClient.ToolCall, delta []*agentClient.ToolCall) ([]byte, []agentClient.ToolCall) {
	if len(delta) == 0 {
		return []byte{}, tools
	}
	chunk, _ := json.Marshal(delta) // nolint:errchkjson

	tool0 := delta[0]
	if len(tools) <= 0 {
		//解析到新tool call
		tools = append(tools, *tool0)
		return chunk, tools
	}

	tools[0].Function.Arguments += tool0.Function.Arguments

	return chunk, tools
}
