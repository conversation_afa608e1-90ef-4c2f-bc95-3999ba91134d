package remote

import (
	"cosy/config"
	"cosy/user"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"cosy/client"
	"cosy/definition"
	"cosy/log"
	"cosy/util"
)

const (
	_tokenRenewAdvancePeriodInMill = 1 * 60 * 60 * 1000
)

var (
	isOnRenew = false
)

func CheckSecurityToken() error {
	if config.OnPremiseMode {
		return nil
	}
	userInfo := user.GetCachedUserInfo()
	if userInfo == nil {
		return fmt.Errorf("cached userinfo is nil")
	}
	tokenTTL := userInfo.TokenExpireTime - time.Now().UnixMilli()
	// 刷新token
	if tokenTTL > 0 && tokenTTL < _tokenRenewAdvancePeriodInMill {
		cosyUser := &user.CosyUser{
			Name:               userInfo.Name,
			Aid:                userInfo.Aid,
			Uid:                userInfo.Uid,
			YxUid:              userInfo.YxUid,
			OrgId:              userInfo.OrgId,
			OrgName:            userInfo.OrgName,
			AvatarUrl:          userInfo.AvatarUrl,
			LoginTimestamp:     time.Now().Unix(),
			UserSourceChannel:  userInfo.UserSourceChannel,
			RefreshToken:       userInfo.RefreshToken,
			SecurityOauthToken: userInfo.SecurityOauthToken,
			UserType:           userInfo.UserType,
			DataPolicyAgreed:   userInfo.DataPolicyAgreed,
		}
		_ = concurrentSafeRenewToken(cosyUser)
	} else if tokenTTL < 0 { // Token 已经过期
		log.Warnf("security token has expired, logout automatically")
		user.Logout()
		return &definition.TokenExpiredError{
			Msg: "security token has expired",
		}
	}

	return nil
}

// RequestRemoteToken 获取远程沙盒使用的token
func RequestRemoteToken() (definition.UserStatusResponse, error) {
	userStatus := definition.UserStatusResponse{}

	req, err := BuildBigModelAuthRequest(http.MethodGet, definition.UrlRequestRemoteToken, nil)
	if err != nil {
		return userStatus, err
	}
	resp, err := client.GetDefaultClient().Do(req)
	if err != nil {
		return userStatus, err
	}
	defer resp.Body.Close()
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return userStatus, err
	}
	if resp.StatusCode != http.StatusOK {
		return userStatus, fmt.Errorf("request remote token error. status code: %d. response body: %s", resp.StatusCode, bodyBytes)
	}
	err = json.Unmarshal(bodyBytes, &userStatus)
	if err != nil {
		return userStatus, err
	}
	return userStatus, nil
}

func concurrentSafeRenewToken(cosyUser *user.CosyUser) error {
	if isOnRenew {
		log.Info("someone else is renewing the token")
		return nil
	}
	isOnRenew = true
	defer func() {
		isOnRenew = false
	}()

	httpClient := client.GetDefaultClient()
	userInfo := user.GetCachedUserInfo()
	authQueryParam := AuthQueryParam{
		UserId:             userInfo.Uid,
		OrgId:              userInfo.OrgId,
		SecurityOauthToken: cosyUser.SecurityOauthToken,
		RefreshToken:       cosyUser.RefreshToken,
		NeedRefresh:        true,
	}
	httpPayload := HttpPayload{
		Payload:       util.ToJsonStr(authQueryParam),
		EncodeVersion: config.Remote.MessageEncode,
	}
	request, err := BuildBigModelSignRequest(http.MethodPost, definition.UrlRefreshToken, httpPayload)
	if err != nil {
		log.Warnf("Failed to build renew token request: " + err.Error())
		return err
	}
	resp, err := httpClient.Do(request)
	if err != nil {
		log.Warnf("Failed to send renew token request: " + err.Error())
		return fmt.Errorf("failed to send renew token request")
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		log.Warnf("Failed to get renew token response: " + resp.Status)
		return fmt.Errorf("failed to get renew token response")
	}
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Warnf("Failed to read renew token response: " + err.Error())
		return fmt.Errorf("failed to read renew token response")
	}
	if len(responseBody) <= 0 {
		log.Warnf("Failed to fetch renew token result, response is empty.")
		return fmt.Errorf("failed to fetch renew token result, response is empty")
	}
	if strings.Contains(string(responseBody), "\"success\":false") {
		// 这个当前Search服务的一个BUG，当遇到403等错误时，返回状态码200，错误在Body里面
		log.Warnf("Renew token got error from auth server: " + string(responseBody))
		return fmt.Errorf("renew token got error from auth server")
	}
	response := definition.UserStatusResponse{}
	err = json.Unmarshal(responseBody, &response)
	if err != nil {
		log.Warnf("Failed to parse renew token  response. resp=%s, error=%v", string(responseBody), err)
		return fmt.Errorf("failed to parse renew token response")
	}
	if response.RefreshToken == "" || response.SecurityOauthToken == "" || response.ExpireTime <= 0 {
		return fmt.Errorf("renew token response invalid")
	}

	cosyUser.RefreshToken = response.RefreshToken
	cosyUser.SecurityOauthToken = response.SecurityOauthToken
	cosyUser.TokenExpireTime = response.ExpireTime
	err = user.SaveUserInfo(cosyUser, userInfo.Token)
	if err != nil {
		return err
	}
	log.Info("renew token success. new expire time: ", response.ExpireTime)
	return nil
}
