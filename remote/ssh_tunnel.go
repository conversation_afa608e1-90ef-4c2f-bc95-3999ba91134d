package remote

import (
	"cosy/config"
	"cosy/log"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"strings"
)

const (
	sshRelayUriTemplate = "/api/v2/remoteAgent/qoder/tasks/%s?withExecution=%t&Encode=%s"
)

// CalculateSSHPassword 计算SSH密码，RemoteAgent模式下，需要用户态计算一个带身份的请求给到SSH Relay
// Relay使用这些信息去RemoteAgentServer验证身份有效性
func CalculateSSHPassword(taskId string) (string, error) {
	urlPath := fmt.Sprintf(sshRelayUriTemplate, taskId, true, config.Remote.MessageEncode)
	req, err := BuildBigModelAuthRequest("GET", urlPath, nil)
	if err != nil {
		return "", err
	}
	headers := map[string]string{}
	for key, values := range req.Header {
		headers[key] = strings.Join(values, "; ")
	}
	data := map[string]any{
		"url":     req.URL.String(),
		"body":    "",
		"headers": headers,
		"method":  "GET",
	}
	marshal, err := json.Marshal(data)
	if err != nil {
		return "", err
	}
	sshPassword := base64.StdEncoding.EncodeToString(marshal)
	log.Debugf("calculated ssh relay password: %s", sshPassword)
	return sshPassword, nil
}
