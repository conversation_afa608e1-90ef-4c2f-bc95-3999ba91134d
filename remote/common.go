package remote

import (
	"cosy/config"
	"cosy/definition"
	"cosy/global"
	"cosy/log"
	"cosy/util"
	"encoding/json"
	"net/http"
	"strings"

	"github.com/spf13/cast"
)

var appCode = "cosy"
var appSalt = "d2FyLCB3YXIgbmV2ZXIgY2hhbmdlcw=="
var appSaltVpc = "&Q3C3!N5mP5bbNcyryMY@KZtUFLRGbTe"

const (
	LingmaPreEndpoint = "https://devops.aliyun.com/search"
)

func addBasicHeaders(req *http.Request) {
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	req.Header.Set("Accept-Encoding", "identity")

	req.Header.Set("Cosy-Version", global.CosyVersion)
	req.Header.Set("Cosy-ClientIp", util.GetMachineIp())
	req.Header.Set("Cosy-MachineId", util.GetMachineId(true))
	req.Header.Set("Cosy-ClientType", cast.ToString(getClientType()))

	//升级协议
	req.Header.Set("Login-Version", "v2")

	if config.Remote.BigModelCookie != "" {
		req.Header.Set("Cookie", config.Remote.BigModelCookie)
	}
	if config.Remote.BigModelHost != "" {
		req.Host = config.Remote.BigModelHost
	}

}

// client端类型
// 0: 通用类型
// 1: 专属
func getClientType() int {
	return ClientCommon
}

func getAppSalt() string {
	if config.OnPremiseMode {
		return appSaltVpc
	}
	return appSalt
}

// 修复url，必须是/algo结尾的url，因为灵码/预发/qoder动态下发的Endpoint格式都不一样，下发的Endpoint没有algo前缀，特殊处理
// https://devops.aliyun.com/search		灵码日常/预发 endpoint，不需要额外添加algo前缀
// https://lingma-api.tongyi.aliyun.com  灵码生产主域名，需要algo前缀
// 其他，qoder国外多region，均需要algo前缀
func BuildAlgoForRequestUrl(endpoint string, urlPath string) string {
	if strings.HasPrefix(endpoint, LingmaPreEndpoint) {
		//日常/预发免algo前缀
		return endpoint + urlPath
	}

	if strings.HasSuffix(endpoint, definition.UrlPathAlgoPrefix) {
		return endpoint + urlPath
	}

	if strings.HasPrefix(urlPath, definition.UrlPathAlgoPrefix) {
		return endpoint + urlPath
	}
	return endpoint + definition.UrlPathAlgoPrefix + urlPath
}

func recordLocalLog(requestBody interface{}, url string) {
	if config.SignDataPolicyStatus == definition.SignStatusAgree &&
		requestBody != nil &&
		(strings.Contains(url, "invoke") || strings.Contains(url, "sse")) {
		// 用于数据回流，在本地记录用户上传的数据长度
		data, err := json.Marshal(requestBody)
		if err == nil {
			log.Infof("This request is to securely record %d bytes of data", len(data))
		} else {
			log.Warnf("encoding error during local record data back-flow,%s", err.Error())
		}
	}
}
