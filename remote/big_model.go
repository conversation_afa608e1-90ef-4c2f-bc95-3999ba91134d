package remote

import (
	"bytes"
	"context"
	"cosy/client"
	"cosy/config"
	"cosy/definition"
	"cosy/global"
	"cosy/log"
	"cosy/user"
	"cosy/util/encrypt"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"
)

// RequestType 定义请求类型
type RequestType string

const (
	RequestTypeAuth   RequestType = "auth"
	RequestTypeSign   RequestType = "sign"
	RequestTypeUpload RequestType = "upload"
)

// RequestBuilder 请求构建器配置
type RequestBuilder struct {
	RouteType   string //可选，路由机制，auto默认/center中心化/infer推理
	Method      string
	URLPath     string
	RequestBody interface{}
	RequestType RequestType
	ModelConfig *definition.ModelConfig
	EnableHA    bool
	Endpoint    string
	Context     context.Context
}

// BuildBigModelSvcRequestWithConfig 构建大模型请求，支持指定模型配置
func BuildBigModelSvcRequestWithConfig(svcRequest BigModelSvcRequest, modelConfig *definition.ModelConfig, requestOptions *definition.RequestOptions) (*http.Request, error) {
	apiType := "invoke"
	if svcRequest.Async {
		apiType = "sse"
	}
	urlPath := fmt.Sprintf("/api/v2/service/pro/%s/%s?FetchKeys=%s", apiType, svcRequest.ServiceName, svcRequest.FetchKey)
	if svcRequest.AgentID != "" {
		urlPath += fmt.Sprintf("&AgentId=%s", svcRequest.AgentID)
	}

	builder := &RequestBuilder{
		Method:      http.MethodPost,
		URLPath:     urlPath,
		RequestBody: svcRequest.RequestBody,
		RequestType: RequestTypeAuth,
	}
	//在国内版lingma不启用region路由机制
	builder.EnableHA = global.IsQoderProduct()

	if requestOptions != nil && requestOptions.RouteType != "" {
		builder.RouteType = requestOptions.RouteType
	}

	req, err := buildRequest(builder)
	if err != nil {
		log.Errorf("Failed to build big model auth request. err: %+v", err)
		return nil, err
	}
	req.Header.Set("X-Request-ID", svcRequest.RequestID)

	if modelConfig != nil && modelConfig.Key != "" {
		req.Header.Set("X-Model-Name", modelConfig.Model)
		req.Header.Set("X-Model-Key", modelConfig.Key)
		req.Header.Set("X-Model-Source", modelConfig.Source)
	}

	return req, nil
}

func BuildBigModelAuthUploadFileRequest(httpMethod string, urlPath string, requestBody []byte) (*http.Request, error) {
	if err := CheckSecurityToken(); err != nil {
		return nil, err
	}

	builder := &RequestBuilder{
		Method:      httpMethod,
		URLPath:     urlPath,
		RequestBody: requestBody,
		RequestType: RequestTypeUpload,
	}

	//在国内版lingma不启用region路由机制
	builder.EnableHA = global.IsQoderProduct()

	return buildRequest(builder)
}

// BuildBigModelAuthRequest 用于需要用户登录态鉴权的接口
func BuildBigModelAuthRequest(httpMethod string, urlPath string, requestBody interface{}) (*http.Request, error) {
	if err := CheckSecurityToken(); err != nil {
		return nil, err
	}

	builder := &RequestBuilder{
		Method:      httpMethod,
		URLPath:     urlPath,
		RequestBody: requestBody,
		RequestType: RequestTypeAuth,
	}

	//在国内版lingma不启用region路由机制
	builder.EnableHA = global.IsQoderProduct()

	req, err := buildRequest(builder)
	if err != nil {
		return nil, err
	}

	fixedUrlPath := trimQueryPath(req.RequestURI, "")
	go recordLocalLog(requestBody, fixedUrlPath)

	return req, nil
}

// BuildBigModelSignRequest 用于无需用户登录态的接口
func BuildBigModelSignRequest(httpMethod string, urlPath string, requestBody interface{}) (*http.Request, error) {
	builder := &RequestBuilder{
		Method:      httpMethod,
		URLPath:     urlPath,
		RequestBody: requestBody,
		RequestType: RequestTypeSign,
	}

	//在国内版lingma不启用region路由机制
	builder.EnableHA = global.IsQoderProduct()

	req, err := buildRequest(builder)
	if err != nil {
		return nil, err
	}

	go recordLocalLog(requestBody, urlPath)
	return req, nil
}

func BuildBigModelSignGetRequest(url string) (*http.Request, error) {
	return BuildBigModelSignRequest(http.MethodGet, url, nil)
}

// buildRequest 统一的请求构建函数
func buildRequest(builder *RequestBuilder) (*http.Request, error) {
	// 选择端点
	endpoint := builder.Endpoint
	routeType := definition.RouteTypeUnknown

	if endpoint == "" {
		endpoint = config.Remote.BigModelEndpoint
		if builder.EnableHA && GlobalRegionFailService != nil {
			endpoint, routeType = routeEndpoint(builder.URLPath, builder.RouteType)

			log.Debugf("【region-ha-route】url path: %s, routeType: %s, endpoint: %s", builder.URLPath, routeType, endpoint)
		}
	}
	if endpoint == "" {
		log.Errorf("【region-ha-route】url path route error: not matched endpoint. path: %s, routeType: %s", builder.URLPath, routeType)

		if routeType != definition.RouteTypeCodebase && routeType != definition.RouteTypeRemoteAgent {
			//兜底保护
			endpoint = config.Remote.BigModelEndpoint

			log.Infof("url path route to default endpoint when no matched route type. path: %s, routeType: %s", builder.URLPath, routeType)
		}
	}

	// 构建URL
	urlString, err := buildURL(endpoint, builder.URLPath, builder.Method, builder.RequestBody)
	if err != nil {
		return nil, err
	}

	// 编码请求体
	bodyBytes, err := encodeRequestBody(builder.RequestBody, builder.Method, urlString)
	if err != nil {
		return nil, err
	}

	// 创建HTTP请求
	req, err := createHTTPRequest(builder.Method, urlString, bodyBytes)
	if err != nil {
		return nil, err
	}

	// 设置基本headers
	addBasicHeaders(req)

	// 根据请求类型设置认证headers
	switch builder.RequestType {
	case RequestTypeAuth:
		urlPath := trimQueryPath(urlString, endpoint)
		err = addBigModelAuthorizationHeaders(req, urlPath, string(bodyBytes))
	case RequestTypeSign:
		addBigModelSignatureHeaders(req)
	case RequestTypeUpload:
		if builder.Method != http.MethodPut {
			return nil, errors.New("upload request only supports PUT method")
		}
		urlPath := trimQueryPath(urlString, endpoint)
		bodyStr := string(bodyBytes)
		if builder.RequestBody != nil {
			if data, ok := builder.RequestBody.([]byte); ok {
				bodyStr = strconv.Itoa(len(data))
			}
		}
		err = addBigModelAuthorizationHeaders(req, urlPath, bodyStr)
	}

	if err != nil {
		return nil, err
	}

	// 设置Host和代理信息
	setHostAndProxy(req, builder.RequestType)

	// 记录请求信息
	logRequest(urlString, req)

	return req, nil
}

// buildURL 构建完整的URL
func buildURL(endpoint, urlPath, method string, requestBody interface{}) (string, error) {
	urlString := BuildAlgoForRequestUrl(endpoint, urlPath)

	_, err := url.Parse(urlString)
	if err != nil {
		log.Errorf("Failed to parse url: " + urlString)
		return "", err
	}

	// 如果需要编码，添加编码参数
	if shouldAddEncodeParam(method, urlString) {
		separator := "?"
		if strings.Contains(urlString, "?") {
			separator = "&"
		}
		urlString += separator + "Encode=" + config.Remote.MessageEncode
	}

	return urlString, nil
}

// encodeRequestBody 编码请求体
func encodeRequestBody(requestBody interface{}, method, urlString string) ([]byte, error) {
	if requestBody == nil {
		return nil, nil
	}

	var bodyBytes []byte
	var err error

	// 处理字节流类型（文件上传）
	if data, ok := requestBody.([]byte); ok {
		bodyBytes = data
	} else {
		// JSON编码
		bodyBytes, err = json.Marshal(requestBody)
		if err != nil {
			log.Errorf("Failed to marshal request body: " + err.Error())
			return nil, errors.New("marshal request failed")
		}
	}

	// 如果需要加密
	if shouldEncryptBody(method, urlString) {
		bodyBytes = encrypt.CustomEncryptV1(bodyBytes)
	}

	return bodyBytes, nil
}

// createHTTPRequest 创建HTTP请求
func createHTTPRequest(method, urlString string, bodyBytes []byte) (*http.Request, error) {
	req, err := http.NewRequest(method, urlString, bytes.NewBuffer(bodyBytes))
	if err != nil {
		return nil, errors.New("create request failed")
	}
	return req, nil
}

// setHostAndProxy 设置Host和代理信息
func setHostAndProxy(req *http.Request, requestType RequestType) {
	if requestType == RequestTypeUpload && config.OnPremiseMode && config.Remote.BigModelHost != "" {
		req.Host = config.Remote.BigModelHost
	} else if config.Remote.BigModelHost != "" {
		req.Host = config.Remote.BigModelHost
	}
}

// logRequest 记录请求信息
func logRequest(urlString string, req *http.Request) {
	extraHeader := ""
	if config.Remote.BigModelCookie != "" {
		extraHeader = fmt.Sprintf(" [%s]", config.Remote.BigModelCookie)
	}
	if config.Remote.BigModelHost != "" {
		extraHeader = fmt.Sprintf("%s [%s]", extraHeader, config.Remote.BigModelHost)
	}

	var clientToUse *http.Client
	switch {
	case strings.Contains(urlString, "/upload"):
		clientToUse = client.GetUploadFileClient()
	default:
		clientToUse = client.GetDefaultClient()
	}

	proxyAddr := extractProxyAddr(req, clientToUse)
	log.Debugf("Request url: %s%s%s", urlString, extraHeader, proxyAddr)
}

// shouldAddEncodeParam 判断是否需要添加编码参数
func shouldAddEncodeParam(method, urlString string) bool {
	if config.Remote.MessageEncode != "1" {
		return false
	}

	return (method == http.MethodPost) ||
		(method == http.MethodPut && strings.Contains(urlString, "/api/v2/remoteAgent/qoder"))
}

// shouldEncryptBody 判断是否需要加密请求体
func shouldEncryptBody(method, urlString string) bool {
	return shouldAddEncodeParam(method, urlString)
}

func addBigModelSignatureHeaders(req *http.Request) {
	req.Header.Set("Date", fmt.Sprint(time.Now().UTC().Format(http.TimeFormat)))
	req.Header.Set("Signature", encrypt.Md5Encode(appCode, getAppSalt(), req.Header.Get("Date")))
	req.Header.Set("Appcode", appCode)
}

func addBigModelAuthorizationHeaders(req *http.Request, urlPath, requestBody string) error {
	timestamp := fmt.Sprintf("%d", time.Now().Unix())
	var authToken, userInfoKey, uid string
	// 普通版本
	authToken, userInfoKey = user.AuthToken(urlPath, requestBody, timestamp)
	if userInfoKey == "" {
		return errors.New("you must login to use remote model")
	}
	_, uid, _ = user.GetUserIdAndName()
	req.Header.Set("Cosy-User", uid)
	req.Header.Set("Cosy-Key", userInfoKey)
	req.Header.Set("Cosy-Date", timestamp)
	req.Header.Set("Authorization", authToken)
	return nil
}

// 去除algo前缀，和服务端ingress部署有关
// /algo/api/v2/service/pro/sse/chat_ask  No
// /api/v2/service/pro/sse/chat_ask       Yes
func trimQueryPath(url string, endpoint string) string {
	trimPath := url
	if index := strings.Index(trimPath, "?"); index >= 0 {
		trimPath = trimPath[0:index]
	}
	if strings.HasPrefix(trimPath, config.Remote.BigModelEndpoint) {
		trimPath = strings.TrimPrefix(trimPath, config.Remote.BigModelEndpoint)
	}

	if endpoint != "" {
		if strings.HasPrefix(trimPath, endpoint) {
			trimPath = strings.TrimPrefix(trimPath, endpoint)
		}
	}
	if strings.HasPrefix(trimPath, definition.UrlPathAlgoPrefix) {
		trimPath = strings.TrimPrefix(trimPath, definition.UrlPathAlgoPrefix)
	}
	return trimPath
}

func extractProxyAddr(req *http.Request, client2 *http.Client) string {
	if client2 == nil || client2.Transport == nil {
		return ""
	}
	proxyAddr := ""
	t, _ := client2.Transport.(*http.Transport)
	if t != nil && t.Proxy != nil {
		proxy := t.Proxy

		proxyUrl, _ := proxy(req)
		proxyAddr = fmt.Sprintf(" {%v}", fmt.Sprintf("%s:%s", proxyUrl.Hostname(), proxyUrl.Port()))
	}
	return proxyAddr
}
