package remote

import "cosy/definition"

var (

	//codebase相关的数据节点path
	CodebaseDataNodeUrlPaths = []string{
		definition.UrlPathCodebaseFileCheckStatus,
		definition.UrlPathCodebaseFileUpload,
		definition.UrlPathCodebaseFileGetChunks,
		definition.UrlPathCodebaseSyncInitCodebase,
		definition.UrlPathCodebaseSyncGetMerkleNode,
		definition.UrlPathCodebaseSyncBatchGetMerkleNodes,
		definition.UrlPathCodebaseSyncUpdateMerkleNodes,
		definition.UrlPathCodebaseSearchRetrieveChunks,
		definition.UrlPathCodebaseOperationDeleteCodebase,
	}

	//central 中心化相关的节点path
	CentralNodeUrlPaths = []string{
		definition.UrlPathReportHeartbeat,
		definition.UrlPathReportTracking,
		definition.UrlPathDeleteChatByRequestId,
		definition.UrlPathQueryRegionEndpoints,
		definition.UrlPathUserDataNodeEndpoint,
		definition.UrlPathLogin,
		definition.UrlPathAuthLogin,
		definition.UrlPathLogout,
		definition.UrlPathLogoutReport,
		definition.UrlPathLoginReport,
		definition.UrlPathGrantAuthInfo,
		definition.UrlPathGetDataPolicy,
		definition.UrlPathUpdateDataPolicy,
		definition.UrlPathQueryAuthStatus,
		definition.UrlRefreshToken,
	}
)
