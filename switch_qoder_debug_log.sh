#!/usr/bin/env zsh

project_path=$(cd "$(dirname "${0}")"; pwd)

# 定义多个config.json文件位置
config_files=(
    "${HOME}/Library/Caches/.qoder/config.json"
    "${HOME}/.qoder/config.json"
    "${HOME}/Library/Application Support/Qoder/SharedClientCache/config.json"
)

echo "switch debug log for multiple config files"

function fix_extra_comma() {
    local config_file="$1"
    sed -i '' -n -e '1h;1!H;${;x;s/,[ ]*\n\([ ]*\)}/\n\1}/g;p;}' "${config_file}"
}

function process_config_file() {
    local config_file="$1"
    local action="$2"
    
    if [[ ! -f "${config_file}" ]]; then
        echo "Config file not found: ${config_file}"
        return 1
    fi
    
    echo "Processing: ${config_file}"
    
    # 删除现有的debug行
    sed -i '' -e '/"debug":/d' "${config_file}"
    
    if [ "${action}" = "on" ]; then
        # 添加debug: true
        sed -i '' "/^{$/a\\
  \"debug\": true,\\
" "${config_file}"
    fi
    
    # 修复多余的逗号
    fix_extra_comma "${config_file}"
    
    echo "Updated: ${config_file}"
}

function show_current_status() {
    echo "Current debug log status:"
    for config_file in "${config_files[@]}"; do
        if [[ -f "${config_file}" ]]; then
            printf "  %s: [" "${config_file}"
            if [ "$(grep '"debug":' "${config_file}" | grep 'true')" = "" ]; then
                echo "OFF]"
            else
                echo "ON]"
            fi
        else
            echo "  ${config_file}: [FILE NOT FOUND]"
        fi
    done
}

if [[ "${1}" = "on" || "${1}" = "off" ]]; then
    echo "Setting debug log to: ${1}"
    
    # 处理所有存在的配置文件
    processed_count=0
    for config_file in "${config_files[@]}"; do
        if process_config_file "${config_file}" "${1}"; then
            ((processed_count++))
        fi
    done
    
    if [ ${processed_count} -gt 0 ]; then
        echo "Successfully processed ${processed_count} config file(s)"
        zsh "${project_path}/restart_process.sh" --yes
    else
        echo "No config files were processed"
    fi
else
    show_current_status
    echo ""
    echo "Usage: $0 [on|off]"
    echo "  on  - Enable debug logging"
    echo "  off - Disable debug logging"
fi
