package remote_model

import (
	"context"
	"cosy/definition"
	"cosy/log"
	"cosy/model/base"
	"cosy/remote"
	"cosy/websocket"
	"encoding/json"
	"errors"
	"io"
	"net/http"
	"net/http/httptrace"
	"strconv"
	"strings"
	"time"
)

func (m *RemoteModel) SyncPredict(ctx context.Context, predictParam base.CompletionModelContext, completionParams definition.CompletionParams) ([]base.Candidate, error) {
	// check cache first
	if !predictParam.Parameter.ForceTrigger {
		cacheId, cacheText := m.getFromCache(predictParam.PrevContent, predictParam.PostContent)
		if cacheText != "" {
			log.Debugf("Got remote model result from cache. prevToken: [%s], requestId: [%s]", predictParam.PrevToken, completionParams.RequestId)

			candidateText := cacheText
			if predictParam.Parameter.WithPrevToken {
				candidateText = predictParam.PrevToken + cacheText
			}
			return []base.Candidate{toCandidate(cacheId, candidateText)}, nil
		}
	}

	// fill in extra parameters
	calculateExtraCompletionParameters(&predictParam)

	// build remote completion request
	body, isBlocked := m.prepareInputData(ctx, predictParam, completionParams)
	if isBlocked {
		return make([]base.Candidate, 0), nil
	}
	m.printRequestParameters(body)

	// make request
	svcRequest := remote.BigModelSvcRequest{
		ServiceName: "llm_completion_snippet",
		FetchKey:    "llm_model_result",
		Async:       false,
		RequestBody: body,
		RequestID:   predictParam.Parameter.RequestId,
		AgentID:     "",
	}
	req, err := remote.BuildBigModelSvcRequestWithConfig(svcRequest, nil, nil)
	if err != nil {
		var tokenExpiredError *definition.TokenExpiredError
		if errors.As(err, &tokenExpiredError) {
			status := definition.AuthStatusResult{
				Status:          definition.AuthStatusInit,
				Quota:           0,
				WhitelistStatus: definition.WhitelistUnknown,
			}
			websocket.SendBroadcastWithTimeout(predictParam.Context, "auth/report", status, nil)
		}
		log.Error("Failed to build request", err)
		return []base.Candidate{}, err
	}

	predictTrace := BuildHttpTrace(predictParam.Parameter.RequestId, "sync")
	req = req.WithContext(httptrace.WithClientTrace(req.Context(), predictTrace.trace))

	// do request
	startTime := predictParam.Performance.ClientEntryTime
	predictParam.Performance.ClientBeforeRequestTime = int(time.Now().UnixMilli() - startTime)
	log.Debug("Client before send request, cost:", predictParam.Performance.ClientBeforeRequestTime)
	predictParam.Performance.ClientInvokeTime = time.Now().UnixMilli()
	responseBody, headers, protocol, err := m.requestRemote(req)
	if err != nil {
		return []base.Candidate{}, err
	}
	predictParam.Performance.ClientFetchTime = time.Now().UnixMilli()
	//cost := time.Since(st).Milliseconds()

	// parse response
	var response definition.ApiResponse
	if err = json.Unmarshal(responseBody, &response); err != nil {
		log.Debugf("Response data: %s", strings.Trim(string(responseBody), " \t\r\n"))
		log.Errorf("Failed to unmarshal response body: %s", err)
		return []base.Candidate{}, err
	}

	if predictTrace.Protocol == "" {
		predictTrace.Protocol = protocol
	}

	if !response.Success {
		log.Debugf("Response data: %s", strings.Trim(string(responseBody), " \t\r\n"))
		log.Error("Remote model execute error")

		//log.Warnf("Remote model execute error. requestId: %s, Response data: %s", completionParams.RequestId, strings.Trim(string(responseBody), " \t\r\n"))
		var errMsg definition.AccountErrorMsg
		err = json.Unmarshal(responseBody, &errMsg)
		if err == nil {
			if predictParam.Parameter.RemoteModelParams.TriggerMode == definition.CompletionTriggerModeAuto {
				//自动触发时提示
				notificationError := definition.NotificationError{}
				notificationError.RequestId = predictParam.Parameter.RequestId
				notificationError.Code = errMsg.Code
				notificationError.Message = errMsg.Message
				m.sendNotificationError(&predictParam, &notificationError)
			}

		}
		return []base.Candidate{}, errors.New("remote model execute error")
	}

	var predictionResult definition.GraphResult
	if err = json.Unmarshal([]byte(response.Result.Body), &predictionResult); err != nil {
		log.Debugf("Response data: %s", strings.Trim(string(responseBody), " \t\r\n"))
		log.Errorf("Failed to unmarshal response result: %s", err)
		return []base.Candidate{}, err
	}
	log.Debug("NodesPath: ", predictionResult.NodePath)

	rawModelOutput, ok := predictionResult.Outputs["llm_model_result"].(string)
	if !ok || !predictionResult.Success {
		log.Debugf("Response data: %s", strings.Trim(string(responseBody), " \t\r\n"))
		log.Error("Failed to fetch remote model's output")
		return []base.Candidate{}, errors.New("failed to fetch remote model's output")
	}

	log.Debugf("Received data: %s", rawModelOutput)
	candidates, err := m.fetchSyncCandidates(rawModelOutput, predictParam)
	if err != nil {
		return []base.Candidate{}, err
	}

	if len(candidates) > 0 {
		if strings.HasPrefix(candidates[0].Text, predictParam.PrevToken) {
			// PrevToken有效，只需要换成PrevToken之后的部分
			m.updateCaches(candidates[0].CacheId, predictParam.PrevContent,
				candidates[0].Text[len(predictParam.PrevToken):], predictParam.PostContent)
		} else {
			// 否则缓存整个返回结果
			m.updateCaches(candidates[0].CacheId, predictParam.PrevContent, candidates[0].Text, predictParam.PostContent)
		}
		log.Debugf("Post processed: %s", candidates[0].Text)
	}

	predictParam.Performance.ServerEntryTime, err = strconv.ParseInt(headers.Get("Entry-Timestamp"), 10, 64)
	if err != nil {
		log.Debug("[WARN] Server entry timestamp is missing")
	}
	predictParam.Performance.ServerInvokeTime, err = strconv.ParseInt(headers.Get("Invoke-Timestamp"), 10, 64)
	if err != nil {
		log.Debug("[WARN] Server invoke timestamp is missing")
	}
	predictParam.Performance.ServerFetchTime, err = strconv.ParseInt(headers.Get("Fetch-Timestamp"), 10, 64)
	if err != nil {
		log.Debug("[WARN] Server fetch timestamp is missing")
	}
	predictParam.Performance.ServerLeaveTime, err = strconv.ParseInt(headers.Get("Leave-Timestamp"), 10, 64)
	if err != nil {
		log.Debug("[WARN] Server leave timestamp is missing")
	}
	predictParam.Performance.ClientLeaveTime = time.Now().UnixMilli()
	predictParam.Performance.Protocol = predictTrace.Protocol
	predictParam.Performance.ConnReused = predictTrace.Reused
	predictParam.Performance.ConnWasIdle = predictTrace.WasIdle
	predictParam.Performance.ConnIdleTime = predictTrace.IdleTime
	predictParam.Performance.Report(false, predictParam.Parameter.RequestId)
	log.Debugf(predictParam.Performance.TimeDistribute(false, predictParam.Parameter.RequestId))

	// 执行后置过滤
	if len(candidates) > 0 {
		doCompletionResultFilter(ctx, body, completionParams, candidates[0].Text)
	}
	return candidates, nil
}

func (m *RemoteModel) requestRemote(req *http.Request) (respBody []byte, header http.Header, protocol string,
	err error) {
	resp, err := m.httpClient.Do(req)
	if err != nil {
		log.Error("Failed to request remote model: ", err)
		// If it's timeout error, change context length to MinContextLength
		if strings.Contains(strings.ToLower(err.Error()), "timeout") {
			log.Info("Request timeout, use minimal context length in later requests")
			m.CtxLength = MinContextLength
		}
		return nil, nil, "", err
	}
	defer resp.Body.Close()
	if resp.StatusCode == http.StatusRequestTimeout {
		log.Error("Request timeout, use minimal context length in later requests")
		m.CtxLength = MinContextLength
		return nil, nil, "", errors.New("request remote timeout")
	}

	// fetch result from response
	responseBody, _ := io.ReadAll(resp.Body)
	return responseBody, resp.Header, resp.Proto, nil
}

func (m *RemoteModel) fetchSyncCandidates(rawModelOutput string, param base.CompletionModelContext) ([]base.Candidate, error) {
	var modelOutput map[string]interface{}
	if err := json.Unmarshal([]byte(rawModelOutput), &modelOutput); err != nil {
		log.Debugf("Model output data: %s", strings.Trim(rawModelOutput, " \t\r\n"))
		log.Error("Failed to unmarshal model outputs")
		return []base.Candidate{}, err
	}
	var text string
	completedCode := getDashScopeOutputText(modelOutput, param.Performance)
	if completedCode != nil {
		text = PostProcess(completedCode.(string), m.getPostProcessContext(param))
		ReportCompletionStat(param.Parameter.RequestId, param, text)
		if strings.Trim(text, " \t\r\n") != "" {
			return []base.Candidate{toCandidate(param.Parameter.RequestId, text)}, nil
		}
	} else {
		log.Debugf("Model output data: %s", strings.Trim(rawModelOutput, " \t\r\n"))
		log.Warn("Parse response got error")
	}
	return []base.Candidate{}, nil
}

func toCandidate(cacheId, text string) base.Candidate {
	return base.Candidate{
		Text:    text,
		LogProb: 1,
		Prob:    1,
		Source:  "remote",
		Type:    base.SeqCandidate,
		CacheId: cacheId,
	}
}

func toCandidateWithFinished(cacheId, text string, finished bool) base.Candidate {
	return base.Candidate{
		Text:           text,
		LogProb:        1,
		Prob:           1,
		Source:         "remote",
		Type:           base.SeqCandidate,
		CacheId:        cacheId,
		StreamFinished: finished,
	}
}
