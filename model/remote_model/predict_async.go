package remote_model

import (
	"context"
	"cosy/definition"
	"cosy/filter"
	"cosy/log"
	"cosy/model/base"
	"cosy/remote"
	"cosy/sse"
	"cosy/websocket"
	"encoding/json"
	"errors"
	"fmt"
	"net/http/httptrace"
	"strings"
	"sync/atomic"
	"time"
)

func (m *RemoteModel) AsyncPredict(ctx context.Context, predictParam base.CompletionModelContext, completionParam definition.CompletionParams) (candidate []base.Candidate, err error) {
	// check cache first
	if !predictParam.Parameter.ForceTrigger {
		cacheId, cacheText := m.getFromCache(predictParam.PrevContent, predictParam.PostContent)
		if cacheText != "" {
			log.Debugf("Got remote model result from cache: [%s]", predictParam.Parameter.RequestId)
			_ = m.sendCandidate(predictParam, toCandidateWithFinished(cacheId, cacheText, true))
			return candidate, nil
		}
	}

	// fill in extra parameters
	calculateExtraCompletionParameters(&predictParam)
	log.Info("Remote model request parameters")
	// build remote completion request
	body, isBlocked := m.prepareInputData(ctx, predictParam, completionParam)
	if isBlocked {
		return make([]base.Candidate, 0), nil
	}
	m.printRequestParameters(body)

	// do request
	log.Info("Building remote model request")
	svcRequest := remote.BigModelSvcRequest{
		ServiceName: "llm_completion_stream",
		FetchKey:    "",
		Async:       true,
		RequestBody: body,
		RequestID:   predictParam.Parameter.RequestId,
		AgentID:     "",
	}
	req, err := remote.BuildBigModelSvcRequestWithConfig(svcRequest, nil, nil)
	if err != nil {
		var tokenExpiredError *definition.TokenExpiredError
		if errors.As(err, &tokenExpiredError) {
			status := definition.AuthStatusResult{
				Status:          definition.AuthStatusInit,
				Quota:           0,
				WhitelistStatus: definition.WhitelistUnknown,
			}
			websocket.SendBroadcastWithTimeout(predictParam.Context, "auth/report", status, nil)
		}
		return nil, err
	}

	predictTrace := BuildHttpTrace(predictParam.Parameter.RequestId, "async")
	req = req.WithContext(httptrace.WithClientTrace(req.Context(), predictTrace.trace))
	startTime := predictParam.Performance.ClientEntryTime
	firstTokenFetchTime := int64(0)
	firstTokenCompleteTime := int64(0)
	lastTokenFetchTime := int64(0)
	lastTokenCompleteTime := int64(0)

	// handle sse response
	go func() {
		var lastData []byte
		var postProcessText string
		var stopFlag atomic.Bool
		stopFlag.Store(false)
		predictParam.Performance.ClientBeforeRequestTime = int(time.Now().UnixMilli() - startTime)
		log.Debug("Client before send request, cost:", predictParam.Performance.ClientBeforeRequestTime)
		err = m.sseClient.Subscribe(req, 5*time.Second, func(msg *sse.Event) {
			fetchTime := time.Now().UnixMilli()
			if firstTokenFetchTime == 0 {
				firstTokenFetchTime = fetchTime
				lastTokenFetchTime = fetchTime
			}
			cacheId := predictParam.Parameter.RequestId
			if msg.Event != nil && (string(msg.Event) == "finish") {
				serverTimestamps := make(map[string]int64)
				err = json.Unmarshal(msg.Data, &serverTimestamps)
				serverTotalMs, serverFirstTokenMs, serverDurationMs := 0, 0, 0
				if err == nil {
					log.Debug("Server timestamps: ", serverTimestamps)
					serverTotalMs = int(serverTimestamps["totalDuration"])
					serverFirstTokenMs = int(serverTimestamps["firstTokenDuration"])
					serverDurationMs = int(serverTimestamps["serverDuration"])
				}
				predictParam.Performance.UpdateData(firstTokenCompleteTime, firstTokenFetchTime, lastTokenCompleteTime,
					lastTokenFetchTime, serverTotalMs, serverFirstTokenMs, serverDurationMs, startTime, fetchTime,
					predictTrace.Protocol, predictTrace.Reused, predictTrace.WasIdle, predictTrace.IdleTime)
				predictParam.Performance.Report(true, predictParam.Parameter.RequestId)
				if strings.Trim(postProcessText, " \t\r\n") != "" {
					m.sendCandidate(predictParam, toCandidateWithFinished(cacheId, postProcessText, true))
				}
				m.updateCaches(cacheId, predictParam.PrevContent, postProcessText, predictParam.PostContent)
				log.Debugf(predictParam.Performance.TimeDistribute(true, predictParam.Parameter.RequestId))
				log.Debugf("Received data: %s", strings.Trim(string(lastData), " \t\r\n"))
				log.Debugf("Post processed: %s", postProcessText)
				ReportCompletionStat(predictParam.Parameter.RequestId, predictParam, postProcessText)
				doCompletionResultFilter(ctx, body, completionParam, postProcessText)
				return
			} else if string(msg.Event) == "error" {
				log.Error("Received error: " + string(msg.Data))
				return
			}
			lastData = msg.Data
			var ret map[string]interface{}
			err = json.Unmarshal(msg.Data, &ret)
			if err != nil {
				log.Warn("invalid json data")
				return
			}
			var bodyStr string
			if value, ok := ret["body"]; ok {
				bodyStr = value.(string)
			} else {
				log.Warn("invalid body data")
				return
			}
			var sseBody map[string]interface{}
			err = json.Unmarshal([]byte(bodyStr), &sseBody)
			if err != nil {
				log.Warn("invalid json body data")
				return
			}
			completeCode := getDashScopeOutputText(sseBody, predictParam.Performance)
			if completeCode == nil {
				log.Debugf("Completion body: %s", bodyStr)

				var errMsg definition.AccountErrorMsg
				err = json.Unmarshal([]byte(bodyStr), &errMsg)
				if err == nil {
					notificationError := definition.NotificationError{}
					notificationError.RequestId = predictParam.Parameter.RequestId
					notificationError.Code = errMsg.Code
					notificationError.Message = errMsg.Message
					if predictParam.Parameter.RemoteModelParams.TriggerMode == definition.CompletionTriggerModeAuto {
						//自动触发时提示
						m.sendNotificationError(&predictParam, &notificationError)
					}
				}
				return
			}
			postProcessText = PostProcess(completeCode.(string), m.getPostProcessContext(predictParam))
			if strings.Trim(postProcessText, " \t\r\n") == "" {
				log.Warn("Ignore invalid post text: ", postProcessText)
				return
			}
			cand := toCandidateWithFinished(cacheId, postProcessText, false)
			if stopFlag.Load() {
				log.Debug("ignore request ", predictParam.Parameter.RequestId)
				return
			}
			result := m.sendCandidate(predictParam, cand)
			stopFlag.Swap(result)

			// 由于finish事件有可能在最后一个Token完成之前就到达，将lastTokenFetchTime的更新放到后处理之后
			// 确保上报的后处理耗时是最后一次可测量的包的后处理耗时
			lastTokenFetchTime = fetchTime
			lastTokenCompleteTime = time.Now().UnixMilli()
			if firstTokenCompleteTime == 0 {
				firstTokenCompleteTime = lastTokenCompleteTime
			}
		}, func() {
			// timeout callback
			log.Error("Async completion request timeout.")
		})
		if err != nil {
			log.Errorf("Async completion client error: %s", err.Error())
		}
	}()
	return candidate, err
}

// doCompletionResultFilter 针对代码补全结果执行后置过滤
func doCompletionResultFilter(ctx context.Context, predictInput map[string]interface{}, completionParams definition.CompletionParams, llmInferredContent string) {
	err := filter.PostFilterCompletionModelResponse(ctx, predictInput, completionParams, llmInferredContent)
	if err != nil {
		// 后置过滤处理失败当前自动降级
		log.Warnf("doCompletionResultFilter error: %s", err.Error())
	}
}

func (m *RemoteModel) sendCandidate(predictParam base.CompletionModelContext, cand base.Candidate) bool {
	item := m.buildCompletionItem(predictParam, cand)
	var result bool
	e := websocket.SendRequestWithTimeout(websocket.CopyContext(predictParam.Context),
		"textDocument/collectCompletionResult", item, &result, 3*time.Second)
	if e != nil {
		log.Error("Send request textDocument/collectCompletionResult error:", e)
	}
	return result
}

func (m *RemoteModel) buildCompletionItem(predictParam base.CompletionModelContext, candidate base.Candidate) definition.CompletionItem {
	var args []interface{}
	args = append(args, definition.ComputeStatisticsParams{
		ID:              predictParam.Parameter.RequestId,
		Index:           0,
		TextDocumentURI: predictParam.Parameter.TextDocument.URI,
	})
	cmd := definition.Command{
		Title:     "Statistics computation",
		Command:   "extension.completionStatistics",
		Arguments: args,
	}
	externData := make(map[string]string)
	externData["RequestId"] = predictParam.Parameter.RequestId
	externData["CacheId"] = candidate.CacheId
	externData["finished"] = fmt.Sprintf("%v", candidate.StreamFinished)
	return definition.CompletionItem{
		Label:      "",
		InsertText: candidate.Text,
		TextEdit:   nil,
		FilterText: predictParam.PrevToken,
		SortText:   "",
		Preselect:  true,
		// WARN: If the documentation style is changed, the sourceRegex in statistics.go must be changed
		Documentation:    "",
		Detail:           "",
		InsertTextFormat: definition.SnippetTextFormat,
		Command:          &cmd,
		Data:             externData,
	}
}
