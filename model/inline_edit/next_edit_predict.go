package inline_edit

// 	nes V2版本
import (
	"context"
	"cosy/definition"
	"cosy/lang/indexer"
	"cosy/log"
	"cosy/model/base"
	"cosy/model/remote_model"
	"cosy/remote"
	"cosy/sse"
	"cosy/util"
	"cosy/websocket"
	"encoding/json"
	"regexp"
	"runtime"
	"strings"
	"time"

	"github.com/spf13/cast"
)

var (
	// 定义正则表达式匹配 <next_edit> 标签中的内容
	nextEditRegex          = regexp.MustCompile(`(?s)<next_edit>(.*?)</next_edit>`)
	nextEditStartLineRegex = regexp.MustCompile(`<next_start_line>(.*?)</next_start_line>`)
	nextEditEndLineRegex   = regexp.MustCompile(`<next_end_line>(.*?)</next_end_line>`)
	nextEditContentRegex   = regexp.MustCompile(`(?s)<next_content>\n*(.*?)\n*</next_content>`)
	// DefaultVisibleLines 前后默认可见行数
	DefaultVisiblePrefixLines = 3
	DefaultVisibleSuffixLines = 3
)

func CallNextPredict(ctx context.Context, params *definition.InlineEditParams,
	contextData map[string]interface{}, validateHandler func(ctx context.Context, params definition.InlineEditParams,
		codeToRewrite string, rewriteCodeResult definition.RewriteCodeActionMessage,
		contextData map[string]interface{}) bool) (definition.RewriteCodeActionMessage, error, int, []int64) {
	defer func() {
		if r := recover(); r != nil {
			// 获取当前堆栈信息
			stack := make([]byte, 4096)
			stack = stack[:runtime.Stack(stack, false)]
			log.Warnf("recover from CallNextEditAction crash. err: %+v, stack: %s", r, stack)
		}
	}()

	timeRecorder, _ := ctx.Value(definition.ContextKeyTimeRecorder).(*util.TimeRecorder)
	performance, _ := ctx.Value(definition.ContextKeyPerformance).(base.PerformanceCollect)
	stopWatch := util.NewStopwatch()
	stopWatch.Start("build_next_predict_request")
	inlineEditRequest, err := NewInlineEditPredictRequestBuilder().Build(ctx, params, contextData)
	if err != nil {
		log.Errorf("Failed to build inline edit request. requestId: %s, err: %v", params.RequestId, err)
		return zeroNextPredictMessage(timeRecorder, &stopWatch, err, 0, nil)
	}
	truncateErr := truncateInlineEditRequestForNextEditLocation(&inlineEditRequest)
	if truncateErr != nil {
		log.Errorf("Failed to truncate inline edit next edit action request. requestId: %s, err: %v", params.RequestId, truncateErr)
		return zeroNextPredictMessage(timeRecorder, &stopWatch, err, 0, nil)
	}

	performance.ClientBeforeRequestTime = int(time.Now().UnixMilli() - performance.ClientEntryTime)
	performance.ClientInvokeTime = time.Now().UnixMilli()
	log.Debugf("Build next edit action request success. requestId: %s, request: %s", inlineEditRequest.RequestId, util.ToJsonStr(inlineEditRequest))
	stopWatch.Start("request_next_predict_model")
	actionMessage, err, actionCount, actionTimes := callModelService(ctx, params, timeRecorder, inlineEditRequest,
		contextData,
		&performance, stopWatch, validateHandler)
	stopWatch.Stop()
	timeRecorder.RecordStopwatch(&stopWatch)
	return actionMessage, err, actionCount, actionTimes
}

func callModelService(ctx context.Context,
	params *definition.InlineEditParams,
	timeRecorder *util.TimeRecorder,
	inlineEditRequest definition.InlineEditPredictRequest,
	contextData map[string]interface{},
	performance *base.PerformanceCollect,
	stopWatch util.Stopwatch,
	validateHandler func(ctx context.Context,
		params definition.InlineEditParams,
		codeToRewrite string, rewriteCodeResult definition.RewriteCodeActionMessage,
		contextData map[string]interface{}) bool) (definition.RewriteCodeActionMessage, error, int, []int64) {
	svcRequest := remote.BigModelSvcRequest{
		ServiceName: definition.AgentNextPredictService,
		FetchKey:    "llm_model_result",
		Async:       true,
		RequestBody: inlineEditRequest,
		RequestID:   inlineEditRequest.RequestId,
		AgentID:     "",
	}
	req, err := remote.BuildBigModelSvcRequestWithConfig(svcRequest, nil, nil)
	if err != nil {
		log.Errorf("Failed to build new request to predict next edit. requestId: %s, err: %v", params.RequestId, err)
		return zeroNextPredictMessage(timeRecorder, &stopWatch, err, 0, nil)
	}
	sseClient := sse.NewSseChatClient(map[string]string{})
	var buffer = ""      // 用于存储接收到的内容
	var historyText = "" // 用于存储历史文本
	var hasValidContent = false
	requestStartTime := time.Now()
	var actionCount = 0 // 用于统计action的数量
	var firstResponse = false
	actionTimes := []int64{}
	startTime := performance.ClientEntryTime
	firstTokenFetchTime := int64(0)
	firstTokenCompleteTime := int64(0)
	lastTokenFetchTime := int64(0)
	lastTokenCompleteTime := int64(0)
	// 用于记录每个 action 的生成时间
	var actionStartTimeMap = make(map[int]time.Time)
	var currentActionIndex = 0
	predictTrace := remote_model.BuildHttpTrace(params.RequestId, "async")
	err = sseClient.Subscribe(req, 115*time.Second, func(msg *sse.Event) {
		fetchTime := time.Now().UnixMilli()
		if firstTokenFetchTime == 0 {
			firstTokenFetchTime = fetchTime
			lastTokenFetchTime = fetchTime
		}
		var response definition.ChatResponse
		if !firstResponse {
			firstResponse = true
			timeRecorder.Record("model_first_response_time", time.Since(requestStartTime))
		}
		if string(msg.Event) == "error" {
			log.Warnf("next edit predict call model service error, reason=%s", msg.Data)
			return
		}
		if string(msg.Event) == "finish" {
			log.Debugf("next edit predict finish. requestId=%s", params.RequestId)
			serverTimestamps := make(map[string]int64)
			err = json.Unmarshal(msg.Data, &serverTimestamps)
			serverTotalMs, serverFirstTokenMs, serverDurationMs := 0, 0, 0
			performance.ClientFetchTime = time.Now().UnixMilli()
			if err == nil {
				log.Debug("Server timestamps: ", serverTimestamps)
				serverTotalMs = int(serverTimestamps["totalDuration"])
				serverFirstTokenMs = int(serverTimestamps["firstTokenDuration"])
				serverDurationMs = int(serverTimestamps["serverDuration"])
			}
			performance.UpdateData(firstTokenCompleteTime, firstTokenFetchTime, lastTokenCompleteTime,
				lastTokenFetchTime, serverTotalMs, serverFirstTokenMs, serverDurationMs, startTime, fetchTime,
				predictTrace.Protocol, predictTrace.Reused, predictTrace.WasIdle, predictTrace.IdleTime)
			performance.Report(true, params.RequestId)
			log.Debugf(performance.TimeDistribute(true, params.RequestId))
			timeRecorder.Record("model_total_response_time", time.Since(requestStartTime))
			// 以</actions>结尾的内容，表示所有actions都已获取到
			// 没有有效的action，则不再发送trigger_edit
			if hasValidContent && strings.HasSuffix(buffer, "</actions>") {
				log.Debugf("next edit predict finish. send triggerEdit, requestId=%s", params.RequestId)
				sendTriggerEdit(params, ctx)
				return
			}
			if !hasValidContent {
				log.Debugf("next edit predict finish. no valid actions, requestId=%s", params.RequestId)
			}
			return
		}
		err := json.Unmarshal(msg.Data, &response)
		if err != nil {
			log.Error("Unmarshal sse msg data error: ", err)
			return
		}
		if response.StatusCodeValue != 200 {
			message := response.Body
			log.Debug("next edit predict finished, reason: " + message)
			return
		}

		body, err := definition.NewChatBody(response.Body)
		if err != nil {
			log.Error("Unmarshal commitMsg body error: ", err)
			return
		}
		usage := body.Usage
		performance.InputTokenCount = usage.InputTokens
		performance.OutputTokenCount = usage.OutputTokens
		performance.ModelTotalMs = (int)(usage.TotalTime)
		performance.ModelFirstTokenMs = (int)(usage.FirstTokenTime)

		text := body.GetOutputText()
		appendText := strings.TrimPrefix(text, historyText)
		buffer += appendText
		historyText = text
		// 查找所有完整标签对
		matches := nextEditRegex.FindAllStringSubmatch(buffer, -1)
		if strings.HasSuffix(text, "</actions>") {
			log.Debugf("next edit predict total text is: %s, requestId: %s, requestIdForModel is: %s", text,
				params.RequestId, body.RequestId)
		}
		// 用于临时记录当前 buffer 中是否包含未闭合的 <next_edit>
		containsOpenTag := strings.Count(buffer, "<next_edit>") > strings.Count(buffer, "</next_edit")
		// 如果当前 buffer 中有未闭合的 <next_edit>，记录开始时间
		if len(matches) > 0 {
			// 遍历所有匹配的标签对，记录开始时间
			for i := 1; i <= len(matches); i++ {
				//actionIndex = currentActionIndex + i
				if _, exists := actionStartTimeMap[currentActionIndex]; !exists {
					actionStartTimeMap[currentActionIndex] = time.Now()
				}
				currentActionIndex++
			}
		}
		// 如果当前 buffer 中有未闭合的 <next_edit>，且没有匹配的标签对，记录开始时间
		if containsOpenTag {
			if (actionCount + len(matches)) == currentActionIndex {
				if _, exists := actionStartTimeMap[currentActionIndex]; !exists {
					actionStartTimeMap[currentActionIndex] = time.Now()
				}
			}
		}
		for _, match := range matches {
			content := match[1]
			log.Debugf("next edit predict action: %s, requestId: %s", match[0], params.RequestId)
			startLine := nextEditStartLineRegex.FindStringSubmatch(content)
			endLine := nextEditEndLineRegex.FindStringSubmatch(content)
			codeContent := nextEditContentRegex.FindStringSubmatch(content)
			if len(startLine) < 2 || len(endLine) < 2 || len(codeContent) < 2 {
				log.Errorf("Failed to parse next edit action response. requestId: %s, err: %v", params.RequestId, err)
				// 遇到异常则停止后续渲染，避免由于错误导致后续action行号不对
				break
			}

			// action计数
			actionCount++
			// 如果当前 action 有开始时间，则记录结束时间
			if startTime, exists := actionStartTimeMap[actionCount-1]; exists {
				elapsed := time.Duration(fetchTime-startTime.UnixMilli()) * time.Millisecond
				actionTimes = append(actionTimes, elapsed.Milliseconds())
				delete(actionStartTimeMap, actionCount-1)
			}

			// 发送action到客户端
			newCodeBlock := ""
			if len(codeContent) > 1 {
				newCodeBlock = codeContent[1]
			}
			sendResult := sendNextEdit(startLine[1], endLine[1], newCodeBlock, params, ctx, contextData,
				validateHandler)
			if !sendResult {
				// 遇到无效next_edit内容，则直接返回，以防后续action都有问题
				break
			}
			// 有一个成功就需要触发trigger_edit
			if !hasValidContent {
				hasValidContent = true
			}
		}
		// 移除已处理的内容
		buffer = nextEditRegex.ReplaceAllString(buffer, "")
		// 由于finish事件有可能在最后一个Token完成之前就到达，将lastTokenFetchTime的更新放到后处理之后
		// 确保上报的后处理耗时是最后一次可测量的包的后处理耗时
		lastTokenFetchTime = fetchTime
		lastTokenCompleteTime = time.Now().UnixMilli()
		if firstTokenCompleteTime == 0 {
			firstTokenCompleteTime = lastTokenCompleteTime
		}
	}, func() {
		log.Error("next edit predict timeout")
		return
	})
	return definition.RewriteCodeActionMessage{}, err, actionCount, actionTimes
}

// sendNextEdit 发送next_edit到客户端
func sendNextEdit(startLine string, endLine string, codeContent string, params *definition.InlineEditParams,
	ctx context.Context, contextData map[string]interface{}, validateHandler func(ctx context.Context,
		params definition.InlineEditParams, codeToRewrite string,
		rewriteCodeResult definition.RewriteCodeActionMessage, contextData map[string]interface{}) bool) bool {
	startLineFloat := reduce1ForLineNumberInTriggerEdit(cast.ToFloat64(startLine))
	endLineFloat := reduce1ForLineNumberInTriggerEdit(cast.ToFloat64(endLine))
	// 1 新增代码行号是startLine 等于 endLine；2.修改代码行号endLine是代码结尾行号+1
	startLineInt := int(startLineFloat)
	endLineInt := int(endLineFloat)
	if startLineFloat < endLineFloat {
		endLineInt--
	}
	originalCode := util.GetCodeRangeFromLines(params.GetFileContentLines(), startLineInt, endLineInt)
	// 代码没有改动
	if codeContent == originalCode {
		log.Debugf("next edit result is invalid, content not change requestId: %s, sessionId: %s", params.RequestId,
			params.SessionId)
		return false
	}
	// 更新contextData中的code_to_rewrite和area_around_code
	updateContextData(contextData, originalCode, startLineInt, endLineInt, params)
	// 检测到一个完整的next_edit后，发送给端侧
	// 每个action计算prefix、suffix，过滤掉重叠部分，需要重新计算startLine、endLine
	codeContent = postprocessToWriteArea(contextData, codeContent, params)
	nextEditResult := definition.RewriteCodeActionMessage{
		BaseInlineEditAction: definition.BaseInlineEditAction{
			Action:    definition.InlineEditNextEdit,
			RequestId: params.RequestId,
			SessionId: params.SessionId,
			Success:   true,
		},
		Data: definition.RewriteCodeAction{
			Content: codeContent,
			EditRange: definition.Range{
				// 模型返回的行数是从1开始，需要减1
				Start: definition.Position{
					Line: startLineFloat,
				},
				End: definition.Position{
					Line: endLineFloat,
				},
			},
		},
	}
	// 1. 校验有效性，有效才推送给端侧
	// 2. 修复缺陷：光标位映射、后处理
	codeToRewrite := contextData[definition.InlineEditContextKeyCodeToRewrite].(definition.CodeToRewriteData)
	isValid := validateHandler(ctx, *params, codeToRewrite.Content, nextEditResult, contextData)
	if !isValid {
		log.Debugf("next edit result is invalid, requestId: %s, sessionId: %s", params.RequestId, params.SessionId)
		return false
	}
	log.Debugf("send next edit action to client. requestId: %s, content: %s", params.RequestId, util.ToJsonStr(nextEditResult))
	// 发送结果到客户端
	if err := websocket.WsInst.NotifyClient(websocket.CopyContext(ctx), "textDocument/nextEditAction",
		nextEditResult); err != nil {
		log.Errorf("Failed to send next edit action to socket client: %v", err)
	}
	return true
}

func updateContextData(contextData map[string]interface{}, originalCode string, startLineInt int, endLineInt int, params *definition.InlineEditParams) {
	contextData[definition.InlineEditContextKeyCodeToRewrite] = definition.CodeToRewriteData{
		Content:    originalCode,
		StartLine:  startLineInt,
		EndLine:    endLineInt,
		CursorLine: endLineInt,
	}
	// 确定更大的区域 area_around_code_to_rewrite
	aroundStartLine, aroundEndLine := getVisibleCodeRangeForPredict(params, startLineInt, endLineInt)

	// 获取area_around_code_to_rewrite区域
	areaAroundCode := util.GetCodeRangeFromLines(params.GetFileContentLines(), aroundStartLine, aroundEndLine)
	contextData[definition.InlineEditContextKeyAreaAroundCode] = definition.AreaAroundCodeData{
		Content:   areaAroundCode,
		StartLine: aroundStartLine,
		EndLine:   aroundEndLine,
	}
}

// 发送trigger_edit到客户端
func sendTriggerEdit(params *definition.InlineEditParams,
	ctx context.Context) {
	// 所有next_edit处理完以后，发送trigger_edit到插件端
	triggerEditParam := definition.InlineEditTriggerEditMessage{
		BaseInlineEditAction: definition.BaseInlineEditAction{
			Action:    definition.InlineEditActionTriggerEdit,
			RequestId: params.RequestId,
			SessionId: params.SessionId,
			Success:   true,
		},
		Data: definition.InlineEditTriggerEdit{
			Type: "current_caret",
		},
	}
	// 发送结果到客户端
	if err := websocket.WsInst.NotifyClient(websocket.CopyContext(ctx), "textDocument/nextEditAction", triggerEditParam); err != nil {
		log.Errorf("Failed to send trigger edit action to socket client: %v", err)
	}
}

func zeroNextPredictMessage(timeRecorder *util.TimeRecorder, stopWatch *util.Stopwatch,
	err error, actionCount int, actionTimes []int64) (definition.RewriteCodeActionMessage, error, int, []int64) {
	stopWatch.Stop()
	timeRecorder.RecordStopwatch(stopWatch)
	return definition.RewriteCodeActionMessage{}, err, actionCount, actionTimes
}

// 预测跳转时，模型输入文件内容是从1开始的，因此返回给插件跳转行号需要减一
func reduce1ForLineNumberInTriggerEdit(lineNumber float64) float64 {
	if lineNumber > 0 {
		return lineNumber - 1
	}
	return lineNumber
}

// 后处理，模型预测的rewrite区域与输入的areaAroundCode有重叠的情况
func postprocessToWriteArea(contextData map[string]interface{}, codeToWrite string,
	params *definition.InlineEditParams) string {
	rewriteCode, ok := contextData[definition.InlineEditContextKeyCodeToRewrite].(definition.CodeToRewriteData)
	if !ok {
		log.Debug("failed to get code to rewrite from context data")
		return codeToWrite
	}
	areaAroundCode, ok := contextData[definition.InlineEditContextKeyAreaAroundCode].(definition.AreaAroundCodeData)
	if !ok {
		log.Debug("failed to get code to rewrite from context data")
		return codeToWrite
	}
	visiblePrefix := ""
	if areaAroundCode.StartLine < rewriteCode.StartLine {
		visiblePrefix = util.GetCodeRange(params.FileContent, areaAroundCode.StartLine, rewriteCode.StartLine-1)
	}
	visibleSuffix := ""
	if rewriteCode.EndLine+1 < areaAroundCode.EndLine {
		visibleSuffix = util.GetCodeRange(params.FileContent, rewriteCode.EndLine+1, areaAroundCode.EndLine)
	}
	if codeToWrite == "" {
		return codeToWrite
	}

	processedCode := removeOverlapArea(codeToWrite, visiblePrefix, visibleSuffix)

	if codeToWrite != processedCode {
		log.Debugf("Rewrite area postprocessed. requestId: %s, original: %s, processed: %s", params.RequestId, codeToWrite, processedCode)
	}
	return processedCode
}

// 从originalCode的当前行开始，向上/向下查找函数或方法，并返回该函数或方法的范围
func getFunctionRangeForPredict(langParser indexer.LangParser, currentLine uint32) (definition.LineRange, bool) {
	// 直接使用LangParser的GetEditingArea方法获取编辑区域
	startLine, endLine, err := langParser.GetEditingArea(currentLine, 0)
	if err != nil {
		log.Debugf("Failed to get editing area: %v", err)
		return definition.LineRange{}, false
	}
	// 返回找到的函数范围
	return definition.LineRange{
		StartLine: startLine,
		EndLine:   endLine,
	}, true
}

// getVisibleCodeRange 获取可见代码范围
func getVisibleCodeRangeForPredict(params *definition.InlineEditParams, rewriteStartLine,
	rewriteEndLine int) (int, int) {
	if len(params.GetFileContentLines()) == 0 {
		return rewriteStartLine, rewriteEndLine
	}

	aroundStartLine := rewriteStartLine - DefaultVisiblePrefixLines
	aroundEndLine := rewriteEndLine + DefaultVisibleSuffixLines
	aroundStartLine = util.IntMax(aroundStartLine, 0)
	aroundEndLine = util.IntMin(aroundEndLine, len(params.GetFileContentLines())-1)
	var ok bool
	aroundStartLine, aroundEndLine, ok = removePrefixSuffixEmptyLines(params, aroundStartLine, aroundEndLine)
	if !ok {
		return rewriteStartLine, rewriteEndLine
	}
	aroundStartLine = util.IntMin(aroundStartLine, rewriteStartLine)
	aroundEndLine = util.IntMax(aroundEndLine, rewriteEndLine)
	return aroundStartLine, aroundEndLine
}

// removePrefixSuffixEmptyLines 移除前缀和后缀的空行，返回新的起始行和结束行
func removePrefixSuffixEmptyLines(params *definition.InlineEditParams, startLine, endLine int) (int, int, bool) {
	if params.FileContent == "" {
		return startLine, endLine, false
	}

	lines := params.GetFileContentLines()

	// 跳过前缀空行
	newStartLine := startLine
	for i := startLine; i < endLine; i++ {
		if strings.TrimSpace(lines[i]) != "" {
			break
		}
		newStartLine++
	}

	// 跳过后缀空行
	newEndLine := endLine
	for i := endLine - 1; i >= startLine; i-- {
		if strings.TrimSpace(lines[i]) != "" {
			break
		}
		newEndLine--
	}

	// 确保至少包含一行（防止所有行都是空行的情况）
	if newStartLine > newEndLine {
		return startLine, endLine, false
	}

	return newStartLine, newEndLine, true
}
